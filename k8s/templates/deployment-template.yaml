# Blue-Green Deployment Template for Blast-Radius Security Tool

apiVersion: apps/v1
kind: Deployment
metadata:
  name: blast-radius-backend-{{COLOR}}
  namespace: blast-radius
  labels:
    app: blast-radius-backend
    version: {{COLOR}}
    component: backend
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: blast-radius-backend
      version: {{COLOR}}
  template:
    metadata:
      labels:
        app: blast-radius-backend
        version: {{COLOR}}
        component: backend
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: blast-radius-backend
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: blast-radius-backend
        image: blast-radius-backend:{{VERSION}}
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
          protocol: TCP
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: VERSION
          value: "{{VERSION}}"
        - name: COLOR
          value: "{{COLOR}}"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: url
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: secret-key
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: jwt-secret
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 1000m
            memory: 2Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: app-config
          mountPath: /app/config
          readOnly: true
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: app-config
        configMap:
          name: blast-radius-config
      - name: logs
        emptyDir: {}
      nodeSelector:
        kubernetes.io/os: linux
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: blast-radius-frontend-{{COLOR}}
  namespace: blast-radius
  labels:
    app: blast-radius-frontend
    version: {{COLOR}}
    component: frontend
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: blast-radius-frontend
      version: {{COLOR}}
  template:
    metadata:
      labels:
        app: blast-radius-frontend
        version: {{COLOR}}
        component: frontend
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/metrics"
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: blast-radius-frontend
        image: blast-radius-frontend:{{VERSION}}
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
          protocol: TCP
        env:
        - name: NODE_ENV
          value: "production"
        - name: VERSION
          value: "{{VERSION}}"
        - name: COLOR
          value: "{{COLOR}}"
        - name: API_BASE_URL
          value: "https://api.blast-radius.com"
        resources:
          requests:
            cpu: 250m
            memory: 512Mi
          limits:
            cpu: 500m
            memory: 1Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/conf.d
          readOnly: true
      volumes:
      - name: nginx-config
        configMap:
          name: nginx-config
      nodeSelector:
        kubernetes.io/os: linux

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: blast-radius-worker-{{COLOR}}
  namespace: blast-radius
  labels:
    app: blast-radius-worker
    version: {{COLOR}}
    component: worker
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: blast-radius-worker
      version: {{COLOR}}
  template:
    metadata:
      labels:
        app: blast-radius-worker
        version: {{COLOR}}
        component: worker
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9540"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: blast-radius-worker
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: blast-radius-worker
        image: blast-radius-backend:{{VERSION}}
        imagePullPolicy: Always
        command: ["celery"]
        args: ["-A", "app.celery_app", "worker", "--loglevel=info", "--concurrency=4"]
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: VERSION
          value: "{{VERSION}}"
        - name: COLOR
          value: "{{COLOR}}"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: url
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: secret-key
        resources:
          requests:
            cpu: 250m
            memory: 512Mi
          limits:
            cpu: 500m
            memory: 1Gi
        livenessProbe:
          exec:
            command:
            - celery
            - -A
            - app.celery_app
            - inspect
            - ping
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - celery
            - -A
            - app.celery_app
            - inspect
            - active
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: worker-config
          mountPath: /app/config
          readOnly: true
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: worker-config
        configMap:
          name: blast-radius-config
      - name: logs
        emptyDir: {}
      nodeSelector:
        kubernetes.io/os: linux
