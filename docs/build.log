Running Sphinx v7.4.7
loading translations [en]... done
Converting `source_suffix = ['.rst', '.md']` to `source_suffix = {'.rst': 'restructuredtext', '.md': 'restructuredtext'}`.
[autosummary] generating autosummary for: DOCUMENTATION_EXPANSION_SUMMARY.md, DOCUMENTATION_SUMMARY.md, LOCAL-DEVELOPMENT.md, README.md, access-control/least-privilege-framework.rst, api/asset-management.rst, api/attack-path-analysis.rst, api/authentication.rst, api/index.rst, api/mitre-attack-integration.rst, ..., user-guides/attack-path-analysis.rst, user-guides/compliance-officers.rst, user-guides/executive-leadership.rst, user-guides/index.rst, user-guides/mitre-attack-integration.rst, user-guides/purple-team-members.rst, user-guides/red-team-members.rst, user-guides/security-architects.rst, user-guides/soc-operators.rst, user-guides/threat-modeling.rst
loading intersphinx inventory 'python' from https://docs.python.org/3/objects.inv...
loading intersphinx inventory 'fastapi' from https://fastapi.tiangolo.com/objects.inv...
loading intersphinx inventory 'sqlalchemy' from https://docs.sqlalchemy.org/en/20/objects.inv...
loading intersphinx inventory 'networkx' from https://networkx.org/documentation/stable/objects.inv...
myst v4.0.0: MdParserConfig(commonmark_only=False, gfm_only=False, enable_extensions={'colon_fence', 'deflist', 'smartquotes', 'substitution', 'tasklist', 'dollarmath', 'html_image', 'html_admonition', 'replacements', 'fieldlist', 'strikethrough'}, disable_syntax=[], all_links_external=False, links_external_new_tab=False, url_schemes=('http', 'https', 'mailto', 'ftp'), ref_domains=None, fence_as_directive=set(), number_code_blocks=[], title_to_header=False, heading_anchors=0, heading_slug_func=None, html_meta={}, footnote_sort=True, footnote_transition=True, words_per_minute=200, substitutions={}, linkify_fuzzy_links=True, dmath_allow_labels=True, dmath_allow_space=True, dmath_allow_digits=True, dmath_double_inline=False, update_mathjax=True, mathjax_classes='tex2jax_process|mathjax_process|math|output_area', enable_checkboxes=False, suppress_warnings=[], highlight_code_blocks=True)
building [mo]: targets for 0 po files that are out of date
writing output... 
building [html]: targets for 81 source files that are out of date
updating environment: [new config] 81 added, 0 changed, 0 removed
reading sources... [  1%] DOCUMENTATION_EXPANSION_SUMMARY
reading sources... [  2%] DOCUMENTATION_SUMMARY
reading sources... [  4%] LOCAL-DEVELOPMENT
reading sources... [  5%] README
reading sources... [  6%] access-control/least-privilege-framework
reading sources... [  7%] api/asset-management
reading sources... [  9%] api/attack-path-analysis
reading sources... [ 10%] api/authentication
reading sources... [ 11%] api/index
reading sources... [ 12%] api/mitre-attack-integration
reading sources... [ 14%] api/threat-modeling
reading sources... [ 15%] architecture/zero-trust-architecture
reading sources... [ 16%] compliance/gdpr-compliance-framework
reading sources... [ 17%] configuration
reading sources... [ 19%] deployment/environment-setup
reading sources... [ 20%] deployment/production-architecture
reading sources... [ 21%] deployment/production-deployment-guide
reading sources... [ 22%] deployment/troubleshooting-guide
reading sources... [ 23%] development/code-standards
reading sources... [ 25%] development/contributing
reading sources... [ 26%] development/setup
reading sources... [ 27%] development/workflow
reading sources... [ 28%] documentation-achievements-summary
reading sources... [ 30%] documentation-overview
reading sources... [ 31%] enhanced-features-summary
reading sources... [ 32%] enhanced-prd-v2
reading sources... [ 33%] implementation-gap-analysis
reading sources... [ 35%] index
reading sources... [ 36%] installation
reading sources... [ 37%] latest-implementations-summary
reading sources... [ 38%] multi-cloud-integration
reading sources... [ 40%] operations/runbooks/backup-recovery-runbooks
reading sources... [ 41%] operations/runbooks/maintenance-procedures
reading sources... [ 42%] operations/runbooks/monitoring-runbooks
reading sources... [ 43%] performance/index
reading sources... [ 44%] performance/optimization
reading sources... [ 46%] phase-integration-plan
reading sources... [ 47%] production-readiness-status
reading sources... [ 48%] quick-start-guide
reading sources... [ 49%] security/architecture/overview
reading sources... [ 51%] security/compliance-documentation
reading sources... [ 52%] security/enhanced-audit-logging
reading sources... [ 53%] security/framework
reading sources... [ 54%] security/incident-response-procedures
reading sources... [ 56%] security/index
reading sources... [ 57%] security/operations/incident-response
reading sources... [ 58%] security/procedures/security-review-process
reading sources... [ 59%] security/procedures/vulnerability-disclosure
reading sources... [ 60%] security/reviews/security-assessment
reading sources... [ 62%] security/reviews/security-review-2025-06-14
reading sources... [ 63%] security/reviews/vulnerability-management
reading sources... [ 64%] security/security-review-processes
reading sources... [ 65%] security/security-summary
reading sources... [ 67%] security/testing/dynamic-testing
reading sources... [ 68%] security/testing/security-automation
reading sources... [ 69%] security/testing/static-analysis
reading sources... [ 70%] technical-specifications/compliance-framework-schema
reading sources... [ 72%] technical-specifications/ml-threat-prediction
reading sources... [ 73%] technical-specifications/thehive-integration
reading sources... [ 74%] technical/attack-path-architecture
reading sources... [ 75%] technical/attack-path-flows
reading sources... [ 77%] technical/database-design
reading sources... [ 78%] technical/index
reading sources... [ 79%] technical/product-requirements
reading sources... [ 80%] testing/index
reading sources... [ 81%] testing/unit-tests
reading sources... [ 83%] troubleshooting/common-issues
reading sources... [ 84%] troubleshooting/faq
reading sources... [ 85%] use-cases/asset-discovery
reading sources... [ 86%] use-cases/attack-path-analysis
reading sources... [ 88%] user-guides/administrators
reading sources... [ 89%] user-guides/attack-path-analysis
reading sources... [ 90%] user-guides/compliance-officers
reading sources... [ 91%] user-guides/executive-leadership
reading sources... [ 93%] user-guides/index
reading sources... [ 94%] user-guides/mitre-attack-integration
reading sources... [ 95%] user-guides/purple-team-members
reading sources... [ 96%] user-guides/red-team-members
reading sources... [ 98%] user-guides/security-architects
reading sources... [ 99%] user-guides/soc-operators
reading sources... [100%] user-guides/threat-modeling

/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:9: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Least Privilege Framework"
           subgraph "Access Request Layer"
               A[Access Request]
               B[Business Justification]
               C[Risk Assessment]
               D[Approval Workflow]
           end

           subgraph "Provisioning Layer"
               E[Just-in-Time Access]
               F[Time-Limited Permissions]
               G[Automated Provisioning]
               H[Role-Based Templates]
           end

           subgraph "Monitoring Layer"
               I[Usage Monitoring]
               J[Anomaly Detection]
               K[Access Reviews]
               L[Compliance Tracking]
           end

           subgraph "Enforcement Layer"
               M[Policy Enforcement]
               N[Privilege Escalation]
               O[Automated Revocation]
               P[Emergency Access]
           end
       end

       A --> E
       B --> F
       C --> G
       D --> H
       E --> I
       F --> J
       G --> K
       H --> L
       I --> M
       J --> N
       K --> O
       L --> P
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:59: WARNING: Title underline too short.

Access Request Management
~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:63: ERROR: Unknown directive type "mermaid".

.. mermaid::

   sequenceDiagram
       participant U as User
       participant AR as Access Request System
       participant RA as Risk Assessment
       participant AM as Approval Manager
       participant PS as Provisioning Service
       participant AS as Audit Service

       U->>AR: Submit Access Request
       AR->>RA: Assess Risk Level
       RA->>AR: Return Risk Score

       alt High Risk
           AR->>AM: Require Approval
           AM->>AM: Route to Approver
           AM->>AR: Approval Decision
       else Low Risk
           AR->>AR: Auto-Approve
       end

       AR->>PS: Provision Access
       PS->>PS: Grant Permissions
       PS->>AS: Log Access Grant
       PS->>U: Access Granted
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:99: WARNING: Title underline too short.

Just-in-Time Access Provisioning
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:99: WARNING: Title underline too short.

Just-in-Time Access Provisioning
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:103: ERROR: Unknown directive type "mermaid".

.. mermaid::

   flowchart TD
       A[Access Need Identified] --> B[Submit JIT Request]
       B --> C[Automated Risk Assessment]
       C --> D{Risk Level}

       D -->|Low| E[Auto-Approve]
       D -->|Medium| F[Manager Approval]
       D -->|High| G[Security Team Approval]

       E --> H[Provision Access]
       F --> H
       G --> H

       H --> I[Set Expiration Timer]
       I --> J[Monitor Usage]
       J --> K{Still Needed?}

       K -->|Yes| L[Extend Access]
       K -->|No| M[Revoke Access]
       K -->|Expired| M

       L --> J
       M --> N[Audit Revocation]
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:138: WARNING: Title underline too short.

Privilege Escalation Management
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:138: WARNING: Title underline too short.

Privilege Escalation Management
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:142: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Escalation Process"
           A[Escalation Request] --> B[Current Role Analysis]
           B --> C[Target Role Validation]
           C --> D[Justification Review]
           D --> E[Approval Required]
           E --> F[Temporary Escalation]
           F --> G[Monitoring & Alerts]
           G --> H[Automatic Reversion]
       end

       subgraph "Security Controls"
           I[Break-Glass Access]
           J[Emergency Procedures]
           K[Audit Logging]
           L[Real-time Monitoring]
       end

       A --> I
       E --> J
       F --> K
       G --> L
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:176: WARNING: Title underline too short.

Access Review and Certification
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:176: WARNING: Title underline too short.

Access Review and Certification
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:180: ERROR: Unknown directive type "mermaid".

.. mermaid::

   gantt
       title Access Review Schedule
       dateFormat  YYYY-MM-DD
       section User Access Reviews
       Regular Users           :2024-01-01, 90d
       Privileged Users        :2024-01-01, 30d
       Service Accounts        :2024-01-01, 60d

       section Role Reviews
       Standard Roles          :2024-01-01, 180d
       Administrative Roles    :2024-01-01, 90d
       Emergency Roles         :2024-01-01, 30d

       section Compliance Reviews
       SOC 2 Certification     :2024-01-01, 365d
       ISO 27001 Review        :2024-01-01, 365d
       Internal Audit          :2024-01-01, 180d
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:209: WARNING: Title underline too short.

Implementation Details
---------------------
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:209: WARNING: Title underline too short.

Implementation Details
---------------------
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:212: WARNING: Title underline too short.

Access Control Architecture
~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:214: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Access Control Service"
           A[Request Handler] --> B[Risk Assessor]
           B --> C[Approval Engine]
           C --> D[Provisioning Manager]
           D --> E[Monitoring Service]
           E --> F[Review Scheduler]
       end

       subgraph "Data Stores"
           G[Access Requests]
           H[User Roles]
           I[Permission Templates]
           J[Audit Logs]
           K[Review Records]
       end

       subgraph "External Systems"
           L[Identity Provider]
           M[RBAC System]
           N[Notification Service]
           O[Approval System]
       end

       A --> G
       B --> H
       C --> I
       D --> J
       E --> K

       C --> L
       D --> M
       A --> N
       C --> O
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:261: WARNING: Title underline too short.

API Endpoints
~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:261: WARNING: Title underline too short.

API Endpoints
~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:328: WARNING: Title underline too short.

Role-Based Access Templates
~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:328: WARNING: Title underline too short.

Role-Based Access Templates
~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:332: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph LR
       subgraph "Role Templates"
           A[Security Analyst] --> B[Read Assets]
           A --> C[Read Threats]
           A --> D[Create Incidents]

           E[Security Admin] --> F[Admin Users]
           E --> G[Admin System]
           E --> H[All Security Analyst]

           I[Auditor] --> J[Read All Data]
           I --> K[Export Reports]
           I --> L[No Modifications]
       end

       subgraph "Permission Sets"
           M[Asset Permissions]
           N[User Permissions]
           O[System Permissions]
           P[Audit Permissions]
       end

       B --> M
       C --> M
       F --> N
       G --> O
       J --> P
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:371: WARNING: Title underline too short.

Risk Assessment Engine
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:371: WARNING: Title underline too short.

Risk Assessment Engine
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:375: ERROR: Unknown directive type "mermaid".

.. mermaid::

   flowchart TD
       A[Access Request] --> B[User Risk Profile]
       A --> C[Resource Sensitivity]
       A --> D[Access Context]
       A --> E[Historical Patterns]

       B --> F[Risk Calculation]
       C --> F
       D --> F
       E --> F

       F --> G{Risk Score}

       G -->|0-30| H[Low Risk - Auto Approve]
       G -->|31-70| I[Medium Risk - Manager Approval]
       G -->|71-100| J[High Risk - Security Approval]

       H --> K[Grant Access]
       I --> L[Approval Workflow]
       J --> M[Enhanced Review]

       L --> K
       M --> K
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:410: WARNING: Title underline too short.

Monitoring and Analytics
-----------------------
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:410: WARNING: Title underline too short.

Monitoring and Analytics
-----------------------
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:413: WARNING: Title underline too short.

Access Usage Monitoring
~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:417: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Monitoring Dimensions"
           A[User Activity]
           B[Resource Access]
           C[Permission Usage]
           D[Time Patterns]
           E[Location Patterns]
           F[Anomaly Detection]
       end

       subgraph "Analytics"
           G[Usage Statistics]
           H[Trend Analysis]
           I[Risk Scoring]
           J[Compliance Metrics]
       end

       subgraph "Alerting"
           K[Real-time Alerts]
           L[Threshold Violations]
           M[Anomaly Notifications]
           N[Compliance Warnings]
       end

       A --> G
       B --> H
       C --> I
       D --> J
       E --> G
       F --> H

       G --> K
       H --> L
       I --> M
       J --> N
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:464: WARNING: Title underline too short.

Access Analytics Dashboard
~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:464: WARNING: Title underline too short.

Access Analytics Dashboard
~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:468: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Dashboard Sections"
           A[Access Overview] --> B[Active Requests]
           A --> C[Recent Approvals]
           A --> D[Pending Reviews]

           E[Risk Analysis] --> F[High-Risk Users]
           E --> G[Sensitive Resources]
           E --> H[Anomaly Alerts]

           I[Compliance Status] --> J[Review Completion]
           I --> K[Policy Violations]
           I --> L[Audit Findings]

           M[Performance Metrics] --> N[Request Processing]
           M --> O[Approval Times]
           M --> P[System Health]
       end
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:501: WARNING: Title underline too short.

Policy Configuration
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:538: WARNING: Title underline too short.

Role Template Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:538: WARNING: Title underline too short.

Role Template Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:580: WARNING: Title underline too short.

Access Management Principles
~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:590: WARNING: Title underline too short.

Operational Guidelines
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:590: WARNING: Title underline too short.

Operational Guidelines
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:617: WARNING: Title underline too short.

Troubleshooting
--------------
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:617: WARNING: Title underline too short.

Troubleshooting
--------------
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:620: WARNING: Title underline too short.

Common Issues
~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:653: WARNING: Title underline too short.

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:653: WARNING: Title underline too short.

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:43: WARNING: Title underline too short.

ATT&CK Data Management
---------------------
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:43: WARNING: Title underline too short.

ATT&CK Data Management
---------------------
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:46: WARNING: Title underline too short.

Get Data Status
~~~~~~~~~~~~~~
WARNING: unknown directive or role name: http:get
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:50: ERROR: Unknown directive type "http:get".

.. http:get:: /data/status

   **Response:**

   .. code-block:: json

      {
        "enterprise": {
          "technique_count": 800,
          "tactic_count": 14,
          "group_count": 150,
          "software_count": 700,
          "last_update": "2024-01-15T10:30:00Z",
          "version": "14.1"
        },
        "mobile": {
          "technique_count": 100,
          "tactic_count": 11,
          "group_count": 50,
          "software_count": 200,
          "last_update": "2024-01-15T10:30:00Z",
          "version": "14.1"
        },
        "ics": {
          "technique_count": 80,
          "tactic_count": 11,
          "group_count": 30,
          "software_count": 100,
          "last_update": "2024-01-15T10:30:00Z",
          "version": "14.1"
        }
      }

   :statuscode 200: Data status retrieved successfully
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:86: WARNING: Title underline too short.

Sync ATT&CK Data
~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:86: WARNING: Title underline too short.

Sync ATT&CK Data
~~~~~~~~~~~~~~~
WARNING: unknown directive or role name: http:post
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:90: ERROR: Unknown directive type "http:post".

.. http:post:: /data/sync

   **Request Body:**

   .. code-block:: json

      {
        "domains": ["enterprise", "mobile", "ics"],
        "force_update": true
      }

   **Response:**

   .. code-block:: json

      {
        "sync_id": "sync_12345",
        "status": "in_progress",
        "domains": ["enterprise", "mobile", "ics"],
        "estimated_completion": "2024-01-15T10:35:00Z"
      }

   :statuscode 202: Synchronization started
   :statuscode 400: Invalid request parameters
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:116: WARNING: Title underline too short.

Get Technique Information
~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:116: WARNING: Title underline too short.

Get Technique Information
~~~~~~~~~~~~~~~~~~~~~~~
WARNING: unknown directive or role name: http:get
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:120: ERROR: Unknown directive type "http:get".

.. http:get:: /techniques/(technique_id)

   **Response:**

   .. code-block:: json

      {
        "technique_id": "T1566.001",
        "name": "Spearphishing Attachment",
        "description": "Adversaries may send spearphishing emails with a malicious attachment...",
        "tactic": "Initial Access",
        "domain": "enterprise",
        "platforms": ["Linux", "macOS", "Windows"],
        "data_sources": ["Application Log", "Email Gateway", "File"],
        "mitigations": ["M1049", "M1031", "M1021"],
        "detection_methods": [
          "Monitor for suspicious email attachments",
          "Analyze file execution patterns"
        ],
        "sub_techniques": [],
        "kill_chain_phases": ["initial-access"],
        "external_references": [
          {
            "source_name": "mitre-attack",
            "url": "https://attack.mitre.org/techniques/T1566/001",
            "external_id": "T1566.001"
          }
        ],
        "created": "2020-03-02T19:05:18.150Z",
        "modified": "2023-10-03T20:10:35.462Z",
        "version": "1.4"
      }

   :statuscode 200: Technique information retrieved
   :statuscode 404: Technique not found
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:157: WARNING: Title underline too short.

Technique Correlation
--------------------
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:157: WARNING: Title underline too short.

Technique Correlation
--------------------
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:160: WARNING: Title underline too short.

Correlate Security Event
~~~~~~~~~~~~~~~~~~~~~~~
WARNING: unknown directive or role name: http:post
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:164: ERROR: Unknown directive type "http:post".

.. http:post:: /correlate

   **Request Body:**

   .. code-block:: json

      {
        "event_id": "evt_12345",
        "event_data": {
          "source": "endpoint_detection",
          "event_type": "process_execution",
          "process_name": "powershell.exe",
          "command_line": "powershell -enc <base64_payload>",
          "parent_process": "winword.exe",
          "user": "john.doe",
          "timestamp": "2024-01-15T10:30:00Z",
          "host": "workstation-001"
        },
        "correlation_options": {
          "min_confidence": 0.5,
          "include_sub_techniques": true,
          "max_techniques": 10
        }
      }

   **Response:**

   .. code-block:: json

      {
        "correlation_id": "corr_67890",
        "event_id": "evt_12345",
        "techniques": [
          {
            "technique_id": "T1059.001",
            "name": "PowerShell",
            "confidence": 0.95,
            "confidence_level": "very_high",
            "evidence": [
              "PowerShell execution detected",
              "Base64 encoded command line",
              "Spawned from Office application"
            ],
            "context": {
              "tactic": "Execution",
              "parent_technique": "T1059",
              "detection_methods": ["Process monitoring", "Command line analysis"]
            }
          },
          {
            "technique_id": "T1566.001",
            "name": "Spearphishing Attachment",
            "confidence": 0.85,
            "confidence_level": "high",
            "evidence": [
              "Office application as parent process",
              "Suspicious PowerShell execution"
            ],
            "context": {
              "tactic": "Initial Access",
              "detection_methods": ["Email analysis", "Process monitoring"]
            }
          }
        ],
        "overall_confidence": 0.90,
        "timestamp": "2024-01-15T10:30:05Z"
      }

   :statuscode 200: Correlation completed successfully
   :statuscode 400: Invalid event data
   :statuscode 422: Correlation failed
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:237: WARNING: Title underline too short.

Batch Correlate Events
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:237: WARNING: Title underline too short.

Batch Correlate Events
~~~~~~~~~~~~~~~~~~~~~
WARNING: unknown directive or role name: http:post
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:241: ERROR: Unknown directive type "http:post".

.. http:post:: /correlate/batch

   **Request Body:**

   .. code-block:: json

      {
        "events": [
          {
            "event_id": "evt_001",
            "event_data": { /* event data */ }
          },
          {
            "event_id": "evt_002",
            "event_data": { /* event data */ }
          }
        ],
        "correlation_options": {
          "min_confidence": 0.6,
          "parallel_processing": true
        }
      }

   **Response:**

   .. code-block:: json

      {
        "batch_id": "batch_123",
        "correlations": [
          {
            "event_id": "evt_001",
            "techniques": [ /* correlation results */ ]
          },
          {
            "event_id": "evt_002",
            "techniques": [ /* correlation results */ ]
          }
        ],
        "processing_time_ms": 250,
        "total_events": 2,
        "successful_correlations": 2
      }
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:286: WARNING: Title underline too short.

Threat Actor Attribution
-----------------------
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:286: WARNING: Title underline too short.

Threat Actor Attribution
-----------------------
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:289: WARNING: Title underline too short.

Attribute Threat Actor
~~~~~~~~~~~~~~~~~~~~~
WARNING: unknown directive or role name: http:post
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:293: ERROR: Unknown directive type "http:post".

.. http:post:: /attribute

   **Request Body:**

   .. code-block:: json

      {
        "techniques": ["T1566.001", "T1078", "T1055", "T1003"],
        "timeframe": "7d",
        "context": {
          "target_sector": "government",
          "geographic_region": "eastern_europe",
          "attack_sophistication": "high"
        },
        "attribution_options": {
          "min_confidence": 0.7,
          "include_campaigns": true,
          "max_groups": 5
        }
      }

   **Response:**

   .. code-block:: json

      {
        "attribution_id": "attr_456",
        "attributed_groups": [
          {
            "group_id": "G0016",
            "name": "APT29",
            "aliases": ["Cozy Bear", "The Dukes"],
            "confidence_score": 0.92,
            "matching_techniques": ["T1566.001", "T1078", "T1055"],
            "behavioral_score": 0.88,
            "evidence": [
              "High overlap in technique usage",
              "Consistent with known APT29 campaigns",
              "Geographic and sector targeting match"
            ]
          },
          {
            "group_id": "G0028",
            "name": "APT28",
            "aliases": ["Fancy Bear", "Sofacy"],
            "confidence_score": 0.75,
            "matching_techniques": ["T1566.001", "T1003"],
            "behavioral_score": 0.70,
            "evidence": [
              "Partial technique overlap",
              "Similar targeting patterns"
            ]
          }
        ],
        "campaign_correlations": [
          {
            "campaign_id": "camp_789",
            "name": "Operation Ghost Writer",
            "confidence": 0.85,
            "attributed_group": "G0016"
          }
        ],
        "overall_confidence": 0.88,
        "timestamp": "2024-01-15T10:30:00Z"
      }

   :statuscode 200: Attribution completed successfully
   :statuscode 400: Invalid technique list
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:363: WARNING: Title underline too short.

Get Threat Actor Profile
~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:363: WARNING: Title underline too short.

Get Threat Actor Profile
~~~~~~~~~~~~~~~~~~~~~~~
WARNING: unknown directive or role name: http:get
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:367: ERROR: Unknown directive type "http:get".

.. http:get:: /groups/(group_id)

   **Response:**

   .. code-block:: json

      {
        "group_id": "G0016",
        "name": "APT29",
        "description": "APT29 is threat group that has been attributed to Russia's Foreign Intelligence Service...",
        "aliases": ["Cozy Bear", "The Dukes", "Minidionis"],
        "techniques": ["T1566.001", "T1078", "T1055", "T1003", "T1071"],
        "software": ["S0154", "S0363", "S0482"],
        "associated_campaigns": ["C0021", "C0024"],
        "target_sectors": ["government", "technology", "healthcare"],
        "geographic_focus": ["north_america", "europe"],
        "sophistication_level": 0.9,
        "external_references": [
          {
            "source_name": "mitre-attack",
            "url": "https://attack.mitre.org/groups/G0016/",
            "external_id": "G0016"
          }
        ],
        "created": "2017-05-31T21:32:11.265Z",
        "modified": "2023-09-29T21:13:49.686Z"
      }
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:396: WARNING: Title underline too short.

Attack Pattern Analysis
----------------------
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:396: WARNING: Title underline too short.

Attack Pattern Analysis
----------------------
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:399: WARNING: Title underline too short.

Analyze Attack Patterns
~~~~~~~~~~~~~~~~~~~~~~
WARNING: unknown directive or role name: http:post
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:403: ERROR: Unknown directive type "http:post".

.. http:post:: /patterns/analyze

   **Request Body:**

   .. code-block:: json

      {
        "timeframe": "30d",
        "analysis_options": {
          "min_confidence": 0.7,
          "include_sub_techniques": true,
          "pattern_types": ["sequence", "temporal", "multi_asset"],
          "min_event_count": 5
        },
        "filters": {
          "asset_types": ["endpoint", "server"],
          "user_groups": ["privileged_users"],
          "severity_levels": ["high", "critical"]
        }
      }

   **Response:**

   .. code-block:: json

      {
        "analysis_id": "analysis_123",
        "patterns": [
          {
            "pattern_id": "pattern_001",
            "name": "Credential Dumping Campaign",
            "techniques": ["T1566.001", "T1059.001", "T1003.001"],
            "sequence": ["T1566.001", "T1059.001", "T1003.001"],
            "confidence": 0.89,
            "first_seen": "2024-01-01T08:00:00Z",
            "last_seen": "2024-01-15T16:30:00Z",
            "event_count": 25,
            "affected_assets": ["ws-001", "ws-002", "srv-001"],
            "attributed_groups": ["G0016"],
            "campaign_id": "camp_456"
          }
        ],
        "summary": {
          "total_patterns": 1,
          "high_confidence_patterns": 1,
          "unique_techniques": 3,
          "affected_assets": 3
        }
      }
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:454: WARNING: Title underline too short.

Track Campaigns
~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:454: WARNING: Title underline too short.

Track Campaigns
~~~~~~~~~~~~~~
WARNING: unknown directive or role name: http:get
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:458: ERROR: Unknown directive type "http:get".

.. http:get:: /campaigns

   **Query Parameters:**

   * ``timeframe`` (optional) - Time range for campaign tracking (default: 30d)
   * ``attribution_threshold`` (optional) - Minimum confidence for attribution (default: 0.8)
   * ``include_ongoing`` (optional) - Include ongoing campaigns (default: true)

   **Response:**

   .. code-block:: json

      {
        "campaigns": [
          {
            "campaign_id": "camp_789",
            "name": "Operation Ghost Writer",
            "attributed_groups": ["G0016"],
            "attribution_confidence": 0.92,
            "start_date": "2024-01-01T00:00:00Z",
            "end_date": null,
            "status": "ongoing",
            "techniques": ["T1566.001", "T1078", "T1055"],
            "affected_assets": ["ws-001", "ws-002", "srv-001"],
            "event_count": 45,
            "target_sectors": ["government"],
            "geographic_regions": ["north_america"]
          }
        ],
        "summary": {
          "total_campaigns": 1,
          "ongoing_campaigns": 1,
          "attributed_campaigns": 1
        }
      }
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:495: WARNING: Title underline too short.

ATT&CK Navigator Integration
---------------------------
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:495: WARNING: Title underline too short.

ATT&CK Navigator Integration
---------------------------
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:498: WARNING: Title underline too short.

Generate Navigator Layer
~~~~~~~~~~~~~~~~~~~~~~~
WARNING: unknown directive or role name: http:post
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:502: ERROR: Unknown directive type "http:post".

.. http:post:: /navigator/generate

   **Request Body:**

   .. code-block:: json

      {
        "layer_name": "Q4 Threat Landscape",
        "description": "Quarterly threat analysis for organization",
        "data_source": "organization_events",
        "timeframe": "90d",
        "layer_options": {
          "domain": "enterprise",
          "color_scheme": "red",
          "include_sub_techniques": true,
          "score_type": "frequency"
        },
        "filters": {
          "min_confidence": 0.7,
          "technique_categories": ["all"]
        }
      }

   **Response:**

   .. code-block:: json

      {
        "layer_id": "layer_123",
        "layer_name": "Q4 Threat Landscape",
        "navigator_layer": {
          "name": "Q4 Threat Landscape",
          "versions": {
            "attack": "14",
            "navigator": "4.9.1",
            "layer": "4.5"
          },
          "domain": "enterprise-attack",
          "description": "Quarterly threat analysis for organization",
          "techniques": [
            {
              "techniqueID": "T1566.001",
              "score": 85,
              "color": "#ff6b6b",
              "comment": "High frequency technique - 85 occurrences"
            }
          ]
        },
        "statistics": {
          "total_techniques": 45,
          "scored_techniques": 45,
          "max_score": 85,
          "avg_score": 12.3
        }
      }
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:559: WARNING: Title underline too short.

Export Navigator Layer
~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:559: WARNING: Title underline too short.

Export Navigator Layer
~~~~~~~~~~~~~~~~~~~~
WARNING: unknown directive or role name: http:get
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:563: ERROR: Unknown directive type "http:get".

.. http:get:: /navigator/layers/(layer_id)/export

   **Query Parameters:**

   * ``format`` - Export format (json, svg, png)
   * ``include_legend`` (optional) - Include legend in visualization
   * ``resolution`` (optional) - Image resolution for PNG export

   **Response:**

   For JSON format:

   .. code-block:: json

      {
        "layer_data": { /* Navigator layer JSON */ },
        "export_format": "json",
        "generated_at": "2024-01-15T10:30:00Z"
      }

   For SVG/PNG formats, returns the binary image data.
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:586: WARNING: Title underline too short.

Threat Intelligence Enrichment
-----------------------------
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:586: WARNING: Title underline too short.

Threat Intelligence Enrichment
-----------------------------
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:589: WARNING: Title underline too short.

Enrich IOCs
~~~~~~~~~~
WARNING: unknown directive or role name: http:post
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:593: ERROR: Unknown directive type "http:post".

.. http:post:: /enrich/iocs

   **Request Body:**

   .. code-block:: json

      {
        "iocs": [
          {
            "type": "domain",
            "value": "malicious-domain.com"
          },
          {
            "type": "hash",
            "value": "a1b2c3d4e5f6789..."
          },
          {
            "type": "ip",
            "value": "*************"
          }
        ],
        "enrichment_options": {
          "include_techniques": true,
          "include_groups": true,
          "include_campaigns": true,
          "confidence_threshold": 0.6
        }
      }

   **Response:**

   .. code-block:: json

      {
        "enrichment_id": "enrich_456",
        "enriched_iocs": [
          {
            "ioc": {
              "type": "domain",
              "value": "malicious-domain.com"
            },
            "enrichment": {
              "techniques": ["T1071.001", "T1573.002"],
              "threat_groups": ["G0016", "G0028"],
              "campaigns": ["camp_789"],
              "confidence": 0.85,
              "first_seen": "2024-01-01T00:00:00Z",
              "last_seen": "2024-01-15T10:30:00Z"
            }
          }
        ],
        "processing_time_ms": 150
      }
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:648: WARNING: Title underline too short.

Get Event Context
~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:648: WARNING: Title underline too short.

Get Event Context
~~~~~~~~~~~~~~~~
WARNING: unknown directive or role name: http:get
/home/<USER>/dev/work/blast-radius/docs/api/mitre-attack-integration.rst:652: ERROR: Unknown directive type "http:get".

.. http:get:: /context/events/(event_id)

   **Query Parameters:**

   * ``include_historical`` (optional) - Include historical context
   * ``correlation_window`` (optional) - Time window for correlation (default: 24h)

   **Response:**

   .. code-block:: json

      {
        "event_id": "evt_67890",
        "context": {
          "primary_technique": "T1059.001",
          "related_techniques": ["T1566.001", "T1055"],
          "threat_actor_probabilities": {
            "G0016": 0.85,
            "G0028": 0.65
          },
          "campaign_correlation": {
            "campaign_id": "camp_789",
            "confidence": 0.78
          },
          "historical_context": {
            "similar_events_7d": 12,
            "technique_frequency": 0.15,
            "baseline_deviation": 2.3
          }
        }
      }
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:46: WARNING: Title underline too short.

Simulate Attack
~~~~~~~~~~~~~~
WARNING: unknown directive or role name: http:post
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:50: ERROR: Unknown directive type "http:post".

.. http:post:: /simulate

   **Request Body:**

   .. code-block:: json

      {
        "threat_actor_id": "APT29",
        "target_assets": ["database_001", "web_server_001"],
        "scenario_name": "Q4 Risk Assessment",
        "simulation_parameters": {
          "max_attack_duration_hours": 168,
          "include_insider_threat": true,
          "consider_supply_chain": true,
          "regulatory_context": ["GDPR", "SOX"]
        }
      }

   **Response:**

   .. code-block:: json

      {
        "simulation_id": "sim_12345",
        "threat_actor_id": "APT29",
        "scenario_name": "Q4 Risk Assessment",
        "target_assets": ["database_001", "web_server_001"],
        "success_probability": 0.75,
        "detection_probability": 0.35,
        "estimated_time_to_compromise": 48.5,
        "financial_impact": 2500000.0,
        "compliance_impact": {
          "affected_regulations": ["GDPR", "SOX"],
          "violation_probability": 0.8,
          "potential_fines": 5000000.0
        },
        "attack_paths": [
          {
            "path_id": "path_001",
            "source_asset": "internet",
            "target_asset": "database_001",
            "attack_techniques": ["T1566", "T1078", "T1055"],
            "success_probability": 0.7,
            "detection_probability": 0.4
          }
        ],
        "created_at": "2024-01-15T10:30:00Z"
      }

   :statuscode 200: Simulation completed successfully
   :statuscode 400: Invalid request parameters
   :statuscode 401: Authentication required
   :statuscode 404: Threat actor or assets not found
   :statuscode 500: Internal server error
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:106: WARNING: Title underline too short.

Get Simulation Results
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:106: WARNING: Title underline too short.

Get Simulation Results
~~~~~~~~~~~~~~~~~~~~~
WARNING: unknown directive or role name: http:get
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:110: ERROR: Unknown directive type "http:get".

.. http:get:: /simulations/(simulation_id)

   **Response:**

   .. code-block:: json

      {
        "simulation_id": "sim_12345",
        "status": "completed",
        "threat_actor_id": "APT29",
        "scenario_name": "Q4 Risk Assessment",
        "results": {
          "success_probability": 0.75,
          "detection_probability": 0.35,
          "estimated_time_to_compromise": 48.5,
          "financial_impact": 2500000.0,
          "detailed_analysis": {
            "entry_points": ["web_server_001"],
            "attack_vectors": ["phishing", "credential_stuffing"],
            "critical_vulnerabilities": ["CVE-2023-1234"],
            "security_gaps": ["insufficient_mfa", "weak_monitoring"]
          }
        }
      }

   :statuscode 200: Simulation results retrieved
   :statuscode 404: Simulation not found
WARNING: unknown directive or role name: http:get
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:143: ERROR: Unknown directive type "http:get".

.. http:get:: /threat-actors

   **Query Parameters:**

   * ``sophistication_level`` (optional) - Filter by sophistication level (0.0-1.0)
   * ``motivation`` (optional) - Filter by primary motivation
   * ``sector`` (optional) - Filter by target sector

   **Response:**

   .. code-block:: json

      {
        "threat_actors": [
          {
            "actor_id": "APT29",
            "name": "Cozy Bear",
            "sophistication_level": 0.9,
            "resource_level": 0.8,
            "persistence_level": 0.85,
            "primary_motivation": "espionage",
            "geographic_origin": "Russia",
            "target_sectors": ["government", "technology", "healthcare"],
            "preferred_techniques": ["T1566", "T1078", "T1055"],
            "known_tools": ["Cobalt Strike", "PowerShell Empire"],
            "attribution_confidence": 0.8
          }
        ]
      }
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:177: WARNING: Title underline too short.

Assess Risk
~~~~~~~~~~
WARNING: unknown directive or role name: http:post
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:181: ERROR: Unknown directive type "http:post".

.. http:post:: /risk-assessment

   **Request Body:**

   .. code-block:: json

      {
        "asset_ids": ["database_001", "web_server_001"],
        "threat_context": {
          "threat_actors": ["APT29", "APT28"],
          "time_horizon_days": 365,
          "business_context": {
            "annual_revenue": 100000000,
            "customer_count": 50000,
            "data_sensitivity": "PII",
            "regulatory_requirements": ["GDPR", "HIPAA"]
          }
        }
      }

   **Response:**

   .. code-block:: json

      {
        "assessment_id": "risk_67890",
        "overall_risk_score": 0.75,
        "risk_level": "high",
        "asset_risk_scores": {
          "database_001": 0.9,
          "web_server_001": 0.6
        },
        "threat_landscape_score": 0.8,
        "vulnerability_score": 0.7,
        "compliance_risk_score": 0.65,
        "financial_impact_estimate": {
          "expected_loss": 1500000.0,
          "worst_case_loss": 5000000.0,
          "confidence_interval": [800000.0, 3200000.0]
        },
        "recommendations": [
          {
            "priority": "high",
            "category": "access_control",
            "description": "Implement multi-factor authentication",
            "risk_reduction": 0.3,
            "estimated_cost": 50000.0,
            "implementation_time_days": 30
          }
        ]
      }
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:234: WARNING: Title underline too short.

Financial Impact Assessment
~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:234: WARNING: Title underline too short.

Financial Impact Assessment
~~~~~~~~~~~~~~~~~~~~~~~~~~
WARNING: unknown directive or role name: http:post
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:238: ERROR: Unknown directive type "http:post".

.. http:post:: /financial-impact

   **Request Body:**

   .. code-block:: json

      {
        "affected_assets": ["database_001", "payment_system"],
        "attack_scenario": "data_breach",
        "business_context": {
          "annual_revenue": 100000000,
          "customer_count": 50000,
          "industry": "financial_services",
          "geographic_regions": ["US", "EU"],
          "data_types": ["PII", "financial_data"],
          "regulatory_requirements": ["GDPR", "PCI_DSS", "SOX"]
        }
      }

   **Response:**

   .. code-block:: json

      {
        "assessment_id": "fin_impact_123",
        "total_impact": 3750000.0,
        "impact_breakdown": {
          "direct_costs": {
            "incident_response": 150000.0,
            "forensic_investigation": 100000.0,
            "system_recovery": 200000.0,
            "legal_fees": 300000.0
          },
          "indirect_costs": {
            "business_disruption": 800000.0,
            "customer_churn": 1200000.0,
            "reputation_damage": 500000.0,
            "competitive_disadvantage": 300000.0
          },
          "regulatory_costs": {
            "gdpr_fines": 200000.0,
            "pci_penalties": 100000.0,
            "sox_compliance": 50000.0
          }
        },
        "recovery_timeline": {
          "immediate_response": "0-24 hours",
          "containment": "1-3 days",
          "investigation": "1-4 weeks",
          "full_recovery": "2-6 months"
        }
      }
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:292: WARNING: Title underline too short.

Compliance Impact Analysis
-------------------------
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:292: WARNING: Title underline too short.

Compliance Impact Analysis
-------------------------
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:295: WARNING: Title underline too short.

GDPR Impact Assessment
~~~~~~~~~~~~~~~~~~~~~
WARNING: unknown directive or role name: http:post
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:299: ERROR: Unknown directive type "http:post".

.. http:post:: /compliance/gdpr

   **Request Body:**

   .. code-block:: json

      {
        "affected_assets": ["customer_database", "web_application"],
        "data_types": ["personal_data", "sensitive_personal_data"],
        "data_subjects_affected": 10000,
        "breach_scenario": "unauthorized_access",
        "data_controller_info": {
          "annual_turnover": 50000000,
          "is_public_authority": false,
          "main_activity": "data_processing"
        }
      }

   **Response:**

   .. code-block:: json

      {
        "assessment_id": "gdpr_123",
        "violation_probability": 0.85,
        "breach_notification_required": true,
        "data_subject_notification_required": true,
        "potential_fines": {
          "administrative_fine_range": {
            "min": 200000.0,
            "max": 1000000.0
          },
          "turnover_based_fine": {
            "percentage": 0.04,
            "amount": 2000000.0
          },
          "likely_fine": 500000.0
        },
        "notification_timeline": {
          "supervisory_authority": "72 hours",
          "data_subjects": "without undue delay"
        },
        "compliance_requirements": [
          "Breach notification to supervisory authority",
          "Data subject notification",
          "Impact assessment documentation",
          "Remedial measures implementation"
        ]
      }
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:350: WARNING: Title underline too short.

HIPAA Impact Assessment
~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:350: WARNING: Title underline too short.

HIPAA Impact Assessment
~~~~~~~~~~~~~~~~~~~~~~
WARNING: unknown directive or role name: http:post
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:354: ERROR: Unknown directive type "http:post".

.. http:post:: /compliance/hipaa

   **Request Body:**

   .. code-block:: json

      {
        "affected_assets": ["patient_records", "billing_system"],
        "phi_records_affected": 5000,
        "breach_type": "unauthorized_disclosure",
        "covered_entity_type": "healthcare_provider",
        "safeguards_in_place": ["encryption", "access_controls", "audit_logs"]
      }

   **Response:**

   .. code-block:: json

      {
        "assessment_id": "hipaa_456",
        "violation_probability": 0.7,
        "breach_notification_required": true,
        "ocr_reporting_required": true,
        "patient_notification_required": true,
        "potential_penalties": {
          "civil_monetary_penalties": {
            "tier_1": 100000.0,
            "tier_2": 250000.0,
            "tier_3": 500000.0,
            "tier_4": 1500000.0
          },
          "likely_penalty": 250000.0
        },
        "notification_timeline": {
          "hhs_ocr": "60 days",
          "affected_individuals": "60 days",
          "media_notification": "if_required"
        }
      }
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:395: WARNING: Title underline too short.

Mitigation Strategies
--------------------
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:395: WARNING: Title underline too short.

Mitigation Strategies
--------------------
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:398: WARNING: Title underline too short.

Generate Mitigations
~~~~~~~~~~~~~~~~~~~
WARNING: unknown directive or role name: http:post
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:402: ERROR: Unknown directive type "http:post".

.. http:post:: /mitigations/generate

   **Request Body:**

   .. code-block:: json

      {
        "simulation_id": "sim_12345",
        "risk_assessment_id": "risk_67890",
        "constraints": {
          "budget_limit": 500000.0,
          "implementation_timeline_days": 90,
          "risk_tolerance": "medium",
          "compliance_requirements": ["GDPR", "SOX"]
        }
      }

   **Response:**

   .. code-block:: json

      {
        "mitigation_plan_id": "plan_789",
        "total_cost": 450000.0,
        "total_risk_reduction": 0.65,
        "implementation_timeline": 85,
        "recommendations": [
          {
            "control_id": "ctrl_001",
            "control_name": "Multi-Factor Authentication",
            "category": "access_control",
            "mitre_techniques_addressed": ["T1078", "T1110"],
            "risk_reduction": 0.3,
            "implementation_cost": 75000.0,
            "implementation_days": 30,
            "roi": 4.2,
            "priority": "high",
            "description": "Implement MFA for all privileged accounts",
            "implementation_steps": [
              "Select MFA solution",
              "Deploy MFA infrastructure",
              "Configure user accounts",
              "Train users",
              "Monitor adoption"
            ]
          }
        ]
      }
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:452: WARNING: Title underline too short.

Continuous Monitoring
--------------------
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:452: WARNING: Title underline too short.

Continuous Monitoring
--------------------
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:455: WARNING: Title underline too short.

Setup Monitoring
~~~~~~~~~~~~~~~
WARNING: unknown directive or role name: http:post
/home/<USER>/dev/work/blast-radius/docs/api/threat-modeling.rst:459: ERROR: Unknown directive type "http:post".

.. http:post:: /monitoring/setup

   **Request Body:**

   .. code-block:: json

      {
        "monitoring_name": "Critical Assets Monitoring",
        "threat_actors": ["APT29", "APT28", "FIN7"],
        "target_assets": ["database_001", "web_server_001"],
        "monitoring_frequency": "daily",
        "alert_thresholds": {
          "risk_score_increase": 0.1,
          "new_attack_paths": 5,
          "compliance_risk_change": 0.05,
          "threat_intelligence_updates": true
        },
        "notification_settings": {
          "email_alerts": ["<EMAIL>"],
          "webhook_url": "https://company.com/security-webhook",
          "slack_channel": "#security-alerts"
        }
      }

   **Response:**

   .. code-block:: json

      {
        "monitoring_id": "mon_456",
        "status": "active",
        "next_assessment": "2024-01-16T10:30:00Z",
        "baseline_risk_score": 0.65,
        "monitored_assets": ["database_001", "web_server_001"],
        "alert_configuration": {
          "enabled": true,
          "thresholds_configured": 4,
          "notification_channels": 3
        }
      }
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:2: WARNING: Title underline too short.

Zero-Trust Architecture Implementation
=====================================
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:9: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Zero-Trust Architecture"
           subgraph "Identity Layer"
               A[Multi-Factor Authentication]
               B[Biometric Verification]
               C[Device Fingerprinting]
               D[Behavioral Analysis]
           end

           subgraph "Network Layer"
               E[Microsegmentation]
               F[Network Policies]
               G[TLS 1.3+ Encryption]
               H[Certificate Pinning]
           end

           subgraph "Application Layer"
               I[Service Accounts]
               J[API Authentication]
               K[Role-Based Access]
               L[Least Privilege]
           end

           subgraph "Data Layer"
               M[Field-Level Encryption]
               N[Data Classification]
               O[Access Logging]
               P[Data Loss Prevention]
           end
       end

       A --> E
       B --> F
       C --> G
       D --> H
       E --> I
       F --> J
       G --> K
       H --> L
       I --> M
       J --> N
       K --> O
       L --> P
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:63: ERROR: Unknown directive type "mermaid".

.. mermaid::

   sequenceDiagram
       participant U as User
       participant IV as Identity Verification
       participant MFA as MFA Service
       participant BIO as Biometric Service
       participant SEC as Security Correlator

       U->>IV: Authentication Request
       IV->>IV: Risk Assessment

       alt High Risk
           IV->>MFA: Require MFA
           MFA->>U: MFA Challenge
           U->>MFA: MFA Response
           MFA->>IV: MFA Result

           IV->>BIO: Require Biometric
           BIO->>U: Biometric Challenge
           U->>BIO: Biometric Data
           BIO->>IV: Biometric Result
       end

       IV->>SEC: Log Security Event
       IV->>U: Authentication Result
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:99: WARNING: Title underline too short.

Service Account Management
~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:99: WARNING: Title underline too short.

Service Account Management
~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:103: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph LR
       subgraph "Service Account Lifecycle"
           A[Create Service Account] --> B[Generate Credentials]
           B --> C[Assign Permissions]
           C --> D[Deploy to Services]
           D --> E[Monitor Usage]
           E --> F[Rotate Credentials]
           F --> G[Update Services]
           G --> H[Revoke Old Credentials]
           H --> E
       end

       subgraph "Security Controls"
           I[Least Privilege]
           J[Time-Limited Access]
           K[Audit Logging]
           L[Anomaly Detection]
       end

       A --> I
       C --> J
       E --> K
       E --> L
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:137: WARNING: Title underline too short.

TLS 1.3+ Enforcement
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:137: WARNING: Title underline too short.

TLS 1.3+ Enforcement
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:141: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "TLS Configuration"
           A[TLS 1.3 Only] --> B[Strong Cipher Suites]
           B --> C[Certificate Validation]
           C --> D[HSTS Headers]
           D --> E[Certificate Pinning]
           E --> F[OCSP Stapling]
       end

       subgraph "Security Features"
           G[Perfect Forward Secrecy]
           H[Session Ticket Disabled]
           I[Compression Disabled]
           J[Renegotiation Disabled]
       end

       A --> G
       B --> H
       C --> I
       D --> J
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:173: WARNING: Title underline too short.

Real-Time Security Event Correlation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:173: WARNING: Title underline too short.

Real-Time Security Event Correlation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:177: ERROR: Unknown directive type "mermaid".

.. mermaid::

   flowchart TD
       A[Security Events] --> B[Event Enrichment]
       B --> C[Threat Intelligence]
       C --> D[Correlation Engine]
       D --> E{Correlation Rules}

       E -->|Failed Auth| F[Brute Force Detection]
       E -->|Privilege Escalation| G[Privilege Abuse Detection]
       E -->|Data Access| H[Data Exfiltration Detection]
       E -->|Network Activity| I[Lateral Movement Detection]

       F --> J[Create Incident]
       G --> J
       H --> J
       I --> J

       J --> K[Automated Response]
       K --> L[Block IP]
       K --> M[Disable User]
       K --> N[Quarantine Asset]
       K --> O[Collect Forensics]
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:210: WARNING: Title underline too short.

Network Security
---------------
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:210: WARNING: Title underline too short.

Network Security
---------------
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:217: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Network Segmentation"
           subgraph "Frontend Zone"
               A[Frontend Pods]
           end

           subgraph "Backend Zone"
               B[Backend Pods]
           end

           subgraph "Database Zone"
               C[Database Pods]
           end

           subgraph "Monitoring Zone"
               D[Monitoring Pods]
           end
       end

       A -->|HTTPS:8000| B
       B -->|PostgreSQL:5432| C
       D -->|Metrics:9090| A
       D -->|Metrics:9090| B

       A -.->|Blocked| C
       A -.->|Blocked| D
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:254: WARNING: Title underline too short.

RBAC Implementation
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:254: WARNING: Title underline too short.

RBAC Implementation
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:258: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph LR
       subgraph "RBAC Hierarchy"
           A[Service Accounts] --> B[Roles]
           B --> C[RoleBindings]
           C --> D[Permissions]

           E[ClusterRoles] --> F[ClusterRoleBindings]
           F --> G[Cluster Permissions]
       end

       subgraph "Security Policies"
           H[Pod Security Policy]
           I[Network Policy]
           J[Resource Quotas]
           K[Admission Controllers]
       end

       D --> H
       G --> I
       B --> J
       C --> K
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:283: WARNING: Title underline too short.

Data Protection
--------------
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:283: WARNING: Title underline too short.

Data Protection
--------------
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:286: WARNING: Title underline too short.

Encryption Implementation
~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:290: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Encryption Layers"
           A[Application Layer] --> B[Field-Level Encryption]
           B --> C[Database Encryption]
           C --> D[Storage Encryption]
           D --> E[Network Encryption]
       end

       subgraph "Key Management"
           F[Master Key] --> G[Data Encryption Keys]
           G --> H[Field Keys]
           H --> I[Session Keys]
       end

       subgraph "Algorithms"
           J[AES-256-GCM]
           K[ChaCha20-Poly1305]
           L[RSA-4096]
           M[ECDSA-P384]
       end

       B --> J
       C --> K
       E --> L
       F --> M
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:326: WARNING: Title underline too short.

Data Classification
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:326: WARNING: Title underline too short.

Data Classification
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:330: ERROR: Unknown directive type "mermaid".

.. mermaid::

   flowchart TD
       A[Data Input] --> B[Content Analysis]
       B --> C[Pattern Matching]
       C --> D[ML Classification]
       D --> E{Classification Level}

       E -->|Public| F[Standard Protection]
       E -->|Internal| G[Access Controls]
       E -->|Confidential| H[Encryption + Access Controls]
       E -->|Secret| I[Full Protection Suite]

       F --> J[Apply Policies]
       G --> J
       H --> J
       I --> J

       J --> K[Audit Logging]
       K --> L[Compliance Reporting]
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:352: WARNING: Title underline too short.

Monitoring and Alerting
----------------------
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:352: WARNING: Title underline too short.

Monitoring and Alerting
----------------------
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:355: WARNING: Title underline too short.

Security Monitoring
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:359: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Data Sources"
           A[Application Logs]
           B[System Metrics]
           C[Network Traffic]
           D[Security Events]
       end

       subgraph "Processing"
           E[Log Aggregation]
           F[Metric Collection]
           G[Event Correlation]
           H[Anomaly Detection]
       end

       subgraph "Alerting"
           I[Prometheus Rules]
           J[Grafana Dashboards]
           K[PagerDuty Integration]
           L[Slack Notifications]
       end

       A --> E
       B --> F
       C --> G
       D --> H

       E --> I
       F --> J
       G --> K
       H --> L
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:401: WARNING: Title underline too short.

Implementation Guidelines
------------------------
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:401: WARNING: Title underline too short.

Implementation Guidelines
------------------------
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:404: WARNING: Title underline too short.

Deployment Checklist
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:434: WARNING: Title underline too short.

Security Validation
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:434: WARNING: Title underline too short.

Security Validation
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:438: ERROR: Unknown directive type "mermaid".

.. mermaid::

   gantt
       title Security Validation Schedule
       dateFormat  YYYY-MM-DD
       section Daily
       Security Scans        :2024-01-01, 1d
       Log Analysis         :2024-01-01, 1d

       section Weekly
       Vulnerability Assessment :2024-01-01, 7d
       Access Review           :2024-01-01, 7d

       section Monthly
       Penetration Testing     :2024-01-01, 30d
       Compliance Audit        :2024-01-01, 30d

       section Quarterly
       Architecture Review     :2024-01-01, 90d
       Disaster Recovery Test  :2024-01-01, 90d
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:463: WARNING: Title underline too short.

Security Principles
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:472: WARNING: Title underline too short.

Implementation Tips
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:472: WARNING: Title underline too short.

Implementation Tips
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:482: WARNING: Title underline too short.

Troubleshooting
--------------
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:482: WARNING: Title underline too short.

Troubleshooting
--------------
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:485: WARNING: Title underline too short.

Common Issues
~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:518: WARNING: Title underline too short.

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:518: WARNING: Title underline too short.

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:9: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "GDPR Compliance Framework"
           subgraph "Data Subject Rights"
               A[Right to Access]
               B[Right to Rectification]
               C[Right to Erasure]
               D[Right to Portability]
               E[Right to Restrict Processing]
               F[Right to Object]
           end

           subgraph "Privacy by Design"
               G[Data Minimization]
               H[Purpose Limitation]
               I[Storage Limitation]
               J[Accuracy]
               K[Integrity & Confidentiality]
               L[Accountability]
           end

           subgraph "Compliance Management"
               M[Consent Management]
               N[Data Breach Notification]
               O[Privacy Impact Assessment]
               P[Record of Processing]
               Q[Data Protection Officer]
               R[Audit & Monitoring]
           end
       end

       A --> G
       B --> H
       C --> I
       D --> J
       E --> K
       F --> L
       G --> M
       H --> N
       I --> O
       J --> P
       K --> Q
       L --> R
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:58: WARNING: Title underline too short.

Data Subject Rights Management
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:62: ERROR: Unknown directive type "mermaid".

.. mermaid::

   sequenceDiagram
       participant DS as Data Subject
       participant API as GDPR API
       participant VS as Verification Service
       participant PS as Processing Service
       participant AS as Audit Service
       participant NS as Notification Service

       DS->>API: Submit Data Subject Request
       API->>VS: Initiate Identity Verification
       VS->>DS: Send Verification Challenge
       DS->>VS: Complete Verification
       VS->>API: Verification Complete

       API->>PS: Process Request
       PS->>PS: Collect/Modify/Delete Data
       PS->>AS: Log Processing Activity
       PS->>API: Processing Complete

       API->>NS: Send Completion Notification
       NS->>DS: Request Completed
       API->>DS: Provide Response Data
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:101: ERROR: Unknown directive type "mermaid".

.. mermaid::

   flowchart TD
       A[User Interaction] --> B{Consent Required?}
       B -->|Yes| C[Present Consent Form]
       B -->|No| D[Proceed with Processing]

       C --> E[User Decision]
       E -->|Accept| F[Record Consent]
       E -->|Decline| G[Restrict Processing]
       E -->|Partial| H[Record Partial Consent]

       F --> I[Enable Processing]
       G --> J[Disable Processing]
       H --> K[Enable Limited Processing]

       I --> L[Monitor Consent Status]
       J --> L
       K --> L

       L --> M{Consent Expired?}
       M -->|Yes| N[Request Renewal]
       M -->|No| O[Continue Processing]

       N --> C
       O --> L
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:137: WARNING: Title underline too short.

Data Processing Records
~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:137: WARNING: Title underline too short.

Data Processing Records
~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:141: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph LR
       subgraph "Processing Activity Record"
           A[Controller Information] --> B[Processing Purposes]
           B --> C[Data Categories]
           C --> D[Data Subjects]
           D --> E[Recipients]
           E --> F[Third Country Transfers]
           F --> G[Retention Periods]
           G --> H[Security Measures]
       end

       subgraph "Automated Tracking"
           I[Data Flow Monitoring]
           J[Purpose Validation]
           K[Retention Enforcement]
           L[Security Assessment]
       end

       A --> I
       C --> J
       G --> K
       H --> L
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:174: WARNING: Title underline too short.

Data Breach Management
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:174: WARNING: Title underline too short.

Data Breach Management
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:178: ERROR: Unknown directive type "mermaid".

.. mermaid::

   sequenceDiagram
       participant S as Security System
       participant BM as Breach Manager
       participant SA as Supervisory Authority
       participant DS as Data Subjects
       participant DPO as Data Protection Officer
       participant MT as Management Team

       S->>BM: Breach Detected
       BM->>BM: Assess Breach Severity
       BM->>DPO: Notify DPO
       DPO->>BM: Confirm Assessment

       alt High Risk Breach
           BM->>SA: Notify Authority (72h)
           BM->>DS: Notify Data Subjects
           BM->>MT: Escalate to Management
       else Medium Risk Breach
           BM->>SA: Notify Authority (72h)
           BM->>DPO: Document Decision
       else Low Risk Breach
           BM->>DPO: Document Only
       end

       BM->>BM: Track Remediation
       BM->>SA: Provide Updates
       BM->>BM: Close Incident
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:221: ERROR: Unknown directive type "mermaid".

.. mermaid::

   flowchart TD
       A[New Processing Activity] --> B[PIA Trigger Assessment]
       B --> C{High Risk?}

       C -->|Yes| D[Initiate PIA]
       C -->|No| E[Document Decision]

       D --> F[Identify Risks]
       F --> G[Assess Impact]
       G --> H[Identify Mitigations]
       H --> I[Implement Controls]
       I --> J[Monitor Effectiveness]

       J --> K{Residual Risk Acceptable?}
       K -->|Yes| L[Approve Processing]
       K -->|No| M[Additional Mitigations]

       M --> I
       L --> N[Regular Review]
       N --> F
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:253: WARNING: Title underline too short.

Implementation Details
---------------------
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:253: WARNING: Title underline too short.

Implementation Details
---------------------
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:256: WARNING: Title underline too short.

GDPR Service Architecture
~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:258: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "GDPR Compliance Service"
           A[Consent Manager] --> B[Request Processor]
           B --> C[Data Mapper]
           C --> D[Breach Manager]
           D --> E[PIA Engine]
           E --> F[Audit Logger]
       end

       subgraph "Data Stores"
           G[Consent Records]
           H[Processing Records]
           I[Breach Incidents]
           J[PIA Documents]
           K[Audit Logs]
       end

       subgraph "External Integrations"
           L[Email Service]
           M[SMS Service]
           N[Document Storage]
           O[Notification APIs]
       end

       A --> G
       B --> H
       D --> I
       E --> J
       F --> K

       A --> L
       B --> M
       E --> N
       D --> O
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:296: WARNING: Title underline too short.

API Endpoints
~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:296: WARNING: Title underline too short.

API Endpoints
~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:345: WARNING: Title underline too short.

Data Processing Workflows
~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:345: WARNING: Title underline too short.

Data Processing Workflows
~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:349: ERROR: Unknown directive type "mermaid".

.. mermaid::

   flowchart TD
       A[Access Request] --> B[Identity Verification]
       B --> C[Data Collection]
       C --> D[Data Aggregation]
       D --> E[Data Anonymization]
       E --> F[Format Conversion]
       F --> G[Security Review]
       G --> H[Delivery Preparation]
       H --> I[Secure Delivery]

       subgraph "Data Sources"
           J[User Profile]
           K[Activity Logs]
           L[Consent Records]
           M[Processing Records]
       end

       C --> J
       C --> K
       C --> L
       C --> M
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:375: ERROR: Unknown directive type "mermaid".

.. mermaid::

   flowchart TD
       A[Erasure Request] --> B[Legal Obligation Check]
       B --> C{Can Erase?}

       C -->|Yes| D[Identify Data Locations]
       C -->|No| E[Document Rejection]

       D --> F[Backup Identification]
       F --> G[Anonymization Process]
       G --> H[Secure Deletion]
       H --> I[Verification]
       I --> J[Audit Documentation]

       E --> K[Notify Data Subject]
       J --> K
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:394: WARNING: Title underline too short.

Compliance Monitoring
--------------------
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:394: WARNING: Title underline too short.

Compliance Monitoring
--------------------
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:397: WARNING: Title underline too short.

Automated Compliance Checks
~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:399: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Compliance Monitoring"
           A[Data Flow Monitoring] --> B[Purpose Validation]
           B --> C[Consent Verification]
           C --> D[Retention Compliance]
           D --> E[Security Assessment]
           E --> F[Breach Detection]
       end

       subgraph "Alerting"
           G[Real-time Alerts]
           H[Compliance Dashboard]
           I[Automated Reports]
           J[Escalation Procedures]
       end

       A --> G
       B --> H
       C --> I
       D --> J
       E --> G
       F --> J
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:432: WARNING: Title underline too short.

Compliance Reporting
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:432: WARNING: Title underline too short.

Compliance Reporting
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:436: ERROR: Unknown directive type "mermaid".

.. mermaid::

   gantt
       title GDPR Compliance Reporting Schedule
       dateFormat  YYYY-MM-DD
       section Daily
       Consent Monitoring       :2024-01-01, 1d
       Breach Detection        :2024-01-01, 1d

       section Weekly
       Data Subject Requests   :2024-01-01, 7d
       Processing Activity Review :2024-01-01, 7d

       section Monthly
       Compliance Assessment   :2024-01-01, 30d
       PIA Reviews            :2024-01-01, 30d

       section Quarterly
       Full Compliance Audit   :2024-01-01, 90d
       DPO Report             :2024-01-01, 90d
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:461: WARNING: Title underline too short.

Privacy by Design Implementation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:472: WARNING: Title underline too short.

Operational Guidelines
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:472: WARNING: Title underline too short.

Operational Guidelines
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:499: WARNING: Title underline too short.

Technical Implementation
-----------------------
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:499: WARNING: Title underline too short.

Technical Implementation
-----------------------
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:502: WARNING: Title underline too short.

Database Schema
~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:536: WARNING: Title underline too short.

Configuration Example
~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:536: WARNING: Title underline too short.

Configuration Example
~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:564: WARNING: Title underline too short.

Troubleshooting
--------------
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:564: WARNING: Title underline too short.

Troubleshooting
--------------
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:567: WARNING: Title underline too short.

Common Issues
~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:600: WARNING: Title underline too short.

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:600: WARNING: Title underline too short.

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:9: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Production Architecture"
           subgraph "Load Balancer Tier"
               A[Application Load Balancer]
               B[Network Load Balancer]
               C[WAF Protection]
           end

           subgraph "Application Tier"
               D[Frontend Cluster]
               E[Backend API Cluster]
               F[Worker Cluster]
               G[Security Services]
           end

           subgraph "Data Tier"
               H[PostgreSQL Cluster]
               I[Redis Cluster]
               J[Neo4j Cluster]
               K[S3 Storage]
           end

           subgraph "Infrastructure Tier"
               L[EKS Cluster]
               M[VPC Network]
               N[Security Groups]
               O[KMS Encryption]
           end
       end

       A --> D
       B --> E
       C --> F
       D --> H
       E --> I
       F --> J
       G --> K

       L --> M
       M --> N
       N --> O
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:61: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "EKS Cluster Architecture"
           subgraph "Control Plane"
               A[EKS Masters]
               B[etcd Cluster]
               C[API Server]
               D[Scheduler]
           end

           subgraph "Worker Nodes"
               E[Application Nodes]
               F[Database Nodes]
               G[Monitoring Nodes]
               H[Security Nodes]
           end

           subgraph "Networking"
               I[VPC CNI]
               J[Network Policies]
               K[Service Mesh]
               L[Ingress Controllers]
           end

           subgraph "Storage"
               M[EBS Volumes]
               N[EFS Storage]
               O[S3 Integration]
               P[Backup Storage]
           end
       end

       A --> E
       B --> F
       C --> G
       D --> H

       E --> I
       F --> J
       G --> K
       H --> L

       I --> M
       J --> N
       K --> O
       L --> P
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:118: WARNING: Title underline too short.

Application Architecture
~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:118: WARNING: Title underline too short.

Application Architecture
~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:122: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph LR
       subgraph "Frontend Services"
           A[React Frontend]
           B[Nginx Proxy]
           C[Static Assets]
       end

       subgraph "API Gateway"
           D[Kong Gateway]
           E[Rate Limiting]
           F[Authentication]
           G[Load Balancing]
       end

       subgraph "Core Services"
           H[Asset Service]
           I[Threat Service]
           J[Analysis Service]
           K[Notification Service]
       end

       subgraph "Security Services"
           L[Identity Service]
           M[Audit Service]
           N[Compliance Service]
           O[Encryption Service]
       end

       subgraph "Worker Services"
           P[Graph Analysis]
           Q[ML Processing]
           R[Data Ingestion]
           S[Report Generation]
       end

       A --> D
       B --> E
       C --> F
       D --> H
       E --> I
       F --> J
       G --> K

       H --> L
       I --> M
       J --> N
       K --> O

       L --> P
       M --> Q
       N --> R
       O --> S
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:186: WARNING: Title underline too short.

Data Architecture
~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:186: WARNING: Title underline too short.

Data Architecture
~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:190: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Data Layer Architecture"
           subgraph "Operational Data"
               A[PostgreSQL Primary]
               B[PostgreSQL Replicas]
               C[Connection Pooling]
           end

           subgraph "Graph Data"
               D[Neo4j Cluster]
               E[Graph Analytics]
               F[Relationship Mapping]
           end

           subgraph "Cache Layer"
               G[Redis Cluster]
               H[Session Storage]
               I[Query Caching]
           end

           subgraph "Object Storage"
               J[S3 Primary]
               K[S3 Cross-Region]
               L[Backup Storage]
           end

           subgraph "Analytics"
               M[Data Warehouse]
               N[ETL Pipelines]
               O[ML Data Store]
           end
       end

       A --> B
       B --> C
       D --> E
       E --> F
       G --> H
       H --> I
       J --> K
       K --> L
       M --> N
       N --> O
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:245: WARNING: Title underline too short.

Network Architecture
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:245: WARNING: Title underline too short.

Network Architecture
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:249: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Network Architecture"
           subgraph "Public Subnets"
               A[Load Balancers]
               B[NAT Gateways]
               C[Bastion Hosts]
           end

           subgraph "Private Subnets"
               D[Application Tier]
               E[Database Tier]
               F[Management Tier]
           end

           subgraph "Security Controls"
               G[WAF]
               H[Security Groups]
               I[NACLs]
               J[VPC Flow Logs]
           end

           subgraph "Connectivity"
               K[VPC Peering]
               L[Transit Gateway]
               M[VPN Connections]
               N[Direct Connect]
           end
       end

       A --> D
       B --> E
       C --> F

       G --> H
       H --> I
       I --> J

       K --> L
       L --> M
       M --> N
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:301: WARNING: Title underline too short.

Security Architecture
--------------------
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:301: WARNING: Title underline too short.

Security Architecture
--------------------
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:304: WARNING: Title underline too short.

Zero-Trust Security Implementation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:308: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Zero-Trust Security"
           subgraph "Identity & Access"
               A[Multi-Factor Auth]
               B[Identity Provider]
               C[Service Accounts]
               D[RBAC Policies]
           end

           subgraph "Network Security"
               E[Network Policies]
               F[Service Mesh]
               G[TLS Encryption]
               H[Certificate Management]
           end

           subgraph "Data Protection"
               I[Encryption at Rest]
               J[Encryption in Transit]
               K[Key Management]
               L[Data Classification]
           end

           subgraph "Monitoring & Response"
               M[Security Monitoring]
               N[Threat Detection]
               O[Incident Response]
               P[Audit Logging]
           end
       end

       A --> E
       B --> F
       C --> G
       D --> H

       E --> I
       F --> J
       G --> K
       H --> L

       I --> M
       J --> N
       K --> O
       L --> P
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:365: WARNING: Title underline too short.

Monitoring Architecture
~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:365: WARNING: Title underline too short.

Monitoring Architecture
~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:369: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph LR
       subgraph "Monitoring Stack"
           subgraph "Metrics"
               A[Prometheus]
               B[Node Exporter]
               C[Application Metrics]
               D[Custom Metrics]
           end

           subgraph "Logging"
               E[Fluentd]
               F[Elasticsearch]
               G[Kibana]
               H[Log Aggregation]
           end

           subgraph "Tracing"
               I[Jaeger]
               J[OpenTelemetry]
               K[Distributed Tracing]
               L[Performance Analysis]
           end

           subgraph "Visualization"
               M[Grafana]
               N[Dashboards]
               O[Alerting]
               P[Reporting]
           end
       end

       A --> E
       B --> F
       C --> G
       D --> H

       E --> I
       F --> J
       G --> K
       H --> L

       I --> M
       J --> N
       K --> O
       L --> P
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:426: WARNING: Title underline too short.

Deployment Strategy
------------------
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:426: WARNING: Title underline too short.

Deployment Strategy
------------------
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:429: WARNING: Title underline too short.

Blue-Green Deployment
~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:433: ERROR: Unknown directive type "mermaid".

.. mermaid::

   sequenceDiagram
       participant CI as CI/CD Pipeline
       participant LB as Load Balancer
       participant BLUE as Blue Environment
       participant GREEN as Green Environment
       participant MON as Monitoring

       CI->>GREEN: Deploy New Version
       GREEN->>GREEN: Run Health Checks
       GREEN->>MON: Start Monitoring
       MON->>CI: Health Status OK

       CI->>LB: Switch Traffic to Green
       LB->>GREEN: Route 10% Traffic
       MON->>CI: Monitor Metrics

       alt Deployment Success
           CI->>LB: Route 100% Traffic
           LB->>GREEN: Full Traffic
           CI->>BLUE: Shutdown Blue
       else Deployment Failure
           CI->>LB: Rollback to Blue
           LB->>BLUE: Restore Traffic
           CI->>GREEN: Shutdown Green
       end
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:470: WARNING: Title underline too short.

Infrastructure as Code
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:470: WARNING: Title underline too short.

Infrastructure as Code
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:474: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "IaC Architecture"
           subgraph "Terraform Modules"
               A[VPC Module]
               B[EKS Module]
               C[RDS Module]
               D[Security Module]
           end

           subgraph "State Management"
               E[Remote State]
               F[State Locking]
               G[Version Control]
               H[Backup Strategy]
           end

           subgraph "CI/CD Integration"
               I[Plan Automation]
               J[Apply Automation]
               K[Drift Detection]
               L[Compliance Checks]
           end

           subgraph "Environment Management"
               M[Development]
               N[Staging]
               O[Production]
               P[Disaster Recovery]
           end
       end

       A --> E
       B --> F
       C --> G
       D --> H

       E --> I
       F --> J
       G --> K
       H --> L

       I --> M
       J --> N
       K --> O
       L --> P
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:531: WARNING: Title underline too short.

Performance Specifications
-------------------------
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:531: WARNING: Title underline too short.

Performance Specifications
-------------------------
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:534: WARNING: Title underline too short.

Scalability Metrics
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:536: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Performance Targets"
           A[10M+ Nodes] --> B[Asset Management]
           C[100K+ Events/sec] --> D[Event Processing]
           E[Sub-second Response] --> F[API Performance]
           G[99.9% Uptime] --> H[Availability]
           I[1TB+ Data] --> J[Storage Capacity]
           K[1000+ Users] --> L[Concurrent Users]
       end

       subgraph "Scaling Strategies"
           M[Horizontal Scaling]
           N[Vertical Scaling]
           O[Auto Scaling]
           P[Load Balancing]
           Q[Caching]
           R[Database Optimization]
       end

       B --> M
       D --> N
       F --> O
       H --> P
       J --> Q
       L --> R
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:573: WARNING: Title underline too short.

Resource Requirements
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:573: WARNING: Title underline too short.

Resource Requirements
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:618: WARNING: Title underline too short.

Disaster Recovery
----------------
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:618: WARNING: Title underline too short.

Disaster Recovery
----------------
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:621: WARNING: Title underline too short.

Backup and Recovery Strategy
~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:625: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Backup Strategy"
           A[Database Backups] --> B[Point-in-Time Recovery]
           C[Application Backups] --> D[Configuration Backup]
           E[Storage Backups] --> F[Cross-Region Replication]
           G[Infrastructure Backups] --> H[Terraform State Backup]
       end

       subgraph "Recovery Procedures"
           I[Automated Recovery] --> J[Health Monitoring]
           K[Manual Recovery] --> L[Runbook Procedures]
           M[Disaster Recovery] --> N[Cross-Region Failover]
           O[Data Recovery] --> P[Backup Restoration]
       end

       B --> I
       D --> K
       F --> M
       H --> O

       J --> P
       L --> N
       N --> P
       P --> J
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:661: WARNING: Title underline too short.

High Availability Design
~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:661: WARNING: Title underline too short.

High Availability Design
~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:665: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Primary Region (us-east-1)"
           A[Primary Cluster]
           B[Primary Database]
           C[Primary Storage]
       end

       subgraph "Secondary Region (us-west-2)"
           D[Standby Cluster]
           E[Read Replica]
           F[Backup Storage]
       end

       subgraph "Global Services"
           G[Route 53 DNS]
           H[CloudFront CDN]
           I[Global Load Balancer]
       end

       A --> D
       B --> E
       C --> F

       G --> A
       H --> D
       I --> A
       I --> D
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:707: WARNING: Title underline too short.

Operational Excellence
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:717: WARNING: Title underline too short.

Security Best Practices
~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:717: WARNING: Title underline too short.

Security Best Practices
~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:727: WARNING: Title underline too short.

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:727: WARNING: Title underline too short.

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/development/workflow.rst:29: ERROR: Unknown directive type "mermaid".

.. mermaid::

   gitGraph
       commit id: "Initial"
       branch develop
       checkout develop
       commit id: "Dev setup"
       branch feature/new-feature
       checkout feature/new-feature
       commit id: "Feature work"
       commit id: "Feature complete"
       checkout develop
       merge feature/new-feature
       commit id: "Merge feature"
       branch release/v1.1.0
       checkout release/v1.1.0
       commit id: "Release prep"
       checkout main
       merge release/v1.1.0
       commit id: "Release v1.1.0"
       checkout develop
       merge main
/home/<USER>/dev/work/blast-radius/docs/documentation-overview.rst:182: WARNING: Title underline too short.

🌟 Industry Leadership and Recognition
-------------------------------------
/home/<USER>/dev/work/blast-radius/docs/documentation-overview.rst:182: WARNING: Title underline too short.

🌟 Industry Leadership and Recognition
-------------------------------------
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:9: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Production Ready Implementation"
           subgraph "Infrastructure & Deployment"
               A[Complete IaC Coverage]
               B[Blue-Green Deployment]
               C[Auto-Scaling Infrastructure]
               D[Comprehensive Monitoring]
           end

           subgraph "Security & Compliance"
               E[Zero-Trust Architecture]
               F[GDPR Compliance Framework]
               G[Enhanced Audit Logging]
               H[Least Privilege Access]
           end

           subgraph "Operations & Documentation"
               I[Operational Runbooks]
               J[Deployment Guides]
               K[Security Procedures]
               L[Troubleshooting Guides]
           end

           subgraph "Advanced Features"
               M[Service Account Management]
               N[TLS 1.3+ Enforcement]
               O[Real-time Event Correlation]
               P[Compliance Automation]
           end
       end

       A --> E
       B --> F
       C --> G
       D --> H

       E --> I
       F --> J
       G --> K
       H --> L

       I --> M
       J --> N
       K --> O
       L --> P
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:58: WARNING: Title underline too short.

Implementation Achievements
--------------------------
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:58: WARNING: Title underline too short.

Implementation Achievements
--------------------------
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:61: WARNING: Title underline too short.

Infrastructure as Code (IaC) - 100% Complete ✅
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:65: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph LR
       subgraph "Terraform Modules"
           A[VPC & Networking] --> B[Security Groups & WAF]
           B --> C[EKS Cluster]
           C --> D[RDS Database]
           D --> E[ElastiCache Redis]
           E --> F[KMS Encryption]
       end

       subgraph "Features"
           G[Auto-Scaling]
           H[High Availability]
           I[Security Hardening]
           J[Monitoring Integration]
       end

       A --> G
       C --> H
       B --> I
       F --> J
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:102: ERROR: Unknown directive type "mermaid".

.. mermaid::

   sequenceDiagram
       participant U as User
       participant IV as Identity Verification
       participant SA as Service Account
       participant TLS as TLS Manager
       participant SEC as Security Correlator

       U->>IV: Authentication Request
       IV->>IV: Multi-Factor Verification
       IV->>IV: Biometric Validation
       IV->>IV: Risk Assessment

       SA->>TLS: Service Authentication
       TLS->>TLS: Certificate Validation
       TLS->>TLS: TLS 1.3 Enforcement

       IV->>SEC: Log Security Event
       SA->>SEC: Log Service Activity
       SEC->>SEC: Real-time Correlation
       SEC->>SEC: Threat Detection
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:138: ERROR: Unknown directive type "mermaid".

.. mermaid::

   flowchart TD
       A[Data Subject Request] --> B[Identity Verification]
       B --> C[Request Processing]
       C --> D{Request Type}

       D -->|Access| E[Data Collection & Export]
       D -->|Rectification| F[Data Correction]
       D -->|Erasure| G[Secure Data Deletion]
       D -->|Portability| H[Structured Data Export]

       E --> I[Secure Delivery]
       F --> I
       G --> I
       H --> I

       I --> J[Audit Logging]
       J --> K[Compliance Reporting]
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:171: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Audit Chain Architecture"
           A[Event Collection] --> B[Event Enrichment]
           B --> C[Integrity Protection]
           C --> D[Block Creation]
           D --> E[Chain Verification]
       end

       subgraph "Security Features"
           F[Cryptographic Hashing]
           G[Digital Signatures]
           H[Merkle Trees]
           I[Tamper Detection]
       end

       A --> F
       B --> G
       C --> H
       D --> I
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:206: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph LR
       subgraph "Access Lifecycle"
           A[Access Request] --> B[Risk Assessment]
           B --> C[Approval Workflow]
           C --> D[JIT Provisioning]
           D --> E[Usage Monitoring]
           E --> F[Automatic Revocation]
       end

       subgraph "Security Controls"
           G[Business Justification]
           H[Time-Limited Access]
           I[Privilege Escalation]
           J[Access Reviews]
       end

       A --> G
       D --> H
       E --> I
       F --> J
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:238: WARNING: Title underline too short.

Production Deployment Infrastructure - 100% Complete ✅
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:238: WARNING: Title underline too short.

Production Deployment Infrastructure - 100% Complete ✅
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:242: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Deployment Pipeline"
           A[Blue-Green Deployment] --> B[Automated Testing]
           B --> C[Health Monitoring]
           C --> D[Traffic Switching]
           D --> E[Rollback Capability]
       end

       subgraph "Monitoring Stack"
           F[Prometheus Metrics]
           G[Grafana Dashboards]
           H[Alertmanager Rules]
           I[PagerDuty Integration]
       end

       A --> F
       B --> G
       C --> H
       D --> I
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:289: WARNING: Title underline too short.

Performance Characteristics
~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:315: WARNING: Title underline too short.

Security Specifications
~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:315: WARNING: Title underline too short.

Security Specifications
~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:341: WARNING: Title underline too short.

Compliance Coverage
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:341: WARNING: Title underline too short.

Compliance Coverage
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:368: WARNING: Title underline too short.

Deployment Architecture
----------------------
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:368: WARNING: Title underline too short.

Deployment Architecture
----------------------
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:371: WARNING: Title underline too short.

Production Environment
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:373: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Production Deployment"
           subgraph "Load Balancer Tier"
               A[Application Load Balancer]
               B[WAF Protection]
               C[SSL Termination]
           end

           subgraph "Application Tier"
               D[Frontend Cluster]
               E[Backend API Cluster]
               F[Worker Services]
               G[Security Services]
           end

           subgraph "Data Tier"
               H[PostgreSQL Primary]
               I[PostgreSQL Replicas]
               J[Redis Cluster]
               K[Neo4j Cluster]
           end

           subgraph "Storage Tier"
               L[S3 Primary Storage]
               M[S3 Backup Storage]
               N[EBS Volumes]
               O[EFS Shared Storage]
           end
       end

       A --> D
       B --> E
       C --> F
       D --> H
       E --> I
       F --> J
       G --> K

       H --> L
       I --> M
       J --> N
       K --> O
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:427: WARNING: Title underline too short.

Next Steps and Recommendations
-----------------------------
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:427: WARNING: Title underline too short.

Next Steps and Recommendations
-----------------------------
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:430: WARNING: Title underline too short.

Immediate Actions
~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:439: WARNING: Title underline too short.

Long-term Roadmap
~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:439: WARNING: Title underline too short.

Long-term Roadmap
~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/performance/index.rst:26: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       A[Load Balancer] --> B[Frontend Cluster]
       A --> C[API Gateway]
       C --> D[Backend Services]
       D --> E[Caching Layer]
       D --> F[Database Cluster]
       D --> G[Graph Database]

       E --> E1[Redis Cluster]
       F --> F1[PostgreSQL Primary]
       F --> F2[PostgreSQL Replicas]
       G --> G1[Neo4j Cluster]

       H[Message Queue] --> D
       I[Background Workers] --> H
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:60: WARNING: Title underline too short.

Infrastructure Security Layer
~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:60: WARNING: Title underline too short.

Infrastructure Security Layer
~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:81: WARNING: Title underline too short.

Data Security Layer
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:81: WARNING: Title underline too short.

Data Security Layer
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:102: WARNING: Title underline too short.

Monitoring and Logging Layer
~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:102: WARNING: Title underline too short.

Monitoring and Logging Layer
~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:123: WARNING: Title underline too short.

Security Controls Implementation
-------------------------------
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:123: WARNING: Title underline too short.

Security Controls Implementation
-------------------------------
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:9: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Audit Logging Architecture"
           subgraph "Event Sources"
               A[User Actions]
               B[System Events]
               C[API Calls]
               D[Security Events]
               E[Data Access]
               F[Configuration Changes]
           end

           subgraph "Processing Layer"
               G[Event Enrichment]
               H[Correlation Engine]
               I[Integrity Protection]
               J[Compliance Mapping]
           end

           subgraph "Storage Layer"
               K[Audit Chain]
               L[Event Buffer]
               M[Compliance Store]
               N[Archive Storage]
           end

           subgraph "Analysis Layer"
               O[Real-time Monitoring]
               P[Forensic Analysis]
               Q[Compliance Reporting]
               R[Threat Detection]
           end
       end

       A --> G
       B --> H
       C --> I
       D --> J
       E --> G
       F --> H

       G --> K
       H --> L
       I --> M
       J --> N

       K --> O
       L --> P
       M --> Q
       N --> R
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:65: WARNING: Title underline too short.

Audit Event Structure
~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:69: ERROR: Unknown directive type "mermaid".

.. mermaid::

   classDiagram
       class AuditEvent {
           +String event_id
           +DateTime timestamp
           +AuditEventType event_type
           +AuditSeverity severity
           +String user_id
           +String session_id
           +String ip_address
           +String user_agent
           +String resource_type
           +String resource_id
           +String action
           +String description
           +Dict old_values
           +Dict new_values
           +List compliance_frameworks
           +Dict control_mappings
           +String application
           +String module
           +String function
           +Dict metadata
           +String checksum
           +String signature
       }

       class AuditEventType {
           <<enumeration>>
           USER_LOGIN
           USER_LOGOUT
           DATA_ACCESSED
           DATA_CREATED
           DATA_UPDATED
           DATA_DELETED
           SECURITY_VIOLATION
           GDPR_REQUEST_SUBMITTED
           PERMISSION_GRANTED
           SYSTEM_CONFIG_CHANGED
       }

       class AuditSeverity {
           <<enumeration>>
           INFO
           WARNING
           ERROR
           CRITICAL
       }

       AuditEvent --> AuditEventType
       AuditEvent --> AuditSeverity
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:132: WARNING: Title underline too short.

Tamper-Proof Audit Chain
~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:132: WARNING: Title underline too short.

Tamper-Proof Audit Chain
~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:136: ERROR: Unknown directive type "mermaid".

.. mermaid::

   sequenceDiagram
       participant E as Event Source
       participant AL as Audit Logger
       participant AC as Audit Chain
       participant V as Verifier

       E->>AL: Submit Audit Event
       AL->>AL: Enrich Event Data
       AL->>AL: Calculate Checksum
       AL->>AL: Sign Event
       AL->>AC: Add to Current Block

       alt Block Full
           AC->>AC: Calculate Merkle Root
           AC->>AC: Calculate Block Hash
           AC->>AC: Sign Block
           AC->>AC: Link to Previous Block
           AC->>AC: Seal Block
       end

       V->>AC: Verify Integrity
       AC->>V: Return Verification Result
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:174: ERROR: Unknown directive type "mermaid".

.. mermaid::

   flowchart TD
       A[Raw Event] --> B[Basic Validation]
       B --> C[User Context Enrichment]
       C --> D[Geolocation Enrichment]
       D --> E[Threat Intelligence]
       E --> F[Risk Scoring]
       F --> G[Compliance Mapping]
       G --> H[Correlation Analysis]
       H --> I[Final Event]

       subgraph "Enrichment Sources"
           J[User Database]
           K[GeoIP Service]
           L[Threat Feeds]
           M[Risk Engine]
           N[Compliance Rules]
           O[Correlation Engine]
       end

       C --> J
       D --> K
       E --> L
       F --> M
       G --> N
       H --> O
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:211: WARNING: Title underline too short.

Real-Time Monitoring
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:211: WARNING: Title underline too short.

Real-Time Monitoring
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:215: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Real-Time Processing"
           A[Event Stream] --> B[Pattern Detection]
           B --> C[Anomaly Detection]
           C --> D[Threshold Monitoring]
           D --> E[Alert Generation]
       end

       subgraph "Alert Routing"
           F[Security Team]
           G[Compliance Team]
           H[System Administrators]
           I[Management]
       end

       subgraph "Response Actions"
           J[Automated Blocking]
           K[User Notification]
           L[Incident Creation]
           M[Escalation]
       end

       E --> F
       E --> G
       E --> H
       E --> I

       F --> J
       G --> K
       H --> L
       I --> M
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:258: WARNING: Title underline too short.

Compliance Integration
---------------------
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:258: WARNING: Title underline too short.

Compliance Integration
---------------------
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:261: WARNING: Title underline too short.

Framework Mapping
~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:265: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph LR
       subgraph "Audit Events"
           A[User Login]
           B[Data Access]
           C[Permission Change]
           D[System Config]
           E[Data Export]
       end

       subgraph "SOC 2 Controls"
           F[CC6.1 - Access Controls]
           G[CC6.3 - Data Protection]
           H[CC6.7 - System Monitoring]
       end

       subgraph "ISO 27001 Controls"
           I[A.9.2.1 - User Registration]
           J[A.9.4.1 - Information Access]
           K[A.12.4.1 - Event Logging]
       end

       subgraph "GDPR Articles"
           L[Article 32 - Security]
           M[Article 25 - Data Protection]
           N[Article 30 - Records]
       end

       A --> F
       A --> I
       B --> G
       B --> J
       B --> L
       C --> F
       C --> I
       D --> H
       D --> K
       E --> M
       E --> N
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:316: WARNING: Title underline too short.

Compliance Reporting
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:316: WARNING: Title underline too short.

Compliance Reporting
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:320: ERROR: Unknown directive type "mermaid".

.. mermaid::

   flowchart TD
       A[Report Request] --> B[Framework Selection]
       B --> C[Time Period Definition]
       C --> D[Event Collection]
       D --> E[Control Mapping]
       E --> F[Evidence Gathering]
       F --> G[Gap Analysis]
       G --> H[Report Generation]
       H --> I[Review & Approval]
       I --> J[Report Delivery]

       subgraph "Report Types"
           K[Executive Summary]
           L[Detailed Technical]
           M[Control Assessment]
           N[Gap Analysis]
           O[Remediation Plan]
       end

       H --> K
       H --> L
       H --> M
       H --> N
       H --> O
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:357: WARNING: Title underline too short.

Implementation Details
---------------------
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:357: WARNING: Title underline too short.

Implementation Details
---------------------
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:360: WARNING: Title underline too short.

Audit Service Architecture
~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:362: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Audit Service Components"
           A[Event Collector] --> B[Event Processor]
           B --> C[Enrichment Engine]
           C --> D[Integrity Manager]
           D --> E[Storage Manager]
           E --> F[Query Engine]
           F --> G[Report Generator]
       end

       subgraph "Storage Systems"
           H[Hot Storage - Redis]
           I[Warm Storage - PostgreSQL]
           J[Cold Storage - S3]
           K[Archive - Glacier]
       end

       subgraph "External Services"
           L[Threat Intelligence]
           M[GeoIP Service]
           N[Notification Service]
           O[SIEM Integration]
       end

       E --> H
       E --> I
       E --> J
       E --> K

       C --> L
       C --> M
       G --> N
       F --> O
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:407: WARNING: Title underline too short.

API Endpoints
~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:407: WARNING: Title underline too short.

API Endpoints
~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:461: WARNING: Title underline too short.

Database Schema
~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:461: WARNING: Title underline too short.

Database Schema
~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:512: WARNING: Title underline too short.

Configuration
~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:512: WARNING: Title underline too short.

Configuration
~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:551: WARNING: Title underline too short.

Monitoring and Alerting
----------------------
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:551: WARNING: Title underline too short.

Monitoring and Alerting
----------------------
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:554: WARNING: Title underline too short.

Audit Monitoring Dashboard
~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:556: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       subgraph "Audit Dashboard"
           A[Event Volume Metrics]
           B[Event Type Distribution]
           C[User Activity Heatmap]
           D[Security Event Timeline]
           E[Compliance Status]
           F[Integrity Verification]
       end

       subgraph "Alert Conditions"
           G[High Volume Anomaly]
           H[Failed Login Spikes]
           I[Privilege Escalation]
           J[Data Export Anomaly]
           K[Integrity Violation]
           L[Compliance Deviation]
       end

       A --> G
       B --> H
       C --> I
       D --> J
       F --> K
       E --> L
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:593: WARNING: Title underline too short.

Alert Configuration
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:593: WARNING: Title underline too short.

Alert Configuration
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:626: WARNING: Title underline too short.

Audit Strategy
~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:636: WARNING: Title underline too short.

Operational Guidelines
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:636: WARNING: Title underline too short.

Operational Guidelines
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:660: WARNING: Title underline too short.

Troubleshooting
--------------
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:660: WARNING: Title underline too short.

Troubleshooting
--------------
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:663: WARNING: Title underline too short.

Common Issues
~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:696: WARNING: Title underline too short.

Performance Tuning
~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:696: WARNING: Title underline too short.

Performance Tuning
~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/framework.rst:27: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       A[Perimeter Security] --> B[Network Security]
       B --> C[Application Security]
       C --> D[Data Security]
       D --> E[Identity & Access]
       E --> F[Endpoint Security]
       F --> G[Monitoring & Response]

       A1[WAF, DDoS Protection] --> A
       B1[Firewalls, IDS/IPS, Segmentation] --> B
       C1[SAST, DAST, API Security] --> C
       D1[Encryption, DLP, Classification] --> D
       E1[MFA, RBAC, PAM] --> E
       F1[EDR, AV, Device Management] --> F
       G1[SIEM, SOAR, Threat Hunting] --> G
/home/<USER>/dev/work/blast-radius/docs/security/index.rst:35: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       A[Application Security] --> B[Infrastructure Security]
       B --> C[Data Security]
       C --> D[Access Control]
       D --> E[Monitoring & Logging]
       E --> F[Compliance & Audit]

       A --> A1[SAST/DAST Testing]
       A --> A2[Secure Coding]
       A --> A3[Dependency Management]

       B --> B1[Container Security]
       B --> B2[Network Security]
       B --> B3[Infrastructure as Code]

       C --> C1[Encryption at Rest]
       C --> C2[Encryption in Transit]
       C --> C3[Data Classification]

       D --> D1[Authentication]
       D --> D2[Authorization]
       D --> D3[Session Management]

       E --> E1[Security Monitoring]
       E --> E2[Audit Logging]
       E --> E3[Incident Response]

       F --> F1[SOC 2 Type II]
       F --> F2[GDPR Compliance]
       F --> F3[ISO 27001]
/home/<USER>/dev/work/blast-radius/docs/security/operations/incident-response.rst:120: WARNING: Title underline too short.

Extended Response Team
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/operations/incident-response.rst:120: WARNING: Title underline too short.

Extended Response Team
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/procedures/security-review-process.rst:18: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       A[Security Review Request] --> B[Review Type Classification]
       B --> C[Reviewer Assignment]
       C --> D[Security Assessment]
       D --> E[Risk Evaluation]
       E --> F[Recommendations]
       F --> G[Remediation Tracking]
       G --> H[Verification]
       H --> I[Documentation]

       B --> B1[Code Review]
       B --> B2[Architecture Review]
       B --> B3[Periodic Assessment]
       B --> B4[Incident Review]

       D --> D1[Automated Scanning]
       D --> D2[Manual Analysis]
       D --> D3[Threat Modeling]

       E --> E1[CVSS Scoring]
       E --> E2[Business Impact]
       E --> E3[Risk Matrix]
/home/<USER>/dev/work/blast-radius/docs/security/procedures/vulnerability-disclosure.rst:263: WARNING: Title underline too short.

Security Researcher Recognition
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/security-summary.rst:23: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       A[Security Documentation] --> B[Architecture & Design]
       A --> C[Testing & Validation]
       A --> D[Reviews & Assessments]
       A --> E[Operations & Procedures]
       A --> F[Compliance & Audit]

       B --> B1[Security Architecture]
       B --> B2[Threat Modeling]
       B --> B3[Security Controls]

       C --> C1[Automated Testing]
       C --> C2[Static Analysis]
       C --> C3[Dynamic Testing]

       D --> D1[Security Reviews]
       D --> D2[Vulnerability Management]
       D --> D3[Risk Assessment]

       E --> E1[Security Procedures]
       E --> E2[Incident Response]
       E --> E3[Monitoring]

       F --> F1[SOC 2 Compliance]
       F --> F2[GDPR Compliance]
       F --> F3[ISO 27001]
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:2: WARNING: Title underline too short.

Dynamic Application Security Testing (DAST)
==========================================
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:170: WARNING: Title underline too short.

DAST Test Categories
-------------------
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:170: WARNING: Title underline too short.

DAST Test Categories
-------------------
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:173: WARNING: Title underline too short.

Authentication Security Tests
~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:215: WARNING: Title underline too short.

Authorization Security Tests
~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:215: WARNING: Title underline too short.

Authorization Security Tests
~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:253: WARNING: Title underline too short.

Input Validation Tests
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:253: WARNING: Title underline too short.

Input Validation Tests
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:293: WARNING: Title underline too short.

Business Logic Tests
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:293: WARNING: Title underline too short.

Business Logic Tests
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:408: WARNING: Title underline too short.

Makefile Integration
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:408: WARNING: Title underline too short.

Makefile Integration
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:438: WARNING: Title underline too short.

DAST Results Analysis
--------------------
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:438: WARNING: Title underline too short.

DAST Results Analysis
--------------------
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:441: WARNING: Title underline too short.

Vulnerability Assessment
~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:505: WARNING: Title underline too short.

Performance Impact Assessment
~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:505: WARNING: Title underline too short.

Performance Impact Assessment
~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:547: WARNING: Title underline too short.

DAST Best Practices
------------------
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:547: WARNING: Title underline too short.

DAST Best Practices
------------------
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:565: WARNING: Title underline too short.

Test Data Management
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:565: WARNING: Title underline too short.

Test Data Management
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:580: WARNING: Title underline too short.

Continuous Improvement
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:580: WARNING: Title underline too short.

Continuous Improvement
~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/security-automation.rst:18: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       A[Developer Workstation] --> B[Pre-commit Hooks]
       B --> C[CI/CD Pipeline]
       C --> D[Security Testing Stage]
       D --> E[Deployment Gates]
       E --> F[Production Monitoring]

       D --> D1[SAST Scanning]
       D --> D2[DAST Testing]
       D --> D3[Dependency Scanning]
       D --> D4[Container Security]
       D --> D5[Infrastructure Testing]

       E --> E1[Security Approval]
       E --> E2[Compliance Check]
       E --> E3[Risk Assessment]

       F --> F1[Runtime Protection]
       F --> F2[Vulnerability Monitoring]
       F --> F3[Threat Detection]
/home/<USER>/dev/work/blast-radius/docs/security/testing/static-analysis.rst:213: WARNING: Title underline too short.

Makefile Integration
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/static-analysis.rst:213: WARNING: Title underline too short.

Makefile Integration
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/static-analysis.rst:292: WARNING: Title underline too short.

SAST Results Analysis
--------------------
/home/<USER>/dev/work/blast-radius/docs/security/testing/static-analysis.rst:292: WARNING: Title underline too short.

SAST Results Analysis
--------------------
/home/<USER>/dev/work/blast-radius/docs/security/testing/static-analysis.rst:295: WARNING: Title underline too short.

Vulnerability Classification
~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/static-analysis.rst:321: WARNING: Title underline too short.

Results Processing
~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/static-analysis.rst:321: WARNING: Title underline too short.

Results Processing
~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/static-analysis.rst:382: WARNING: Title underline too short.

False Positive Management
~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/static-analysis.rst:382: WARNING: Title underline too short.

False Positive Management
~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/static-analysis.rst:410: WARNING: Title underline too short.

Key Metrics
~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/static-analysis.rst:431: WARNING: Title underline too short.

Reporting Dashboard
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/security/testing/static-analysis.rst:431: WARNING: Title underline too short.

Reporting Dashboard
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/attack-path-architecture.rst:11: ERROR: Unknown directive type "mermaid".

.. mermaid::

    graph TB
        subgraph "Client Layer"
            WebUI[Web Interface]
            MobileApp[Mobile App]
            CLI[Command Line Interface]
            SDKs[Python/JS/Go SDKs]
        end

        subgraph "API Gateway"
            Gateway[API Gateway]
            Auth[Authentication]
            RateLimit[Rate Limiting]
            LoadBalancer[Load Balancer]
        end

        subgraph "Application Layer"
            subgraph "Core Services"
                AttackPathAPI[Attack Path API]
                AssetAPI[Asset Management API]
                UserAPI[User Management API]
                DiscoveryAPI[Discovery API]
            end

            subgraph "Analysis Services"
                GraphEngine[Graph Engine]
                AttackAnalyzer[Attack Path Analyzer]
                BlastRadius[Blast Radius Calculator]
                MitreMapper[MITRE ATT&CK Mapper]
            end

            subgraph "Background Services"
                TaskQueue[Task Queue]
                Scheduler[Job Scheduler]
                CacheManager[Cache Manager]
                MetricsCollector[Metrics Collector]
            end
        end

        subgraph "Data Layer"
            subgraph "Primary Storage"
                PostgreSQL[(PostgreSQL Database)]
                Redis[(Redis Cache)]
            end

            subgraph "External Integrations"
                CloudAPIs[Cloud Provider APIs]
                SIEM[SIEM Systems]
                ThreatIntel[Threat Intelligence]
                CMDB[Configuration Management DB]
            end
        end

        subgraph "Infrastructure"
            Monitoring[Monitoring & Alerting]
            Logging[Centralized Logging]
            Backup[Backup & Recovery]
            Security[Security Controls]
        end

        WebUI --> Gateway
        MobileApp --> Gateway
        CLI --> Gateway
        SDKs --> Gateway

        Gateway --> Auth
        Gateway --> RateLimit
        Gateway --> LoadBalancer

        LoadBalancer --> AttackPathAPI
        LoadBalancer --> AssetAPI
        LoadBalancer --> UserAPI
        LoadBalancer --> DiscoveryAPI

        AttackPathAPI --> GraphEngine
        AttackPathAPI --> AttackAnalyzer
        AttackPathAPI --> BlastRadius
        AttackPathAPI --> MitreMapper

        GraphEngine --> PostgreSQL
        GraphEngine --> Redis
        AttackAnalyzer --> TaskQueue
        BlastRadius --> CacheManager

        TaskQueue --> Scheduler
        CacheManager --> Redis
        MetricsCollector --> Monitoring

        AssetAPI --> CloudAPIs
        DiscoveryAPI --> SIEM
        AttackAnalyzer --> ThreatIntel
        AssetAPI --> CMDB

        PostgreSQL --> Backup
        Redis --> Backup
/home/<USER>/dev/work/blast-radius/docs/technical/attack-path-flows.rst:11: ERROR: Unknown directive type "mermaid".

.. mermaid::

    flowchart TD
        A[Start Attack Path Analysis] --> B{Asset Discovery Complete?}
        B -->|No| C[Trigger Asset Discovery]
        C --> D[Scan Network Infrastructure]
        D --> E[Collect Asset Metadata]
        E --> F[Map Asset Relationships]
        F --> B

        B -->|Yes| G[Initialize Graph Engine]
        G --> H[Load Asset Data into Graph]
        H --> I[Calculate Edge Weights]
        I --> J[Validate Graph Connectivity]

        J --> K{Source Asset Exists?}
        K -->|No| L[Return Asset Not Found Error]
        K -->|Yes| M{Target Assets Specified?}

        M -->|No| N[Identify High-Value Targets]
        N --> O[Filter by Business Criticality]
        O --> P[Filter by Data Classification]
        P --> Q[Create Target Asset List]
        Q --> R[Start Path Discovery]

        M -->|Yes| S[Validate Target Assets]
        S --> T{All Targets Valid?}
        T -->|No| U[Return Invalid Target Error]
        T -->|Yes| R

        R --> V[Initialize Pathfinding Algorithm]
        V --> W[Set Maximum Path Length]
        W --> X[Set Maximum Paths per Target]
        X --> Y[Begin Breadth-First Search]

        Y --> Z{Path Found?}
        Z -->|No| AA[Check Next Target]
        Z -->|Yes| BB[Calculate Path Risk Score]
        BB --> CC[Calculate Path Likelihood]
        CC --> DD[Identify Attack Techniques]
        DD --> EE[Map to MITRE ATT&CK]
        EE --> FF[Calculate Criticality Score]
        FF --> GG[Store Path in Results]
        GG --> AA

        AA --> HH{More Targets?}
        HH -->|Yes| Y
        HH -->|No| II[Sort Paths by Criticality]
        II --> JJ[Apply Result Limits]
        JJ --> KK[Cache Results]
        KK --> LL[Return Attack Paths]

        L --> MM[End]
        U --> MM
        LL --> MM
/home/<USER>/dev/work/blast-radius/docs/technical/attack-path-flows.rst:72: ERROR: Unknown directive type "mermaid".

.. mermaid::

    flowchart TD
        A[Start Blast Radius Calculation] --> B{Source Asset Valid?}
        B -->|No| C[Return Asset Not Found Error]
        B -->|Yes| D[Initialize Blast Radius Engine]

        D --> E[Set Maximum Degrees]
        E --> F[Initialize Impact Tracking]
        F --> G[Create Affected Assets Set]
        G --> H[Set Current Degree = 0]
        H --> I[Add Source Asset to Current Level]

        I --> J{Current Level Empty?}
        J -->|Yes| K[Calculate Total Impact]
        J -->|No| L[Process Current Level Assets]

        L --> M[For Each Asset in Current Level]
        M --> N[Get Asset Neighbors]
        N --> O[Calculate Propagation Probability]
        O --> P{Propagation Likely?}

        P -->|No| Q[Skip to Next Asset]
        P -->|Yes| R[Add to Next Level]
        R --> S[Calculate Asset Impact Score]
        S --> T[Update Impact by Degree]
        T --> U[Check Asset Criticality]
        U --> V{Asset Critical?}

        V -->|Yes| W[Add to Critical Assets]
        V -->|No| X[Check Data Classification]
        X --> Y{Contains Sensitive Data?}
        Y -->|Yes| Z[Add to Data Assets]
        Y -->|No| AA[Continue Processing]

        W --> AA
        Z --> AA
        AA --> Q
        Q --> BB{More Assets in Level?}
        BB -->|Yes| M
        BB -->|No| CC[Increment Degree]
        CC --> DD{Degree < Max Degrees?}
        DD -->|Yes| EE[Set Current Level = Next Level]
        EE --> FF[Clear Next Level]
        FF --> J
        DD -->|No| K

        K --> GG[Calculate Financial Impact]
        GG --> HH[Estimate Recovery Time]
        HH --> II[Assess Compliance Impact]
        II --> JJ[Calculate Service Disruption]
        JJ --> KK[Generate Blast Radius Result]
        KK --> LL[Cache Result]
        LL --> MM[Return Blast Radius]

        C --> NN[End]
        MM --> NN
/home/<USER>/dev/work/blast-radius/docs/technical/attack-path-flows.rst:135: ERROR: Unknown directive type "mermaid".

.. mermaid::

    flowchart TD
        A[Start Attack Scenario Creation] --> B[Validate Input Parameters]
        B --> C{Entry Points Valid?}
        C -->|No| D[Return Invalid Entry Points Error]
        C -->|Yes| E{Objectives Valid?}
        E -->|No| F[Return Invalid Objectives Error]
        E -->|Yes| G[Initialize Scenario Builder]

        G --> H[Create Scenario ID]
        H --> I[Set Threat Actor Profile]
        I --> J[Initialize Attack Path Collection]
        J --> K[For Each Entry Point]

        K --> L[For Each Objective]
        L --> M[Find Attack Paths]
        M --> N{Paths Found?}
        N -->|No| O[Log No Path Warning]
        N -->|Yes| P[Add Paths to Collection]

        O --> Q{More Objectives?}
        P --> Q
        Q -->|Yes| L
        Q -->|No| R{More Entry Points?}
        R -->|Yes| K
        R -->|No| S[Analyze Path Collection]

        S --> T[Calculate Scenario Risk Score]
        T --> U[Determine Overall Likelihood]
        U --> V[Calculate Total Impact Score]
        V --> W[Estimate Attack Duration]
        W --> X[Identify Required Resources]
        X --> Y[Calculate Detection Probability]
        Y --> Z[Generate Mitigation Strategies]
        Z --> AA[Determine Criticality Level]
        AA --> BB[Create Scenario Object]
        BB --> CC[Cache Scenario]
        CC --> DD[Return Attack Scenario]

        D --> EE[End]
        F --> EE
        DD --> EE
/home/<USER>/dev/work/blast-radius/docs/technical/attack-path-flows.rst:184: ERROR: Unknown directive type "mermaid".

.. mermaid::

    flowchart TD
        A[Start MITRE Mapping] --> B[Analyze Attack Path]
        B --> C[Examine Source Asset Type]
        C --> D{Public Facing?}
        D -->|Yes| E[Add Initial Access - T1190]
        D -->|No| F{Internal Asset?}
        F -->|Yes| G[Add Initial Access - T1078]
        F -->|No| H[Add Initial Access - T1566]

        E --> I[Analyze Path Length]
        G --> I
        H --> I
        I --> J{Path Length > 1?}
        J -->|No| K[Single Hop Attack]
        J -->|Yes| L[Multi-Hop Attack]

        K --> M[Check Target Asset Type]
        L --> N[Add Lateral Movement - T1021]
        N --> O[Check Privilege Requirements]
        O --> P{Requires Privilege Escalation?}
        P -->|Yes| Q[Add Privilege Escalation - T1068]
        P -->|No| R[Continue Analysis]

        Q --> R
        R --> S[Analyze Asset Relationships]
        S --> T{Credential Access Required?}
        T -->|Yes| U[Add Credential Access - T1003]
        T -->|No| V[Check Discovery Requirements]

        U --> V
        V --> W{Network Discovery Required?}
        W -->|Yes| X[Add Discovery - T1018]
        W -->|No| Y[Check Target Type]

        X --> Y
        M --> Y
        Y --> Z{Target is Database?}
        Z -->|Yes| AA[Add Collection - T1005]
        Z -->|No| BB{Target is Critical System?}
        BB -->|Yes| CC[Add Impact - T1485]
        BB -->|No| DD[Add Impact - T1499]

        AA --> EE[Check Data Exfiltration]
        CC --> EE
        DD --> EE
        EE --> FF{Data Exfiltration Likely?}
        FF -->|Yes| GG[Add Exfiltration - T1041]
        FF -->|No| HH[Finalize Mapping]

        GG --> HH
        HH --> II[Generate Mitigation Recommendations]
        II --> JJ[Generate Detection Methods]
        JJ --> KK[Return MITRE Mapping]
        KK --> LL[End]
/home/<USER>/dev/work/blast-radius/docs/technical/attack-path-flows.rst:246: ERROR: Unknown directive type "mermaid".

.. mermaid::

    flowchart TD
        A[Start Risk Scoring] --> B[Initialize Score Components]
        B --> C[Calculate Asset Risk Component]
        C --> D[Get Source Asset Risk Score]
        D --> E[Get Target Asset Risk Score]
        E --> F[Calculate Path Asset Scores]
        F --> G[Apply Distance Weighting]
        G --> H[Asset Risk = Weighted Average]

        H --> I[Calculate Path Complexity Component]
        I --> J[Path Length Factor = Length * 0.1]
        J --> K[Relationship Complexity Factor]
        K --> L[Protocol Security Factor]
        L --> M[Complexity Score = Combined Factors]

        M --> N[Calculate Security Controls Component]
        N --> O[For Each Path Edge]
        O --> P{Encrypted Connection?}
        P -->|Yes| Q[Apply Encryption Bonus]
        P -->|No| R{Authenticated Connection?}

        Q --> R
        R -->|Yes| S[Apply Authentication Bonus]
        R -->|No| T{Monitored Connection?}

        S --> T
        T -->|Yes| U[Apply Monitoring Bonus]
        T -->|No| V[Continue to Next Edge]

        U --> V
        V --> W{More Edges?}
        W -->|Yes| O
        W -->|No| X[Calculate Controls Score]

        X --> Y[Calculate Business Impact Component]
        Y --> Z[Get Target Business Criticality]
        Z --> AA{Critical Asset?}
        AA -->|Yes| BB[Impact Multiplier = 2.0]
        AA -->|No| CC{High Value Asset?}
        CC -->|Yes| DD[Impact Multiplier = 1.5]
        CC -->|No| EE{Medium Value Asset?}
        EE -->|Yes| FF[Impact Multiplier = 1.0]
        EE -->|No| GG[Impact Multiplier = 0.5]

        BB --> HH[Combine All Components]
        DD --> HH
        FF --> HH
        GG --> HH

        HH --> II[Risk Score = Asset Risk * 0.4]
        II --> JJ[+ Complexity * 0.2]
        JJ --> KK[+ Security Controls * 0.3]
        KK --> LL[+ Business Impact * 0.1]
        LL --> MM[Normalize to 0-100 Scale]
        MM --> NN[Return Risk Score]
        NN --> OO[End]
/home/<USER>/dev/work/blast-radius/docs/technical/attack-path-flows.rst:310: ERROR: Unknown directive type "mermaid".

.. mermaid::

    flowchart TD
        A[Receive Analysis Request] --> B[Generate Cache Key]
        B --> C[Check L1 Cache - Memory]
        C --> D{Cache Hit?}
        D -->|Yes| E[Update Access Time]
        E --> F[Return Cached Result]

        D -->|No| G[Check L2 Cache - Redis]
        G --> H{Cache Hit?}
        H -->|Yes| I[Load into L1 Cache]
        I --> J[Return Cached Result]

        H -->|No| K[Execute Analysis]
        K --> L[Generate Results]
        L --> M[Calculate Result Size]
        M --> N{Size < Cache Limit?}
        N -->|No| O[Return Results Without Caching]
        N -->|Yes| P[Store in L1 Cache]
        P --> Q[Store in L2 Cache]
        Q --> R[Set TTL Based on Analysis Type]
        R --> S{Attack Path Analysis?}
        S -->|Yes| T[TTL = 1 Hour]
        S -->|No| U{Blast Radius Analysis?}
        U -->|Yes| V[TTL = 30 Minutes]
        U -->|No| W[TTL = 15 Minutes]

        T --> X[Return Results]
        V --> X
        W --> X
        F --> Y[End]
        J --> Y
        O --> Y
        X --> Y
/home/<USER>/dev/work/blast-radius/docs/technical/attack-path-flows.rst:351: ERROR: Unknown directive type "mermaid".

.. mermaid::

    flowchart TD
        A[Analysis Request Received] --> B[Validate Input Parameters]
        B --> C{Parameters Valid?}
        C -->|No| D[Return 400 Bad Request]
        C -->|Yes| E[Check Authentication]
        E --> F{User Authenticated?}
        F -->|No| G[Return 401 Unauthorized]
        F -->|Yes| H[Check Authorization]
        H --> I{User Authorized?}
        I -->|No| J[Return 403 Forbidden]
        I -->|Yes| K[Check Rate Limits]
        K --> L{Within Rate Limit?}
        L -->|No| M[Return 429 Too Many Requests]
        L -->|Yes| N[Start Analysis]

        N --> O[Try Analysis Execution]
        O --> P{Analysis Successful?}
        P -->|Yes| Q[Return Results]
        P -->|No| R[Analyze Error Type]

        R --> S{Timeout Error?}
        S -->|Yes| T[Return 504 Gateway Timeout]
        S -->|No| U{Resource Not Found?}
        U -->|Yes| V[Return 404 Not Found]
        U -->|No| W{Database Error?}
        W -->|Yes| X[Log Database Error]
        X --> Y[Return 500 Internal Server Error]
        W -->|No| Z{Graph Processing Error?}
        Z -->|Yes| AA[Log Graph Error]
        AA --> BB[Return 422 Unprocessable Entity]
        Z -->|No| CC[Log Unknown Error]
        CC --> DD[Return 500 Internal Server Error]

        D --> EE[Log Request Error]
        G --> EE
        J --> EE
        M --> EE
        T --> EE
        V --> EE
        Y --> EE
        BB --> EE
        DD --> EE
        Q --> FF[Log Successful Request]

        EE --> GG[Update Error Metrics]
        FF --> HH[Update Success Metrics]
        GG --> II[End]
        HH --> II
/home/<USER>/dev/work/blast-radius/docs/technical/attack-path-flows.rst:407: ERROR: Unknown directive type "mermaid".

.. mermaid::

    flowchart TD
        A[Monitor Analysis Performance] --> B[Check Response Time]
        B --> C{Response Time > Threshold?}
        C -->|No| D[Continue Monitoring]
        C -->|Yes| E[Analyze Performance Bottleneck]

        E --> F{High CPU Usage?}
        F -->|Yes| G[Check Graph Size]
        G --> H{Graph Too Large?}
        H -->|Yes| I[Implement Graph Partitioning]
        H -->|No| J[Increase Worker Threads]

        F -->|No| K{High Memory Usage?}
        K -->|Yes| L[Check Cache Size]
        L --> M{Cache Too Large?}
        M -->|Yes| N[Reduce Cache Size]
        M -->|No| O[Optimize Graph Storage]

        K -->|No| P{High Database Load?}
        P -->|Yes| Q[Check Query Performance]
        Q --> R{Slow Queries Detected?}
        R -->|Yes| S[Optimize Database Indexes]
        R -->|No| T[Implement Connection Pooling]

        P -->|No| U{High Network Latency?}
        U -->|Yes| V[Implement Result Compression]
        U -->|No| W[Profile Application Code]

        I --> X[Test Performance Improvement]
        J --> X
        N --> X
        O --> X
        S --> X
        T --> X
        V --> X
        W --> X

        X --> Y{Performance Improved?}
        Y -->|Yes| Z[Deploy Optimization]
        Y -->|No| AA[Try Alternative Optimization]

        Z --> D
        AA --> E
        D --> BB[End Monitoring Cycle]
/home/<USER>/dev/work/blast-radius/docs/technical/database-design.rst:20: ERROR: Unknown directive type "mermaid".

.. mermaid::

    graph TB
        subgraph "Core Tables"
            Users[Users]
            Assets[Assets]
            AssetTypes[Asset Types]
            Providers[Providers]
            Environments[Environments]
        end

        subgraph "Relationship Tables"
            AssetRelationships[Asset Relationships]
            AssetDependencies[Asset Dependencies]
            AssetCommunications[Asset Communications]
        end

        subgraph "Extended Asset Data"
            AssetVulnerabilities[Asset Vulnerabilities]
            AssetCompliance[Asset Compliance]
            AssetMetrics[Asset Metrics]
            AssetConfigurations[Asset Configurations]
        end

        subgraph "Discovery & Analysis"
            DiscoveryJobs[Discovery Jobs]
            DiscoverySources[Discovery Sources]
            AttackPaths[Attack Paths]
            BlastRadius[Blast Radius Results]
        end

        subgraph "Audit & Security"
            AuditLogs[Audit Logs]
            UserSessions[User Sessions]
            APIKeys[API Keys]
            DataRetention[Data Retention Policies]
        end

        Users --> Assets
        Assets --> AssetTypes
        Assets --> Providers
        Assets --> Environments
        Assets --> AssetRelationships
        Assets --> AssetVulnerabilities
        Assets --> AssetCompliance
        Assets --> AssetMetrics
        Assets --> AssetConfigurations
        Assets --> AttackPaths
        DiscoveryJobs --> Assets
        DiscoverySources --> DiscoveryJobs
        Users --> AuditLogs
        Users --> UserSessions
        Users --> APIKeys
/home/<USER>/dev/work/blast-radius/docs/technical/database-design.rst:77: ERROR: Unknown directive type "mermaid".

.. mermaid::

    erDiagram
        USERS {
            uuid id PK
            string username UK
            string email UK
            string password_hash
            string first_name
            string last_name
            enum role
            boolean is_active
            boolean is_verified
            timestamp created_at
            timestamp updated_at
            timestamp last_login
        }

        ASSETS {
            uuid id PK
            string name
            enum asset_type FK
            enum provider FK
            string environment FK
            string ip_addresses
            jsonb configuration
            jsonb properties
            float risk_score
            enum status
            boolean is_deleted
            timestamp deleted_at
            uuid created_by FK
            timestamp created_at
            timestamp updated_at
        }

        ASSET_TYPES {
            string value PK
            string display_name
            string description
            jsonb default_properties
            boolean is_active
        }

        PROVIDERS {
            string value PK
            string display_name
            string description
            jsonb configuration_schema
            boolean is_active
        }

        ENVIRONMENTS {
            string name PK
            string description
            enum criticality_level
            jsonb configuration
            boolean is_active
        }

        ASSET_RELATIONSHIPS {
            uuid id PK
            uuid source_asset_id FK
            uuid target_asset_id FK
            enum relationship_type
            string protocol
            integer port
            enum direction
            string description
            float confidence
            boolean is_active
            timestamp created_at
            timestamp updated_at
        }

        ASSET_VULNERABILITIES {
            uuid id PK
            uuid asset_id FK
            string vulnerability_id
            string title
            text description
            enum severity
            float cvss_score
            string cve_id
            jsonb details
            enum status
            timestamp discovered_at
            timestamp updated_at
        }

        ASSET_COMPLIANCE {
            uuid id PK
            uuid asset_id FK
            string framework
            string control_id
            string control_name
            enum compliance_status
            text findings
            timestamp assessed_at
            timestamp next_assessment
        }

        DISCOVERY_JOBS {
            uuid id PK
            string name
            enum job_type
            jsonb configuration
            enum status
            timestamp started_at
            timestamp completed_at
            integer assets_discovered
            integer assets_updated
            text error_message
            uuid created_by FK
        }

        AUDIT_LOGS {
            uuid id PK
            uuid user_id FK
            string action
            string resource_type
            uuid resource_id
            jsonb old_values
            jsonb new_values
            string ip_address
            string user_agent
            timestamp created_at
        }

        USERS ||--o{ ASSETS : creates
        USERS ||--o{ DISCOVERY_JOBS : initiates
        USERS ||--o{ AUDIT_LOGS : generates
        ASSETS ||--o{ ASSET_RELATIONSHIPS : source
        ASSETS ||--o{ ASSET_RELATIONSHIPS : target
        ASSETS ||--o{ ASSET_VULNERABILITIES : has
        ASSETS ||--o{ ASSET_COMPLIANCE : assessed
        ASSETS }o--|| ASSET_TYPES : classified_as
        ASSETS }o--|| PROVIDERS : hosted_on
        ASSETS }o--|| ENVIRONMENTS : deployed_in
/home/<USER>/dev/work/blast-radius/docs/technical/database-design.rst:220: ERROR: Unknown directive type "mermaid".

.. mermaid::

    erDiagram
        ATTACK_PATHS {
            uuid id PK
            string path_id UK
            uuid source_asset_id FK
            uuid target_asset_id FK
            jsonb path_nodes
            jsonb path_edges
            enum path_type
            jsonb attack_techniques
            float risk_score
            float likelihood
            float impact_score
            integer blast_radius
            integer estimated_time
            jsonb required_privileges
            float detection_difficulty
            float mitigation_cost
            integer path_length
            float criticality_score
            timestamp created_at
            timestamp updated_at
        }

        ATTACK_SCENARIOS {
            uuid id PK
            string scenario_id UK
            string name
            text description
            string threat_actor
            jsonb entry_points
            jsonb objectives
            float total_risk_score
            float likelihood
            float impact_score
            integer estimated_duration
            jsonb required_resources
            float detection_probability
            jsonb mitigation_strategies
            enum criticality_level
            uuid created_by FK
            timestamp created_at
            timestamp updated_at
        }

        SCENARIO_PATHS {
            uuid id PK
            uuid scenario_id FK
            uuid attack_path_id FK
            integer sequence_order
            timestamp created_at
        }

        BLAST_RADIUS_RESULTS {
            uuid id PK
            uuid source_asset_id FK
            jsonb affected_assets
            jsonb impact_by_degree
            float total_impact_score
            jsonb critical_assets_affected
            jsonb data_assets_affected
            float service_disruption_score
            float financial_impact
            jsonb compliance_impact
            integer recovery_time_estimate
            integer max_degrees
            timestamp calculated_at
            timestamp expires_at
        }

        MITRE_ATTACK_MAPPINGS {
            uuid id PK
            string technique_id UK
            string technique_name
            string tactic
            jsonb platforms
            jsonb data_sources
            jsonb mitigations
            jsonb detection_methods
            text description
            boolean is_active
        }

        ATTACK_PATHS }o--|| ASSETS : source
        ATTACK_PATHS }o--|| ASSETS : target
        ATTACK_SCENARIOS ||--o{ SCENARIO_PATHS : contains
        SCENARIO_PATHS }o--|| ATTACK_PATHS : includes
        BLAST_RADIUS_RESULTS }o--|| ASSETS : originates_from
        ATTACK_SCENARIOS }o--|| USERS : created_by
/home/<USER>/dev/work/blast-radius/docs/technical/index.rst:327: WARNING: Title underline too short.

Contributing and Development
---------------------------
/home/<USER>/dev/work/blast-radius/docs/technical/index.rst:327: WARNING: Title underline too short.

Contributing and Development
---------------------------
/home/<USER>/dev/work/blast-radius/docs/technical/index.rst:368: WARNING: Title underline too short.

Support and Resources
--------------------
/home/<USER>/dev/work/blast-radius/docs/technical/index.rst:368: WARNING: Title underline too short.

Support and Resources
--------------------
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:27: ERROR: Unknown directive type "mermaid".

.. mermaid::

    mindmap
        root((Blast-Radius Security Tool))
            Attack Path Analysis
                Multi-hop Discovery
                Risk Scoring
                MITRE ATT&CK Integration
                Real-time Analysis
            Asset Management
                Multi-cloud Discovery
                Comprehensive Metadata
                Relationship Mapping
                Audit Trails
            Threat Modeling
                Scenario Creation
                Threat Actor Profiling
                Impact Assessment
                Mitigation Planning
            Enterprise Features
                Role-based Access
                Audit Logging
                API Integration
                Scalable Architecture
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:152: WARNING: Title underline too short.

**FR-003: MITRE ATT&CK Integration**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:152: WARNING: Title underline too short.

**FR-003: MITRE ATT&CK Integration**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:187: WARNING: Title underline too short.

**FR-004: Attack Scenario Modeling**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:187: WARNING: Title underline too short.

**FR-004: Attack Scenario Modeling**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:223: WARNING: Title underline too short.

**FR-005: Asset Discovery and Management**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:223: WARNING: Title underline too short.

**FR-005: Asset Discovery and Management**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:321: WARNING: Title underline too short.

**TAR-001: Graph Processing Engine**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:332: WARNING: Title underline too short.

**TAR-002: Database Architecture**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:332: WARNING: Title underline too short.

**TAR-002: Database Architecture**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:343: WARNING: Title underline too short.

**TAR-003: API Architecture**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:343: WARNING: Title underline too short.

**TAR-003: API Architecture**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:354: WARNING: Title underline too short.

**TAR-004: Security Architecture**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:354: WARNING: Title underline too short.

**TAR-004: Security Architecture**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:368: WARNING: Title underline too short.

**IR-001: SIEM Integration**
~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:382: WARNING: Title underline too short.

**IR-002: SOAR Integration**
~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:382: WARNING: Title underline too short.

**IR-002: SOAR Integration**
~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:396: WARNING: Title underline too short.

**IR-003: Cloud Platform Integration**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:396: WARNING: Title underline too short.

**IR-003: Cloud Platform Integration**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:413: WARNING: Title underline too short.

**CR-001: Data Protection**
~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:424: WARNING: Title underline too short.

**CR-002: Security Frameworks**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:424: WARNING: Title underline too short.

**CR-002: Security Frameworks**
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:438: WARNING: Title underline too short.

**Business Metrics**
~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:446: WARNING: Title underline too short.

**Technical Metrics**
~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:446: WARNING: Title underline too short.

**Technical Metrics**
~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:454: WARNING: Title underline too short.

**User Adoption Metrics**
~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:454: WARNING: Title underline too short.

**User Adoption Metrics**
~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:465: WARNING: Title underline too short.

**Technical Risks**
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:467: ERROR: Unknown directive type "mermaid".

.. mermaid::

    quadrantChart
        title Technical Risk Assessment
        x-axis Low Impact --> High Impact
        y-axis Low Probability --> High Probability

        Performance Degradation: [0.7, 0.3]
        Security Vulnerabilities: [0.9, 0.2]
        Data Loss: [0.8, 0.1]
        Integration Failures: [0.5, 0.4]
        Scalability Issues: [0.6, 0.3]
        API Breaking Changes: [0.4, 0.2]
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:491: WARNING: Title underline too short.

**Business Risks**
~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:491: WARNING: Title underline too short.

**Business Risks**
~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/testing/index.rst:34: ERROR: Unknown directive type "mermaid".

.. mermaid::

   graph TB
       A[End-to-End Tests] --> B[Integration Tests]
       B --> C[Unit Tests]

       A1[UI Tests<br/>API Tests<br/>Security Tests] --> A
       B1[Service Integration<br/>Database Tests<br/>External API Tests] --> B
       C1[Function Tests<br/>Class Tests<br/>Module Tests] --> C

       style A fill:#ff9999
       style B fill:#ffcc99
       style C fill:#99ff99
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:24: WARNING: Title underline too short.

Who should use this tool?
~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:24: WARNING: Title underline too short.

Who should use this tool?
~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:74: WARNING: Title underline too short.

What are the default login credentials?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:74: WARNING: Title underline too short.

What are the default login credentials?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:85: WARNING: Title underline too short.

How do I reset the admin password?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:85: WARNING: Title underline too short.

How do I reset the admin password?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:98: WARNING: Title underline too short.

Why can't I access the web interface?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:98: WARNING: Title underline too short.

Why can't I access the web interface?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:162: WARNING: Title underline too short.

How do I add new users?
~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:162: WARNING: Title underline too short.

How do I add new users?
~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:182: WARNING: Title underline too short.

How do I enable Multi-Factor Authentication (MFA)?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:182: WARNING: Title underline too short.

How do I enable Multi-Factor Authentication (MFA)?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:202: WARNING: Title underline too short.

How does attack path analysis work?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:220: WARNING: Title underline too short.

How long does attack path analysis take?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:220: WARNING: Title underline too short.

How long does attack path analysis take?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:236: WARNING: Title underline too short.

Why am I not seeing any attack paths?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:236: WARNING: Title underline too short.

Why am I not seeing any attack paths?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:254: WARNING: Title underline too short.

Can I customize risk scoring?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:254: WARNING: Title underline too short.

Can I customize risk scoring?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:297: WARNING: Title underline too short.

How do I backup my data?
~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:297: WARNING: Title underline too short.

How do I backup my data?
~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:322: WARNING: Title underline too short.

How do I update to a new version?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:322: WARNING: Title underline too short.

How do I update to a new version?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:341: WARNING: Title underline too short.

How do I integrate with my SIEM?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:364: WARNING: Title underline too short.

Can I use the API for automation?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:364: WARNING: Title underline too short.

Can I use the API for automation?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:394: WARNING: Title underline too short.

How do I get API credentials?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:394: WARNING: Title underline too short.

How do I get API credentials?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:411: WARNING: Title underline too short.

Is my data secure?
~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:429: WARNING: Title underline too short.

How do I configure audit logging?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:429: WARNING: Title underline too short.

How do I configure audit logging?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:452: WARNING: Title underline too short.

Can I run this in an air-gapped environment?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:452: WARNING: Title underline too short.

Can I run this in an air-gapped environment?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:475: WARNING: Title underline too short.

How do I get help?
~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:494: WARNING: Title underline too short.

Is there a community or forum?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:494: WARNING: Title underline too short.

Is there a community or forum?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:504: WARNING: Title underline too short.

How can I contribute?
~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:504: WARNING: Title underline too short.

How can I contribute?
~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:517: WARNING: Title underline too short.

What's the roadmap for future features?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:517: WARNING: Title underline too short.

What's the roadmap for future features?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/use-cases/attack-path-analysis.rst:538: WARNING: Title underline too short.

SIEM Integration
~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/use-cases/attack-path-analysis.rst:561: WARNING: Title underline too short.

SOAR Integration
~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/use-cases/attack-path-analysis.rst:561: WARNING: Title underline too short.

SOAR Integration
~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/use-cases/attack-path-analysis.rst:589: WARNING: Title underline too short.

Analysis Guidelines
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/administrators.rst:22: WARNING: Title underline too short.

Administrator Dashboard Features
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:137: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:152: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:167: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:175: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:197: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:212: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:234: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:249: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:264: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:278: WARNING: Title underline too short.

Advanced Risk Assessment and Management Framework
------------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:278: WARNING: Title underline too short.

Advanced Risk Assessment and Management Framework
------------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:286: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:301: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:315: WARNING: Title underline too short.

Cross-Functional Collaboration and Integration
---------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:315: WARNING: Title underline too short.

Cross-Functional Collaboration and Integration
---------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:323: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:338: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:352: WARNING: Title underline too short.

Professional Development and Excellence Framework
------------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:352: WARNING: Title underline too short.

Professional Development and Excellence Framework
------------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:360: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:375: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:397: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/compliance-officers.rst:412: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:15: WARNING: Title underline too short.

Executive Summary for Strategic Security Leadership
--------------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:150: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:165: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:179: WARNING: Title underline too short.

Business-Aligned Security Performance Management
-----------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:179: WARNING: Title underline too short.

Business-Aligned Security Performance Management
-----------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:187: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:202: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:216: WARNING: Title underline too short.

Strategic Security Investment and Resource Management
---------------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:216: WARNING: Title underline too short.

Strategic Security Investment and Resource Management
---------------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:224: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:239: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:261: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:276: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:290: WARNING: Title underline too short.

Regulatory Compliance and Governance Excellence
----------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:290: WARNING: Title underline too short.

Regulatory Compliance and Governance Excellence
----------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:298: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:312: WARNING: Title underline too short.

Strategic Security Communication and Stakeholder Management
----------------------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:312: WARNING: Title underline too short.

Strategic Security Communication and Stakeholder Management
----------------------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:320: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:335: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:350: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:364: WARNING: Title underline too short.

Digital Transformation and Innovation Enablement
-----------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:364: WARNING: Title underline too short.

Digital Transformation and Innovation Enablement
-----------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:372: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:387: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:401: WARNING: Title underline too short.

Professional Development and Executive Excellence
------------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:401: WARNING: Title underline too short.

Professional Development and Executive Excellence
------------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:409: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:424: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:446: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:466: WARNING: Title underline too short.

Conclusion: Excellence in Executive Security Leadership
------------------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/executive-leadership.rst:466: WARNING: Title underline too short.

Conclusion: Excellence in Executive Security Leadership
------------------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/index.rst:241: WARNING: Title underline too short.

Threat Modeling
~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/index.rst:241: WARNING: Title underline too short.

Threat Modeling
~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/index.rst:288: WARNING: Title underline too short.

Common Workflows
---------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/index.rst:288: WARNING: Title underline too short.

Common Workflows
---------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:48: WARNING: Title underline too short.

MITRE ATT&CK Data Management
---------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:48: WARNING: Title underline too short.

MITRE ATT&CK Data Management
---------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:51: WARNING: Title underline too short.

Automatic Data Synchronization
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:97: WARNING: Title underline too short.

Technique Correlation Engine
---------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:97: WARNING: Title underline too short.

Technique Correlation Engine
---------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:100: WARNING: Title underline too short.

Real-time Event Correlation
~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:153: WARNING: Title underline too short.

Automated Attribution Engine
~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:203: WARNING: Title underline too short.

Attack Pattern Analysis
----------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:203: WARNING: Title underline too short.

Attack Pattern Analysis
----------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:206: WARNING: Title underline too short.

Pattern Recognition Engine
~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:256: WARNING: Title underline too short.

ATT&CK Navigator Integration
---------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:256: WARNING: Title underline too short.

ATT&CK Navigator Integration
---------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:259: WARNING: Title underline too short.

Automated Visualization
~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:304: WARNING: Title underline too short.

Threat Intelligence Enrichment
-----------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:304: WARNING: Title underline too short.

Threat Intelligence Enrichment
-----------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:307: WARNING: Title underline too short.

IOC Enhancement
~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:350: WARNING: Title underline too short.

Advanced Analytics
-----------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:350: WARNING: Title underline too short.

Advanced Analytics
-----------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:353: WARNING: Title underline too short.

Behavioral Analytics
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:391: WARNING: Title underline too short.

API Integration
--------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:391: WARNING: Title underline too short.

API Integration
--------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:394: WARNING: Title underline too short.

RESTful API
~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:430: WARNING: Title underline too short.

Correlation Accuracy
~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:438: WARNING: Title underline too short.

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst:438: WARNING: Title underline too short.

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:235: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:257: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:283: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:305: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:331: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:353: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:379: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:401: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:427: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:449: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:478: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:500: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:524: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:550: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:565: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:580: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:599: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:614: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:649: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:664: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:679: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:707: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:722: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:737: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:765: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:780: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:795: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:814: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:829: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:844: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:868: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:883: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:898: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:915: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:932: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:960: WARNING: Title underline too short.

Conclusion: Excellence in Purple Team Operations and Collaborative Security
--------------------------------------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/purple-team-members.rst:960: WARNING: Title underline too short.

Conclusion: Excellence in Purple Team Operations and Collaborative Security
--------------------------------------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:217: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:237: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:265: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:289: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:313: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:328: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:345: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:364: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:379: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:398: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:420: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:451: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:476: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:502: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:526: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:554: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:578: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:604: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:626: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:650: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:674: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:698: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:722: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:748: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:770: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:785: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:800: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:824: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:843: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:870: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:891: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:908: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:925: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:946: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:961: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:976: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:1000: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:1024: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:1063: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:1078: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:1093: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:1108: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:1136: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:1151: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:1166: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:1194: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:1209: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:1224: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:1248: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:1263: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:1278: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:1295: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/red-team-members.rst:1312: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:202: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:227: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:248: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:273: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:294: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:313: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:330: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:349: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:368: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:422: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:446: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:470: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:494: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:520: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:542: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:564: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:583: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:598: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:612: WARNING: Title underline too short.

Advanced Security Architecture Design and Implementation
-------------------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:612: WARNING: Title underline too short.

Advanced Security Architecture Design and Implementation
-------------------------------------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:622: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:646: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:670: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:696: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:718: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:740: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:766: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:781: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:805: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:827: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:851: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:877: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:899: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:925: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:947: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:966: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:988: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:1012: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:1039: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:1065: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:1082: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:1101: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:1120: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:1139: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:1165: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:1189: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/security-architects.rst:1213: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/soc-operators.rst:234: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/soc-operators.rst:261: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/soc-operators.rst:280: ERROR: Unexpected indentation.
/home/<USER>/dev/work/blast-radius/docs/user-guides/threat-modeling.rst:176: WARNING: Title underline too short.

Quantitative Risk Assessment
---------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/threat-modeling.rst:176: WARNING: Title underline too short.

Quantitative Risk Assessment
---------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/threat-modeling.rst:229: WARNING: Title underline too short.

Compliance Impact Analysis
-------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/threat-modeling.rst:229: WARNING: Title underline too short.

Compliance Impact Analysis
-------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/threat-modeling.rst:232: WARNING: Title underline too short.

Regulatory Compliance Assessment
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/threat-modeling.rst:286: WARNING: Title underline too short.

Mitigation Strategy Generation
-----------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/threat-modeling.rst:286: WARNING: Title underline too short.

Mitigation Strategy Generation
-----------------------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/threat-modeling.rst:321: WARNING: Title underline too short.

Advanced Features
----------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/threat-modeling.rst:321: WARNING: Title underline too short.

Advanced Features
----------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/threat-modeling.rst:324: WARNING: Title underline too short.

Continuous Risk Monitoring
~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/threat-modeling.rst:343: WARNING: Title underline too short.

Threat Intelligence Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/threat-modeling.rst:343: WARNING: Title underline too short.

Threat Intelligence Integration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
looking for now-outdated files... none found
pickling environment... done
checking consistency... /home/<USER>/dev/work/blast-radius/docs/DOCUMENTATION_EXPANSION_SUMMARY.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/DOCUMENTATION_SUMMARY.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/LOCAL-DEVELOPMENT.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/README.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/development/code-standards.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/development/setup.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/development/workflow.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/enhanced-features-summary.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/enhanced-prd-v2.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/implementation-gap-analysis.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/multi-cloud-integration.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/operations/runbooks/backup-recovery-runbooks.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/operations/runbooks/maintenance-procedures.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/operations/runbooks/monitoring-runbooks.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/performance/index.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/performance/optimization.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/phase-integration-plan.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/compliance-documentation.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/framework.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/operations/incident-response.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/procedures/security-review-process.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/procedures/vulnerability-disclosure.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/reviews/security-assessment.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/reviews/security-review-2025-06-14.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/reviews/vulnerability-management.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/testing/security-automation.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/testing/static-analysis.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/technical-specifications/compliance-framework-schema.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/technical-specifications/ml-threat-prediction.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/technical-specifications/thehive-integration.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/technical/index.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/testing/index.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/testing/unit-tests.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/common-issues.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/user-guides/threat-modeling.rst: WARNING: document isn't included in any toctree
done
preparing documents... WARNING: unsupported theme option 'flyout_display' given
WARNING: unsupported theme option 'version_selector' given
WARNING: unsupported theme option 'language_selector' given
done
copying assets... 
copying static files... done
copying extra files... done
copying assets: done
writing output... [  1%] DOCUMENTATION_EXPANSION_SUMMARY
writing output... [  2%] DOCUMENTATION_SUMMARY
writing output... [  4%] LOCAL-DEVELOPMENT
writing output... [  5%] README
writing output... [  6%] access-control/least-privilege-framework
writing output... [  7%] api/asset-management
writing output... [  9%] api/attack-path-analysis
writing output... [ 10%] api/authentication
writing output... [ 11%] api/index
writing output... [ 12%] api/mitre-attack-integration
writing output... [ 14%] api/threat-modeling
writing output... [ 15%] architecture/zero-trust-architecture
writing output... [ 16%] compliance/gdpr-compliance-framework
writing output... [ 17%] configuration
writing output... [ 19%] deployment/environment-setup
writing output... [ 20%] deployment/production-architecture
writing output... [ 21%] deployment/production-deployment-guide
writing output... [ 22%] deployment/troubleshooting-guide
writing output... [ 23%] development/code-standards
writing output... [ 25%] development/contributing
writing output... [ 26%] development/setup
writing output... [ 27%] development/workflow
writing output... [ 28%] documentation-achievements-summary
writing output... [ 30%] documentation-overview
writing output... [ 31%] enhanced-features-summary
writing output... [ 32%] enhanced-prd-v2
writing output... [ 33%] implementation-gap-analysis
writing output... [ 35%] index
writing output... [ 36%] installation
writing output... [ 37%] latest-implementations-summary
writing output... [ 38%] multi-cloud-integration
writing output... [ 40%] operations/runbooks/backup-recovery-runbooks
writing output... [ 41%] operations/runbooks/maintenance-procedures
writing output... [ 42%] operations/runbooks/monitoring-runbooks
writing output... [ 43%] performance/index
writing output... [ 44%] performance/optimization
writing output... [ 46%] phase-integration-plan
writing output... [ 47%] production-readiness-status
writing output... [ 48%] quick-start-guide
writing output... [ 49%] security/architecture/overview
writing output... [ 51%] security/compliance-documentation
writing output... [ 52%] security/enhanced-audit-logging
writing output... [ 53%] security/framework
writing output... [ 54%] security/incident-response-procedures
writing output... [ 56%] security/index
writing output... [ 57%] security/operations/incident-response
writing output... [ 58%] security/procedures/security-review-process
writing output... [ 59%] security/procedures/vulnerability-disclosure
writing output... [ 60%] security/reviews/security-assessment
writing output... [ 62%] security/reviews/security-review-2025-06-14
writing output... [ 63%] security/reviews/vulnerability-management
writing output... [ 64%] security/security-review-processes
writing output... [ 65%] security/security-summary
writing output... [ 67%] security/testing/dynamic-testing
writing output... [ 68%] security/testing/security-automation
writing output... [ 69%] security/testing/static-analysis
writing output... [ 70%] technical-specifications/compliance-framework-schema
writing output... [ 72%] technical-specifications/ml-threat-prediction
writing output... [ 73%] technical-specifications/thehive-integration
writing output... [ 74%] technical/attack-path-architecture
writing output... [ 75%] technical/attack-path-flows
writing output... [ 77%] technical/database-design
writing output... [ 78%] technical/index
writing output... [ 79%] technical/product-requirements
writing output... [ 80%] testing/index
writing output... [ 81%] testing/unit-tests
writing output... [ 83%] troubleshooting/common-issues
writing output... [ 84%] troubleshooting/faq
writing output... [ 85%] use-cases/asset-discovery
writing output... [ 86%] use-cases/attack-path-analysis
writing output... [ 88%] user-guides/administrators
writing output... [ 89%] user-guides/attack-path-analysis
writing output... [ 90%] user-guides/compliance-officers
writing output... [ 91%] user-guides/executive-leadership
writing output... [ 93%] user-guides/index
writing output... [ 94%] user-guides/mitre-attack-integration
writing output... [ 95%] user-guides/purple-team-members
writing output... [ 96%] user-guides/red-team-members
writing output... [ 98%] user-guides/security-architects
writing output... [ 99%] user-guides/soc-operators
writing output... [100%] user-guides/threat-modeling

/home/<USER>/dev/work/blast-radius/docs/LOCAL-DEVELOPMENT.md:392: WARNING: 'myst' cross-reference target not found: '../ARCHITECTURE.md'
/home/<USER>/dev/work/blast-radius/docs/LOCAL-DEVELOPMENT.md:393: WARNING: 'myst' cross-reference target not found: '../SECURITY.md'
/home/<USER>/dev/work/blast-radius/docs/LOCAL-DEVELOPMENT.md:394: WARNING: 'myst' cross-reference target not found: '../DEPLOYMENT.md'
/home/<USER>/dev/work/blast-radius/docs/README.md:195: WARNING: 'myst' cross-reference target not found: '../CONTRIBUTING.md'
/home/<USER>/dev/work/blast-radius/docs/api/authentication.rst:323: WARNING: unknown document: '../security/access-control'
/home/<USER>/dev/work/blast-radius/docs/deployment/production-deployment-guide.md:541: WARNING: 'myst' cross-reference target not found: '../operations/'
/home/<USER>/dev/work/blast-radius/docs/development/code-standards.rst:303: WARNING: Lexing literal_block "// Interfaces - PascalCase with 'I' prefix (optional)\ninterface AttackPathResult {\n  paths: AttackPath[];\n  riskScore: number;\n}\n\n// Types - PascalCase\ntype AssetType = 'server' | 'database' | 'workstation';\n\n// Components - PascalCase\nconst AttackPathVisualization: React.FC<Props> = ({ data }) => {\n  return <div>{/* Component content */}</div>;\n};\n\n// Functions and variables - camelCase\nconst analyzeAttackPath = async (sourceId: number): Promise<AttackPath[]> => {\n  const analysisResult = await api.analyzeAttackPath(sourceId);\n  return analysisResult.paths;\n};\n\n// Constants - UPPER_SNAKE_CASE\nconst MAX_RETRY_ATTEMPTS = 3;\nconst API_BASE_URL = process.env.REACT_APP_API_URL;" as "typescript" resulted in an error at token: '/'. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/development/code-standards.rst:331: WARNING: Lexing literal_block "import React, { useState, useCallback, useEffect } from 'react';\nimport { AttackPath, Asset } from '../types';\nimport { attackPathService } from '../services';\n\ninterface AttackPathAnalyzerProps {\n  sourceAsset: Asset;\n  targetAsset: Asset;\n  onAnalysisComplete: (paths: AttackPath[]) => void;\n  className?: string;\n}\n\nexport const AttackPathAnalyzer: React.FC<AttackPathAnalyzerProps> = ({\n  sourceAsset,\n  targetAsset,\n  onAnalysisComplete,\n  className = ''\n}) => {\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleAnalysis = useCallback(async () => {\n    if (!sourceAsset || !targetAsset) {\n      setError('Source and target assets are required');\n      return;\n    }\n\n    setIsAnalyzing(true);\n    setError(null);\n\n    try {\n      const paths = await attackPathService.analyze({\n        sourceAssetId: sourceAsset.id,\n        targetAssetId: targetAsset.id\n      });\n      onAnalysisComplete(paths);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Analysis failed';\n      setError(errorMessage);\n    } finally {\n      setIsAnalyzing(false);\n    }\n  }, [sourceAsset, targetAsset, onAnalysisComplete]);\n\n  useEffect(() => {\n    // Auto-analyze when assets change\n    if (sourceAsset && targetAsset) {\n      handleAnalysis();\n    }\n  }, [sourceAsset?.id, targetAsset?.id, handleAnalysis]);\n\n  return (\n    <div className={`attack-path-analyzer ${className}`}>\n      {/* Component JSX */}\n    </div>\n  );\n};" as "typescript" resulted in an error at token: '/'. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/development/contributing.rst:288: WARNING: Lexing literal_block '// Good examples\n\ninterface AttackPathProps {\n  sourceAsset: Asset;\n  targetAsset: Asset;\n  onAnalysisComplete: (result: AttackPathResult) => void;\n}\n\nconst AttackPathAnalyzer: React.FC<AttackPathProps> = ({\n  sourceAsset,\n  targetAsset,\n  onAnalysisComplete\n}) => {\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const analyzeAttackPath = useCallback(async () => {\n    setIsAnalyzing(true);\n    setError(null);\n\n    try {\n      const result = await attackPathService.analyze({\n        sourceAssetId: sourceAsset.id,\n        targetAssetId: targetAsset.id\n      });\n      onAnalysisComplete(result);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \'Analysis failed\');\n    } finally {\n      setIsAnalyzing(false);\n    }\n  }, [sourceAsset.id, targetAsset.id, onAnalysisComplete]);\n\n  return (\n    <div className="attack-path-analyzer">\n      {/* Component JSX */}\n    </div>\n  );\n};' as "typescript" resulted in an error at token: '/'. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/development/workflow.rst:612: WARNING: unknown document: 'testing'
/home/<USER>/dev/work/blast-radius/docs/installation.rst:405: WARNING: unknown document: 'security/access-control'
/home/<USER>/dev/work/blast-radius/docs/installation.rst:406: WARNING: unknown document: 'security/best-practices'
/home/<USER>/dev/work/blast-radius/docs/multi-cloud-integration.md:11: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/performance/index.rst:502: WARNING: unknown document: 'monitoring'
/home/<USER>/dev/work/blast-radius/docs/performance/index.rst:503: WARNING: unknown document: 'scalability'
/home/<USER>/dev/work/blast-radius/docs/performance/index.rst:504: WARNING: unknown document: 'benchmarks'
/home/<USER>/dev/work/blast-radius/docs/performance/optimization.rst:289: WARNING: Lexing literal_block '// Before: Inefficient query\nMATCH (source:Asset)-[*1..5]-(target:Asset)\nWHERE source.id = $sourceId AND target.id = $targetId\nRETURN path;\n\n// After: Optimized query with constraints\nMATCH path = (source:Asset)-[*1..5]-(target:Asset)\nWHERE source.id = $sourceId\nAND target.id = $targetId\nAND ALL(n IN nodes(path) WHERE n.is_active = true)\nRETURN path\nLIMIT 100;\n\n// Use indexes for better performance\nCREATE INDEX asset_id_index FOR (a:Asset) ON (a.id);\nCREATE INDEX asset_type_index FOR (a:Asset) ON (a.type);' as "cypher" resulted in an error at token: '$'. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/quick-start-guide.rst:310: WARNING: unknown document: 'security/best-practices'
/home/<USER>/dev/work/blast-radius/docs/quick-start-guide.rst:311: WARNING: unknown document: 'technical/monitoring'
/home/<USER>/dev/work/blast-radius/docs/quick-start-guide.rst:312: WARNING: unknown document: 'technical/deployment'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:127: WARNING: unknown document: 'threat-model'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:128: WARNING: unknown document: 'security-controls'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:129: WARNING: unknown document: 'data-flow'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:130: WARNING: unknown document: '../access-control/authentication'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:131: WARNING: unknown document: '../data-protection/encryption'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:132: WARNING: unknown document: '../infrastructure/network-security'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:147: WARNING: unknown document: '../compliance/soc2'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:148: WARNING: unknown document: '../compliance/gdpr'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:149: WARNING: unknown document: '../compliance/iso27001'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:164: WARNING: unknown document: '../testing/overview'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:165: WARNING: unknown document: '../testing/penetration-testing'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:181: WARNING: unknown document: '../operations/vulnerability-management'
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:429: WARNING: Lexing literal_block '# Get audit trail\nGET /api/v1/audit/events?user_id=user-123&start_time=2024-01-01&limit=100\n\n# Get specific event\nGET /api/v1/audit/events/{event_id}' as "python" resulted in an error at token: '?'. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/security/index.rst:208: WARNING: unknown document: 'testing/penetration-testing'
/home/<USER>/dev/work/blast-radius/docs/security/operations/incident-response.rst:457: WARNING: unknown document: 'security-monitoring'
/home/<USER>/dev/work/blast-radius/docs/security/operations/incident-response.rst:458: WARNING: unknown document: 'vulnerability-management'
/home/<USER>/dev/work/blast-radius/docs/security/operations/incident-response.rst:460: WARNING: unknown document: '../compliance/soc2'
/home/<USER>/dev/work/blast-radius/docs/security/procedures/vulnerability-disclosure.rst:432: WARNING: unknown document: '../testing/overview'
/home/<USER>/dev/work/blast-radius/docs/security/procedures/vulnerability-disclosure.rst:433: WARNING: unknown document: '../best-practices/secure-development'
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:602: WARNING: unknown document: 'penetration-testing'
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:604: WARNING: unknown document: 'overview'
/home/<USER>/dev/work/blast-radius/docs/security/testing/static-analysis.rst:488: WARNING: unknown document: 'penetration-testing'
/home/<USER>/dev/work/blast-radius/docs/security/testing/static-analysis.rst:490: WARNING: unknown document: 'overview'
/home/<USER>/dev/work/blast-radius/docs/technical/database-design.rst:573: WARNING: Lexing literal_block '-- Soft delete trigger function\nCREATE OR REPLACE FUNCTION soft_delete_asset()\nRETURNS TRIGGER AS $$\nBEGIN\n    IF NEW.is_deleted = true AND OLD.is_deleted = false THEN\n        NEW.deleted_at = CURRENT_TIMESTAMP;\n    ELSIF NEW.is_deleted = false AND OLD.is_deleted = true THEN\n        NEW.deleted_at = NULL;\n    END IF;\n    RETURN NEW;\nEND;\n$$ LANGUAGE plpgsql;\n\n-- Apply soft delete trigger\nCREATE TRIGGER trigger_soft_delete_asset\n    BEFORE UPDATE ON assets\n    FOR EACH ROW\n    EXECUTE FUNCTION soft_delete_asset();' as "sql" resulted in an error at token: '$'. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/technical/database-design.rst:596: WARNING: Lexing literal_block "-- Audit log retention (90 days)\nCREATE OR REPLACE FUNCTION cleanup_audit_logs()\nRETURNS void AS $$\nBEGIN\n    DELETE FROM audit_logs\n    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days';\nEND;\n$$ LANGUAGE plpgsql;\n\n-- Attack path cache cleanup (30 days)\nCREATE OR REPLACE FUNCTION cleanup_attack_paths()\nRETURNS void AS $$\nBEGIN\n    DELETE FROM attack_paths\n    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '30 days'\n    AND path_id NOT IN (\n        SELECT DISTINCT unnest(string_to_array(path_nodes::text, ','))\n        FROM attack_scenarios\n        WHERE created_at > CURRENT_TIMESTAMP - INTERVAL '7 days'\n    );\nEND;\n$$ LANGUAGE plpgsql;" as "sql" resulted in an error at token: '$'. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/technical/database-design.rst:626: WARNING: Lexing literal_block '-- Automated maintenance procedures\nCREATE OR REPLACE FUNCTION maintenance_vacuum_analyze()\nRETURNS void AS $$\nBEGIN\n    -- Vacuum and analyze high-traffic tables\n    VACUUM ANALYZE assets;\n    VACUUM ANALYZE asset_relationships;\n    VACUUM ANALYZE attack_paths;\n    VACUUM ANALYZE audit_logs;\n\n    -- Update table statistics\n    ANALYZE assets;\n    ANALYZE asset_relationships;\n    ANALYZE attack_paths;\n    ANALYZE attack_scenarios;\nEND;\n$$ LANGUAGE plpgsql;' as "sql" resulted in an error at token: '$'. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/technical/database-design.rst:648: WARNING: Lexing literal_block '-- Reindex heavily used indexes\nCREATE OR REPLACE FUNCTION maintenance_reindex()\nRETURNS void AS $$\nBEGIN\n    REINDEX INDEX CONCURRENTLY idx_assets_type_provider;\n    REINDEX INDEX CONCURRENTLY idx_asset_relationships_source;\n    REINDEX INDEX CONCURRENTLY idx_attack_paths_source_target;\n    REINDEX INDEX CONCURRENTLY idx_attack_paths_criticality;\nEND;\n$$ LANGUAGE plpgsql;' as "sql" resulted in an error at token: '$'. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/testing/index.rst:645: WARNING: unknown document: 'integration-tests'
/home/<USER>/dev/work/blast-radius/docs/testing/index.rst:646: WARNING: unknown document: 'security-tests'
/home/<USER>/dev/work/blast-radius/docs/testing/index.rst:647: WARNING: unknown document: 'performance-tests'
/home/<USER>/dev/work/blast-radius/docs/testing/index.rst:332: WARNING: Lexing literal_block '# locustfile.py\nfrom locust import HttpUser, task, between\n\nclass BlastRadiusUser(HttpUser):\n    wait_time = between(1, 3)\n\n    def on_start(self):\n        # Login\n        response = self.client.post("/api/v1/auth/login", json={\n            "email": "<EMAIL>",\n            "password": "testpassword"\n        })\n        self.token = response.json()["access_token"]\n        self.headers = {"Authorization": f"Bearer {self.token}"}\n\n    @task(3)\n    def view_assets(self):\n        self.client.get("/api/v1/assets", headers=self.headers)\n\n    @task(2)\n    def view_attack_paths(self):\n        self.client.get("/api/v1/attack-paths", headers=self.headers)\n\n    @task(1)\n    def run_analysis(self):\n        self.client.post("/api/v1/analysis/attack-paths",\n            json={\n                "source_asset_id": 1,\n                "target_asset_id": 2,\n                "max_depth": 5\n            },\n            headers=self.headers\n        )' as "yaml" resulted in an error at token: ','. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:335: WARNING: unknown document: '../releases/changelog'
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:525: WARNING: unknown document: '../releases/roadmap'
generating indices... genindex done
highlighting module code... 
writing additional pages... search opensearch done
dumping search index in English (code: en)... done
dumping object inventory... done
build succeeded, 809 warnings.

The HTML pages are in _build/html.
