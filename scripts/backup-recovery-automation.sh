#!/bin/bash

# Backup and Recovery Automation for Blast-Radius Security Tool
# This script provides automated backup and recovery capabilities

set -e

# Configuration
PROJECT_NAME="blast-radius"
BACKUP_BUCKET="blast-radius-backups"
NAMESPACE="blast-radius"
OPERATION=${1}
BACKUP_TYPE=${2:-full}
RESTORE_TARGET=${3}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
Backup and Recovery Automation for Blast-Radius Security Tool

Usage: $0 <operation> [backup-type] [restore-target]

Operations:
    backup          - Create backup
    restore         - Restore from backup
    list            - List available backups
    verify          - Verify backup integrity
    cleanup         - Clean up old backups
    schedule        - Setup automated backup schedule

Backup Types:
    full            - Complete system backup (default)
    database        - Database only
    config          - Configuration only
    files           - File system only

Examples:
    $0 backup full                    # Create full system backup
    $0 backup database                # Create database backup only
    $0 restore full backup-20240101   # Restore from specific backup
    $0 list                          # List all available backups
    $0 verify backup-20240101        # Verify backup integrity
    $0 cleanup 30                    # Clean backups older than 30 days

EOF
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."

    # Check required tools
    local required_tools=("kubectl" "aws" "pg_dump" "pg_restore")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "$tool is not installed"
            exit 1
        fi
    done

    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS credentials not configured"
        exit 1
    fi

    # Check Kubernetes connectivity
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi

    # Check S3 bucket exists
    if ! aws s3 ls "s3://$BACKUP_BUCKET" &> /dev/null; then
        log_warning "Backup bucket does not exist, creating..."
        aws s3 mb "s3://$BACKUP_BUCKET"
        aws s3api put-bucket-versioning --bucket "$BACKUP_BUCKET" --versioning-configuration Status=Enabled
    fi

    log_success "Prerequisites check passed"
}

# Create database backup
backup_database() {
    local backup_name="database-$(date +%Y%m%d_%H%M%S)"
    local backup_file="/tmp/$backup_name.sql"
    
    log_info "Creating database backup: $backup_name"
    
    # Get database credentials
    local db_host=$(kubectl get secret database-credentials -n $NAMESPACE -o jsonpath='{.data.host}' | base64 -d)
    local db_user=$(kubectl get secret database-credentials -n $NAMESPACE -o jsonpath='{.data.username}' | base64 -d)
    local db_password=$(kubectl get secret database-credentials -n $NAMESPACE -o jsonpath='{.data.password}' | base64 -d)
    local db_name=$(kubectl get secret database-credentials -n $NAMESPACE -o jsonpath='{.data.database}' | base64 -d)
    
    # Create backup
    PGPASSWORD="$db_password" pg_dump \
        -h "$db_host" \
        -U "$db_user" \
        -d "$db_name" \
        --verbose \
        --no-password \
        --format=custom \
        --compress=9 \
        --file="$backup_file"
    
    # Upload to S3
    aws s3 cp "$backup_file" "s3://$BACKUP_BUCKET/database/$backup_name.sql" \
        --metadata "backup-type=database,created-date=$(date --iso-8601),size=$(stat -c%s $backup_file)"
    
    # Create RDS snapshot
    local snapshot_id="$PROJECT_NAME-$backup_name"
    aws rds create-db-snapshot \
        --db-instance-identifier "$PROJECT_NAME-database" \
        --db-snapshot-identifier "$snapshot_id" \
        --tags Key=BackupType,Value=automated Key=CreatedBy,Value=backup-script
    
    # Cleanup local file
    rm "$backup_file"
    
    log_success "Database backup completed: $backup_name"
    echo "$backup_name"
}

# Create configuration backup
backup_config() {
    local backup_name="config-$(date +%Y%m%d_%H%M%S)"
    local backup_dir="/tmp/$backup_name"
    
    log_info "Creating configuration backup: $backup_name"
    
    mkdir -p "$backup_dir"/{k8s,terraform,monitoring}
    
    # Backup Kubernetes resources
    kubectl get all -n $NAMESPACE -o yaml > "$backup_dir/k8s/all-resources.yaml"
    kubectl get configmaps -n $NAMESPACE -o yaml > "$backup_dir/k8s/configmaps.yaml"
    kubectl get secrets -n $NAMESPACE -o yaml > "$backup_dir/k8s/secrets.yaml"
    kubectl get pvc -n $NAMESPACE -o yaml > "$backup_dir/k8s/pvc.yaml"
    kubectl get networkpolicies -n $NAMESPACE -o yaml > "$backup_dir/k8s/networkpolicies.yaml"
    
    # Backup Terraform state
    aws s3 cp "s3://$PROJECT_NAME-terraform-state/terraform.tfstate" "$backup_dir/terraform/" || log_warning "Terraform state backup failed"
    
    # Backup monitoring configurations
    kubectl get configmaps -n $NAMESPACE prometheus-config -o yaml > "$backup_dir/monitoring/prometheus.yaml" || true
    kubectl get configmaps -n $NAMESPACE grafana-dashboards -o yaml > "$backup_dir/monitoring/grafana.yaml" || true
    
    # Create archive
    tar -czf "/tmp/$backup_name.tar.gz" -C "/tmp" "$backup_name"
    
    # Upload to S3
    aws s3 cp "/tmp/$backup_name.tar.gz" "s3://$BACKUP_BUCKET/config/$backup_name.tar.gz" \
        --metadata "backup-type=config,created-date=$(date --iso-8601)"
    
    # Cleanup
    rm -rf "$backup_dir" "/tmp/$backup_name.tar.gz"
    
    log_success "Configuration backup completed: $backup_name"
    echo "$backup_name"
}

# Create file system backup
backup_files() {
    local backup_name="files-$(date +%Y%m%d_%H%M%S)"
    
    log_info "Creating file system backup: $backup_name"
    
    # Create volume snapshots
    kubectl apply -f - << EOF
apiVersion: snapshot.storage.k8s.io/v1
kind: VolumeSnapshot
metadata:
  name: $backup_name
  namespace: $NAMESPACE
spec:
  source:
    persistentVolumeClaimName: blast-radius-data-pvc
  volumeSnapshotClassName: ebs-csi-snapshot-class
EOF
    
    # Wait for snapshot completion
    kubectl wait --for=condition=ReadyToUse volumesnapshot/$backup_name -n $NAMESPACE --timeout=600s
    
    # Backup application files
    kubectl exec -it deployment/blast-radius-backend -n $NAMESPACE -- \
        tar -czf "/tmp/$backup_name.tar.gz" /app/uploads/ /app/logs/ || true
    
    # Copy to S3
    kubectl exec -it deployment/blast-radius-backend -n $NAMESPACE -- \
        aws s3 cp "/tmp/$backup_name.tar.gz" "s3://$BACKUP_BUCKET/files/$backup_name.tar.gz"
    
    # Cleanup
    kubectl exec -it deployment/blast-radius-backend -n $NAMESPACE -- \
        rm "/tmp/$backup_name.tar.gz" || true
    
    log_success "File system backup completed: $backup_name"
    echo "$backup_name"
}

# Create full backup
backup_full() {
    log_info "Creating full system backup..."
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_name="full-$timestamp"
    
    # Create individual backups
    local db_backup=$(backup_database)
    local config_backup=$(backup_config)
    local files_backup=$(backup_files)
    
    # Create manifest
    cat > "/tmp/$backup_name-manifest.json" << EOF
{
    "backup_name": "$backup_name",
    "backup_type": "full",
    "created_date": "$(date --iso-8601)",
    "components": {
        "database": "$db_backup",
        "config": "$config_backup",
        "files": "$files_backup"
    },
    "version": "$(kubectl get deployment blast-radius-backend -n $NAMESPACE -o jsonpath='{.spec.template.spec.containers[0].image}' | cut -d: -f2)"
}
EOF
    
    # Upload manifest
    aws s3 cp "/tmp/$backup_name-manifest.json" "s3://$BACKUP_BUCKET/manifests/$backup_name-manifest.json"
    
    # Cleanup
    rm "/tmp/$backup_name-manifest.json"
    
    log_success "Full backup completed: $backup_name"
    echo "$backup_name"
}

# Restore database
restore_database() {
    local backup_name=$1
    
    log_info "Restoring database from backup: $backup_name"
    
    # Download backup
    aws s3 cp "s3://$BACKUP_BUCKET/database/$backup_name.sql" "/tmp/$backup_name.sql"
    
    # Get database credentials
    local db_host=$(kubectl get secret database-credentials -n $NAMESPACE -o jsonpath='{.data.host}' | base64 -d)
    local db_user=$(kubectl get secret database-credentials -n $NAMESPACE -o jsonpath='{.data.username}' | base64 -d)
    local db_password=$(kubectl get secret database-credentials -n $NAMESPACE -o jsonpath='{.data.password}' | base64 -d)
    local db_name=$(kubectl get secret database-credentials -n $NAMESPACE -o jsonpath='{.data.database}' | base64 -d)
    
    # Create temporary database
    local temp_db="${db_name}_restore_$(date +%s)"
    PGPASSWORD="$db_password" createdb -h "$db_host" -U "$db_user" "$temp_db"
    
    # Restore to temporary database
    PGPASSWORD="$db_password" pg_restore \
        -h "$db_host" \
        -U "$db_user" \
        -d "$temp_db" \
        --verbose \
        --no-password \
        "/tmp/$backup_name.sql"
    
    # Verify restore
    local table_count=$(PGPASSWORD="$db_password" psql -h "$db_host" -U "$db_user" -d "$temp_db" -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';")
    
    if [ "$table_count" -gt 0 ]; then
        log_success "Database restore verification passed"
        
        # Swap databases (requires application downtime)
        log_warning "Swapping databases requires application downtime"
        kubectl scale deployment blast-radius-backend --replicas=0 -n $NAMESPACE
        
        # Rename databases
        PGPASSWORD="$db_password" psql -h "$db_host" -U "$db_user" -d postgres -c "ALTER DATABASE $db_name RENAME TO ${db_name}_old;"
        PGPASSWORD="$db_password" psql -h "$db_host" -U "$db_user" -d postgres -c "ALTER DATABASE $temp_db RENAME TO $db_name;"
        
        # Restart application
        kubectl scale deployment blast-radius-backend --replicas=3 -n $NAMESPACE
        
        log_success "Database restored successfully"
    else
        log_error "Database restore verification failed"
        PGPASSWORD="$db_password" dropdb -h "$db_host" -U "$db_user" "$temp_db"
        exit 1
    fi
    
    # Cleanup
    rm "/tmp/$backup_name.sql"
}

# List available backups
list_backups() {
    log_info "Available backups:"
    
    echo "Database Backups:"
    aws s3 ls "s3://$BACKUP_BUCKET/database/" --human-readable --summarize
    
    echo -e "\nConfiguration Backups:"
    aws s3 ls "s3://$BACKUP_BUCKET/config/" --human-readable --summarize
    
    echo -e "\nFile System Backups:"
    aws s3 ls "s3://$BACKUP_BUCKET/files/" --human-readable --summarize
    
    echo -e "\nFull Backup Manifests:"
    aws s3 ls "s3://$BACKUP_BUCKET/manifests/" --human-readable --summarize
}

# Verify backup integrity
verify_backup() {
    local backup_name=$1
    
    log_info "Verifying backup integrity: $backup_name"
    
    # Check if backup exists
    if ! aws s3 ls "s3://$BACKUP_BUCKET/database/$backup_name.sql" &> /dev/null; then
        log_error "Backup not found: $backup_name"
        exit 1
    fi
    
    # Download and verify
    aws s3 cp "s3://$BACKUP_BUCKET/database/$backup_name.sql" "/tmp/$backup_name.sql"
    
    # Check file integrity
    if pg_restore --list "/tmp/$backup_name.sql" &> /dev/null; then
        log_success "Backup integrity verified: $backup_name"
    else
        log_error "Backup integrity check failed: $backup_name"
        exit 1
    fi
    
    # Cleanup
    rm "/tmp/$backup_name.sql"
}

# Cleanup old backups
cleanup_backups() {
    local retention_days=${1:-30}
    
    log_info "Cleaning up backups older than $retention_days days"
    
    # Calculate cutoff date
    local cutoff_date=$(date -d "$retention_days days ago" +%Y-%m-%d)
    
    # Cleanup S3 backups
    aws s3api list-objects-v2 --bucket "$BACKUP_BUCKET" --query "Contents[?LastModified<'$cutoff_date'].Key" --output text | \
    while read -r key; do
        if [ -n "$key" ]; then
            aws s3 rm "s3://$BACKUP_BUCKET/$key"
            log_info "Deleted old backup: $key"
        fi
    done
    
    # Cleanup RDS snapshots
    aws rds describe-db-snapshots --db-instance-identifier "$PROJECT_NAME-database" \
        --query "DBSnapshots[?SnapshotCreateTime<'$cutoff_date'].DBSnapshotIdentifier" --output text | \
    while read -r snapshot; do
        if [ -n "$snapshot" ]; then
            aws rds delete-db-snapshot --db-snapshot-identifier "$snapshot"
            log_info "Deleted old RDS snapshot: $snapshot"
        fi
    done
    
    log_success "Backup cleanup completed"
}

# Setup automated backup schedule
setup_schedule() {
    log_info "Setting up automated backup schedule..."
    
    # Create CronJob for daily backups
    kubectl apply -f - << EOF
apiVersion: batch/v1
kind: CronJob
metadata:
  name: blast-radius-backup
  namespace: $NAMESPACE
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: backup
            image: blast-radius-backup:latest
            command:
            - /bin/bash
            - -c
            - |
              /scripts/backup-recovery-automation.sh backup full
              /scripts/backup-recovery-automation.sh cleanup 30
            env:
            - name: AWS_DEFAULT_REGION
              value: "us-west-2"
            volumeMounts:
            - name: backup-scripts
              mountPath: /scripts
          volumes:
          - name: backup-scripts
            configMap:
              name: backup-scripts
              defaultMode: 0755
          restartPolicy: OnFailure
EOF
    
    log_success "Automated backup schedule configured"
}

# Main function
main() {
    case $OPERATION in
        "backup")
            check_prerequisites
            case $BACKUP_TYPE in
                "database") backup_database ;;
                "config") backup_config ;;
                "files") backup_files ;;
                "full") backup_full ;;
                *) log_error "Unknown backup type: $BACKUP_TYPE"; show_help; exit 1 ;;
            esac
            ;;
        "restore")
            check_prerequisites
            if [ -z "$RESTORE_TARGET" ]; then
                log_error "Restore target is required"
                show_help
                exit 1
            fi
            restore_database "$RESTORE_TARGET"
            ;;
        "list")
            list_backups
            ;;
        "verify")
            check_prerequisites
            if [ -z "$BACKUP_TYPE" ]; then
                log_error "Backup name is required for verification"
                show_help
                exit 1
            fi
            verify_backup "$BACKUP_TYPE"
            ;;
        "cleanup")
            check_prerequisites
            cleanup_backups "$BACKUP_TYPE"
            ;;
        "schedule")
            check_prerequisites
            setup_schedule
            ;;
        *)
            log_error "Unknown operation: $OPERATION"
            show_help
            exit 1
            ;;
    esac
}

# Check if help is requested
if [ "$1" = "-h" ] || [ "$1" = "--help" ] || [ -z "$1" ]; then
    show_help
    exit 0
fi

# Make script executable
chmod +x "$0"

# Run main function
main "$@"
