# Prometheus Configuration for Blast-Radius Security Tool (Production)

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    environment: 'production'
    project: 'blast-radius'
    cluster: 'blast-radius-prod'

rule_files:
  - "blast-radius-rules.yml"
  - "security-rules.yml"
  - "infrastructure-rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
      timeout: 10s
      api_version: v2

scrape_configs:
  # Blast-Radius Backend API (Multiple instances)
  - job_name: 'blast-radius-backend'
    kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names:
            - blast-radius
    relabel_configs:
      - source_labels: [__meta_kubernetes_service_name]
        action: keep
        regex: blast-radius-backend
      - source_labels: [__meta_kubernetes_endpoint_port_name]
        action: keep
        regex: metrics
      - source_labels: [__meta_kubernetes_pod_name]
        target_label: instance
      - source_labels: [__meta_kubernetes_namespace]
        target_label: kubernetes_namespace
      - source_labels: [__meta_kubernetes_service_name]
        target_label: kubernetes_name
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  # Blast-Radius Frontend
  - job_name: 'blast-radius-frontend'
    kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names:
            - blast-radius
    relabel_configs:
      - source_labels: [__meta_kubernetes_service_name]
        action: keep
        regex: blast-radius-frontend
      - source_labels: [__meta_kubernetes_endpoint_port_name]
        action: keep
        regex: metrics
    metrics_path: '/metrics'
    scrape_interval: 30s

  # PostgreSQL Database Metrics (RDS)
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter.blast-radius.svc.cluster.local:9187']
    scrape_interval: 15s
    metrics_path: '/metrics'
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'blast-radius-postgres-prod'

  # Redis Cache Metrics (ElastiCache)
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter.blast-radius.svc.cluster.local:9121']
    scrape_interval: 15s
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'blast-radius-redis-prod'

  # Kubernetes Cluster Metrics
  - job_name: 'kubernetes-apiservers'
    kubernetes_sd_configs:
      - role: endpoints
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: default;kubernetes;https

  - job_name: 'kubernetes-nodes'
    kubernetes_sd_configs:
      - role: node
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)
      - target_label: __address__
        replacement: kubernetes.default.svc:443
      - source_labels: [__meta_kubernetes_node_name]
        regex: (.+)
        target_label: __metrics_path__
        replacement: /api/v1/nodes/${1}/proxy/metrics

  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
      - role: pod
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_pod_label_(.+)
      - source_labels: [__meta_kubernetes_namespace]
        action: replace
        target_label: kubernetes_namespace
      - source_labels: [__meta_kubernetes_pod_name]
        action: replace
        target_label: kubernetes_pod_name

  # Celery Worker Metrics
  - job_name: 'celery-worker'
    kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names:
            - blast-radius
    relabel_configs:
      - source_labels: [__meta_kubernetes_service_name]
        action: keep
        regex: celery-worker
      - source_labels: [__meta_kubernetes_endpoint_port_name]
        action: keep
        regex: metrics
    scrape_interval: 15s

  # Node Exporter for System Metrics
  - job_name: 'node-exporter'
    kubernetes_sd_configs:
      - role: endpoints
    relabel_configs:
      - source_labels: [__meta_kubernetes_service_name]
        action: keep
        regex: node-exporter
      - source_labels: [__meta_kubernetes_endpoint_port_name]
        action: keep
        regex: metrics

  # kube-state-metrics
  - job_name: 'kube-state-metrics'
    static_configs:
      - targets: ['kube-state-metrics.kube-system.svc.cluster.local:8080']

  # Prometheus Self-Monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s

  # Grafana Metrics
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana.blast-radius.svc.cluster.local:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Alertmanager Metrics
  - job_name: 'alertmanager'
    static_configs:
      - targets: ['alertmanager.blast-radius.svc.cluster.local:9093']
    scrape_interval: 30s

  # AWS Load Balancer Controller
  - job_name: 'aws-load-balancer-controller'
    kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names:
            - kube-system
    relabel_configs:
      - source_labels: [__meta_kubernetes_service_name]
        action: keep
        regex: aws-load-balancer-webhook-service

  # Traefik Metrics (if using Traefik)
  - job_name: 'traefik'
    static_configs:
      - targets: ['traefik.kube-system.svc.cluster.local:8080']
    metrics_path: '/metrics'
    scrape_interval: 30s

# Remote write configuration for long-term storage
remote_write:
  - url: "https://prometheus-remote-write.monitoring.amazonaws.com/workspaces/ws-xxxxxxxx/api/v1/remote_write"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500
    sigv4:
      region: us-west-2
      service: aps

# Storage configuration for production
storage:
  tsdb:
    retention.time: 30d
    retention.size: 50GB
    wal-compression: true
    min-block-duration: 2h
    max-block-duration: 25h
