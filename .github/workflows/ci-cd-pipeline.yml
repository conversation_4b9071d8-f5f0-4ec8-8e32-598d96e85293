# Comprehensive CI/CD Pipeline for Blast-Radius Security Tool

name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
    tags: ['v*']
  pull_request:
    branches: [main, develop]

env:
  AWS_REGION: us-west-2
  ECR_REPOSITORY_BACKEND: blast-radius-backend
  ECR_REPOSITORY_FRONTEND: blast-radius-frontend
  KUBERNETES_NAMESPACE: blast-radius

jobs:
  # Code Quality and Security Checks
  code-quality:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install Python dependencies
        run: |
          cd backend
          pip install -r requirements/development.txt

      - name: Install Node.js dependencies
        run: |
          cd frontend
          npm ci

      - name: Python code formatting check
        run: |
          cd backend
          black --check .
          isort --check-only .

      - name: Python linting
        run: |
          cd backend
          flake8 .
          pylint app/

      - name: Python type checking
        run: |
          cd backend
          mypy app/

      - name: Frontend linting
        run: |
          cd frontend
          npm run lint

      - name: Security scan - Python
        run: |
          cd backend
          bandit -r app/ -f json -o bandit-report.json
          safety check --json --output safety-report.json

      - name: Security scan - Node.js
        run: |
          cd frontend
          npm audit --audit-level=high

      - name: Secret scanning
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD

      - name: Upload security reports
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-reports
          path: |
            backend/bandit-report.json
            backend/safety-report.json

  # Unit Tests
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    needs: code-quality
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_blast_radius
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install Python dependencies
        run: |
          cd backend
          pip install -r requirements/test.txt

      - name: Install Node.js dependencies
        run: |
          cd frontend
          npm ci

      - name: Run Python unit tests
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_blast_radius
          REDIS_URL: redis://localhost:6379/0
        run: |
          cd backend
          pytest tests/unit/ \
            --cov=app \
            --cov-report=xml:coverage.xml \
            --cov-report=html:htmlcov \
            --junitxml=test-results.xml \
            -v

      - name: Run Frontend unit tests
        run: |
          cd frontend
          npm run test:unit -- --coverage --watchAll=false --ci

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results
          path: |
            backend/test-results.xml
            backend/coverage.xml
            backend/htmlcov/
            frontend/coverage/

  # Build and Push Images
  build-images:
    name: Build & Push Images
    runs-on: ubuntu-latest
    needs: unit-tests
    if: github.event_name == 'push'
    outputs:
      backend-image: ${{ steps.build-backend.outputs.image }}
      frontend-image: ${{ steps.build-frontend.outputs.image }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Generate image tags
        id: meta
        run: |
          if [[ $GITHUB_REF == refs/tags/* ]]; then
            VERSION=${GITHUB_REF#refs/tags/}
          elif [[ $GITHUB_REF == refs/heads/main ]]; then
            VERSION=latest
          else
            VERSION=${GITHUB_SHA::8}
          fi
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "registry=${{ steps.login-ecr.outputs.registry }}" >> $GITHUB_OUTPUT

      - name: Build and push backend image
        id: build-backend
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: true
          tags: |
            ${{ steps.meta.outputs.registry }}/${{ env.ECR_REPOSITORY_BACKEND }}:${{ steps.meta.outputs.version }}
            ${{ steps.meta.outputs.registry }}/${{ env.ECR_REPOSITORY_BACKEND }}:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64

      - name: Build and push frontend image
        id: build-frontend
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          file: ./frontend/Dockerfile
          push: true
          tags: |
            ${{ steps.meta.outputs.registry }}/${{ env.ECR_REPOSITORY_FRONTEND }}:${{ steps.meta.outputs.version }}
            ${{ steps.meta.outputs.registry }}/${{ env.ECR_REPOSITORY_FRONTEND }}:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64

      - name: Scan images for vulnerabilities
        run: |
          # Install Trivy
          sudo apt-get update
          sudo apt-get install wget apt-transport-https gnupg lsb-release
          wget -qO - https://aquasecurity.github.io/trivy-repo/deb/public.key | sudo apt-key add -
          echo "deb https://aquasecurity.github.io/trivy-repo/deb $(lsb_release -sc) main" | sudo tee -a /etc/apt/sources.list.d/trivy.list
          sudo apt-get update
          sudo apt-get install trivy

          # Scan backend image
          trivy image --exit-code 1 --severity HIGH,CRITICAL \
            ${{ steps.meta.outputs.registry }}/${{ env.ECR_REPOSITORY_BACKEND }}:${{ steps.meta.outputs.version }}

          # Scan frontend image
          trivy image --exit-code 1 --severity HIGH,CRITICAL \
            ${{ steps.meta.outputs.registry }}/${{ env.ECR_REPOSITORY_FRONTEND }}:${{ steps.meta.outputs.version }}

      - name: Set image outputs
        run: |
          echo "image=${{ steps.meta.outputs.registry }}/${{ env.ECR_REPOSITORY_BACKEND }}:${{ steps.meta.outputs.version }}" >> $GITHUB_OUTPUT

  # Integration Tests
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: build-images
    if: github.event_name == 'push'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Set up kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure kubectl for staging
        run: |
          aws eks update-kubeconfig --region ${{ env.AWS_REGION }} --name blast-radius-staging-cluster

      - name: Deploy to staging
        run: |
          # Update image tags in manifests
          sed -i 's|{{BACKEND_IMAGE}}|${{ needs.build-images.outputs.backend-image }}|g' k8s/staging/*.yaml
          sed -i 's|{{FRONTEND_IMAGE}}|${{ needs.build-images.outputs.frontend-image }}|g' k8s/staging/*.yaml
          
          # Apply manifests
          kubectl apply -f k8s/staging/ -n blast-radius-staging
          
          # Wait for deployment
          kubectl rollout status deployment/blast-radius-backend -n blast-radius-staging --timeout=300s

      - name: Run integration tests
        run: |
          chmod +x scripts/automated-testing-pipeline.sh
          ./scripts/automated-testing-pipeline.sh staging integration

      - name: Upload integration test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: integration-test-results
          path: test-reports/

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-images, integration-tests]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Set up kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure kubectl for production
        run: |
          aws eks update-kubeconfig --region ${{ env.AWS_REGION }} --name blast-radius-cluster

      - name: Create pre-deployment backup
        run: |
          chmod +x scripts/backup-recovery-automation.sh
          ./scripts/backup-recovery-automation.sh backup full

      - name: Blue-Green Deployment
        run: |
          chmod +x scripts/blue-green-deployment.sh
          ./scripts/blue-green-deployment.sh ${{ github.sha }} production

      - name: Run production smoke tests
        run: |
          chmod +x scripts/automated-testing-pipeline.sh
          ./scripts/automated-testing-pipeline.sh production e2e

      - name: Update deployment status
        if: always()
        run: |
          if [ $? -eq 0 ]; then
            echo "✅ Production deployment successful"
          else
            echo "❌ Production deployment failed"
            # Trigger rollback if needed
            ./scripts/blue-green-deployment.sh ${{ github.sha }} production true
          fi

  # Security Scanning
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: build-images
    if: github.event_name == 'push'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run OWASP ZAP scan
        uses: zaproxy/action-full-scan@v0.7.0
        with:
          target: 'https://staging.blast-radius.com'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a'

      - name: Upload ZAP scan results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: zap-scan-results
          path: report_html.html

  # Performance Testing
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: integration-tests
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          pip install locust

      - name: Run performance tests
        run: |
          chmod +x scripts/automated-testing-pipeline.sh
          ./scripts/automated-testing-pipeline.sh staging performance

      - name: Upload performance test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: performance-test-results
          path: test-reports/performance/

  # Cleanup
  cleanup:
    name: Cleanup
    runs-on: ubuntu-latest
    needs: [deploy-production, security-scan, performance-tests]
    if: always()
    steps:
      - name: Cleanup staging environment
        if: github.ref == 'refs/heads/main'
        run: |
          # Scale down staging environment to save costs
          kubectl scale deployment --all --replicas=0 -n blast-radius-staging || true

      - name: Cleanup old images
        run: |
          # Keep only last 10 images in ECR
          aws ecr list-images --repository-name ${{ env.ECR_REPOSITORY_BACKEND }} \
            --filter tagStatus=UNTAGGED \
            --query 'imageIds[?imageDigest!=null]' \
            --output json | jq '.[:10]' | \
            aws ecr batch-delete-image --repository-name ${{ env.ECR_REPOSITORY_BACKEND }} \
            --image-ids file:///dev/stdin || true
