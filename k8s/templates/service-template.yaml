# Blue-Green Service Template for Blast-Radius Security Tool

apiVersion: v1
kind: Service
metadata:
  name: blast-radius-backend-{{COLOR}}
  namespace: blast-radius
  labels:
    app: blast-radius-backend
    version: {{COLOR}}
    component: backend
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: blast-radius-backend
    version: {{COLOR}}

---
apiVersion: v1
kind: Service
metadata:
  name: blast-radius-frontend-{{COLOR}}
  namespace: blast-radius
  labels:
    app: blast-radius-frontend
    version: {{COLOR}}
    component: frontend
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: blast-radius-frontend
    version: {{COLOR}}

---
# Main backend service (traffic routing)
apiVersion: v1
kind: Service
metadata:
  name: blast-radius-backend
  namespace: blast-radius
  labels:
    app: blast-radius-backend
    component: backend
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: blast-radius-backend
    version: {{COLOR}}  # This will be updated during traffic switch

---
# Main frontend service (traffic routing)
apiVersion: v1
kind: Service
metadata:
  name: blast-radius-frontend
  namespace: blast-radius
  labels:
    app: blast-radius-frontend
    component: frontend
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: blast-radius-frontend
    version: {{COLOR}}  # This will be updated during traffic switch

---
# Headless service for service discovery
apiVersion: v1
kind: Service
metadata:
  name: blast-radius-backend-headless
  namespace: blast-radius
  labels:
    app: blast-radius-backend
    component: backend
spec:
  clusterIP: None
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: blast-radius-backend

---
# Service for monitoring both colors
apiVersion: v1
kind: Service
metadata:
  name: blast-radius-monitoring
  namespace: blast-radius
  labels:
    app: blast-radius
    component: monitoring
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: blast-radius-backend
    # No version selector to monitor both colors
