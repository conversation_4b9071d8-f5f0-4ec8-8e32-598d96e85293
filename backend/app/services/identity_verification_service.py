"""
Enhanced Identity Verification Service for Zero-Trust Architecture.

This service provides comprehensive identity verification including:
- Multi-factor authentication (MFA)
- Biometric verification
- Device fingerprinting
- Behavioral analysis
- Risk-based authentication
- Continuous authentication
"""

import hashlib
import secrets
import base64
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass
import asyncio

from sqlalchemy.orm import Session
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import pyotp
import qrcode
from io import BytesIO

from app.core.config import settings
from app.core.logging import get_logger
from app.db.models.user import User
from app.db.models.mfa_device import MFADevice, MFADeviceType
from app.services.security_event_correlator import SecurityEventCorrelator, SecurityEventData, EventCategory, EventSeverity

logger = get_logger(__name__)


class VerificationMethod(Enum):
    """Identity verification methods."""
    PASSWORD = "password"
    MFA_TOTP = "mfa_totp"
    MFA_SMS = "mfa_sms"
    MFA_EMAIL = "mfa_email"
    BIOMETRIC_FINGERPRINT = "biometric_fingerprint"
    BIOMETRIC_FACE = "biometric_face"
    DEVICE_CERTIFICATE = "device_certificate"
    HARDWARE_TOKEN = "hardware_token"
    PUSH_NOTIFICATION = "push_notification"


class RiskLevel(Enum):
    """Authentication risk levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AuthenticationResult(Enum):
    """Authentication result types."""
    SUCCESS = "success"
    FAILURE = "failure"
    REQUIRES_MFA = "requires_mfa"
    REQUIRES_ADDITIONAL_VERIFICATION = "requires_additional_verification"
    BLOCKED = "blocked"


@dataclass
class DeviceFingerprint:
    """Device fingerprint data."""
    user_agent: str
    screen_resolution: str
    timezone: str
    language: str
    platform: str
    plugins: List[str]
    canvas_fingerprint: str
    webgl_fingerprint: str
    audio_fingerprint: str
    fingerprint_hash: str


@dataclass
class BiometricData:
    """Biometric verification data."""
    biometric_type: str
    template_hash: str
    confidence_score: float
    quality_score: float
    liveness_score: float
    metadata: Dict[str, Any]


@dataclass
class BehavioralProfile:
    """User behavioral profile for analysis."""
    user_id: str
    typing_patterns: Dict[str, float]
    mouse_patterns: Dict[str, float]
    login_times: List[datetime]
    location_patterns: List[str]
    device_patterns: List[str]
    application_usage: Dict[str, int]
    risk_score: float


@dataclass
class AuthenticationContext:
    """Authentication context for risk assessment."""
    ip_address: str
    user_agent: str
    device_fingerprint: Optional[DeviceFingerprint]
    geolocation: Optional[Dict[str, Any]]
    time_of_day: datetime
    is_new_device: bool
    is_new_location: bool
    recent_failed_attempts: int
    account_age_days: int
    last_successful_login: Optional[datetime]


@dataclass
class VerificationChallenge:
    """Identity verification challenge."""
    challenge_id: str
    user_id: str
    method: VerificationMethod
    challenge_data: Dict[str, Any]
    expires_at: datetime
    attempts_remaining: int
    created_at: datetime


class IdentityVerificationService:
    """Enhanced identity verification service for zero-trust architecture."""
    
    def __init__(self, db: Session, security_correlator: SecurityEventCorrelator):
        self.db = db
        self.security_correlator = security_correlator
        self._encryption_key = self._derive_encryption_key()
        self._cipher = Fernet(self._encryption_key)
        
        # Behavioral analysis cache
        self.behavioral_profiles = {}
        
        # Device fingerprint cache
        self.device_fingerprints = {}
        
        # Active challenges
        self.active_challenges = {}
    
    async def authenticate_user(
        self,
        username: str,
        password: str,
        context: AuthenticationContext,
        mfa_code: Optional[str] = None,
        biometric_data: Optional[BiometricData] = None
    ) -> Tuple[AuthenticationResult, Dict[str, Any]]:
        """
        Perform comprehensive user authentication with risk assessment.
        
        Args:
            username: Username or email
            password: User password
            context: Authentication context
            mfa_code: Optional MFA code
            biometric_data: Optional biometric verification data
            
        Returns:
            Tuple of (AuthenticationResult, additional_data)
        """
        try:
            # Find user
            user = self.db.query(User).filter(
                (User.username == username) | (User.email == username)
            ).first()
            
            if not user:
                await self._log_security_event(
                    "authentication_failed",
                    f"Authentication failed: user not found - {username}",
                    context,
                    EventSeverity.MEDIUM
                )
                return AuthenticationResult.FAILURE, {"error": "Invalid credentials"}
            
            # Check if account is locked or disabled
            if not user.is_active:
                await self._log_security_event(
                    "authentication_blocked",
                    f"Authentication blocked: account disabled - {username}",
                    context,
                    EventSeverity.HIGH,
                    user_id=str(user.id)
                )
                return AuthenticationResult.BLOCKED, {"error": "Account disabled"}
            
            # Verify password
            if not user.verify_password(password):
                user.failed_login_attempts += 1
                user.last_failed_login = datetime.utcnow()
                self.db.commit()
                
                await self._log_security_event(
                    "authentication_failed",
                    f"Authentication failed: invalid password - {username}",
                    context,
                    EventSeverity.MEDIUM,
                    user_id=str(user.id)
                )
                
                # Check for account lockout
                if user.failed_login_attempts >= 5:
                    user.locked_until = datetime.utcnow() + timedelta(minutes=30)
                    self.db.commit()
                    
                    await self._log_security_event(
                        "account_locked",
                        f"Account locked due to failed attempts - {username}",
                        context,
                        EventSeverity.HIGH,
                        user_id=str(user.id)
                    )
                    
                    return AuthenticationResult.BLOCKED, {"error": "Account locked"}
                
                return AuthenticationResult.FAILURE, {"error": "Invalid credentials"}
            
            # Password verified - perform risk assessment
            risk_assessment = await self._assess_authentication_risk(user, context)
            
            # Check if additional verification is required based on risk
            if risk_assessment["risk_level"] in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
                if not mfa_code and not biometric_data:
                    # Require additional verification
                    challenge = await self._create_verification_challenge(user, context, risk_assessment)
                    return AuthenticationResult.REQUIRES_ADDITIONAL_VERIFICATION, {
                        "challenge": challenge,
                        "risk_level": risk_assessment["risk_level"].value,
                        "required_methods": risk_assessment["required_methods"]
                    }
            
            # Verify MFA if provided or required
            if user.mfa_enabled or mfa_code:
                if not mfa_code:
                    return AuthenticationResult.REQUIRES_MFA, {
                        "mfa_methods": await self._get_user_mfa_methods(user)
                    }
                
                mfa_result = await self._verify_mfa(user, mfa_code, context)
                if not mfa_result["success"]:
                    await self._log_security_event(
                        "mfa_verification_failed",
                        f"MFA verification failed - {username}",
                        context,
                        EventSeverity.HIGH,
                        user_id=str(user.id)
                    )
                    return AuthenticationResult.FAILURE, {"error": "Invalid MFA code"}
            
            # Verify biometric data if provided
            if biometric_data:
                biometric_result = await self._verify_biometric(user, biometric_data, context)
                if not biometric_result["success"]:
                    await self._log_security_event(
                        "biometric_verification_failed",
                        f"Biometric verification failed - {username}",
                        context,
                        EventSeverity.HIGH,
                        user_id=str(user.id)
                    )
                    return AuthenticationResult.FAILURE, {"error": "Biometric verification failed"}
            
            # Update user login information
            user.last_login = datetime.utcnow()
            user.failed_login_attempts = 0
            user.locked_until = None
            self.db.commit()
            
            # Update behavioral profile
            await self._update_behavioral_profile(user, context)
            
            # Log successful authentication
            await self._log_security_event(
                "authentication_success",
                f"Successful authentication - {username}",
                context,
                EventSeverity.LOW,
                user_id=str(user.id)
            )
            
            return AuthenticationResult.SUCCESS, {
                "user_id": str(user.id),
                "risk_level": risk_assessment["risk_level"].value,
                "session_duration": risk_assessment["recommended_session_duration"],
                "requires_continuous_auth": risk_assessment["requires_continuous_auth"]
            }
            
        except Exception as e:
            logger.error(f"Error during authentication: {str(e)}")
            await self._log_security_event(
                "authentication_error",
                f"Authentication error - {username}: {str(e)}",
                context,
                EventSeverity.HIGH
            )
            return AuthenticationResult.FAILURE, {"error": "Authentication error"}
    
    async def _assess_authentication_risk(
        self,
        user: User,
        context: AuthenticationContext
    ) -> Dict[str, Any]:
        """
        Assess authentication risk based on various factors.
        
        Args:
            user: User attempting authentication
            context: Authentication context
            
        Returns:
            Risk assessment results
        """
        risk_factors = []
        risk_score = 0.0
        
        # Check for new device
        if context.is_new_device:
            risk_factors.append("new_device")
            risk_score += 0.3
        
        # Check for new location
        if context.is_new_location:
            risk_factors.append("new_location")
            risk_score += 0.2
        
        # Check time of day (unusual login times)
        hour = context.time_of_day.hour
        if hour < 6 or hour > 22:  # Outside normal business hours
            risk_factors.append("unusual_time")
            risk_score += 0.1
        
        # Check recent failed attempts
        if context.recent_failed_attempts > 0:
            risk_factors.append("recent_failed_attempts")
            risk_score += min(context.recent_failed_attempts * 0.1, 0.3)
        
        # Check account age
        if context.account_age_days < 30:
            risk_factors.append("new_account")
            risk_score += 0.2
        
        # Check time since last login
        if context.last_successful_login:
            days_since_login = (datetime.utcnow() - context.last_successful_login).days
            if days_since_login > 30:
                risk_factors.append("long_absence")
                risk_score += 0.1
        
        # Check behavioral patterns
        behavioral_risk = await self._assess_behavioral_risk(user, context)
        risk_score += behavioral_risk["score"]
        risk_factors.extend(behavioral_risk["factors"])
        
        # Check threat intelligence
        threat_risk = await self._assess_threat_intelligence_risk(context)
        risk_score += threat_risk["score"]
        risk_factors.extend(threat_risk["factors"])
        
        # Determine risk level
        if risk_score >= 0.8:
            risk_level = RiskLevel.CRITICAL
        elif risk_score >= 0.6:
            risk_level = RiskLevel.HIGH
        elif risk_score >= 0.3:
            risk_level = RiskLevel.MEDIUM
        else:
            risk_level = RiskLevel.LOW
        
        # Determine required verification methods
        required_methods = []
        if risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
            required_methods.append(VerificationMethod.MFA_TOTP)
            if risk_level == RiskLevel.CRITICAL:
                required_methods.append(VerificationMethod.BIOMETRIC_FINGERPRINT)
        
        # Determine session duration based on risk
        if risk_level == RiskLevel.CRITICAL:
            session_duration = 15  # 15 minutes
        elif risk_level == RiskLevel.HIGH:
            session_duration = 60  # 1 hour
        elif risk_level == RiskLevel.MEDIUM:
            session_duration = 240  # 4 hours
        else:
            session_duration = 480  # 8 hours
        
        return {
            "risk_level": risk_level,
            "risk_score": risk_score,
            "risk_factors": risk_factors,
            "required_methods": required_methods,
            "recommended_session_duration": session_duration,
            "requires_continuous_auth": risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]
        }
    
    async def _verify_mfa(
        self,
        user: User,
        mfa_code: str,
        context: AuthenticationContext
    ) -> Dict[str, Any]:
        """
        Verify MFA code for user.
        
        Args:
            user: User to verify MFA for
            mfa_code: MFA code to verify
            context: Authentication context
            
        Returns:
            Verification result
        """
        # Get user's MFA devices
        mfa_devices = self.db.query(MFADevice).filter(
            MFADevice.user_id == user.id,
            MFADevice.is_active == True
        ).all()
        
        if not mfa_devices:
            return {"success": False, "error": "No MFA devices configured"}
        
        # Try to verify code with each device
        for device in mfa_devices:
            if device.device_type == MFADeviceType.TOTP:
                # Verify TOTP code
                secret = self._decrypt_secret(device.secret_key)
                totp = pyotp.TOTP(secret)
                
                if totp.verify(mfa_code, valid_window=1):
                    # Update device last used
                    device.last_used_at = datetime.utcnow()
                    self.db.commit()
                    
                    return {
                        "success": True,
                        "device_id": str(device.id),
                        "device_type": device.device_type.value
                    }
            
            elif device.device_type == MFADeviceType.SMS:
                # Verify SMS code (implementation would check against sent code)
                # This is a simplified example
                if await self._verify_sms_code(device, mfa_code):
                    device.last_used_at = datetime.utcnow()
                    self.db.commit()
                    
                    return {
                        "success": True,
                        "device_id": str(device.id),
                        "device_type": device.device_type.value
                    }
        
        return {"success": False, "error": "Invalid MFA code"}
    
    async def _verify_biometric(
        self,
        user: User,
        biometric_data: BiometricData,
        context: AuthenticationContext
    ) -> Dict[str, Any]:
        """
        Verify biometric data for user.
        
        Args:
            user: User to verify biometric for
            biometric_data: Biometric data to verify
            context: Authentication context
            
        Returns:
            Verification result
        """
        # Get stored biometric templates for user
        stored_templates = await self._get_user_biometric_templates(user, biometric_data.biometric_type)
        
        if not stored_templates:
            return {"success": False, "error": "No biometric templates enrolled"}
        
        # Verify biometric data against stored templates
        for template in stored_templates:
            similarity_score = await self._compare_biometric_templates(
                template["template_hash"],
                biometric_data.template_hash
            )
            
            # Check if similarity meets threshold and quality/liveness scores are acceptable
            if (similarity_score >= 0.85 and 
                biometric_data.confidence_score >= 0.8 and
                biometric_data.quality_score >= 0.7 and
                biometric_data.liveness_score >= 0.8):
                
                return {
                    "success": True,
                    "biometric_type": biometric_data.biometric_type,
                    "confidence_score": biometric_data.confidence_score,
                    "similarity_score": similarity_score
                }
        
        return {"success": False, "error": "Biometric verification failed"}
    
    async def _create_verification_challenge(
        self,
        user: User,
        context: AuthenticationContext,
        risk_assessment: Dict[str, Any]
    ) -> VerificationChallenge:
        """
        Create a verification challenge for high-risk authentication.
        
        Args:
            user: User to create challenge for
            context: Authentication context
            risk_assessment: Risk assessment results
            
        Returns:
            Verification challenge
        """
        challenge_id = secrets.token_urlsafe(32)
        
        # Select verification method based on risk level and available methods
        available_methods = await self._get_available_verification_methods(user)
        required_methods = risk_assessment["required_methods"]
        
        # Choose the most secure available method
        selected_method = None
        for method in required_methods:
            if method in available_methods:
                selected_method = method
                break
        
        if not selected_method:
            selected_method = available_methods[0] if available_methods else VerificationMethod.MFA_TOTP
        
        # Create challenge data based on method
        challenge_data = await self._create_challenge_data(user, selected_method, context)
        
        challenge = VerificationChallenge(
            challenge_id=challenge_id,
            user_id=str(user.id),
            method=selected_method,
            challenge_data=challenge_data,
            expires_at=datetime.utcnow() + timedelta(minutes=10),
            attempts_remaining=3,
            created_at=datetime.utcnow()
        )
        
        self.active_challenges[challenge_id] = challenge
        
        return challenge
    
    def _derive_encryption_key(self) -> bytes:
        """Derive encryption key for sensitive data."""
        password = settings.SECRET_KEY.encode()
        salt = b"blast_radius_identity_verification"
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        return base64.urlsafe_b64encode(kdf.derive(password))
    
    def _encrypt_secret(self, secret: str) -> str:
        """Encrypt a secret for storage."""
        return self._cipher.encrypt(secret.encode()).decode()
    
    def _decrypt_secret(self, encrypted_secret: str) -> str:
        """Decrypt a stored secret."""
        return self._cipher.decrypt(encrypted_secret.encode()).decode()
    
    async def _log_security_event(
        self,
        event_type: str,
        description: str,
        context: AuthenticationContext,
        severity: EventSeverity,
        user_id: Optional[str] = None
    ) -> None:
        """Log security event for correlation and analysis."""
        event_data = SecurityEventData(
            event_id=f"{event_type}_{int(datetime.utcnow().timestamp())}_{secrets.token_hex(8)}",
            timestamp=datetime.utcnow(),
            source_ip=context.ip_address,
            user_id=user_id,
            service_account_id=None,
            event_type=event_type,
            category=EventCategory.AUTHENTICATION,
            severity=severity,
            description=description,
            raw_data={
                "user_agent": context.user_agent,
                "geolocation": context.geolocation,
                "is_new_device": context.is_new_device,
                "is_new_location": context.is_new_location
            },
            indicators=[],
            metadata={}
        )
        
        await self.security_correlator.process_security_event(event_data)
    
    # Additional helper methods would be implemented here...
    async def _assess_behavioral_risk(self, user: User, context: AuthenticationContext) -> Dict[str, Any]:
        """Assess behavioral risk factors."""
        return {"score": 0.0, "factors": []}
    
    async def _assess_threat_intelligence_risk(self, context: AuthenticationContext) -> Dict[str, Any]:
        """Assess threat intelligence risk factors."""
        return {"score": 0.0, "factors": []}
    
    async def _get_user_mfa_methods(self, user: User) -> List[str]:
        """Get available MFA methods for user."""
        return ["totp", "sms"]
    
    async def _verify_sms_code(self, device: MFADevice, code: str) -> bool:
        """Verify SMS code."""
        return True  # Simplified implementation
    
    async def _get_user_biometric_templates(self, user: User, biometric_type: str) -> List[Dict[str, Any]]:
        """Get stored biometric templates for user."""
        return []  # Simplified implementation
    
    async def _compare_biometric_templates(self, template1: str, template2: str) -> float:
        """Compare biometric templates."""
        return 0.9  # Simplified implementation
    
    async def _get_available_verification_methods(self, user: User) -> List[VerificationMethod]:
        """Get available verification methods for user."""
        return [VerificationMethod.MFA_TOTP]
    
    async def _create_challenge_data(self, user: User, method: VerificationMethod, context: AuthenticationContext) -> Dict[str, Any]:
        """Create challenge data for verification method."""
        return {}
    
    async def _update_behavioral_profile(self, user: User, context: AuthenticationContext) -> None:
        """Update user behavioral profile."""
        # Implementation would update behavioral patterns
        pass
