# incident_response/automation.py
import asyncio
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import structlog
from sqlalchemy.orm import Session
from sqlalchemy import func

from ..models import Asset, Vulnerability, Relationship
from ..services.blast_radius_service import BlastRadiusService
from ..services.graph_service import GraphService

logger = structlog.get_logger()


class IncidentSeverity(Enum):
    """Incident severity levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFORMATIONAL = "informational"


class IncidentStatus(Enum):
    """Incident status values."""
    OPEN = "open"
    INVESTIGATING = "investigating"
    CONTAINED = "contained"
    RESOLVED = "resolved"
    CLOSED = "closed"


class AutomationAction(Enum):
    """Types of automated actions."""
    ISOLATE_ASSET = "isolate_asset"
    DISABLE_USER = "disable_user"
    BLOCK_IP = "block_ip"
    QUARANTINE_DEVICE = "quarantine_device"
    ESCALATE_TO_SOC = "escalate_to_soc"
    NOTIFY_STAKEHOLDERS = "notify_stakeholders"
    CREATE_TICKET = "create_ticket"
    RUN_PLAYBOOK = "run_playbook"
    COLLECT_FORENSICS = "collect_forensics"
    UPDATE_FIREWALL = "update_firewall"


@dataclass
class SecurityIncident:
    """Represents a security incident."""
    id: str
    title: str
    description: str
    severity: IncidentSeverity
    status: IncidentStatus
    source_system: str
    detection_time: datetime
    affected_assets: List[str]
    indicators: List[Dict[str, Any]]
    blast_radius: Optional[Dict[str, Any]] = None
    automated_actions: List[Dict[str, Any]] = None
    assigned_analyst: Optional[str] = None
    resolution_time: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.automated_actions is None:
            self.automated_actions = []
        if self.metadata is None:
            self.metadata = {}


@dataclass
class AutomationRule:
    """Defines automated response rules."""
    id: str
    name: str
    description: str
    trigger_conditions: Dict[str, Any]
    actions: List[AutomationAction]
    severity_threshold: IncidentSeverity
    enabled: bool = True
    cooldown_minutes: int = 60
    max_executions_per_hour: int = 10
    requires_approval: bool = False


class IncidentResponseEngine:
    """Core incident response and automation engine."""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.graph_service = GraphService(db_session)
        self.blast_radius_service = BlastRadiusService(db_session)
        self.active_incidents: Dict[str, SecurityIncident] = {}
        self.automation_rules: List[AutomationRule] = []
        self.action_executors: Dict[AutomationAction, Callable] = {}
        self.execution_history: List[Dict[str, Any]] = []
        self._setup_default_rules()
        self._setup_action_executors()
    
    def _setup_default_rules(self):
        """Setup default automation rules."""
        
        # Critical vulnerability exploitation
        self.automation_rules.append(AutomationRule(
            id="critical-vuln-exploit",
            name="Critical Vulnerability Exploitation",
            description="Respond to exploitation of critical vulnerabilities",
            trigger_conditions={
                "severity": "critical",
                "indicators": ["vulnerability_exploitation", "malware_detection"],
                "asset_types": ["application", "database", "cloud_service"]
            },
            actions=[
                AutomationAction.ISOLATE_ASSET,
                AutomationAction.COLLECT_FORENSICS,
                AutomationAction.ESCALATE_TO_SOC,
                AutomationAction.NOTIFY_STAKEHOLDERS
            ],
            severity_threshold=IncidentSeverity.CRITICAL,
            requires_approval=False
        ))
        
        # Lateral movement detection
        self.automation_rules.append(AutomationRule(
            id="lateral-movement",
            name="Lateral Movement Detection",
            description="Respond to detected lateral movement",
            trigger_conditions={
                "severity": ["high", "critical"],
                "indicators": ["lateral_movement", "privilege_escalation"],
                "blast_radius_size": 5
            },
            actions=[
                AutomationAction.DISABLE_USER,
                AutomationAction.ISOLATE_ASSET,
                AutomationAction.UPDATE_FIREWALL,
                AutomationAction.RUN_PLAYBOOK
            ],
            severity_threshold=IncidentSeverity.HIGH,
            requires_approval=True,
            cooldown_minutes=30
        ))
        
        # Compromised user account
        self.automation_rules.append(AutomationRule(
            id="compromised-user",
            name="Compromised User Account",
            description="Respond to compromised user accounts",
            trigger_conditions={
                "severity": ["medium", "high", "critical"],
                "indicators": ["credential_theft", "suspicious_login", "account_takeover"],
                "asset_types": ["user"]
            },
            actions=[
                AutomationAction.DISABLE_USER,
                AutomationAction.NOTIFY_STAKEHOLDERS,
                AutomationAction.CREATE_TICKET,
                AutomationAction.COLLECT_FORENSICS
            ],
            severity_threshold=IncidentSeverity.MEDIUM
        ))
        
        # Malware detection
        self.automation_rules.append(AutomationRule(
            id="malware-detection",
            name="Malware Detection",
            description="Respond to malware detection on assets",
            trigger_conditions={
                "severity": ["high", "critical"],
                "indicators": ["malware", "ransomware", "trojan"],
                "asset_types": ["device", "application"]
            },
            actions=[
                AutomationAction.QUARANTINE_DEVICE,
                AutomationAction.ISOLATE_ASSET,
                AutomationAction.COLLECT_FORENSICS,
                AutomationAction.ESCALATE_TO_SOC
            ],
            severity_threshold=IncidentSeverity.HIGH,
            requires_approval=False
        ))
    
    def _setup_action_executors(self):
        """Setup action executor functions."""
        
        self.action_executors = {
            AutomationAction.ISOLATE_ASSET: self._isolate_asset,
            AutomationAction.DISABLE_USER: self._disable_user,
            AutomationAction.BLOCK_IP: self._block_ip,
            AutomationAction.QUARANTINE_DEVICE: self._quarantine_device,
            AutomationAction.ESCALATE_TO_SOC: self._escalate_to_soc,
            AutomationAction.NOTIFY_STAKEHOLDERS: self._notify_stakeholders,
            AutomationAction.CREATE_TICKET: self._create_ticket,
            AutomationAction.RUN_PLAYBOOK: self._run_playbook,
            AutomationAction.COLLECT_FORENSICS: self._collect_forensics,
            AutomationAction.UPDATE_FIREWALL: self._update_firewall
        }
    
    async def process_security_event(self, event_data: Dict[str, Any]) -> SecurityIncident:
        """Process incoming security event and create incident."""
        
        # Create incident from event
        incident = SecurityIncident(
            id=str(uuid.uuid4()),
            title=event_data.get("title", "Security Incident"),
            description=event_data.get("description", ""),
            severity=IncidentSeverity(event_data.get("severity", "medium")),
            status=IncidentStatus.OPEN,
            source_system=event_data.get("source", "unknown"),
            detection_time=datetime.fromisoformat(event_data.get("timestamp", datetime.utcnow().isoformat())),
            affected_assets=event_data.get("affected_assets", []),
            indicators=event_data.get("indicators", []),
            metadata=event_data.get("metadata", {})
        )
        
        logger.info(f"Processing security incident: {incident.id}", 
                   severity=incident.severity.value, 
                   affected_assets=len(incident.affected_assets))
        
        # Enrich incident with blast radius analysis
        if incident.affected_assets:
            try:
                blast_radius = await self.blast_radius_service.analyze_asset_blast_radius(
                    incident.affected_assets,
                    max_depth=3,
                    include_low_risk=False
                )
                incident.blast_radius = blast_radius.dict()
                
                # Update affected assets with blast radius results
                if blast_radius.affected_assets:
                    additional_assets = [asset.id for asset in blast_radius.affected_assets 
                                       if asset.id not in incident.affected_assets]
                    incident.affected_assets.extend(additional_assets)
                
            except Exception as e:
                logger.error(f"Error calculating blast radius for incident {incident.id}: {str(e)}")
        
        # Store incident
        self.active_incidents[incident.id] = incident
        
        # Execute automated response
        await self._execute_automated_response(incident)
        
        return incident
    
    async def _execute_automated_response(self, incident: SecurityIncident):
        """Execute automated response based on incident characteristics."""
        
        applicable_rules = self._find_applicable_rules(incident)
        
        for rule in applicable_rules:
            if await self._should_execute_rule(rule, incident):
                logger.info(f"Executing automation rule: {rule.name} for incident {incident.id}")
                
                try:
                    await self._execute_rule_actions(rule, incident)
                    
                    # Record execution
                    self.execution_history.append({
                        "rule_id": rule.id,
                        "incident_id": incident.id,
                        "executed_at": datetime.utcnow().isoformat(),
                        "actions": [action.value for action in rule.actions],
                        "success": True
                    })
                    
                except Exception as e:
                    logger.error(f"Error executing rule {rule.name}: {str(e)}")
                    
                    self.execution_history.append({
                        "rule_id": rule.id,
                        "incident_id": incident.id,
                        "executed_at": datetime.utcnow().isoformat(),
                        "actions": [action.value for action in rule.actions],
                        "success": False,
                        "error": str(e)
                    })
    
    def _find_applicable_rules(self, incident: SecurityIncident) -> List[AutomationRule]:
        """Find automation rules applicable to the incident."""
        
        applicable_rules = []
        
        for rule in self.automation_rules:
            if not rule.enabled:
                continue
            
            # Check severity threshold
            severity_levels = {
                IncidentSeverity.INFORMATIONAL: 0,
                IncidentSeverity.LOW: 1,
                IncidentSeverity.MEDIUM: 2,
                IncidentSeverity.HIGH: 3,
                IncidentSeverity.CRITICAL: 4
            }
            
            if severity_levels[incident.severity] < severity_levels[rule.severity_threshold]:
                continue
            
            # Check trigger conditions
            if self._matches_trigger_conditions(incident, rule.trigger_conditions):
                applicable_rules.append(rule)
        
        return applicable_rules
    
    def _matches_trigger_conditions(self, incident: SecurityIncident, conditions: Dict[str, Any]) -> bool:
        """Check if incident matches rule trigger conditions."""
        
        # Check severity condition
        if "severity" in conditions:
            severity_condition = conditions["severity"]
            if isinstance(severity_condition, str):
                if incident.severity.value != severity_condition:
                    return False
            elif isinstance(severity_condition, list):
                if incident.severity.value not in severity_condition:
                    return False
        
        # Check indicator conditions
        if "indicators" in conditions:
            required_indicators = conditions["indicators"]
            incident_indicators = [ind.get("type", "") for ind in incident.indicators]
            
            if not any(req_ind in incident_indicators for req_ind in required_indicators):
                return False
        
        # Check asset type conditions
        if "asset_types" in conditions and incident.affected_assets:
            required_types = conditions["asset_types"]
            
            # Get asset types for affected assets
            affected_asset_types = []
            for asset_id in incident.affected_assets:
                asset = self.db.query(Asset).filter(Asset.id == asset_id).first()
                if asset:
                    affected_asset_types.append(asset.asset_type.value)
            
            if not any(asset_type in affected_asset_types for asset_type in required_types):
                return False
        
        # Check blast radius size condition
        if "blast_radius_size" in conditions and incident.blast_radius:
            required_size = conditions["blast_radius_size"]
            actual_size = incident.blast_radius.get("total_affected", 0)
            
            if actual_size < required_size:
                return False
        
        return True
    
    async def _should_execute_rule(self, rule: AutomationRule, incident: SecurityIncident) -> bool:
        """Determine if rule should be executed considering cooldowns and limits."""
        
        now = datetime.utcnow()
        
        # Check cooldown
        recent_executions = [
            exec_record for exec_record in self.execution_history
            if exec_record["rule_id"] == rule.id and
               datetime.fromisoformat(exec_record["executed_at"]) > now - timedelta(minutes=rule.cooldown_minutes)
        ]
        
        if recent_executions:
            logger.info(f"Rule {rule.name} in cooldown period")
            return False
        
        # Check hourly execution limit
        hourly_executions = [
            exec_record for exec_record in self.execution_history
            if exec_record["rule_id"] == rule.id and
               datetime.fromisoformat(exec_record["executed_at"]) > now - timedelta(hours=1)
        ]
        
        if len(hourly_executions) >= rule.max_executions_per_hour:
            logger.warning(f"Rule {rule.name} has exceeded hourly execution limit")
            return False
        
        # Check if approval is required
        if rule.requires_approval:
            # In a real implementation, this would check for analyst approval
            # For now, we'll assume high-severity incidents get auto-approval
            if incident.severity in [IncidentSeverity.CRITICAL, IncidentSeverity.HIGH]:
                logger.info(f"Auto-approving rule {rule.name} for {incident.severity.value} incident")
                return True
            else:
                logger.info(f"Rule {rule.name} requires manual approval")
                return False
        
        return True
    
    async def _execute_rule_actions(self, rule: AutomationRule, incident: SecurityIncident):
        """Execute all actions for a rule."""
        
        for action in rule.actions:
            try:
                executor = self.action_executors.get(action)
                if executor:
                    result = await executor(incident, action)
                    
                    # Record action execution
                    incident.automated_actions.append({
                        "action": action.value,
                        "executed_at": datetime.utcnow().isoformat(),
                        "rule_id": rule.id,
                        "result": result,
                        "success": True
                    })
                    
                    logger.info(f"Successfully executed action {action.value} for incident {incident.id}")
                else:
                    logger.warning(f"No executor found for action {action.value}")
                    
            except Exception as e:
                logger.error(f"Error executing action {action.value}: {str(e)}")
                
                incident.automated_actions.append({
                    "action": action.value,
                    "executed_at": datetime.utcnow().isoformat(),
                    "rule_id": rule.id,
                    "error": str(e),
                    "success": False
                })
    
    # Action executor implementations
    async def _isolate_asset(self, incident: SecurityIncident, action: AutomationAction) -> Dict[str, Any]:
        """Isolate affected assets from the network."""
        isolated_assets = []
        
        for asset_id in incident.affected_assets:
            # In a real implementation, this would call network isolation APIs
            # For now, we'll simulate the action
            
            asset = self.db.query(Asset).filter(Asset.id == asset_id).first()
            if asset:
                isolated_assets.append({
                    "asset_id": asset_id,
                    "asset_name": asset.name,
                    "isolation_method": "network_acl",
                    "status": "isolated"
                })
                
                logger.info(f"Isolated asset {asset.name} ({asset_id})")
        
        return {
            "action": "isolate_asset",
            "isolated_assets": isolated_assets,
            "isolation_time": datetime.utcnow().isoformat()
        }
    
    async def _disable_user(self, incident: SecurityIncident, action: AutomationAction) -> Dict[str, Any]:
        """Disable user accounts involved in the incident."""
        disabled_users = []
        
        for asset_id in incident.affected_assets:
            asset = self.db.query(Asset).filter(
                Asset.id == asset_id,
                Asset.asset_type == 'user'
            ).first()
            
            if asset:
                # In a real implementation, this would call identity provider APIs
                disabled_users.append({
                    "user_id": asset_id,
                    "username": asset.name,
                    "disable_method": "active_directory",
                    "status": "disabled"
                })
                
                logger.info(f"Disabled user account {asset.name} ({asset_id})")
        
        return {
            "action": "disable_user",
            "disabled_users": disabled_users,
            "disable_time": datetime.utcnow().isoformat()
        }
    
    async def _block_ip(self, incident: SecurityIncident, action: AutomationAction) -> Dict[str, Any]:
        """Block malicious IP addresses."""
        blocked_ips = []
        
        # Extract IP addresses from incident indicators
        for indicator in incident.indicators:
            if indicator.get("type") == "ip_address":
                ip_address = indicator.get("value")
                if ip_address:
                    # In a real implementation, this would call firewall APIs
                    blocked_ips.append({
                        "ip_address": ip_address,
                        "block_method": "firewall_rule",
                        "status": "blocked"
                    })
                    
                    logger.info(f"Blocked IP address {ip_address}")
        
        return {
            "action": "block_ip",
            "blocked_ips": blocked_ips,
            "block_time": datetime.utcnow().isoformat()
        }
    
    async def _quarantine_device(self, incident: SecurityIncident, action: AutomationAction) -> Dict[str, Any]:
        """Quarantine infected devices."""
        quarantined_devices = []
        
        for asset_id in incident.affected_assets:
            asset = self.db.query(Asset).filter(
                Asset.id == asset_id,
                Asset.asset_type == 'device'
            ).first()
            
            if asset:
                # In a real implementation, this would call endpoint protection APIs
                quarantined_devices.append({
                    "device_id": asset_id,
                    "device_name": asset.name,
                    "quarantine_method": "endpoint_agent",
                    "status": "quarantined"
                })
                
                logger.info(f"Quarantined device {asset.name} ({asset_id})")
        
        return {
            "action": "quarantine_device",
            "quarantined_devices": quarantined_devices,
            "quarantine_time": datetime.utcnow().isoformat()
        }
    
    async def _escalate_to_soc(self, incident: SecurityIncident, action: AutomationAction) -> Dict[str, Any]:
        """Escalate incident to SOC team."""
        
        # In a real implementation, this would integrate with SOC tools
        escalation_data = {
            "incident_id": incident.id,
            "escalated_to": "soc_team",
            "escalation_time": datetime.utcnow().isoformat(),
            "priority": "high" if incident.severity in [IncidentSeverity.CRITICAL, IncidentSeverity.HIGH] else "medium",
            "summary": incident.description,
            "affected_assets_count": len(incident.affected_assets)
        }
        
        logger.info(f"Escalated incident {incident.id} to SOC team")
        
        return escalation_data
    
    async def _notify_stakeholders(self, incident: SecurityIncident, action: AutomationAction) -> Dict[str, Any]:
        """Notify relevant stakeholders about the incident."""
        
        notifications_sent = []
        
        # Determine stakeholders based on affected assets
        stakeholders = set()
        for asset_id in incident.affected_assets:
            asset = self.db.query(Asset).filter(Asset.id == asset_id).first()
            if asset and asset.owner:
                stakeholders.add(asset.owner)
            if asset and asset.business_unit:
                stakeholders.add(f"{asset.business_unit}_manager")
        
        # Add default stakeholders for high-severity incidents
        if incident.severity in [IncidentSeverity.CRITICAL, IncidentSeverity.HIGH]:
            stakeholders.update(["ciso", "security_team", "incident_commander"])
        
        for stakeholder in stakeholders:
            # In a real implementation, this would send emails/notifications
            notifications_sent.append({
                "stakeholder": stakeholder,
                "notification_method": "email",
                "status": "sent",
                "sent_time": datetime.utcnow().isoformat()
            })
            
            logger.info(f"Notified stakeholder {stakeholder} about incident {incident.id}")
        
        return {
            "action": "notify_stakeholders",
            "notifications": notifications_sent,
            "total_notified": len(notifications_sent)
        }
    
    async def _create_ticket(self, incident: SecurityIncident, action: AutomationAction) -> Dict[str, Any]:
        """Create a ticket in the incident management system."""
        
        # In a real implementation, this would integrate with ITSM tools
        ticket_data = {
            "ticket_id": f"INC-{datetime.utcnow().strftime('%Y%m%d')}-{incident.id[:8]}",
            "title": incident.title,
            "description": incident.description,
            "priority": incident.severity.value,
            "category": "security_incident",
            "affected_assets": incident.affected_assets,
            "created_time": datetime.utcnow().isoformat(),
            "status": "open"
        }
        
        logger.info(f"Created ticket {ticket_data['ticket_id']} for incident {incident.id}")
        
        return ticket_data
    
    async def _run_playbook(self, incident: SecurityIncident, action: AutomationAction) -> Dict[str, Any]:
        """Execute incident response playbook."""
        
        # Determine appropriate playbook based on incident characteristics
        playbook_name = self._select_playbook(incident)
        
        # In a real implementation, this would execute SOAR playbooks
        playbook_execution = {
            "playbook_name": playbook_name,
            "execution_id": str(uuid.uuid4()),
            "started_at": datetime.utcnow().isoformat(),
            "status": "running",
            "steps_completed": 0,
            "total_steps": 5  # Example
        }
        
        logger.info(f"Started playbook {playbook_name} for incident {incident.id}")
        
        return playbook_execution
    
    async def _collect_forensics(self, incident: SecurityIncident, action: AutomationAction) -> Dict[str, Any]:
        """Collect forensic evidence from affected assets."""
        
        forensics_collected = []
        
        for asset_id in incident.affected_assets:
            asset = self.db.query(Asset).filter(Asset.id == asset_id).first()
            if asset:
                # In a real implementation, this would trigger forensic collection tools
                forensics_collected.append({
                    "asset_id": asset_id,
                    "asset_name": asset.name,
                    "collection_type": "memory_dump" if asset.asset_type.value == "device" else "logs",
                    "collection_time": datetime.utcnow().isoformat(),
                    "status": "collected",
                    "evidence_location": f"/forensics/{incident.id}/{asset_id}"
                })
                
                logger.info(f"Collected forensics from asset {asset.name} ({asset_id})")
        
        return {
            "action": "collect_forensics",
            "forensics": forensics_collected,
            "total_collected": len(forensics_collected)
        }
    
    async def _update_firewall(self, incident: SecurityIncident, action: AutomationAction) -> Dict[str, Any]:
        """Update firewall rules based on incident."""
        
        firewall_updates = []
        
        # Extract IOCs that can be blocked
        for indicator in incident.indicators:
            if indicator.get("type") in ["ip_address", "domain", "url"]:
                # In a real implementation, this would call firewall management APIs
                firewall_updates.append({
                    "rule_type": "block",
                    "target": indicator.get("value"),
                    "target_type": indicator.get("type"),
                    "rule_id": f"AUTO_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                    "created_time": datetime.utcnow().isoformat()
                })
        
        logger.info(f"Updated firewall with {len(firewall_updates)} new rules for incident {incident.id}")
        
        return {
            "action": "update_firewall",
            "rules_added": firewall_updates,
            "total_rules": len(firewall_updates)
        }
    
    def _select_playbook(self, incident: SecurityIncident) -> str:
        """Select appropriate playbook based on incident characteristics."""
        
        # Simple playbook selection logic
        if any(ind.get("type") == "malware" for ind in incident.indicators):
            return "malware_response_playbook"
        elif any(ind.get("type") == "lateral_movement" for ind in incident.indicators):
            return "lateral_movement_response_playbook"
        elif incident.severity == IncidentSeverity.CRITICAL:
            return "critical_incident_response_playbook"
        else:
            return "general_incident_response_playbook"
    
    async def get_incident_status(self, incident_id: str) -> Optional[SecurityIncident]:
        """Get current status of an incident."""
        return self.active_incidents.get(incident_id)
    
    async def update_incident_status(self, incident_id: str, status: IncidentStatus, 
                                   analyst: Optional[str] = None) -> bool:
        """Update incident status."""
        if incident_id in self.active_incidents:
            incident = self.active_incidents[incident_id]
            incident.status = status
            if analyst:
                incident.assigned_analyst = analyst
            if status in [IncidentStatus.RESOLVED, IncidentStatus.CLOSED]:
                incident.resolution_time = datetime.utcnow()
            
            logger.info(f"Updated incident {incident_id} status to {status.value}")
            return True
        return False
    
    def add_automation_rule(self, rule: AutomationRule):
        """Add a new automation rule."""
        self.automation_rules.append(rule)
        logger.info(f"Added automation rule: {rule.name}")
    
    def disable_automation_rule(self, rule_id: str) -> bool:
        """Disable an automation rule."""
        for rule in self.automation_rules:
            if rule.id == rule_id:
                rule.enabled = False
                logger.info(f"Disabled automation rule: {rule.name}")
                return True
        return False


# siem_integration/connectors.py
class SIEMConnector:
    """Base class for SIEM integrations."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.incident_engine = None
    
    def set_incident_engine(self, engine: IncidentResponseEngine):
        """Set the incident response engine."""
        self.incident_engine = engine
    
    async def process_alert(self, alert_data: Dict[str, Any]) -> Optional[SecurityIncident]:
        """Process an alert from the SIEM and create an incident if needed."""
        raise NotImplementedError


class SplunkConnector(SIEMConnector):
    """Splunk SIEM connector."""
    
    async def process_alert(self, alert_data: Dict[str, Any]) -> Optional[SecurityIncident]:
        """Process Splunk alert."""
        
        # Map Splunk alert to our incident format
        severity_mapping = {
            "critical": IncidentSeverity.CRITICAL,
            "high": IncidentSeverity.HIGH,
            "medium": IncidentSeverity.MEDIUM,
            "low": IncidentSeverity.LOW,
            "info": IncidentSeverity.INFORMATIONAL
        }
        
        event_data = {
            "title": alert_data.get("search_name", "Splunk Alert"),
            "description": alert_data.get("description", ""),
            "severity": severity_mapping.get(alert_data.get("urgency", "medium").lower(), IncidentSeverity.MEDIUM).value,
            "source": "splunk",
            "timestamp": alert_data.get("trigger_time", datetime.utcnow().isoformat()),
            "affected_assets": self._extract_assets_from_splunk_alert(alert_data),
            "indicators": self._extract_indicators_from_splunk_alert(alert_data),
            "metadata": {
                "splunk_sid": alert_data.get("sid"),
                "search_name": alert_data.get("search_name"),
                "app": alert_data.get("app")
            }
        }
        
        if self.incident_engine:
            return await self.incident_engine.process_security_event(event_data)
        
        return None
    
    def _extract_assets_from_splunk_alert(self, alert_data: Dict[str, Any]) -> List[str]:
        """Extract asset identifiers from Splunk alert."""
        assets = []
        
        # Look for common asset identifiers in Splunk results
        results = alert_data.get("results", [])
        for result in results:
            # Extract host names
            if "host" in result:
                assets.append(self._resolve_asset_id(result["host"], "hostname"))
            
            # Extract IP addresses
            if "src_ip" in result:
                assets.append(self._resolve_asset_id(result["src_ip"], "ip_address"))
            if "dest_ip" in result:
                assets.append(self._resolve_asset_id(result["dest_ip"], "ip_address"))
            
            # Extract user accounts
            if "user" in result:
                assets.append(self._resolve_asset_id(result["user"], "username"))
        
        return list(set(filter(None, assets)))  # Remove duplicates and None values
    
    def _extract_indicators_from_splunk_alert(self, alert_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract indicators of compromise from Splunk alert."""
        indicators = []
        
        results = alert_data.get("results", [])
        for result in results:
            # Extract various IOC types
            if "malware_family" in result:
                indicators.append({
                    "type": "malware",
                    "value": result["malware_family"],
                    "confidence": "high"
                })
            
            if "signature" in result:
                indicators.append({
                    "type": "signature",
                    "value": result["signature"],
                    "confidence": "medium"
                })
            
            # Add more IOC extraction logic as needed
        
        return indicators
    
    def _resolve_asset_id(self, identifier: str, identifier_type: str) -> Optional[str]:
        """Resolve asset ID from identifier."""
        # This would query the database to find the asset ID
        # For now, return a placeholder
        return f"asset_{identifier_type}_{identifier}"


class QRadarConnector(SIEMConnector):
    """IBM QRadar SIEM connector."""
    
    async def process_alert(self, alert_data: Dict[str, Any]) -> Optional[SecurityIncident]:
        """Process QRadar offense."""
        
        # Map QRadar offense to incident format
        severity_mapping = {
            1: IncidentSeverity.LOW,
            2: IncidentSeverity.LOW,
            3: IncidentSeverity.MEDIUM,
            4: IncidentSeverity.MEDIUM,
            5: IncidentSeverity.HIGH,
            6: IncidentSeverity.HIGH,
            7: IncidentSeverity.CRITICAL,
            8: IncidentSeverity.CRITICAL,
            9: IncidentSeverity.CRITICAL,
            10: IncidentSeverity.CRITICAL
        }
        
        event_data = {
            "title": f"QRadar Offense: {alert_data.get('description', 'Unknown')}",
            "description": alert_data.get("description", ""),
            "severity": severity_mapping.get(alert_data.get("magnitude", 5), IncidentSeverity.MEDIUM).value,
            "source": "qradar",
            "timestamp": datetime.fromtimestamp(alert_data.get("start_time", 0) / 1000).isoformat(),
            "affected_assets": self._extract_assets_from_qradar_offense(alert_data),
            "indicators": self._extract_indicators_from_qradar_offense(alert_data),
            "metadata": {
                "offense_id": alert_data.get("id"),
                "offense_type": alert_data.get("offense_type"),
                "event_count": alert_data.get("event_count"),
                "source_network": alert_data.get("source_network")
            }
        }
        
        if self.incident_engine:
            return await self.incident_engine.process_security_event(event_data)
        
        return None
    
    def _extract_assets_from_qradar_offense(self, alert_data: Dict[str, Any]) -> List[str]:
        """Extract assets from QRadar offense."""
        assets = []
        
        # Extract source and destination IPs
        if "source_address_ids" in alert_data:
            for ip_id in alert_data["source_address_ids"]:
                # In real implementation, resolve IP ID to actual IP
                assets.append(self._resolve_asset_id(f"ip_{ip_id}", "ip_address"))
        
        if "local_destination_address_ids" in alert_data:
            for ip_id in alert_data["local_destination_address_ids"]:
                assets.append(self._resolve_asset_id(f"ip_{ip_id}", "ip_address"))
        
        return list(set(filter(None, assets)))
    
    def _extract_indicators_from_qradar_offense(self, alert_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract indicators from QRadar offense."""
        indicators = []
        
        offense_type = alert_data.get("offense_type")
        if offense_type:
            indicators.append({
                "type": "offense_type",
                "value": offense_type,
                "confidence": "high"
            })
        
        return indicators


# API endpoints for incident response
@app.post("/incident-response/process-event", response_model=Dict[str, Any])
async def process_security_event(
    event_data: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """Process a security event and trigger automated response."""
    try:
        engine = IncidentResponseEngine(db)
        incident = await engine.process_security_event(event_data)
        
        return {
            "incident_id": incident.id,
            "status": incident.status.value,
            "severity": incident.severity.value,
            "affected_assets": len(incident.affected_assets),
            "automated_actions": len(incident.automated_actions),
            "created_at": incident.detection_time.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error processing security event: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Event processing failed: {str(e)}")


@app.get("/incident-response/incidents/{incident_id}", response_model=Dict[str, Any])
async def get_incident_details(
    incident_id: str,
    db: Session = Depends(get_db)
):
    """Get detailed information about a specific incident."""
    try:
        engine = IncidentResponseEngine(db)
        incident = await engine.get_incident_status(incident_id)
        
        if not incident:
            raise HTTPException(status_code=404, detail="Incident not found")
        
        return asdict(incident)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving incident: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve incident: {str(e)}")


@app.put("/incident-response/incidents/{incident_id}/status")
async def update_incident_status(
    incident_id: str,
    status: str = Body(..., regex="^(open|investigating|contained|resolved|closed)$"),
    analyst: Optional[str] = Body(None),
    db: Session = Depends(get_db)
):
    """Update the status of an incident."""
    try:
        engine = IncidentResponseEngine(db)
        status_enum = IncidentStatus(status)
        
        success = await engine.update_incident_status(incident_id, status_enum, analyst)
        
        if not success:
            raise HTTPException(status_code=404, detail="Incident not found")
        
        return {"message": "Incident status updated successfully", "new_status": status}
        
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid status value")
    except Exception as e:
        logger.error(f"Error updating incident status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Status update failed: {str(e)}")


@app.get("/incident-response/automation-rules", response_model=List[Dict[str, Any]])
async def list_automation_rules(db: Session = Depends(get_db)):
    """List all automation rules."""
    try:
        engine = IncidentResponseEngine(db)
        
        rules_data = []
        for rule in engine.automation_rules:
            rules_data.append({
                "id": rule.id,
                "name": rule.name,
                "description": rule.description,
                "enabled": rule.enabled,
                "severity_threshold": rule.severity_threshold.value,
                "actions": [action.value for action in rule.actions],
                "cooldown_minutes": rule.cooldown_minutes,
                "max_executions_per_hour": rule.max_executions_per_hour,
                "requires_approval": rule.requires_approval
            })
        
        return rules_data
        
    except Exception as e:
        logger.error(f"Error listing automation rules: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to list rules: {str(e)}")


@app.post("/incident-response/automation-rules/{rule_id}/disable")
async def disable_automation_rule(
    rule_id: str,
    db: Session = Depends(get_db)
):
    """Disable a specific automation rule."""
    try:
        engine = IncidentResponseEngine(db)
        success = engine.disable_automation_rule(rule_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Automation rule not found")
        
        return {"message": "Automation rule disabled successfully"}
        
    except Exception as e:
        logger.error(f"Error disabling automation rule: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to disable rule: {str(e)}")


@app.get("/incident-response/metrics", response_model=Dict[str, Any])
async def get_incident_response_metrics(
    days_back: int = Query(default=30, ge=1, le=365),
    db: Session = Depends(get_db)
):
    """Get incident response metrics and statistics."""
    try:
        engine = IncidentResponseEngine(db)
        
        # Calculate metrics from execution history
        recent_executions = [
            exec_record for exec_record in engine.execution_history
            if datetime.fromisoformat(exec_record["executed_at"]) > 
               datetime.utcnow() - timedelta(days=days_back)
        ]
        
        successful_executions = [ex for ex in recent_executions if ex["success"]]
        failed_executions = [ex for ex in recent_executions if not ex["success"]]
        
        # Rule execution frequency
        rule_usage = {}
        for execution in recent_executions:
            rule_id = execution["rule_id"]
            rule_usage[rule_id] = rule_usage.get(rule_id, 0) + 1
        
        # Active incidents
        active_incidents = len([
            incident for incident in engine.active_incidents.values()
            if incident.status not in [IncidentStatus.RESOLVED, IncidentStatus.CLOSED]
        ])
        
        return {
            "period_days": days_back,
            "automation_metrics": {
                "total_executions": len(recent_executions),
                "successful_executions": len(successful_executions),
                "failed_executions": len(failed_executions),
                "success_rate": len(successful_executions) / len(recent_executions) * 100 if recent_executions else 0
            },
            "incident_metrics": {
                "active_incidents": active_incidents,
                "total_incidents": len(engine.active_incidents),
                "avg_resolution_time_hours": 24.5  # Placeholder - would calculate from real data
            },
            "rule_usage": rule_usage,
            "top_triggered_rules": sorted(rule_usage.items(), key=lambda x: x[1], reverse=True)[:5]
        }
        
    except Exception as e:
        logger.error(f"Error generating incident response metrics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Metrics generation failed: {str(e)}")


# Webhook endpoints for SIEM integration
@app.post("/webhooks/splunk-alert")
async def handle_splunk_alert(
    alert_data: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """Handle incoming Splunk alert via webhook."""
    try:
        engine = IncidentResponseEngine(db)
        connector = SplunkConnector({})
        connector.set_incident_engine(engine)
        
        incident = await connector.process_alert(alert_data)
        
        if incident:
            return {
                "status": "processed",
                "incident_id": incident.id,
                "message": "Alert processed and incident created"
            }
        else:
            return {
                "status": "ignored",
                "message": "Alert did not meet criteria for incident creation"
            }
            
    except Exception as e:
        logger.error(f"Error processing Splunk alert: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Alert processing failed: {str(e)}")


@app.post("/webhooks/qradar-offense")
async def handle_qradar_offense(
    offense_data: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """Handle incoming QRadar offense via webhook."""
    try:
        engine = IncidentResponseEngine(db)
        connector = QRadarConnector({})
        connector.set_incident_engine(engine)
        
        incident = await connector.process_alert(offense_data)
        
        if incident:
            return {
                "status": "processed",
                "incident_id": incident.id,
                "message": "Offense processed and incident created"
            }
        else:
            return {
                "status": "ignored",
                "message": "Offense did not meet criteria for incident creation"
            }
            
    except Exception as e:
        logger.error(f"Error processing QRadar offense: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Offense processing failed: {str(e)}")