"""
GDPR Compliance Service for Blast-Radius Security Tool.

This service implements comprehensive GDPR compliance features including:
- Data subject rights (access, rectification, erasure, portability)
- Consent management
- Data processing records
- Privacy impact assessments
- Data breach notification
- Data protection by design and by default
"""

import json
import hashlib
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from dataclasses import dataclass, asdict
from uuid import UUID, uuid4

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.core.config import settings
from app.core.logging import get_logger
from app.core.encryption import get_encryption_service
from app.db.models.user import User
from app.db.models.asset import Asset
from app.services.notification_service import NotificationService

logger = get_logger(__name__)


class LegalBasisType(Enum):
    """GDPR legal basis for processing personal data."""
    CONSENT = "consent"
    CONTRACT = "contract"
    LEGAL_OBLIGATION = "legal_obligation"
    VITAL_INTERESTS = "vital_interests"
    PUBLIC_TASK = "public_task"
    LEGITIMATE_INTERESTS = "legitimate_interests"


class DataSubjectRightType(Enum):
    """GDPR data subject rights."""
    ACCESS = "access"  # Article 15
    RECTIFICATION = "rectification"  # Article 16
    ERASURE = "erasure"  # Article 17 (Right to be forgotten)
    RESTRICT_PROCESSING = "restrict_processing"  # Article 18
    DATA_PORTABILITY = "data_portability"  # Article 20
    OBJECT = "object"  # Article 21
    WITHDRAW_CONSENT = "withdraw_consent"  # Article 7(3)


class ConsentStatus(Enum):
    """Consent status for data processing."""
    GIVEN = "given"
    WITHDRAWN = "withdrawn"
    EXPIRED = "expired"
    PENDING = "pending"


class ProcessingPurpose(Enum):
    """Purposes for processing personal data."""
    SECURITY_MONITORING = "security_monitoring"
    THREAT_DETECTION = "threat_detection"
    INCIDENT_RESPONSE = "incident_response"
    COMPLIANCE_REPORTING = "compliance_reporting"
    SYSTEM_ADMINISTRATION = "system_administration"
    USER_AUTHENTICATION = "user_authentication"
    AUDIT_LOGGING = "audit_logging"


@dataclass
class ConsentRecord:
    """Record of user consent for data processing."""
    consent_id: str
    user_id: str
    purpose: ProcessingPurpose
    legal_basis: LegalBasisType
    status: ConsentStatus
    given_at: Optional[datetime]
    withdrawn_at: Optional[datetime]
    expires_at: Optional[datetime]
    consent_text: str
    version: str
    ip_address: str
    user_agent: str
    metadata: Dict[str, Any]


@dataclass
class DataSubjectRequest:
    """Data subject request under GDPR."""
    request_id: str
    user_id: str
    request_type: DataSubjectRightType
    status: str  # pending, in_progress, completed, rejected
    submitted_at: datetime
    completed_at: Optional[datetime]
    description: str
    verification_method: str
    verification_completed: bool
    response_data: Optional[Dict[str, Any]]
    rejection_reason: Optional[str]
    metadata: Dict[str, Any]


@dataclass
class ProcessingActivity:
    """Record of processing activities (Article 30)."""
    activity_id: str
    name: str
    description: str
    controller: str
    processor: Optional[str]
    data_categories: List[str]
    data_subjects: List[str]
    purposes: List[ProcessingPurpose]
    legal_basis: LegalBasisType
    recipients: List[str]
    third_country_transfers: List[str]
    retention_period: str
    security_measures: List[str]
    created_at: datetime
    updated_at: datetime


@dataclass
class DataBreach:
    """Data breach record for GDPR notification."""
    breach_id: str
    detected_at: datetime
    reported_at: Optional[datetime]
    description: str
    affected_data_subjects: int
    data_categories_affected: List[str]
    likely_consequences: str
    measures_taken: List[str]
    measures_proposed: List[str]
    notification_required: bool
    authority_notified: bool
    subjects_notified: bool
    risk_level: str  # low, medium, high
    metadata: Dict[str, Any]


class GDPRComplianceService:
    """Service for GDPR compliance management."""
    
    def __init__(self, db: Session, notification_service: NotificationService):
        self.db = db
        self.notification_service = notification_service
        self.encryption_service = get_encryption_service()
        
        # In-memory storage for demonstration (would use database in production)
        self.consent_records: Dict[str, ConsentRecord] = {}
        self.data_subject_requests: Dict[str, DataSubjectRequest] = {}
        self.processing_activities: Dict[str, ProcessingActivity] = {}
        self.data_breaches: Dict[str, DataBreach] = {}
        
        # Initialize default processing activities
        self._initialize_processing_activities()
    
    async def record_consent(
        self,
        user_id: str,
        purpose: ProcessingPurpose,
        legal_basis: LegalBasisType,
        consent_text: str,
        version: str,
        ip_address: str,
        user_agent: str,
        expires_in_days: Optional[int] = None
    ) -> ConsentRecord:
        """
        Record user consent for data processing.
        
        Args:
            user_id: ID of the user giving consent
            purpose: Purpose for data processing
            legal_basis: Legal basis for processing
            consent_text: Text of consent given to user
            version: Version of consent text
            ip_address: IP address of user
            user_agent: User agent string
            expires_in_days: Optional expiration period
            
        Returns:
            ConsentRecord
        """
        consent_id = str(uuid4())
        now = datetime.utcnow()
        
        expires_at = None
        if expires_in_days:
            expires_at = now + timedelta(days=expires_in_days)
        
        consent_record = ConsentRecord(
            consent_id=consent_id,
            user_id=user_id,
            purpose=purpose,
            legal_basis=legal_basis,
            status=ConsentStatus.GIVEN,
            given_at=now,
            withdrawn_at=None,
            expires_at=expires_at,
            consent_text=consent_text,
            version=version,
            ip_address=ip_address,
            user_agent=user_agent,
            metadata={}
        )
        
        self.consent_records[consent_id] = consent_record
        
        logger.info(f"Recorded consent for user {user_id}, purpose {purpose.value}")
        
        return consent_record
    
    async def withdraw_consent(
        self,
        user_id: str,
        purpose: ProcessingPurpose,
        ip_address: str,
        user_agent: str
    ) -> bool:
        """
        Withdraw user consent for data processing.
        
        Args:
            user_id: ID of the user withdrawing consent
            purpose: Purpose to withdraw consent for
            ip_address: IP address of user
            user_agent: User agent string
            
        Returns:
            True if consent was withdrawn, False if not found
        """
        # Find active consent for user and purpose
        for consent_record in self.consent_records.values():
            if (consent_record.user_id == user_id and 
                consent_record.purpose == purpose and 
                consent_record.status == ConsentStatus.GIVEN):
                
                consent_record.status = ConsentStatus.WITHDRAWN
                consent_record.withdrawn_at = datetime.utcnow()
                consent_record.metadata.update({
                    "withdrawal_ip": ip_address,
                    "withdrawal_user_agent": user_agent
                })
                
                logger.info(f"Withdrew consent for user {user_id}, purpose {purpose.value}")
                
                # Trigger data processing restriction
                await self._restrict_processing_for_purpose(user_id, purpose)
                
                return True
        
        return False
    
    async def submit_data_subject_request(
        self,
        user_id: str,
        request_type: DataSubjectRightType,
        description: str,
        verification_method: str = "email"
    ) -> DataSubjectRequest:
        """
        Submit a data subject request under GDPR.
        
        Args:
            user_id: ID of the data subject
            request_type: Type of request
            description: Description of the request
            verification_method: Method to verify identity
            
        Returns:
            DataSubjectRequest
        """
        request_id = str(uuid4())
        
        request = DataSubjectRequest(
            request_id=request_id,
            user_id=user_id,
            request_type=request_type,
            status="pending",
            submitted_at=datetime.utcnow(),
            completed_at=None,
            description=description,
            verification_method=verification_method,
            verification_completed=False,
            response_data=None,
            rejection_reason=None,
            metadata={}
        )
        
        self.data_subject_requests[request_id] = request
        
        # Send verification email/SMS
        await self._send_verification_request(request)
        
        logger.info(f"Submitted data subject request {request_id} for user {user_id}")
        
        return request
    
    async def process_data_subject_request(self, request_id: str) -> Dict[str, Any]:
        """
        Process a verified data subject request.
        
        Args:
            request_id: ID of the request to process
            
        Returns:
            Processing result
        """
        if request_id not in self.data_subject_requests:
            raise ValueError(f"Request {request_id} not found")
        
        request = self.data_subject_requests[request_id]
        
        if not request.verification_completed:
            raise ValueError("Request verification not completed")
        
        if request.status != "pending":
            raise ValueError(f"Request already processed with status: {request.status}")
        
        request.status = "in_progress"
        
        try:
            if request.request_type == DataSubjectRightType.ACCESS:
                response_data = await self._process_access_request(request.user_id)
            elif request.request_type == DataSubjectRightType.RECTIFICATION:
                response_data = await self._process_rectification_request(request)
            elif request.request_type == DataSubjectRightType.ERASURE:
                response_data = await self._process_erasure_request(request.user_id)
            elif request.request_type == DataSubjectRightType.DATA_PORTABILITY:
                response_data = await self._process_portability_request(request.user_id)
            elif request.request_type == DataSubjectRightType.RESTRICT_PROCESSING:
                response_data = await self._process_restriction_request(request.user_id)
            elif request.request_type == DataSubjectRightType.OBJECT:
                response_data = await self._process_objection_request(request.user_id)
            else:
                raise ValueError(f"Unsupported request type: {request.request_type}")
            
            request.status = "completed"
            request.completed_at = datetime.utcnow()
            request.response_data = response_data
            
            # Notify user of completion
            await self._notify_request_completion(request)
            
            logger.info(f"Completed data subject request {request_id}")
            
            return {"status": "completed", "data": response_data}
            
        except Exception as e:
            request.status = "rejected"
            request.rejection_reason = str(e)
            
            logger.error(f"Failed to process data subject request {request_id}: {str(e)}")
            
            return {"status": "rejected", "reason": str(e)}
    
    async def _process_access_request(self, user_id: str) -> Dict[str, Any]:
        """Process data access request (Article 15)."""
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise ValueError("User not found")
        
        # Collect all personal data
        personal_data = {
            "user_profile": {
                "id": str(user.id),
                "username": user.username,
                "email": user.email,
                "full_name": user.full_name,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "last_login": user.last_login.isoformat() if user.last_login else None,
                "is_active": user.is_active,
                "roles": [role.name for role in user.roles] if user.roles else []
            },
            "consent_records": [
                asdict(consent) for consent in self.consent_records.values()
                if consent.user_id == user_id
            ],
            "processing_activities": [
                {
                    "activity": activity.name,
                    "purpose": [p.value for p in activity.purposes],
                    "legal_basis": activity.legal_basis.value,
                    "retention_period": activity.retention_period
                }
                for activity in self.processing_activities.values()
            ],
            "audit_logs": await self._get_user_audit_logs(user_id),
            "assets_accessed": await self._get_user_asset_access(user_id)
        }
        
        return personal_data
    
    async def _process_erasure_request(self, user_id: str) -> Dict[str, Any]:
        """Process data erasure request (Article 17 - Right to be forgotten)."""
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise ValueError("User not found")
        
        # Check if erasure is legally possible
        legal_obligations = await self._check_legal_obligations(user_id)
        if legal_obligations:
            raise ValueError(f"Cannot erase data due to legal obligations: {legal_obligations}")
        
        # Perform erasure
        erasure_results = {
            "user_profile_anonymized": False,
            "consent_records_deleted": 0,
            "audit_logs_anonymized": 0,
            "assets_anonymized": 0
        }
        
        # Anonymize user profile (keep for audit purposes but remove PII)
        user.email = f"anonymized_{hashlib.sha256(user.email.encode()).hexdigest()[:8]}@deleted.local"
        user.full_name = "Anonymized User"
        user.is_active = False
        erasure_results["user_profile_anonymized"] = True
        
        # Delete consent records
        consent_count = 0
        for consent_id, consent in list(self.consent_records.items()):
            if consent.user_id == user_id:
                del self.consent_records[consent_id]
                consent_count += 1
        erasure_results["consent_records_deleted"] = consent_count
        
        # Anonymize audit logs (implementation would depend on audit log structure)
        erasure_results["audit_logs_anonymized"] = await self._anonymize_user_audit_logs(user_id)
        
        # Anonymize user's asset access records
        erasure_results["assets_anonymized"] = await self._anonymize_user_asset_access(user_id)
        
        self.db.commit()
        
        logger.info(f"Processed erasure request for user {user_id}")
        
        return erasure_results
    
    async def report_data_breach(
        self,
        description: str,
        affected_data_subjects: int,
        data_categories_affected: List[str],
        likely_consequences: str,
        measures_taken: List[str],
        risk_level: str = "medium"
    ) -> DataBreach:
        """
        Report a data breach for GDPR compliance.
        
        Args:
            description: Description of the breach
            affected_data_subjects: Number of affected individuals
            data_categories_affected: Categories of data affected
            likely_consequences: Likely consequences for individuals
            measures_taken: Measures already taken
            risk_level: Risk level (low, medium, high)
            
        Returns:
            DataBreach record
        """
        breach_id = str(uuid4())
        now = datetime.utcnow()
        
        # Determine notification requirements
        notification_required = (
            risk_level in ["medium", "high"] or 
            affected_data_subjects > 100 or
            "financial" in data_categories_affected or
            "health" in data_categories_affected
        )
        
        breach = DataBreach(
            breach_id=breach_id,
            detected_at=now,
            reported_at=None,
            description=description,
            affected_data_subjects=affected_data_subjects,
            data_categories_affected=data_categories_affected,
            likely_consequences=likely_consequences,
            measures_taken=measures_taken,
            measures_proposed=[],
            notification_required=notification_required,
            authority_notified=False,
            subjects_notified=False,
            risk_level=risk_level,
            metadata={}
        )
        
        self.data_breaches[breach_id] = breach
        
        # If high risk, immediate notification required
        if risk_level == "high":
            await self._notify_supervisory_authority(breach)
            await self._notify_affected_subjects(breach)
        
        logger.warning(f"Data breach reported: {breach_id}")
        
        return breach
    
    async def generate_processing_record(self) -> Dict[str, Any]:
        """Generate Article 30 record of processing activities."""
        return {
            "controller": {
                "name": settings.ORGANIZATION_NAME,
                "contact": settings.DPO_CONTACT,
                "representative": settings.EU_REPRESENTATIVE if hasattr(settings, 'EU_REPRESENTATIVE') else None
            },
            "processing_activities": [
                asdict(activity) for activity in self.processing_activities.values()
            ],
            "generated_at": datetime.utcnow().isoformat(),
            "version": "1.0"
        }
    
    def _initialize_processing_activities(self) -> None:
        """Initialize default processing activities."""
        activities = [
            ProcessingActivity(
                activity_id="security_monitoring",
                name="Security Monitoring",
                description="Monitoring system security and detecting threats",
                controller=settings.ORGANIZATION_NAME,
                processor=None,
                data_categories=["user_identifiers", "system_logs", "access_logs"],
                data_subjects=["employees", "system_users"],
                purposes=[ProcessingPurpose.SECURITY_MONITORING, ProcessingPurpose.THREAT_DETECTION],
                legal_basis=LegalBasisType.LEGITIMATE_INTERESTS,
                recipients=["security_team", "system_administrators"],
                third_country_transfers=[],
                retention_period="2 years",
                security_measures=["encryption", "access_controls", "audit_logging"],
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            ),
            ProcessingActivity(
                activity_id="user_authentication",
                name="User Authentication",
                description="Authenticating users and managing access",
                controller=settings.ORGANIZATION_NAME,
                processor=None,
                data_categories=["credentials", "authentication_logs", "session_data"],
                data_subjects=["employees", "system_users"],
                purposes=[ProcessingPurpose.USER_AUTHENTICATION],
                legal_basis=LegalBasisType.CONTRACT,
                recipients=["authentication_service"],
                third_country_transfers=[],
                retention_period="1 year after account deletion",
                security_measures=["encryption", "mfa", "secure_storage"],
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
        ]
        
        for activity in activities:
            self.processing_activities[activity.activity_id] = activity
    
    # Additional helper methods would be implemented here...
    async def _restrict_processing_for_purpose(self, user_id: str, purpose: ProcessingPurpose) -> None:
        """Restrict processing for specific purpose."""
        pass
    
    async def _send_verification_request(self, request: DataSubjectRequest) -> None:
        """Send verification request to user."""
        pass
    
    async def _notify_request_completion(self, request: DataSubjectRequest) -> None:
        """Notify user of request completion."""
        pass
    
    async def _get_user_audit_logs(self, user_id: str) -> List[Dict[str, Any]]:
        """Get audit logs for user."""
        return []
    
    async def _get_user_asset_access(self, user_id: str) -> List[Dict[str, Any]]:
        """Get user's asset access records."""
        return []
    
    async def _check_legal_obligations(self, user_id: str) -> List[str]:
        """Check if there are legal obligations preventing erasure."""
        return []
    
    async def _anonymize_user_audit_logs(self, user_id: str) -> int:
        """Anonymize user's audit logs."""
        return 0
    
    async def _anonymize_user_asset_access(self, user_id: str) -> int:
        """Anonymize user's asset access records."""
        return 0
    
    async def _notify_supervisory_authority(self, breach: DataBreach) -> None:
        """Notify supervisory authority of data breach."""
        pass
    
    async def _notify_affected_subjects(self, breach: DataBreach) -> None:
        """Notify affected data subjects of breach."""
        pass
