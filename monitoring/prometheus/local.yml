# Prometheus Configuration for Blast-Radius Security Tool (Local Development)

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    environment: 'local'
    project: 'blast-radius'

rule_files:
  - "blast-radius-rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Blast-Radius Backend API
  - job_name: 'blast-radius-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s
    honor_labels: true
    params:
      format: ['prometheus']
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'blast-radius-backend'

  # Blast-Radius Frontend (if metrics enabled)
  - job_name: 'blast-radius-frontend'
    static_configs:
      - targets: ['frontend:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # PostgreSQL Database Metrics
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 15s
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'blast-radius-postgres'

  # Redis Cache Metrics
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 15s
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'blast-radius-redis'

  # Node Exporter for System Metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'blast-radius-host'

  # Prometheus Self-Monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s

  # Grafana Metrics
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Alertmanager Metrics
  - job_name: 'alertmanager'
    static_configs:
      - targets: ['alertmanager:9093']
    scrape_interval: 30s

  # Celery Worker Metrics (if enabled)
  - job_name: 'celery-worker'
    static_configs:
      - targets: ['celery-worker:9540']
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s

  # Docker Container Metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    metrics_path: '/metrics'

# Remote write configuration for long-term storage (optional)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint"
#     basic_auth:
#       username: "username"
#       password: "password"

# Storage configuration
storage:
  tsdb:
    retention.time: 15d
    retention.size: 10GB
    wal-compression: true
