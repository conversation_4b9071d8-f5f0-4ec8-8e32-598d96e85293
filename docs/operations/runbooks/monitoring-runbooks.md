# Monitoring Runbooks - Blast-Radius Security Tool

## Overview

This document provides operational runbooks for monitoring the Blast-Radius Security Tool, including alert response procedures, performance monitoring, and system health checks.

## Alert Response Procedures

### Critical Alerts (P0)

#### BlastRadiusAPIDown
**Alert Description**: The Blast-Radius API is completely unavailable

**Immediate Actions (0-5 minutes)**:
```bash
# 1. Verify alert accuracy
curl -f https://blast-radius.com/health
kubectl get pods -n blast-radius -l app=blast-radius-backend

# 2. Check service status
kubectl get services -n blast-radius
kubectl describe service blast-radius-backend -n blast-radius

# 3. Check ingress and load balancer
kubectl get ingress -n blast-radius
aws elbv2 describe-target-health --target-group-arn $TARGET_GROUP_ARN
```

**Investigation Steps (5-15 minutes)**:
```bash
# 1. Check pod logs
kubectl logs -f deployment/blast-radius-backend -n blast-radius --tail=100

# 2. Check recent events
kubectl get events -n blast-radius --sort-by='.lastTimestamp' | tail -20

# 3. Check resource usage
kubectl top pods -n blast-radius
kubectl describe nodes

# 4. Check database connectivity
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  pg_isready -h $DATABASE_HOST -p 5432
```

**Resolution Actions**:
```bash
# If pods are down - restart deployment
kubectl rollout restart deployment/blast-radius-backend -n blast-radius

# If database issues - check RDS status
aws rds describe-db-instances --db-instance-identifier blast-radius-database

# If load balancer issues - check ALB configuration
aws elbv2 describe-load-balancers --names blast-radius-alb

# Emergency rollback if needed
kubectl rollout undo deployment/blast-radius-backend -n blast-radius
```

**Communication Template**:
```
🚨 INCIDENT: Blast-Radius API Down
Status: Investigating/Identified/Monitoring/Resolved
Impact: Complete service unavailability
ETA: [Estimated resolution time]
Actions: [Current actions being taken]
Next Update: [Time of next update]
```

#### SecurityThreatSpike
**Alert Description**: Unusual spike in security threats detected

**Immediate Actions (0-5 minutes)**:
```bash
# 1. Check threat dashboard
kubectl port-forward service/grafana 3000:3000 -n blast-radius
# Navigate to Security Dashboard

# 2. Check recent security events
kubectl logs -f deployment/blast-radius-backend -n blast-radius | grep -i "security\|threat\|attack"

# 3. Check source IPs and patterns
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  python manage.py analyze-security-events --last-hour
```

**Investigation Steps (5-30 minutes)**:
```bash
# 1. Analyze threat patterns
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  python manage.py security-report --detailed

# 2. Check WAF logs
aws logs filter-log-events \
  --log-group-name /aws/wafv2/blast-radius \
  --start-time $(date -d '1 hour ago' +%s)000

# 3. Review access logs
kubectl logs -f deployment/blast-radius-backend -n blast-radius | \
  grep -E "POST|PUT|DELETE" | tail -50
```

**Resolution Actions**:
```bash
# If attack detected - implement blocking
kubectl apply -f security/emergency-block-ips.yaml -n blast-radius

# If false positive - adjust detection thresholds
kubectl patch configmap security-config -n blast-radius -p \
  '{"data":{"THREAT_THRESHOLD":"10"}}'

# If legitimate traffic spike - scale up
kubectl scale deployment blast-radius-backend --replicas=6 -n blast-radius
```

### High Priority Alerts (P1)

#### HighMemoryUsage
**Alert Description**: Memory usage above 85% threshold

**Investigation Steps**:
```bash
# 1. Check current memory usage
kubectl top pods -n blast-radius --sort-by=memory

# 2. Check memory limits
kubectl describe pods -n blast-radius | grep -A 5 "Limits\|Requests"

# 3. Identify memory-intensive processes
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  ps aux --sort=-%mem | head -10

# 4. Check for memory leaks
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  python -c "import psutil; print(f'Memory: {psutil.virtual_memory()}')"
```

**Resolution Actions**:
```bash
# Immediate relief - restart high-memory pods
kubectl delete pod $(kubectl get pods -n blast-radius --sort-by=.status.containerStatuses[0].restartCount | tail -1 | awk '{print $1}') -n blast-radius

# Scale horizontally
kubectl scale deployment blast-radius-backend --replicas=5 -n blast-radius

# Increase memory limits if needed
kubectl patch deployment blast-radius-backend -n blast-radius -p \
  '{"spec":{"template":{"spec":{"containers":[{"name":"blast-radius-backend","resources":{"limits":{"memory":"4Gi"}}}]}}}}'
```

#### DatabaseConnectionsHigh
**Alert Description**: Database connection count approaching limit

**Investigation Steps**:
```bash
# 1. Check current connections
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT count(*) FROM pg_stat_activity;"

# 2. Check connection sources
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT client_addr, count(*) FROM pg_stat_activity GROUP BY client_addr;"

# 3. Check long-running queries
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT pid, now() - pg_stat_activity.query_start AS duration, query FROM pg_stat_activity WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes';"
```

**Resolution Actions**:
```bash
# Kill long-running queries
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE (now() - pg_stat_activity.query_start) > interval '10 minutes';"

# Restart application to reset connections
kubectl rollout restart deployment/blast-radius-backend -n blast-radius

# Scale down if necessary
kubectl scale deployment blast-radius-backend --replicas=2 -n blast-radius
```

## Performance Monitoring Procedures

### Daily Performance Checks

#### Morning Health Check (9:00 AM)
```bash
#!/bin/bash
# scripts/daily-health-check.sh

echo "=== Daily Health Check - $(date) ==="

# 1. Check pod status
echo "Pod Status:"
kubectl get pods -n blast-radius

# 2. Check resource usage
echo "Resource Usage:"
kubectl top nodes
kubectl top pods -n blast-radius

# 3. Check service endpoints
echo "Service Health:"
curl -s https://blast-radius.com/health | jq .
curl -s https://blast-radius.com/api/health | jq .

# 4. Check database performance
echo "Database Performance:"
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT schemaname,tablename,n_tup_ins,n_tup_upd,n_tup_del FROM pg_stat_user_tables ORDER BY n_tup_ins DESC LIMIT 5;"

# 5. Check error rates
echo "Error Rates (last hour):"
kubectl logs deployment/blast-radius-backend -n blast-radius --since=1h | \
  grep -i error | wc -l

# 6. Generate summary report
echo "Summary: $(date)" >> /var/log/daily-health-check.log
```

#### Weekly Performance Review (Monday 10:00 AM)
```bash
#!/bin/bash
# scripts/weekly-performance-review.sh

echo "=== Weekly Performance Review - $(date) ==="

# 1. Generate performance metrics
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  python manage.py generate-performance-report --days=7

# 2. Check database growth
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT pg_size_pretty(pg_database_size('blast_radius'));"

# 3. Review slow queries
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"

# 4. Check cache hit rates
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  redis-cli -h $REDIS_HOST info stats | grep hit_rate

# 5. Review capacity planning
kubectl describe nodes | grep -A 5 "Allocated resources"
```

### Performance Optimization Procedures

#### Database Optimization
```bash
# 1. Update table statistics
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "ANALYZE;"

# 2. Reindex tables if needed
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "REINDEX DATABASE blast_radius;"

# 3. Check for missing indexes
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  python manage.py check-missing-indexes

# 4. Vacuum tables
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "VACUUM ANALYZE;"
```

#### Cache Optimization
```bash
# 1. Check cache memory usage
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  redis-cli -h $REDIS_HOST info memory

# 2. Clear expired keys
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  redis-cli -h $REDIS_HOST --scan --pattern "*:expired:*" | xargs redis-cli -h $REDIS_HOST del

# 3. Optimize cache configuration
kubectl patch configmap redis-config -n blast-radius -p \
  '{"data":{"maxmemory-policy":"allkeys-lru"}}'

# 4. Monitor cache hit rates
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  redis-cli -h $REDIS_HOST info stats | grep -E "keyspace_hits|keyspace_misses"
```

## System Health Monitoring

### Automated Health Checks

#### Kubernetes Health Check
```bash
#!/bin/bash
# scripts/k8s-health-check.sh

# Check cluster health
kubectl cluster-info
kubectl get componentstatuses

# Check node health
kubectl get nodes -o wide
kubectl describe nodes | grep -E "Ready|OutOfDisk|MemoryPressure|DiskPressure"

# Check critical system pods
kubectl get pods -n kube-system | grep -E "coredns|aws-node|kube-proxy"

# Check resource usage
kubectl top nodes
kubectl describe nodes | grep -A 5 "Allocated resources"
```

#### Application Health Check
```bash
#!/bin/bash
# scripts/app-health-check.sh

# Check application pods
kubectl get pods -n blast-radius -o wide

# Check service discovery
kubectl get endpoints -n blast-radius

# Test application endpoints
curl -f https://blast-radius.com/health
curl -f https://blast-radius.com/api/health
curl -f https://blast-radius.com/api/v1/assets?limit=1

# Check application metrics
curl -s https://blast-radius.com/metrics | grep -E "http_requests_total|response_time"
```

### Monitoring Dashboard Procedures

#### Grafana Dashboard Management
```bash
# 1. Import new dashboards
kubectl exec -it deployment/grafana -n blast-radius -- \
  grafana-cli admin import-dashboard /var/lib/grafana/dashboards/new-dashboard.json

# 2. Update dashboard permissions
kubectl exec -it deployment/grafana -n blast-radius -- \
  curl -X POST http://admin:$GRAFANA_PASSWORD@localhost:3000/api/dashboards/db \
  -H "Content-Type: application/json" \
  -d @dashboard-config.json

# 3. Export dashboard backup
kubectl exec -it deployment/grafana -n blast-radius -- \
  curl -X GET http://admin:$GRAFANA_PASSWORD@localhost:3000/api/dashboards/uid/$DASHBOARD_UID \
  > dashboard-backup.json

# 4. Set up dashboard alerts
kubectl exec -it deployment/grafana -n blast-radius -- \
  curl -X POST http://admin:$GRAFANA_PASSWORD@localhost:3000/api/alerts \
  -H "Content-Type: application/json" \
  -d @alert-config.json
```

#### Prometheus Configuration Management
```bash
# 1. Reload Prometheus configuration
kubectl exec -it deployment/prometheus -n blast-radius -- \
  curl -X POST http://localhost:9090/-/reload

# 2. Check configuration validity
kubectl exec -it deployment/prometheus -n blast-radius -- \
  promtool check config /etc/prometheus/prometheus.yml

# 3. Add new scrape targets
kubectl patch configmap prometheus-config -n blast-radius -p \
  '{"data":{"prometheus.yml":"$(cat monitoring/prometheus/updated-config.yml | base64 -w 0)"}}'

# 4. Check target status
kubectl port-forward service/prometheus 9090:9090 -n blast-radius
# Visit http://localhost:9090/targets
```

## Log Management Procedures

### Log Analysis
```bash
# 1. Search for errors in the last hour
kubectl logs deployment/blast-radius-backend -n blast-radius --since=1h | \
  grep -i "error\|exception\|fail" | tail -20

# 2. Analyze request patterns
kubectl logs deployment/blast-radius-backend -n blast-radius --since=1h | \
  grep "POST\|PUT\|DELETE" | awk '{print $1}' | sort | uniq -c | sort -nr

# 3. Check security events
kubectl logs deployment/blast-radius-backend -n blast-radius --since=1h | \
  grep -i "security\|auth\|login\|unauthorized"

# 4. Monitor performance logs
kubectl logs deployment/blast-radius-backend -n blast-radius --since=1h | \
  grep "response_time" | awk '{print $NF}' | sort -n | tail -10
```

### Log Retention Management
```bash
# 1. Check log volume
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  du -sh /var/log/

# 2. Rotate logs
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  logrotate /etc/logrotate.conf

# 3. Archive old logs
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  tar -czf /tmp/logs-$(date +%Y%m%d).tar.gz /var/log/*.log.1

# 4. Clean up old logs
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  find /var/log -name "*.log.*" -mtime +30 -delete
```

## Escalation Procedures

### Alert Escalation Matrix

| Alert Severity | Initial Response | Escalation Time | Escalation Target |
|---------------|------------------|-----------------|-------------------|
| Critical (P0) | On-call engineer | 15 minutes | Team lead |
| High (P1) | On-call engineer | 30 minutes | Senior engineer |
| Medium (P2) | Team member | 2 hours | Team lead |
| Low (P3) | Team member | 24 hours | Team lead |

### Communication Procedures
```bash
# 1. Send alert notification
./scripts/send-alert-notification.sh "$ALERT_NAME" "$SEVERITY" "$DESCRIPTION"

# 2. Update status page
curl -X POST https://api.statuspage.io/v1/pages/$PAGE_ID/incidents \
  -H "Authorization: OAuth $STATUS_PAGE_TOKEN" \
  -d "incident[name]=$INCIDENT_NAME&incident[status]=investigating"

# 3. Notify stakeholders
./scripts/notify-stakeholders.sh "$INCIDENT_ID" "$STATUS_UPDATE"

# 4. Create incident ticket
curl -X POST https://api.jira.com/rest/api/2/issue \
  -H "Authorization: Basic $JIRA_TOKEN" \
  -d @incident-ticket.json
```

This monitoring runbook provides comprehensive procedures for maintaining system health, responding to alerts, and ensuring optimal performance of the Blast-Radius Security Tool.
