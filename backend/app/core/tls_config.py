"""
TLS 1.3+ Configuration for Zero-Trust Architecture.

This module provides comprehensive TLS configuration enforcement
for secure communications in a zero-trust environment.
"""

import ssl
import socket
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

from cryptography import x509
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, ec
from cryptography.x509.oid import NameOID, ExtensionOID

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class TLSVersion(Enum):
    """Supported TLS versions for zero-trust architecture."""
    TLS_1_2 = "TLSv1.2"
    TLS_1_3 = "TLSv1.3"


class CipherSuite(Enum):
    """Approved cipher suites for zero-trust communications."""
    # TLS 1.3 cipher suites (AEAD only)
    TLS_AES_256_GCM_SHA384 = "TLS_AES_256_GCM_SHA384"
    TLS_CHACHA20_POLY1305_SHA256 = "TLS_CHACHA20_POLY1305_SHA256"
    TLS_AES_128_GCM_SHA256 = "TLS_AES_128_GCM_SHA256"
    
    # TLS 1.2 cipher suites (for backward compatibility if needed)
    ECDHE_ECDSA_WITH_AES_256_GCM_SHA384 = "ECDHE-ECDSA-AES256-GCM-SHA384"
    ECDHE_RSA_WITH_AES_256_GCM_SHA384 = "ECDHE-RSA-AES256-GCM-SHA384"
    ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256 = "ECDHE-ECDSA-CHACHA20-POLY1305"
    ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256 = "ECDHE-RSA-CHACHA20-POLY1305"


@dataclass
class TLSConfiguration:
    """TLS configuration for zero-trust communications."""
    
    # Protocol versions
    min_version: TLSVersion = TLSVersion.TLS_1_3
    max_version: TLSVersion = TLSVersion.TLS_1_3
    
    # Cipher suites
    allowed_ciphers: List[CipherSuite] = None
    
    # Certificate validation
    verify_mode: ssl.VerifyMode = ssl.CERT_REQUIRED
    check_hostname: bool = True
    verify_certificate_chain: bool = True
    
    # HSTS (HTTP Strict Transport Security)
    hsts_enabled: bool = True
    hsts_max_age: int = 31536000  # 1 year
    hsts_include_subdomains: bool = True
    hsts_preload: bool = True
    
    # Certificate pinning
    certificate_pinning_enabled: bool = True
    pinned_certificates: List[str] = None  # SHA256 hashes of pinned certificates
    
    # OCSP (Online Certificate Status Protocol)
    ocsp_stapling_enabled: bool = True
    ocsp_must_staple: bool = True
    
    # Session management
    session_tickets_enabled: bool = False  # Disable for perfect forward secrecy
    session_cache_enabled: bool = False
    
    # Security headers
    security_headers_enabled: bool = True
    
    def __post_init__(self):
        if self.allowed_ciphers is None:
            self.allowed_ciphers = [
                CipherSuite.TLS_AES_256_GCM_SHA384,
                CipherSuite.TLS_CHACHA20_POLY1305_SHA256,
                CipherSuite.TLS_AES_128_GCM_SHA256,
            ]
        
        if self.pinned_certificates is None:
            self.pinned_certificates = []


class TLSConfigurationManager:
    """Manager for TLS configuration in zero-trust architecture."""
    
    def __init__(self, config: Optional[TLSConfiguration] = None):
        self.config = config or TLSConfiguration()
        self._ssl_context = None
    
    def create_ssl_context(
        self,
        purpose: ssl.Purpose = ssl.Purpose.SERVER_AUTH,
        cert_file: Optional[str] = None,
        key_file: Optional[str] = None,
        ca_file: Optional[str] = None
    ) -> ssl.SSLContext:
        """
        Create an SSL context with zero-trust configuration.
        
        Args:
            purpose: SSL purpose (SERVER_AUTH or CLIENT_AUTH)
            cert_file: Path to certificate file
            key_file: Path to private key file
            ca_file: Path to CA certificate file
            
        Returns:
            Configured SSL context
        """
        # Create SSL context
        context = ssl.SSLContext(ssl.PROTOCOL_TLS)
        
        # Set minimum and maximum TLS versions
        if self.config.min_version == TLSVersion.TLS_1_3:
            context.minimum_version = ssl.TLSVersion.TLSv1_3
        else:
            context.minimum_version = ssl.TLSVersion.TLSv1_2
        
        if self.config.max_version == TLSVersion.TLS_1_3:
            context.maximum_version = ssl.TLSVersion.TLSv1_3
        else:
            context.maximum_version = ssl.TLSVersion.TLSv1_2
        
        # Set cipher suites
        cipher_string = self._build_cipher_string()
        context.set_ciphers(cipher_string)
        
        # Configure certificate verification
        context.verify_mode = self.config.verify_mode
        context.check_hostname = self.config.check_hostname
        
        # Load certificates
        if cert_file and key_file:
            context.load_cert_chain(cert_file, key_file)
        
        if ca_file:
            context.load_verify_locations(ca_file)
        else:
            context.load_default_certs(purpose)
        
        # Security options
        context.options |= ssl.OP_NO_SSLv2
        context.options |= ssl.OP_NO_SSLv3
        context.options |= ssl.OP_NO_TLSv1
        context.options |= ssl.OP_NO_TLSv1_1
        context.options |= ssl.OP_NO_COMPRESSION
        context.options |= ssl.OP_CIPHER_SERVER_PREFERENCE
        context.options |= ssl.OP_SINGLE_DH_USE
        context.options |= ssl.OP_SINGLE_ECDH_USE
        
        # Disable session tickets for perfect forward secrecy
        if not self.config.session_tickets_enabled:
            context.options |= ssl.OP_NO_TICKET
        
        # Set security level (OpenSSL 1.1.0+)
        try:
            context.set_ciphers("@SECLEVEL=2")  # 112-bit security level
        except ssl.SSLError:
            logger.warning("Unable to set security level, OpenSSL version may be too old")
        
        self._ssl_context = context
        return context
    
    def _build_cipher_string(self) -> str:
        """Build cipher string from allowed cipher suites."""
        cipher_names = []
        
        for cipher in self.config.allowed_ciphers:
            if cipher in [CipherSuite.TLS_AES_256_GCM_SHA384, 
                         CipherSuite.TLS_CHACHA20_POLY1305_SHA256,
                         CipherSuite.TLS_AES_128_GCM_SHA256]:
                # TLS 1.3 ciphers are handled automatically
                continue
            else:
                cipher_names.append(cipher.value)
        
        # For TLS 1.2 compatibility, add secure ciphers
        if self.config.min_version == TLSVersion.TLS_1_2:
            cipher_names.extend([
                "ECDHE+AESGCM",
                "ECDHE+CHACHA20",
                "DHE+AESGCM",
                "DHE+CHACHA20",
                "!aNULL",
                "!eNULL",
                "!EXPORT",
                "!DES",
                "!RC4",
                "!MD5",
                "!PSK",
                "!SRP",
                "!CAMELLIA"
            ])
        
        return ":".join(cipher_names) if cipher_names else "HIGH:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5"
    
    def validate_certificate(self, cert_data: bytes) -> Dict[str, Any]:
        """
        Validate a certificate against zero-trust requirements.
        
        Args:
            cert_data: Certificate data in DER or PEM format
            
        Returns:
            Dictionary with validation results
        """
        try:
            # Parse certificate
            if cert_data.startswith(b'-----'):
                cert = x509.load_pem_x509_certificate(cert_data)
            else:
                cert = x509.load_der_x509_certificate(cert_data)
            
            validation_result = {
                "valid": True,
                "errors": [],
                "warnings": [],
                "certificate_info": {}
            }
            
            # Extract certificate information
            subject = cert.subject
            issuer = cert.issuer
            
            validation_result["certificate_info"] = {
                "subject": subject.rfc4514_string(),
                "issuer": issuer.rfc4514_string(),
                "serial_number": str(cert.serial_number),
                "not_valid_before": cert.not_valid_before.isoformat(),
                "not_valid_after": cert.not_valid_after.isoformat(),
                "signature_algorithm": cert.signature_algorithm_oid._name,
                "version": cert.version.name
            }
            
            # Check expiration
            from datetime import datetime, timezone
            now = datetime.now(timezone.utc)
            
            if cert.not_valid_after.replace(tzinfo=timezone.utc) < now:
                validation_result["valid"] = False
                validation_result["errors"].append("Certificate has expired")
            
            if cert.not_valid_before.replace(tzinfo=timezone.utc) > now:
                validation_result["valid"] = False
                validation_result["errors"].append("Certificate is not yet valid")
            
            # Check key size
            public_key = cert.public_key()
            if isinstance(public_key, rsa.RSAPublicKey):
                key_size = public_key.key_size
                if key_size < 2048:
                    validation_result["valid"] = False
                    validation_result["errors"].append(f"RSA key size {key_size} is too small (minimum 2048)")
                elif key_size < 3072:
                    validation_result["warnings"].append(f"RSA key size {key_size} is acceptable but 3072+ recommended")
            elif isinstance(public_key, ec.EllipticCurvePublicKey):
                curve_name = public_key.curve.name
                if curve_name not in ["secp256r1", "secp384r1", "secp521r1"]:
                    validation_result["warnings"].append(f"Elliptic curve {curve_name} may not be optimal")
            
            # Check signature algorithm
            sig_alg = cert.signature_algorithm_oid._name
            if "sha1" in sig_alg.lower():
                validation_result["valid"] = False
                validation_result["errors"].append("SHA-1 signature algorithm is not allowed")
            elif "md5" in sig_alg.lower():
                validation_result["valid"] = False
                validation_result["errors"].append("MD5 signature algorithm is not allowed")
            
            # Check extensions
            try:
                key_usage = cert.extensions.get_extension_for_oid(ExtensionOID.KEY_USAGE).value
                if not key_usage.digital_signature:
                    validation_result["warnings"].append("Digital signature not enabled in key usage")
                if not key_usage.key_encipherment and not key_usage.key_agreement:
                    validation_result["warnings"].append("Key encipherment or key agreement should be enabled")
            except x509.ExtensionNotFound:
                validation_result["warnings"].append("Key usage extension not found")
            
            # Check for required extensions
            required_extensions = [
                ExtensionOID.BASIC_CONSTRAINTS,
                ExtensionOID.KEY_USAGE,
                ExtensionOID.SUBJECT_ALTERNATIVE_NAME
            ]
            
            for ext_oid in required_extensions:
                try:
                    cert.extensions.get_extension_for_oid(ext_oid)
                except x509.ExtensionNotFound:
                    validation_result["warnings"].append(f"Missing recommended extension: {ext_oid._name}")
            
            return validation_result
            
        except Exception as e:
            return {
                "valid": False,
                "errors": [f"Certificate parsing error: {str(e)}"],
                "warnings": [],
                "certificate_info": {}
            }
    
    def get_security_headers(self) -> Dict[str, str]:
        """
        Get security headers for HTTP responses.
        
        Returns:
            Dictionary of security headers
        """
        headers = {}
        
        if self.config.security_headers_enabled:
            # HSTS header
            if self.config.hsts_enabled:
                hsts_value = f"max-age={self.config.hsts_max_age}"
                if self.config.hsts_include_subdomains:
                    hsts_value += "; includeSubDomains"
                if self.config.hsts_preload:
                    hsts_value += "; preload"
                headers["Strict-Transport-Security"] = hsts_value
            
            # Other security headers
            headers.update({
                "X-Content-Type-Options": "nosniff",
                "X-Frame-Options": "DENY",
                "X-XSS-Protection": "1; mode=block",
                "Referrer-Policy": "strict-origin-when-cross-origin",
                "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'",
                "Permissions-Policy": "geolocation=(), microphone=(), camera=()",
                "Cross-Origin-Embedder-Policy": "require-corp",
                "Cross-Origin-Opener-Policy": "same-origin",
                "Cross-Origin-Resource-Policy": "same-origin"
            })
        
        return headers
    
    def verify_certificate_pinning(self, cert_data: bytes) -> bool:
        """
        Verify certificate against pinned certificates.
        
        Args:
            cert_data: Certificate data
            
        Returns:
            True if certificate is pinned or pinning is disabled
        """
        if not self.config.certificate_pinning_enabled:
            return True
        
        if not self.config.pinned_certificates:
            logger.warning("Certificate pinning enabled but no certificates pinned")
            return True
        
        # Calculate SHA256 hash of certificate
        cert_hash = hashes.Hash(hashes.SHA256())
        cert_hash.update(cert_data)
        cert_fingerprint = cert_hash.finalize().hex()
        
        return cert_fingerprint in self.config.pinned_certificates
    
    def check_tls_connection(self, hostname: str, port: int = 443) -> Dict[str, Any]:
        """
        Check TLS connection to a host and validate configuration.
        
        Args:
            hostname: Target hostname
            port: Target port
            
        Returns:
            Dictionary with connection check results
        """
        result = {
            "success": False,
            "tls_version": None,
            "cipher_suite": None,
            "certificate_valid": False,
            "errors": [],
            "warnings": []
        }
        
        try:
            # Create SSL context
            context = self.create_ssl_context()
            
            # Connect to host
            with socket.create_connection((hostname, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    result["success"] = True
                    result["tls_version"] = ssock.version()
                    result["cipher_suite"] = ssock.cipher()
                    
                    # Get peer certificate
                    cert_der = ssock.getpeercert_chain()[0].public_bytes(serialization.Encoding.DER)
                    cert_validation = self.validate_certificate(cert_der)
                    result["certificate_valid"] = cert_validation["valid"]
                    
                    if not cert_validation["valid"]:
                        result["errors"].extend(cert_validation["errors"])
                    
                    result["warnings"].extend(cert_validation["warnings"])
                    
                    # Check certificate pinning
                    if not self.verify_certificate_pinning(cert_der):
                        result["errors"].append("Certificate pinning validation failed")
                        result["certificate_valid"] = False
        
        except ssl.SSLError as e:
            result["errors"].append(f"SSL error: {str(e)}")
        except socket.error as e:
            result["errors"].append(f"Connection error: {str(e)}")
        except Exception as e:
            result["errors"].append(f"Unexpected error: {str(e)}")
        
        return result


# Global TLS configuration instance
tls_config = TLSConfigurationManager()


def get_tls_config() -> TLSConfigurationManager:
    """Get the global TLS configuration manager."""
    return tls_config


def enforce_tls_1_3() -> ssl.SSLContext:
    """
    Create an SSL context that enforces TLS 1.3 only.
    
    Returns:
        SSL context configured for TLS 1.3 only
    """
    config = TLSConfiguration(
        min_version=TLSVersion.TLS_1_3,
        max_version=TLSVersion.TLS_1_3
    )
    
    manager = TLSConfigurationManager(config)
    return manager.create_ssl_context()
