# Maintenance Procedures - Blast-Radius Security Tool

## Overview

This document provides comprehensive maintenance procedures for the Blast-Radius Security Tool, including routine maintenance tasks, system updates, performance optimization, and preventive maintenance schedules.

## Routine Maintenance Schedule

### Daily Maintenance (Automated)

#### System Health Check (6:00 AM UTC)
```bash
#!/bin/bash
# scripts/daily-maintenance.sh

echo "=== Daily Maintenance - $(date) ==="

# 1. Check system status
kubectl get pods -n blast-radius --field-selector=status.phase!=Running
kubectl get nodes --no-headers | awk '$2 != "Ready" {print $1 " is " $2}'

# 2. Check disk usage
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  df -h | awk '$5 > 80 {print "WARNING: " $0}'

# 3. Check memory usage
kubectl top nodes | awk 'NR>1 && $5 > 80 {print "WARNING: High memory on " $1 ": " $5}'

# 4. Check database connections
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT count(*) as active_connections FROM pg_stat_activity WHERE state = 'active';"

# 5. Check error rates
ERROR_COUNT=$(kubectl logs deployment/blast-radius-backend -n blast-radius --since=24h | grep -i error | wc -l)
if [ $ERROR_COUNT -gt 100 ]; then
    echo "WARNING: High error count in last 24h: $ERROR_COUNT"
fi

# 6. Check certificate expiration
openssl s_client -connect blast-radius.com:443 -servername blast-radius.com 2>/dev/null | \
  openssl x509 -noout -dates | grep notAfter

# 7. Generate daily report
cat > /tmp/daily-maintenance-report.txt << EOF
Daily Maintenance Report - $(date)
=====================================
System Status: $(kubectl get pods -n blast-radius --no-headers | wc -l) pods running
Error Count (24h): $ERROR_COUNT
Database Connections: $(kubectl exec -it deployment/blast-radius-backend -n blast-radius -- psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME -t -c "SELECT count(*) FROM pg_stat_activity;")
Disk Usage: $(kubectl exec -it deployment/blast-radius-backend -n blast-radius -- df -h / | tail -1 | awk '{print $5}')
Memory Usage: $(kubectl top nodes --no-headers | awk '{sum+=$5} END {print sum/NR "%"}')
EOF

# 8. Send report if issues found
if [ $ERROR_COUNT -gt 100 ] || kubectl get pods -n blast-radius --field-selector=status.phase!=Running | grep -q .; then
    ./scripts/send-maintenance-alert.sh /tmp/daily-maintenance-report.txt
fi

echo "Daily maintenance completed"
```

#### Log Rotation and Cleanup (2:00 AM UTC)
```bash
#!/bin/bash
# scripts/daily-log-cleanup.sh

echo "Starting daily log cleanup - $(date)"

# 1. Rotate application logs
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  logrotate /etc/logrotate.d/blast-radius

# 2. Clean old log files
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  find /var/log -name "*.log.*" -mtime +7 -delete

# 3. Clean Docker logs
docker system prune -f --filter "until=24h"

# 4. Clean temporary files
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  find /tmp -type f -mtime +1 -delete

# 5. Vacuum database logs
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT pg_rotate_logfile();"

echo "Daily log cleanup completed"
```

### Weekly Maintenance (Sunday 3:00 AM UTC)

#### Database Maintenance
```bash
#!/bin/bash
# scripts/weekly-database-maintenance.sh

echo "Starting weekly database maintenance - $(date)"

# 1. Update table statistics
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "ANALYZE;"

# 2. Vacuum tables
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "VACUUM (VERBOSE, ANALYZE);"

# 3. Check for bloated tables
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT schemaname, tablename, n_dead_tup, n_live_tup, 
      ROUND(n_dead_tup * 100.0 / (n_live_tup + n_dead_tup), 2) AS dead_percentage
      FROM pg_stat_user_tables 
      WHERE n_dead_tup > 1000 AND n_dead_tup > n_live_tup * 0.1
      ORDER BY dead_percentage DESC;"

# 4. Check index usage
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
      FROM pg_stat_user_indexes 
      WHERE idx_scan = 0 
      ORDER BY schemaname, tablename;"

# 5. Check slow queries
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT query, mean_time, calls, total_time
      FROM pg_stat_statements 
      WHERE mean_time > 1000 
      ORDER BY mean_time DESC 
      LIMIT 10;"

# 6. Reset query statistics
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT pg_stat_statements_reset();"

echo "Weekly database maintenance completed"
```

#### Security Updates Check
```bash
#!/bin/bash
# scripts/weekly-security-check.sh

echo "Starting weekly security check - $(date)"

# 1. Check for container image vulnerabilities
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image blast-radius-backend:latest \
  --severity HIGH,CRITICAL --format json > /tmp/security-scan.json

# 2. Check for outdated packages
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  pip list --outdated --format=json > /tmp/outdated-packages.json

# 3. Check SSL certificate expiration
CERT_EXPIRY=$(openssl s_client -connect blast-radius.com:443 -servername blast-radius.com 2>/dev/null | \
  openssl x509 -noout -enddate | cut -d= -f2)
DAYS_TO_EXPIRY=$(( ($(date -d "$CERT_EXPIRY" +%s) - $(date +%s)) / 86400 ))

if [ $DAYS_TO_EXPIRY -lt 30 ]; then
    echo "WARNING: SSL certificate expires in $DAYS_TO_EXPIRY days"
fi

# 4. Check for security advisories
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  safety check --json > /tmp/security-advisories.json

# 5. Generate security report
python3 << EOF
import json
import sys

# Load scan results
with open('/tmp/security-scan.json') as f:
    vuln_scan = json.load(f)

with open('/tmp/security-advisories.json') as f:
    advisories = json.load(f)

# Count vulnerabilities
high_vulns = len([v for v in vuln_scan.get('Results', [{}])[0].get('Vulnerabilities', []) if v.get('Severity') == 'HIGH'])
critical_vulns = len([v for v in vuln_scan.get('Results', [{}])[0].get('Vulnerabilities', []) if v.get('Severity') == 'CRITICAL'])

print(f"Security Report - $(date)")
print(f"Critical Vulnerabilities: {critical_vulns}")
print(f"High Vulnerabilities: {high_vulns}")
print(f"SSL Certificate Days to Expiry: $DAYS_TO_EXPIRY")

if critical_vulns > 0 or high_vulns > 5:
    sys.exit(1)
EOF

echo "Weekly security check completed"
```

### Monthly Maintenance (First Sunday 4:00 AM UTC)

#### Performance Optimization
```bash
#!/bin/bash
# scripts/monthly-performance-optimization.sh

echo "Starting monthly performance optimization - $(date)"

# 1. Database performance analysis
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT schemaname, tablename, seq_scan, seq_tup_read, idx_scan, idx_tup_fetch
      FROM pg_stat_user_tables 
      WHERE seq_scan > idx_scan AND seq_tup_read > 10000
      ORDER BY seq_tup_read DESC;"

# 2. Check for missing indexes
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  python manage.py check-missing-indexes

# 3. Analyze query performance
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT query, calls, total_time, mean_time, stddev_time
      FROM pg_stat_statements 
      WHERE calls > 100
      ORDER BY total_time DESC 
      LIMIT 20;"

# 4. Check cache hit ratios
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  redis-cli -h $REDIS_HOST info stats | grep -E "keyspace_hits|keyspace_misses"

# 5. Optimize Redis memory
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  redis-cli -h $REDIS_HOST --scan --pattern "*:expired:*" | \
  xargs -r kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  redis-cli -h $REDIS_HOST del

# 6. Check application metrics
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  curl -s http://localhost:8000/metrics | grep -E "response_time|request_count|error_rate"

# 7. Generate performance report
cat > /tmp/performance-report.txt << EOF
Monthly Performance Report - $(date)
====================================
Database Size: $(kubectl exec -it deployment/blast-radius-backend -n blast-radius -- psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME -t -c "SELECT pg_size_pretty(pg_database_size('blast_radius'));")
Cache Hit Rate: $(kubectl exec -it deployment/blast-radius-backend -n blast-radius -- redis-cli -h $REDIS_HOST info stats | grep keyspace_hits | cut -d: -f2)
Average Response Time: $(kubectl exec -it deployment/blast-radius-backend -n blast-radius -- curl -s http://localhost:8000/metrics | grep response_time_avg | cut -d' ' -f2)
Active Connections: $(kubectl exec -it deployment/blast-radius-backend -n blast-radius -- psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME -t -c "SELECT count(*) FROM pg_stat_activity;")
EOF

echo "Monthly performance optimization completed"
```

#### Capacity Planning Review
```bash
#!/bin/bash
# scripts/monthly-capacity-review.sh

echo "Starting monthly capacity review - $(date)"

# 1. Check resource utilization trends
kubectl top nodes --sort-by=cpu
kubectl top pods -n blast-radius --sort-by=cpu

# 2. Check storage growth
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size, 
      schemaname, tablename 
      FROM pg_tables 
      WHERE schemaname = 'public' 
      ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC 
      LIMIT 10;"

# 3. Check PVC usage
kubectl get pvc -n blast-radius -o custom-columns=NAME:.metadata.name,CAPACITY:.spec.resources.requests.storage,USED:.status.capacity.storage

# 4. Analyze traffic patterns
kubectl logs deployment/blast-radius-backend -n blast-radius --since=720h | \
  grep -E "GET|POST|PUT|DELETE" | \
  awk '{print $1}' | sort | uniq -c | sort -nr | head -20

# 5. Check auto-scaling metrics
kubectl get hpa -n blast-radius
kubectl describe hpa blast-radius-backend-hpa -n blast-radius

# 6. Generate capacity report
cat > /tmp/capacity-report.txt << EOF
Monthly Capacity Report - $(date)
=================================
Node CPU Usage: $(kubectl top nodes --no-headers | awk '{sum+=$3} END {print sum/NR}')
Node Memory Usage: $(kubectl top nodes --no-headers | awk '{sum+=$5} END {print sum/NR}')
Database Size: $(kubectl exec -it deployment/blast-radius-backend -n blast-radius -- psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME -t -c "SELECT pg_size_pretty(pg_database_size('blast_radius'));")
Storage Usage: $(kubectl get pvc -n blast-radius --no-headers | awk '{print $4}')
Pod Count: $(kubectl get pods -n blast-radius --no-headers | wc -l)
EOF

echo "Monthly capacity review completed"
```

## System Updates and Patches

### Kubernetes Cluster Updates
```bash
#!/bin/bash
# scripts/k8s-cluster-update.sh

NEW_VERSION=${1}
if [ -z "$NEW_VERSION" ]; then
    echo "Usage: $0 <new-k8s-version>"
    echo "Current version: $(kubectl version --short)"
    exit 1
fi

echo "Starting Kubernetes cluster update to version: $NEW_VERSION"

# 1. Pre-update backup
./scripts/pre-deployment-backup.sh k8s-update-$NEW_VERSION

# 2. Update EKS cluster control plane
aws eks update-cluster-version \
  --name blast-radius-cluster \
  --kubernetes-version $NEW_VERSION

# 3. Wait for update completion
aws eks wait cluster-active --name blast-radius-cluster

# 4. Update node groups
NODE_GROUPS=$(aws eks list-nodegroups --cluster-name blast-radius-cluster --query 'nodegroups[]' --output text)
for ng in $NODE_GROUPS; do
    aws eks update-nodegroup-version \
      --cluster-name blast-radius-cluster \
      --nodegroup-name $ng \
      --kubernetes-version $NEW_VERSION
done

# 5. Update add-ons
aws eks update-addon \
  --cluster-name blast-radius-cluster \
  --addon-name vpc-cni \
  --addon-version latest

aws eks update-addon \
  --cluster-name blast-radius-cluster \
  --addon-name coredns \
  --addon-version latest

# 6. Verify update
kubectl version --short
kubectl get nodes

echo "Kubernetes cluster update completed"
```

### Application Updates
```bash
#!/bin/bash
# scripts/application-update.sh

NEW_VERSION=${1}
if [ -z "$NEW_VERSION" ]; then
    echo "Usage: $0 <new-app-version>"
    exit 1
fi

echo "Starting application update to version: $NEW_VERSION"

# 1. Pre-update checks
./scripts/pre-deployment-backup.sh app-update-$NEW_VERSION
./scripts/health-check.sh

# 2. Build and push new images
docker build -t blast-radius-backend:$NEW_VERSION -f backend/Dockerfile backend/
docker tag blast-radius-backend:$NEW_VERSION $ECR_REPO/blast-radius-backend:$NEW_VERSION
docker push $ECR_REPO/blast-radius-backend:$NEW_VERSION

# 3. Update deployment
kubectl set image deployment/blast-radius-backend \
  blast-radius-backend=$ECR_REPO/blast-radius-backend:$NEW_VERSION \
  -n blast-radius

# 4. Monitor rollout
kubectl rollout status deployment/blast-radius-backend -n blast-radius --timeout=300s

# 5. Run post-deployment tests
./scripts/post-deployment-tests.sh

# 6. Verify health
./scripts/health-check.sh

echo "Application update completed to version: $NEW_VERSION"
```

### Security Patches
```bash
#!/bin/bash
# scripts/security-patch-update.sh

echo "Starting security patch update - $(date)"

# 1. Check for security updates
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  apt list --upgradable | grep -i security

# 2. Update base images
docker pull python:3.11-slim
docker pull node:18-alpine
docker pull postgres:15

# 3. Rebuild images with security patches
docker build --no-cache -t blast-radius-backend:security-patch-$(date +%Y%m%d) \
  -f backend/Dockerfile backend/

# 4. Scan for vulnerabilities
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image blast-radius-backend:security-patch-$(date +%Y%m%d) \
  --severity HIGH,CRITICAL

# 5. Deploy if scan passes
if [ $? -eq 0 ]; then
    kubectl set image deployment/blast-radius-backend \
      blast-radius-backend=blast-radius-backend:security-patch-$(date +%Y%m%d) \
      -n blast-radius
    
    kubectl rollout status deployment/blast-radius-backend -n blast-radius
fi

echo "Security patch update completed"
```

## Preventive Maintenance

### Certificate Renewal
```bash
#!/bin/bash
# scripts/certificate-renewal.sh

echo "Starting certificate renewal check - $(date)"

# 1. Check current certificate expiration
CERT_EXPIRY=$(openssl s_client -connect blast-radius.com:443 -servername blast-radius.com 2>/dev/null | \
  openssl x509 -noout -enddate | cut -d= -f2)
DAYS_TO_EXPIRY=$(( ($(date -d "$CERT_EXPIRY" +%s) - $(date +%s)) / 86400 ))

echo "Certificate expires in $DAYS_TO_EXPIRY days"

# 2. Renew if expiring within 30 days
if [ $DAYS_TO_EXPIRY -lt 30 ]; then
    echo "Renewing certificate..."
    
    # Request new certificate
    NEW_CERT_ARN=$(aws acm request-certificate \
      --domain-name blast-radius.com \
      --subject-alternative-names "*.blast-radius.com" \
      --validation-method DNS \
      --query CertificateArn --output text)
    
    # Wait for validation
    aws acm wait certificate-validated --certificate-arn $NEW_CERT_ARN
    
    # Update load balancer
    kubectl patch ingress blast-radius-ingress -n blast-radius -p \
      '{"metadata":{"annotations":{"alb.ingress.kubernetes.io/certificate-arn":"'$NEW_CERT_ARN'"}}}'
    
    echo "Certificate renewed: $NEW_CERT_ARN"
else
    echo "Certificate renewal not needed"
fi
```

### Database Maintenance
```bash
#!/bin/bash
# scripts/database-preventive-maintenance.sh

echo "Starting database preventive maintenance - $(date)"

# 1. Check database health
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT version();"

# 2. Check for long-running transactions
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT pid, now() - xact_start AS duration, query 
      FROM pg_stat_activity 
      WHERE xact_start IS NOT NULL 
      ORDER BY duration DESC;"

# 3. Check for table bloat
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT schemaname, tablename, n_dead_tup, n_live_tup,
      ROUND(n_dead_tup * 100.0 / NULLIF(n_live_tup + n_dead_tup, 0), 2) AS dead_percentage
      FROM pg_stat_user_tables 
      WHERE n_dead_tup > 1000
      ORDER BY dead_percentage DESC;"

# 4. Optimize tables with high bloat
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "VACUUM (VERBOSE, ANALYZE) assets;"

# 5. Update database statistics
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "ANALYZE;"

echo "Database preventive maintenance completed"
```

## Maintenance Windows

### Scheduled Maintenance Window
```bash
#!/bin/bash
# scripts/maintenance-window.sh

MAINTENANCE_TYPE=${1:-routine}
DURATION=${2:-2h}

echo "Starting maintenance window: $MAINTENANCE_TYPE (Duration: $DURATION)"

# 1. Enable maintenance mode
kubectl patch configmap blast-radius-config -n blast-radius -p \
  '{"data":{"MAINTENANCE_MODE":"true"}}'

# 2. Update status page
curl -X POST https://api.statuspage.io/v1/pages/$PAGE_ID/incidents \
  -H "Authorization: OAuth $STATUS_PAGE_TOKEN" \
  -d "incident[name]=Scheduled Maintenance&incident[status]=investigating"

# 3. Scale down non-essential services
kubectl scale deployment blast-radius-worker --replicas=0 -n blast-radius

# 4. Perform maintenance tasks based on type
case $MAINTENANCE_TYPE in
    "routine")
        ./scripts/weekly-database-maintenance.sh
        ./scripts/weekly-security-check.sh
        ;;
    "security")
        ./scripts/security-patch-update.sh
        ;;
    "performance")
        ./scripts/monthly-performance-optimization.sh
        ;;
    "update")
        ./scripts/application-update.sh $3
        ;;
esac

# 5. Scale services back up
kubectl scale deployment blast-radius-worker --replicas=2 -n blast-radius

# 6. Disable maintenance mode
kubectl patch configmap blast-radius-config -n blast-radius -p \
  '{"data":{"MAINTENANCE_MODE":"false"}}'

# 7. Update status page
curl -X PATCH https://api.statuspage.io/v1/pages/$PAGE_ID/incidents/$INCIDENT_ID \
  -H "Authorization: OAuth $STATUS_PAGE_TOKEN" \
  -d "incident[status]=resolved"

# 8. Verify system health
./scripts/health-check.sh

echo "Maintenance window completed: $MAINTENANCE_TYPE"
```

This maintenance procedures runbook provides comprehensive guidance for keeping the Blast-Radius Security Tool running optimally through regular maintenance, updates, and preventive care.
