Search.setIndex({"alltitles": {"1. AWS Configuration": [[14, "aws-configuration"]], "1. Build and Push Images": [[14, "build-and-push-images"]], "1. Change Impact Assessment": [[49, "change-impact-assessment"]], "1. Clone Repository": [[14, "clone-repository"]], "1. Code Security Review": [[49, "code-security-review"]], "1. Database & Models \u2705 Implemented": [[24, "database-models-implemented"]], "1. Feature Engineering Pipeline": [[55, "feature-engineering-pipeline"]], "1. Final Infrastructure Review": [[14, "final-infrastructure-review"]], "1. Information Gathering": [[17, "information-gathering"]], "1. Input Validation and Sanitization": [[49, "input-validation-and-sanitization"]], "1. Machine Learning & Analytics \ud83c\udd95": [[25, "machine-learning-analytics"]], "1. Machine Learning Infrastructure": [[23, "machine-learning-infrastructure"]], "1. Performance & Scalability (docs/performance/index.rst)": [[0, "performance-scalability-docs-performance-index-rst"]], "1. TheHive API Connector": [[56, "thehive-api-connector"]], "1. Threat Modeling": [[49, "threat-modeling"]], "1. Weak Cryptographic Hash \u2705 FIXED": [[47, "weak-cryptographic-hash-fixed"]], "1. compliance_frameworks": [[54, "compliance-frameworks"]], "1.1 Configure AWS CLI": [[16, "configure-aws-cli"]], "1.1 Incident Detection": [[41, "incident-detection"]], "1.2 Initial Assessment": [[41, "initial-assessment"]], "1.2 Set Environment Variables": [[16, "set-environment-variables"]], "1.3 Team Notification": [[41, "team-notification"]], "10.1 Database Backup": [[16, "database-backup"]], "10.2 Application Data Backup": [[16, "application-data-backup"]], "11.1 Set Up Alerts": [[16, "set-up-alerts"]], "11.2 Performance Monitoring": [[16, "performance-monitoring"]], "2. Authentication and Authorization": [[49, "authentication-and-authorization"]], "2. Compliance Automation Framework": [[23, "compliance-automation-framework"]], "2. Compliance Automation \ud83c\udd95": [[25, "compliance-automation"]], "2. Deploy to Staging": [[14, "deploy-to-staging"]], "2. Documentation Requirements": [[49, "documentation-requirements"]], "2. Environment Configuration": [[14, "environment-configuration"]], "2. Incident to Case Mapping Service": [[56, "incident-to-case-mapping-service"]], "2. Infrastructure Security Review": [[49, "infrastructure-security-review"]], "2. Log Analysis": [[17, "log-analysis"]], "2. ML Model Implementation": [[55, "ml-model-implementation"]], "2. Production Image Preparation": [[14, "production-image-preparation"]], "2. Security Architecture Patterns": [[49, "security-architecture-patterns"]], "2. Service Layer \u2705 Partially Implemented": [[24, "service-layer-partially-implemented"]], "2. Terraform Deployment": [[14, "terraform-deployment"]], "2. Testing & Quality Assurance (docs/testing/index.rst)": [[0, "testing-quality-assurance-docs-testing-index-rst"]], "2. Unsafe Pickle Deserialization \u2705 FIXED": [[47, "unsafe-pickle-deserialization-fixed"]], "2. compliance_domains": [[54, "compliance-domains"]], "2.1 Deploy Core Infrastructure": [[16, "deploy-core-infrastructure"]], "2.1 Incident War Room Setup": [[41, "incident-war-room-setup"]], "2.2 Configure kubectl Access": [[16, "configure-kubectl-access"]], "2.2 Technical Investigation": [[41, "technical-investigation"]], "2.3 Install Cluster Add-ons": [[16, "install-cluster-add-ons"]], "2.3 Security Assessment (if applicable)": [[41, "security-assessment-if-applicable"]], "2.5.1 Compliance Framework Enhancement": [[24, "compliance-framework-enhancement"]], "2.5.2 ML Threat Prediction Integration": [[24, "ml-threat-prediction-integration"]], "2.5.3 Enhanced Asset Intelligence": [[24, "enhanced-asset-intelligence"]], "3. API Framework \u2705 Implemented": [[24, "api-framework-implemented"]], "3. Architecture Security Review": [[49, "architecture-security-review"]], "3. Backend Setup": [[14, "backend-setup"]], "3. Batch Processing Service": [[55, "batch-processing-service"]], "3. Blue-Green Deployment": [[14, "blue-green-deployment"]], "3. Configure Staging Data": [[14, "configure-staging-data"]], "3. Data Protection": [[49, "data-protection"]], "3. Development Environment Setup (docs/development/setup.rst)": [[0, "development-environment-setup-docs-development-setup-rst"]], "3. Integration Security": [[49, "integration-security"]], "3. Kubernetes Configuration": [[14, "kubernetes-configuration"]], "3. Network Connectivity": [[17, "network-connectivity"]], "3. SIEM Integration (TheHive)": [[23, "siem-integration-thehive"]], "3. SIEM Integration (TheHive) \ud83c\udd95": [[25, "siem-integration-thehive"]], "3. Weak Random Number Generation \u2705 FIXED": [[47, "weak-random-number-generation-fixed"]], "3. Webhook Processing": [[56, "webhook-processing"]], "3. compliance_controls": [[54, "compliance-controls"]], "3.1 Database Initialization": [[16, "database-initialization"]], "3.1 Fix Implementation": [[41, "fix-implementation"]], "3.1 Machine Learning Foundation (Weeks 1-3)": [[24, "machine-learning-foundation-weeks-1-3"]], "3.2 Database Migration": [[16, "database-migration"]], "3.2 SIEM Integration & Incident Response (Weeks 4-6)": [[24, "siem-integration-incident-response-weeks-4-6"]], "3.2 System Recovery": [[41, "system-recovery"]], "3.3 Advanced Visualization & Analytics (Weeks 7-8)": [[24, "advanced-visualization-analytics-weeks-7-8"]], "4. Asset Discovery Use Cases (docs/use-cases/asset-discovery.rst)": [[0, "asset-discovery-use-cases-docs-use-cases-asset-discovery-rst"]], "4. Compliance Review": [[49, "compliance-review"]], "4. Database Models": [[56, "database-models"]], "4. Database Models for ML Results": [[55, "database-models-for-ml-results"]], "4. Error Handling and Logging": [[49, "error-handling-and-logging"]], "4. Frontend Setup": [[14, "frontend-setup"]], "4. Incident Response Automation \ud83c\udd95": [[25, "incident-response-automation"]], "4. Machine Learning & Analytics \ud83c\udd95 New": [[24, "machine-learning-analytics-new"]], "4. compliance_assessments": [[54, "compliance-assessments"]], "4.1 Build and Push Images": [[16, "build-and-push-images"]], "4.1 Client SDK Development (Weeks 1-4)": [[24, "client-sdk-development-weeks-1-4"]], "4.1 Status Updates": [[41, "status-updates"]], "4.2 Interactive Visualization (Weeks 5-6)": [[24, "interactive-visualization-weeks-5-6"]], "4.2 Resolution Communication": [[41, "resolution-communication"]], "4.3 Automated Response Workflows (Weeks 7-8)": [[24, "automated-response-workflows-weeks-7-8"]], "5. Client Libraries \ud83c\udd95": [[25, "client-libraries"]], "5. Compliance Automation \ud83c\udd95 New": [[24, "compliance-automation-new"]], "5. Cryptography and Secrets": [[49, "cryptography-and-secrets"]], "5. Start Development Services": [[14, "start-development-services"]], "5. Troubleshooting Guide (docs/troubleshooting/common-issues.rst)": [[0, "troubleshooting-guide-docs-troubleshooting-common-issues-rst"]], "5. compliance_control_assessments": [[54, "compliance-control-assessments"]], "5.1 Create Namespace and Secrets": [[16, "create-namespace-and-secrets"]], "5.1 Performance & Scalability": [[24, "performance-scalability"]], "5.1 Post-Mortem Meeting": [[41, "post-mortem-meeting"]], "5.2 Deploy Application Components": [[16, "deploy-application-components"]], "5.2 Documentation": [[41, "documentation"]], "5.2 Enterprise Features": [[24, "enterprise-features"]], "5.3 Configure Ingress and Load Balancer": [[16, "configure-ingress-and-load-balancer"]], "6. Documentation Expansion Summary (docs/DOCUMENTATION_EXPANSION_SUMMARY.md)": [[0, "documentation-expansion-summary-docs-documentation-expansion-summary-md"]], "6. Enhanced Visualization \ud83c\udd95": [[25, "enhanced-visualization"]], "6. SIEM Integration \ud83c\udd95 New": [[24, "siem-integration-new"]], "6. Verify Development Setup": [[14, "verify-development-setup"]], "6. compliance_control_mappings": [[54, "compliance-control-mappings"]], "6.1 Request SSL Certificate": [[16, "request-ssl-certificate"]], "6.2 Configure HTTPS Redirect": [[16, "configure-https-redirect"]], "7. Visualization & Frontend \ud83c\udd95 New": [[24, "visualization-frontend-new"]], "7.1 Initialize Application Data": [[16, "initialize-application-data"]], "7.2 Configure Monitoring and Alerting": [[16, "configure-monitoring-and-alerting"]], "8. Client Libraries \ud83c\udd95 New": [[24, "client-libraries-new"]], "8.1 Network Policies": [[16, "network-policies"]], "8.2 Pod Security Policies": [[16, "pod-security-policies"]], "9.1 Health Checks": [[16, "health-checks"]], "9.2 Functional Testing": [[16, "functional-testing"]], "9.3 Security Testing": [[16, "security-testing"]], "A.10 - Cryptography": [[38, "a-10-cryptography"]], "A.12 - Operations Security": [[38, "a-12-operations-security"]], "A.13 - Communications Security": [[38, "a-13-communications-security"]], "A.14 - System Acquisition, Development and Maintenance": [[38, "a-14-system-acquisition-development-and-maintenance"]], "A.16 - Information Security Incident Management": [[38, "a-16-information-security-incident-management"]], "A.17 - Business Continuity Management": [[38, "a-17-business-continuity-management"]], "A.18 - Compliance": [[38, "a-18-compliance"]], "A.5 - Information Security Policies": [[38, "a-5-information-security-policies"]], "A.6 - Organization of Information Security": [[38, "a-6-organization-of-information-security"]], "A.8 - Asset Management": [[38, "a-8-asset-management"]], "A.9 - Access Control": [[38, "a-9-access-control"]], "AI-Driven Security Recommendations": [[78, "ai-driven-security-recommendations"]], "API Development": [[20, "api-development"], [60, "api-development"]], "API Discovery": [[66, "api-discovery"]], "API Documentation": [[26, "api-documentation"]], "API Documentation Section": [[1, "api-documentation-section"]], "API Endpoints": [[4, "api-endpoints"], [7, "api-endpoints"], [8, "api-endpoints"], [12, "api-endpoints"], [29, "api-endpoints"], [39, "api-endpoints"]], "API Integration": [[73, "api-integration"]], "API Reference": [[8, null], [26, null]], "API Reference (api/)": [[3, "api-reference-api"]], "API Standards": [[18, "api-standards"]], "ATT&CK Data Management": [[9, "att-ck-data-management"]], "ATT&CK Navigator Integration": [[9, "att-ck-navigator-integration"], [73, "att-ck-navigator-integration"]], "AWS Access Requirements": [[16, "aws-access-requirements"]], "AWS Authentication": [[29, "aws-authentication"]], "AWS Configuration": [[29, "aws-configuration"]], "AWS SDK": [[29, "aws-sdk"]], "AWS Service Coverage": [[66, "id1"]], "AWS Services": [[29, "aws-services"]], "Accelerated Development Timeline": [[35, "accelerated-development-timeline"]], "Access Analytics Dashboard": [[4, "access-analytics-dashboard"]], "Access Control Architecture": [[4, "access-control-architecture"]], "Access Control Settings": [[13, "access-control-settings"]], "Access Management Principles": [[4, "access-management-principles"]], "Access Request Management": [[4, "access-request-management"]], "Access Review and Certification": [[4, "access-review-and-certification"]], "Access Usage Monitoring": [[4, "access-usage-monitoring"]], "Adding New Documentation": [[3, "adding-new-documentation"]], "Adding New Features": [[20, "adding-new-features"]], "Additional Documentation (Future)": [[1, "additional-documentation-future"]], "Administrative Controls": [[40, "administrative-controls"], [40, "id2"]], "Administrative Workflow": [[68, "administrative-workflow"]], "Administrator Dashboard Features": [[68, "administrator-dashboard-features"]], "Administrators": [[72, "administrators"]], "Administrators Guide": [[68, null]], "Advanced Analysis Features": [[67, "advanced-analysis-features"]], "Advanced Analytics": [[24, "advanced-analytics"], [73, "advanced-analytics"]], "Advanced Analytics and Executive Communication Framework": [[76, "advanced-analytics-and-executive-communication-framework"]], "Advanced Attack Path Discovery and Exploitation Framework": [[75, "advanced-attack-path-discovery-and-exploitation-framework"]], "Advanced Attack Path Visualization and Analysis": [[77, "advanced-attack-path-visualization-and-analysis"]], "Advanced Attack Simulation and Specialized Testing Scenarios": [[75, "advanced-attack-simulation-and-specialized-testing-scenarios"]], "Advanced Build System": [[3, "advanced-build-system"]], "Advanced Compliance Platform Capabilities": [[70, "advanced-compliance-platform-capabilities"]], "Advanced Configuration": [[36, "advanced-configuration"]], "Advanced Detection Engineering and Security Control Optimization": [[74, "advanced-detection-engineering-and-security-control-optimization"]], "Advanced Discovery Features": [[66, "advanced-discovery-features"]], "Advanced Escalation Management and Communication Framework": [[77, "advanced-escalation-management-and-communication-framework"]], "Advanced Executive Security Platform Capabilities": [[71, "advanced-executive-security-platform-capabilities"]], "Advanced Features": [[78, "advanced-features"]], "Advanced Incident Investigation Methodologies": [[77, "advanced-incident-investigation-methodologies"]], "Advanced Optimizations": [[33, "advanced-optimizations"]], "Advanced Platform Capabilities for Security Architecture": [[76, "advanced-platform-capabilities-for-security-architecture"]], "Advanced Purple Team Platform Capabilities": [[74, "advanced-purple-team-platform-capabilities"]], "Advanced Real-time Monitoring and Threat Detection": [[77, "advanced-real-time-monitoring-and-threat-detection"]], "Advanced Red Team Platform Capabilities": [[75, "advanced-red-team-platform-capabilities"]], "Advanced Regulatory Framework Management": [[70, "advanced-regulatory-framework-management"]], "Advanced Reporting and Documentation Framework": [[77, "advanced-reporting-and-documentation-framework"]], "Advanced Reporting and Performance Metrics Framework": [[75, "advanced-reporting-and-performance-metrics-framework"]], "Advanced Risk Assessment and Management Framework": [[70, "advanced-risk-assessment-and-management-framework"]], "Advanced Risk Assessment and Quantitative Analysis Framework": [[76, "advanced-risk-assessment-and-quantitative-analysis-framework"]], "Advanced Security Architecture Design and Implementation": [[76, "advanced-security-architecture-design-and-implementation"]], "Advanced Support and Advisory Services": [[71, "advanced-support-and-advisory-services"]], "Advanced Support and Troubleshooting Framework": [[70, "advanced-support-and-troubleshooting-framework"]], "Advanced Troubleshooting and Technical Support": [[75, "advanced-troubleshooting-and-technical-support"]], "Advanced Troubleshooting and Technical Support Framework": [[74, "advanced-troubleshooting-and-technical-support-framework"]], "After Expansion:": [[0, "after-expansion"]], "Agent-Based Discovery": [[66, "agent-based-discovery"]], "Alembic Migration Script": [[54, "alembic-migration-script"]], "Alert Configuration": [[39, "alert-configuration"]], "Alert Escalation Matrix": [[32, "alert-escalation-matrix"]], "Alert Response Procedures": [[32, "alert-response-procedures"]], "Algorithm Optimization": [[34, "algorithm-optimization"]], "Analysis Configuration": [[69, "analysis-configuration"]], "Analysis Guidelines": [[67, "analysis-guidelines"]], "Analysis Types": [[67, "analysis-types"]], "Analytics and Monitoring": [[3, "analytics-and-monitoring"]], "Analyze Attack Paths": [[6, "analyze-attack-paths"]], "Analyze Attack Patterns": [[9, "analyze-attack-patterns"]], "Application Architecture": [[15, "application-architecture"]], "Application Data Backup": [[30, "application-data-backup"]], "Application Deployment": [[14, "application-deployment"], [16, "application-deployment"]], "Application Health Check": [[32, "application-health-check"]], "Application Monitoring": [[2, "application-monitoring"]], "Application Optimization": [[33, "application-optimization"], [34, "application-optimization"]], "Application Recovery": [[30, "application-recovery"]], "Application Security Layer": [[37, "application-security-layer"]], "Application Settings": [[13, "application-settings"]], "Application Startup Issues": [[17, "application-startup-issues"]], "Application Updates": [[31, "application-updates"]], "Architecture": [[26, null], [29, "architecture"]], "Architecture & Design": [[26, "architecture-design"]], "Architecture Documentation Requirements": [[44, "id4"]], "Architecture Principles": [[37, "architecture-principles"]], "Architecture Review Areas": [[44, "id2"]], "Architecture Review Process": [[44, "architecture-review-process"]], "Architecture Security Review": [[49, "id2"]], "Architecture Security Reviews": [[44, "architecture-security-reviews"]], "Architecture and Design": [[60, "architecture-and-design"]], "Article 15 - Right of Access \u2705": [[38, "article-15-right-of-access"]], "Article 16 - Right to Rectification \u2705": [[38, "article-16-right-to-rectification"]], "Article 17 - Right to Erasure \u2705": [[38, "article-17-right-to-erasure"]], "Article 18 - Right to Restriction \u2705": [[38, "article-18-right-to-restriction"]], "Article 20 - Right to Data Portability \u2705": [[38, "article-20-right-to-data-portability"]], "Article 21 - Right to Object \u2705": [[38, "article-21-right-to-object"]], "Article 33 - Notification to Supervisory Authority": [[38, "article-33-notification-to-supervisory-authority"]], "Article 34 - Communication to Data Subject": [[38, "article-34-communication-to-data-subject"]], "Article 5 - Principles of Processing": [[38, "article-5-principles-of-processing"]], "Assess Risk": [[10, "assess-risk"]], "Assessment Methodology": [[46, "assessment-methodology"]], "Assessment Results Summary": [[46, "assessment-results-summary"]], "Asset CRUD Operations": [[5, "asset-crud-operations"]], "Asset Data Models": [[60, "asset-data-models"]], "Asset Discovery": [[5, "asset-discovery"]], "Asset Discovery Use Cases": [[66, null]], "Asset Discovery and Management": [[72, "asset-discovery-and-management"]], "Asset Management": [[8, "asset-management"], [36, "asset-management"]], "Asset Management API": [[5, null]], "Asset Management \u2705": [[25, "asset-management"]], "Asset Relationships": [[5, "asset-relationships"]], "Asset Tags and Metadata": [[5, "asset-tags-and-metadata"]], "Asset Type Mapping": [[29, "asset-type-mapping"]], "Asset Types and Risk Assessment": [[29, "asset-types-and-risk-assessment"]], "Attack Path Analysis": [[8, "attack-path-analysis"], [65, "attack-path-analysis"], [72, "attack-path-analysis"]], "Attack Path Analysis API": [[6, null]], "Attack Path Analysis Architecture": [[57, null]], "Attack Path Analysis Flows": [[58, null]], "Attack Path Analysis Schema": [[59, "attack-path-analysis-schema"]], "Attack Path Analysis Tables": [[59, "attack-path-analysis-tables"]], "Attack Path Analysis Use Cases": [[67, null]], "Attack Path Analysis User Guide": [[69, null]], "Attack Path Discovery": [[69, "attack-path-discovery"]], "Attack Path Discovery Flow": [[58, "attack-path-discovery-flow"]], "Attack Path Intelligence Engine": [[77, "attack-path-intelligence-engine"]], "Attack Path Types": [[69, "attack-path-types"]], "Attack Pattern Analysis": [[9, "attack-pattern-analysis"], [73, "attack-pattern-analysis"]], "Attack Scenario Creation": [[69, "attack-scenario-creation"]], "Attack Scenario Creation Flow": [[58, "attack-scenario-creation-flow"]], "Attack Scenario Modeling": [[69, "attack-scenario-modeling"]], "Attack Simulation": [[78, "attack-simulation"]], "AttackPathAnalyzer": [[57, "attackpathanalyzer"]], "Attribute Threat Actor": [[9, "attribute-threat-actor"]], "Audit Event Structure": [[39, "audit-event-structure"]], "Audit Monitoring Dashboard": [[39, "audit-monitoring-dashboard"]], "Audit Service Architecture": [[39, "audit-service-architecture"]], "Audit Strategy": [[39, "audit-strategy"]], "Authentication": [[5, "authentication"], [6, "authentication"], [8, "authentication"], [9, "authentication"], [10, "authentication"]], "Authentication & Authorization Issues": [[64, "authentication-authorization-issues"]], "Authentication & User Management": [[8, "authentication-user-management"]], "Authentication API": [[7, null]], "Authentication Endpoints": [[7, "authentication-endpoints"]], "Authentication Flow": [[7, "authentication-flow"]], "Authentication Security Tests": [[51, "authentication-security-tests"]], "Authentication Settings": [[13, "authentication-settings"]], "Authentication Setup": [[29, "authentication-setup"]], "Authorization Security Tests": [[51, "authorization-security-tests"]], "Auto-fix Capabilities": [[52, "auto-fix-capabilities"]], "Automated Assessment Engine": [[25, "automated-assessment-engine"]], "Automated Attribution Engine": [[73, "automated-attribution-engine"]], "Automated Checks": [[18, "automated-checks"]], "Automated Compliance Checks": [[12, "automated-compliance-checks"]], "Automated Compliance Monitoring and Management": [[70, "automated-compliance-monitoring-and-management"]], "Automated DAST Pipeline": [[51, "automated-dast-pipeline"]], "Automated Daily Backup": [[30, "automated-daily-backup"]], "Automated Health Checks": [[32, "automated-health-checks"]], "Automated Remediation": [[52, "automated-remediation"]], "Automated Security Scanning": [[2, "automated-security-scanning"], [49, "automated-security-scanning"]], "Automated Visualization": [[73, "automated-visualization"]], "Automated Vulnerability Scanning": [[48, "automated-vulnerability-scanning"]], "Automatic Data Synchronization": [[73, "automatic-data-synchronization"]], "Automation Tools": [[21, "automation-tools"]], "Availability (A1)": [[38, "availability-a1"]], "Azure Authentication": [[29, "azure-authentication"]], "Azure Configuration": [[29, "azure-configuration"]], "Azure SDK": [[29, "azure-sdk"]], "Azure Services": [[29, "azure-services"]], "Backend Debugging": [[20, "backend-debugging"]], "Backend Tests": [[2, "backend-tests"]], "Backup Monitoring and Validation": [[30, "backup-monitoring-and-validation"]], "Backup Procedures": [[30, "backup-procedures"]], "Backup Verification": [[30, "backup-verification"]], "Backup and Disaster Recovery": [[68, "backup-and-disaster-recovery"]], "Backup and Recovery Runbooks - Blast-Radius Security Tool": [[30, null]], "Backup and Recovery Setup": [[16, "backup-and-recovery-setup"]], "Backup and Recovery Strategy": [[15, "backup-and-recovery-strategy"]], "Base Model with Compliance Extensions": [[54, "base-model-with-compliance-extensions"]], "Base URL": [[5, "base-url"], [6, "base-url"], [8, "base-url"], [9, "base-url"], [10, "base-url"]], "Basic ATT&CK Integration Workflow": [[73, "basic-att-ck-integration-workflow"]], "Basic Attack Path Analysis": [[69, "basic-attack-path-analysis"]], "Basic Commands": [[3, "basic-commands"]], "Basic Configuration": [[36, "basic-configuration"]], "Basic Test Structure": [[63, "basic-test-structure"]], "Basic Threat Modeling Workflow": [[78, "basic-threat-modeling-workflow"]], "Batch Correlate Events": [[9, "batch-correlate-events"]], "Before Expansion:": [[0, "before-expansion"]], "Before You Start": [[19, "before-you-start"]], "Behavioral Analytics": [[73, "behavioral-analytics"]], "Benchmark Results": [[33, "benchmark-results"]], "Best Practices": [[2, "best-practices"], [4, "best-practices"], [6, "best-practices"], [7, "best-practices"], [8, "best-practices"], [11, "best-practices"], [12, "best-practices"], [13, "best-practices"], [15, "best-practices"], [21, "best-practices"], [34, "best-practices"], [39, "best-practices"], [53, "best-practices"], [62, "best-practices"], [63, "best-practices"], [66, "best-practices"], [67, "best-practices"], [69, "best-practices"], [72, "best-practices"], [73, "best-practices"], [78, "best-practices"]], "Best Practices and Guidelines": [[44, "best-practices-and-guidelines"], [52, "best-practices-and-guidelines"]], "Best Practices for Platform Administration": [[68, "best-practices-for-platform-administration"]], "Blast Radius Algorithm": [[57, "blast-radius-algorithm"]], "Blast Radius Analysis": [[69, "blast-radius-analysis"]], "Blast Radius Assessment": [[67, "id4"]], "Blast Radius Calculation": [[69, "blast-radius-calculation"]], "Blast Radius Calculation Flow": [[58, "blast-radius-calculation-flow"]], "Blast-Radius Security Tool - Complete User Guide Documentation": [[1, null]], "Blast-Radius Security Tool - Enhanced PRD v2.0": [[24, null]], "Blast-Radius Security Tool Documentation": [[26, null]], "BlastRadiusAPIDown": [[32, "blastradiusapidown"]], "Blue-Green Deployment": [[15, "blue-green-deployment"]], "Branch Naming Conventions": [[21, "branch-naming-conventions"]], "Branch Types and Purposes": [[21, "id1"]], "Branching Strategy": [[21, "branching-strategy"]], "Breach Notification Procedures": [[38, "breach-notification-procedures"]], "Bug Bounty Program": [[45, "bug-bounty-program"]], "Build Commands": [[3, "build-commands"]], "Build Images": [[2, "build-images"]], "Building on Current Strengths": [[35, "building-on-current-strengths"]], "Bulk Import": [[5, "bulk-import"]], "Bulk Operations": [[5, "bulk-operations"]], "Bulk Update": [[5, "bulk-update"]], "Business Impact": [[47, "business-impact"]], "Business Impact Assessment": [[48, "business-impact-assessment"]], "Business Impact Levels": [[48, "id5"]], "Business Impact and Value": [[50, "business-impact-and-value"]], "Business KPIs": [[23, "business-kpis"]], "Business Logic Tests": [[51, "business-logic-tests"]], "Business Metrics": [[24, "business-metrics"], [61, "business-metrics"]], "Business Risks": [[61, "business-risks"]], "Business Security Metrics": [[50, "id7"]], "Business-Aligned Security Performance Management": [[71, "business-aligned-security-performance-management"]], "CI/CD Pipeline": [[62, "ci-cd-pipeline"]], "CI/CD Pipeline Configuration": [[14, "ci-cd-pipeline-configuration"]], "CI/CD Security Integration": [[46, "ci-cd-security-integration"]], "CI/CD Security Pipeline": [[52, "ci-cd-security-pipeline"]], "CR-001: Data Protection": [[61, "cr-001-data-protection"]], "CR-002: Security Frameworks": [[61, "cr-002-security-frameworks"]], "CVSS Risk Levels": [[46, "id4"]], "CVSS Scoring": [[48, "cvss-scoring"]], "Cache Optimization": [[32, "cache-optimization"]], "Caching Strategies": [[34, "caching-strategies"]], "Caching Strategy": [[57, "caching-strategy"]], "Caching Strategy Decision Flow": [[58, "caching-strategy-decision-flow"]], "Caching and Incremental Updates": [[29, "caching-and-incremental-updates"]], "Calculate Blast Radius": [[6, "calculate-blast-radius"]], "Can I customize risk scoring?": [[65, "can-i-customize-risk-scoring"]], "Can I run this in an air-gapped environment?": [[65, "can-i-run-this-in-an-air-gapped-environment"]], "Can I use the API for automation?": [[65, "can-i-use-the-api-for-automation"]], "Can\u2019t Access Web Interface": [[36, "can-t-access-web-interface"]], "Capacity Planning Review": [[31, "capacity-planning-review"]], "Certificate Renewal": [[31, "certificate-renewal"]], "Change Password": [[7, "change-password"]], "Clear Analysis Cache": [[6, "clear-analysis-cache"]], "Client Integration \ud83d\udd04": [[25, "client-integration"]], "Cloud Provider Discovery": [[66, "cloud-provider-discovery"]], "Cloud Provider Integration": [[36, "cloud-provider-integration"], [64, "cloud-provider-integration"]], "Cloud Provider Settings": [[13, "cloud-provider-settings"]], "Code Documentation": [[18, "code-documentation"]], "Code Examples": [[3, "code-examples"]], "Code Examples Added:": [[0, "code-examples-added"]], "Code Organization": [[21, "code-organization"]], "Code Quality Tools": [[20, "code-quality-tools"]], "Code Review Checklist": [[19, "code-review-checklist"]], "Code Review Process": [[21, "code-review-process"], [44, "code-review-process"]], "Code Review Security Checklist": [[44, "id1"]], "Code Security Review Process": [[49, "code-security-review-process"]], "Code Security Reviews": [[44, "code-security-reviews"]], "Code Standards": [[19, "code-standards"]], "Code Standards & Style Guide": [[18, null]], "Code Style": [[18, "code-style"], [18, "id1"]], "Code of Conduct": [[19, "code-of-conduct"]], "Code-Level Optimizations": [[34, "code-level-optimizations"]], "Collaboration": [[21, "collaboration"]], "Commit Message Standards": [[21, "commit-message-standards"]], "Commit Types": [[21, "id2"]], "Common Administrative Issues": [[68, "common-administrative-issues"]], "Common Development Tasks": [[20, "common-development-tasks"]], "Common Error Codes": [[5, "common-error-codes"]], "Common Issues": [[2, "common-issues"], [4, "common-issues"], [11, "common-issues"], [12, "common-issues"], [20, "common-issues"], [27, "common-issues"], [29, "common-issues"], [33, "common-issues"], [39, "common-issues"], [66, "common-issues"], [69, "common-issues"]], "Common Issues & Troubleshooting": [[64, null]], "Common Issues and Solutions": [[17, "common-issues-and-solutions"]], "Common Pitfalls": [[34, "common-pitfalls"]], "Common Quick Start Issues": [[36, "common-quick-start-issues"]], "Common Response Formats": [[8, "common-response-formats"]], "Common Security Review Pitfalls": [[44, "common-security-review-pitfalls"]], "Common Testing Patterns": [[63, "common-testing-patterns"]], "Common Workflows": [[72, "common-workflows"]], "Communication": [[19, "communication"], [41, "communication"]], "Communication Guidelines": [[45, "communication-guidelines"]], "Communication Procedures": [[32, "communication-procedures"], [43, "communication-procedures"]], "Communications Lead": [[41, "communications-lead"]], "Community Contribution": [[3, "community-contribution"]], "Community Guidelines": [[19, "community-guidelines"]], "Compensating Controls": [[48, "compensating-controls"]], "Complete System Recovery": [[17, "complete-system-recovery"], [30, "complete-system-recovery"]], "Compliance Automation": [[24, "compliance-automation"]], "Compliance Coverage": [[28, "compliance-coverage"]], "Compliance Documentation - Blast-Radius Security Tool": [[38, null]], "Compliance Framework": [[40, "compliance-framework"]], "Compliance Framework Schema - Technical Specification": [[54, null]], "Compliance Framework Status": [[50, "id4"]], "Compliance Impact Analysis": [[10, "compliance-impact-analysis"], [78, "compliance-impact-analysis"]], "Compliance Integration": [[39, "compliance-integration"]], "Compliance Monitoring": [[12, "compliance-monitoring"]], "Compliance Monitoring and Reporting": [[38, "compliance-monitoring-and-reporting"]], "Compliance Officers": [[72, "compliance-officers"]], "Compliance Officers Comprehensive Guide": [[70, null]], "Compliance Report Elements": [[66, "id3"]], "Compliance Reporting": [[12, "compliance-reporting"], [39, "compliance-reporting"]], "Compliance Requirements": [[61, "compliance-requirements"]], "Compliance Review Process": [[49, "compliance-review-process"]], "Compliance Review Workflow": [[49, "compliance-review-workflow"]], "Compliance and Audit": [[46, "compliance-and-audit"]], "Compliance and Regulatory Excellence": [[50, "compliance-and-regulatory-excellence"]], "Compliance and Standards": [[37, "compliance-and-standards"]], "Comprehensive Audit Management and Preparation": [[70, "comprehensive-audit-management-and-preparation"]], "Comprehensive Compliance and Governance Framework": [[76, "comprehensive-compliance-and-governance-framework"]], "Comprehensive Coverage": [[0, "comprehensive-coverage"], [1, "comprehensive-coverage"]], "Comprehensive Daily Operational Workflows": [[77, "comprehensive-daily-operational-workflows"]], "Comprehensive Dashboard Architecture": [[77, "comprehensive-dashboard-architecture"]], "Comprehensive Incident Response Framework": [[77, "comprehensive-incident-response-framework"]], "Comprehensive Purple Team Methodology and Framework": [[74, "comprehensive-purple-team-methodology-and-framework"]], "Comprehensive Purple Team Scenarios and Implementation Framework": [[74, "comprehensive-purple-team-scenarios-and-implementation-framework"]], "Comprehensive Red Team Methodology and Campaign Framework": [[75, "comprehensive-red-team-methodology-and-campaign-framework"]], "Comprehensive Scenario-Based Training and Response Procedures": [[77, "comprehensive-scenario-based-training-and-response-procedures"]], "Comprehensive Support Resources and Professional Development": [[77, "comprehensive-support-resources-and-professional-development"]], "Comprehensive User Ecosystem": [[26, "comprehensive-user-ecosystem"]], "Conclusion": [[4, "conclusion"], [11, "conclusion"], [12, "conclusion"], [15, "conclusion"], [28, "conclusion"], [39, "conclusion"], [43, "conclusion"], [44, "conclusion"], [45, "conclusion"], [46, "conclusion"], [47, "conclusion"], [48, "conclusion"], [50, "conclusion"], [51, "conclusion"], [52, "conclusion"], [53, "conclusion"], [68, "conclusion"], [69, "conclusion"]], "Conclusion: Excellence in Compliance and Governance": [[70, "conclusion-excellence-in-compliance-and-governance"]], "Conclusion: Excellence in Executive Security Leadership": [[71, "conclusion-excellence-in-executive-security-leadership"]], "Conclusion: Excellence in Purple Team Operations and Collaborative Security": [[74, "conclusion-excellence-in-purple-team-operations-and-collaborative-security"]], "Conclusion: Excellence in Red Team Operations": [[75, "conclusion-excellence-in-red-team-operations"]], "Conclusion: Excellence in SOC Operations": [[77, "conclusion-excellence-in-soc-operations"]], "Conclusion: Excellence in Security Architecture Leadership": [[76, "conclusion-excellence-in-security-architecture-leadership"]], "Concurrent Discovery": [[29, "concurrent-discovery"]], "Confidentiality (C1)": [[38, "confidentiality-c1"]], "Configuration": [[27, "configuration"], [29, "configuration"], [39, "configuration"]], "Configuration Backup": [[30, "configuration-backup"]], "Configuration Example": [[12, "configuration-example"]], "Configuration Guide": [[13, null]], "Configuration Hardening": [[48, "configuration-hardening"]], "Configuration Issues": [[64, "configuration-issues"]], "Configuration Management": [[4, "configuration-management"]], "Configuration Recovery": [[30, "configuration-recovery"]], "Configuration Templates:": [[0, "configuration-templates"]], "Configuration Validation": [[13, "configuration-validation"]], "Configuration and Usage": [[65, "configuration-and-usage"]], "Configuring Integrations": [[27, "configuring-integrations"]], "Congratulations!": [[36, "congratulations"]], "Consent Management System": [[12, "consent-management-system"]], "Contact Information": [[45, "contact-information"]], "Container Debugging": [[2, "container-debugging"]], "Container Discovery": [[66, "container-discovery"]], "Container Issues": [[2, "container-issues"]], "Container Optimization": [[34, "container-optimization"]], "Content Quality": [[1, "content-quality"]], "Content Volume": [[1, "content-volume"]], "Continuous Evolution": [[3, "continuous-evolution"]], "Continuous Improvement": [[37, "continuous-improvement"], [40, "continuous-improvement"], [46, "continuous-improvement"], [48, "continuous-improvement"], [51, "continuous-improvement"]], "Continuous Integration": [[3, "continuous-integration"], [21, "continuous-integration"], [62, "continuous-integration"], [63, "continuous-integration"]], "Continuous Monitoring": [[10, "continuous-monitoring"], [38, "continuous-monitoring"], [40, "continuous-monitoring"]], "Continuous Risk Monitoring": [[78, "continuous-risk-monitoring"]], "Continuous Security": [[47, "continuous-security"]], "Continuous Security Monitoring": [[52, "continuous-security-monitoring"]], "Contributing": [[3, "contributing"]], "Contributing Guide": [[19, null]], "Contributing Guidelines": [[60, "contributing-guidelines"]], "Contributing and Development": [[60, "contributing-and-development"]], "Contribution Workflow": [[19, "contribution-workflow"]], "Control Implementation Matrix": [[38, "control-implementation-matrix"]], "Core Components": [[4, "core-components"], [11, "core-components"], [12, "core-components"], [15, "core-components"], [24, "core-components"], [39, "core-components"], [55, "core-components"], [56, "core-components"], [57, "core-components"]], "Core Concepts": [[69, "core-concepts"]], "Core Configuration": [[13, "core-configuration"]], "Core Entity Relationship Diagram": [[59, "core-entity-relationship-diagram"]], "Core Infrastructure \u2705": [[25, "core-infrastructure"]], "Core Platform Capabilities for SOC Operations": [[77, "core-platform-capabilities-for-soc-operations"]], "Core Principles": [[18, "core-principles"]], "Core Response Team": [[43, "core-response-team"]], "Core System Settings": [[68, "core-system-settings"]], "Core Tables": [[54, "core-tables"], [59, "core-tables"]], "Core Value Proposition": [[61, "core-value-proposition"]], "Correlate Security Event": [[9, "correlate-security-event"]], "Correlation Accuracy": [[73, "correlation-accuracy"]], "Coverage Completeness": [[1, "coverage-completeness"]], "Coverage Reports": [[62, "coverage-reports"]], "Coverage Requirements": [[63, "coverage-requirements"]], "Coverage Requirements by Component": [[63, "id1"]], "Coverage and Quality": [[63, "coverage-and-quality"]], "Create Asset": [[5, "create-asset"]], "Create Attack Scenario": [[6, "create-attack-scenario"]], "Create Relationship": [[5, "create-relationship"]], "Creating Admin User": [[27, "creating-admin-user"]], "Credential Management": [[29, "credential-management"]], "Crisis Management and Business Continuity": [[71, "crisis-management-and-business-continuity"]], "Critical (CVSS 9.0-10.0)": [[45, "critical-cvss-9-0-10-0"]], "Critical (P0)": [[41, "critical-p0"]], "Critical Alerts (P0)": [[32, "critical-alerts-p0"]], "Critical Security Issues Resolved": [[47, "critical-security-issues-resolved"]], "Critical Vulnerabilities (CVSS 9.0+)": [[41, "critical-vulnerabilities-cvss-9-0"]], "Cross-Functional Collaboration and Integration": [[70, "cross-functional-collaboration-and-integration"]], "Cross-References": [[3, "cross-references"]], "Current Security Posture": [[46, "current-security-posture"]], "Current State \u2705": [[23, "current-state"]], "Current Status Integration": [[24, "current-status-integration"]], "Current Vulnerability Status": [[46, "current-vulnerability-status"]], "Custom CSS (_static/custom.css)": [[3, "custom-css-static-custom-css"]], "Custom Integrations": [[60, "custom-integrations"]], "Custom Security Rules": [[53, "custom-security-rules"]], "Custom Sphinx Roles": [[3, "custom-sphinx-roles"]], "D3.js Components": [[25, "d3-js-components"]], "DAST Automation and CI/CD Integration": [[51, "dast-automation-and-ci-cd-integration"]], "DAST Benefits": [[51, "dast-benefits"]], "DAST Best Practices": [[51, "dast-best-practices"]], "DAST Results Analysis": [[51, "dast-results-analysis"]], "DAST Test Categories": [[51, "dast-test-categories"]], "DAST Tools and Coverage": [[46, "id2"], [48, "id2"]], "DAST Tools and Implementation": [[51, "dast-tools-and-implementation"]], "Daily Development Cycle": [[2, "daily-development-cycle"]], "Daily Maintenance (Automated)": [[31, "daily-maintenance-automated"]], "Daily Performance Checks": [[32, "daily-performance-checks"]], "Dashboard Overview": [[36, "dashboard-overview"], [68, "dashboard-overview"]], "Data Architecture": [[15, "data-architecture"]], "Data Breach Management": [[12, "data-breach-management"]], "Data Breach Response": [[41, "data-breach-response"]], "Data Classification": [[11, "data-classification"]], "Data Issues": [[64, "data-issues"]], "Data Models and Schemas": [[60, "data-models-and-schemas"]], "Data Processing Records": [[12, "data-processing-records"]], "Data Processing Workflows": [[12, "data-processing-workflows"]], "Data Protection": [[11, "data-protection"], [29, "data-protection"], [47, "data-protection"]], "Data Protection Principles": [[38, "data-protection-principles"]], "Data Retention and Archival": [[59, "data-retention-and-archival"]], "Data Security Layer": [[37, "data-security-layer"]], "Data Subject Rights Implementation": [[38, "data-subject-rights-implementation"]], "Data Subject Rights Management": [[12, "data-subject-rights-management"]], "Database Architecture": [[59, "database-architecture"]], "Database Backup Procedures": [[30, "database-backup-procedures"]], "Database Configuration": [[13, "database-configuration"]], "Database Connection Issues": [[16, "database-connection-issues"], [64, "database-connection-issues"]], "Database Design": [[60, "database-design"]], "Database Design and Schema": [[59, null]], "Database Issues": [[2, "database-issues"], [17, "database-issues"]], "Database Maintenance": [[31, "database-maintenance"], [31, "id1"], [59, "database-maintenance"]], "Database Management": [[20, "database-management"]], "Database Optimization": [[32, "database-optimization"], [33, "database-optimization"], [34, "database-optimization"]], "Database Recovery": [[30, "database-recovery"]], "Database Rollback": [[16, "database-rollback"]], "Database Schema": [[12, "database-schema"], [39, "database-schema"]], "Database Standards": [[18, "database-standards"]], "DatabaseConnectionsHigh": [[32, "databaseconnectionshigh"]], "Debug Mode": [[2, "debug-mode"], [29, "debug-mode"]], "Debugging": [[20, "debugging"]], "Debugging Tests": [[63, "debugging-tests"]], "Definition of Done": [[21, "definition-of-done"]], "Delete Asset": [[5, "delete-asset"]], "Dependency Scanning Tools": [[48, "id3"]], "Dependency Security Scanning": [[52, "dependency-security-scanning"]], "Deployment": [[60, "deployment"]], "Deployment & Operations": [[26, "deployment-operations"], [26, null]], "Deployment Architecture": [[28, "deployment-architecture"]], "Deployment Checklist": [[11, "deployment-checklist"]], "Deployment Gates Configuration": [[52, "deployment-gates-configuration"]], "Deployment Script": [[14, "deployment-script"]], "Deployment Strategy": [[15, "deployment-strategy"]], "Design Review Process": [[49, "design-review-process"]], "Detect (DE)": [[38, "detect-de"]], "Detection Effectiveness Analysis": [[67, "id2"]], "Developer Guidelines": [[52, "developer-guidelines"]], "Developer Responsibilities": [[49, "developer-responsibilities"]], "Developer Workstation Security": [[52, "developer-workstation-security"]], "Development": [[26, "development"], [26, null]], "Development Commands": [[3, "development-commands"]], "Development Configuration": [[14, "development-configuration"]], "Development Environment": [[13, "development-environment"], [14, "development-environment"], [60, "development-environment"]], "Development Environment Setup": [[14, "development-environment-setup"], [20, null]], "Development Process": [[21, "development-process"]], "Development Tools": [[2, "development-tools"], [16, "development-tools"], [18, "development-tools"], [21, "development-tools"]], "Development Workflow": [[3, "development-workflow"], [20, "development-workflow"], [21, null]], "Development and Integration": [[60, "development-and-integration"]], "Digital Transformation and Innovation Enablement": [[71, "digital-transformation-and-innovation-enablement"]], "Disaster Recovery": [[15, "disaster-recovery"]], "Disaster Recovery Procedures": [[30, "disaster-recovery-procedures"]], "Discovery Automation": [[66, "discovery-automation"]], "Discovery Engine Architecture": [[29, "discovery-engine-architecture"]], "Discovery Job Monitoring": [[29, "discovery-job-monitoring"]], "Discovery Methods": [[66, "discovery-methods"]], "Discovery Results": [[5, "discovery-results"]], "Discovery Results Example": [[66, "id2"]], "Discovery Status": [[5, "discovery-status"]], "Discovery Strategy": [[66, "discovery-strategy"]], "Discovery Types": [[5, "id7"]], "Docker & Traefik Integration": [[23, "docker-traefik-integration"], [24, "docker-traefik-integration"]], "Docker Commands": [[3, "docker-commands"]], "Docker Deployment": [[3, "docker-deployment"]], "Docker Development": [[3, "docker-development"]], "Docker Installation Problems": [[64, "docker-installation-problems"]], "Documentation": [[2, "documentation"], [41, "id1"]], "Documentation & Operations - 100% Complete \u2705": [[28, "documentation-operations-100-complete"]], "Documentation Build and Deployment": [[1, "documentation-build-and-deployment"]], "Documentation Completion Summary": [[1, "documentation-completion-summary"]], "Documentation Expansion Summary": [[0, null]], "Documentation Maintenance": [[1, "documentation-maintenance"]], "Documentation Metrics": [[3, "documentation-metrics"]], "Documentation Overview and Achievements": [[22, null]], "Documentation Quality:": [[0, "documentation-quality"]], "Documentation Standards": [[18, "documentation-standards"], [19, "documentation-standards"]], "Documentation Standards:": [[0, "documentation-standards"]], "Documentation Types": [[19, "documentation-types"]], "Documentation Updates": [[41, "documentation-updates"]], "Documentation and Resources": [[49, "documentation-and-resources"]], "Dynamic Application Security Testing (DAST)": [[46, "dynamic-application-security-testing-dast"], [51, null], [52, "dynamic-application-security-testing-dast"]], "Emergency Contacts": [[41, "emergency-contacts"]], "Emergency Procedures": [[17, "emergency-procedures"]], "Emergency Rollback": [[16, "emergency-rollback"]], "Enable Debug Logging": [[2, "enable-debug-logging"]], "Enable MFA": [[7, "enable-mfa"]], "Encryption Implementation": [[11, "encryption-implementation"]], "Encryption Settings": [[13, "encryption-settings"]], "End-to-End Testing": [[62, "end-to-end-testing"]], "End-to-End Tests": [[2, "end-to-end-tests"]], "Endpoints": [[6, "endpoints"]], "Enforcement": [[18, "enforcement"]], "Enhanced Audit Logging - 100% Complete \u2705": [[28, "enhanced-audit-logging-100-complete"]], "Enhanced Audit Logging System": [[39, null]], "Enhanced Capabilities \ud83c\udd95": [[23, "enhanced-capabilities"]], "Enhanced Configuration": [[3, "enhanced-configuration"]], "Enhanced Features Summary - Blast-Radius Security Tool": [[23, null]], "Enhanced Implementation Details": [[35, "enhanced-implementation-details"], [35, "id1"]], "Enhanced KPIs Building on Phase 2": [[35, "enhanced-kpis-building-on-phase-2"]], "Enhanced Security Configuration": [[47, "enhanced-security-configuration"]], "Enrich IOCs": [[9, "enrich-iocs"]], "Enterprise Risk Assessment": [[50, "enterprise-risk-assessment"]], "Enterprise Security Architecture Best Practices and Excellence": [[76, "enterprise-security-architecture-best-practices-and-excellence"]], "Enterprise Security Architecture Methodology": [[76, "enterprise-security-architecture-methodology"]], "Enumeration Types": [[59, "enumeration-types"]], "Environment Cleanup Script": [[14, "environment-cleanup-script"]], "Environment Configurations": [[2, "environment-configurations"]], "Environment Management Scripts": [[14, "environment-management-scripts"]], "Environment Setup Documentation - Blast-Radius Security Tool": [[14, null]], "Environment Types": [[14, "environment-types"]], "Environment Variable Problems": [[64, "environment-variable-problems"]], "Environment Variables": [[2, "environment-variables"], [27, "environment-variables"]], "Environment-Specific Configuration": [[13, "environment-specific-configuration"]], "Environment-Specific Configurations": [[14, "environment-specific-configurations"]], "Error Codes": [[5, "id9"]], "Error Handling": [[5, "error-handling"], [9, "error-handling"], [10, "error-handling"], [29, "error-handling"]], "Error Handling and Recovery Flow": [[58, "error-handling-and-recovery-flow"]], "Error Response": [[8, "error-response"]], "Error Response Format": [[5, "error-response-format"]], "Error Responses": [[6, "error-responses"]], "Escalation Procedures": [[17, "escalation-procedures"], [32, "escalation-procedures"]], "Essential Features Tour": [[36, "essential-features-tour"]], "Event Enrichment Pipeline": [[39, "event-enrichment-pipeline"]], "Evidence Handling": [[43, "evidence-handling"]], "Example Review Process": [[49, "example-review-process"]], "Example Security Policies": [[49, "example-security-policies"]], "Examples": [[6, "examples"], [8, "examples"]], "Exception Testing": [[63, "exception-testing"]], "Executive Leadership": [[72, "executive-leadership"]], "Executive Leadership Comprehensive Guide": [[71, null]], "Executive Overview": [[50, "executive-overview"]], "Executive Reporting": [[48, "executive-reporting"]], "Executive Summary": [[24, "executive-summary"], [26, "executive-summary"], [47, "executive-summary"], [61, "executive-summary"], [77, "executive-summary"]], "Executive Summary for Compliance Excellence": [[70, "executive-summary-for-compliance-excellence"]], "Executive Summary for Purple Team Excellence": [[74, "executive-summary-for-purple-team-excellence"]], "Executive Summary for Red Team Operations": [[75, "executive-summary-for-red-team-operations"]], "Executive Summary for Security Architects": [[76, "executive-summary-for-security-architects"]], "Executive Summary for Strategic Security Leadership": [[71, "executive-summary-for-strategic-security-leadership"]], "Export Analysis Results": [[6, "export-analysis-results"]], "Export Navigator Layer": [[9, "export-navigator-layer"]], "Extended Response Team": [[43, "extended-response-team"]], "External Communications": [[43, "external-communications"]], "External Contacts": [[41, "external-contacts"]], "External Dependencies": [[16, "external-dependencies"]], "External Support": [[17, "external-support"]], "FR-001: Attack Path Discovery": [[61, "fr-001-attack-path-discovery"]], "FR-002: Blast Radius Calculation": [[61, "fr-002-blast-radius-calculation"]], "FR-003: MITRE ATT&CK Integration": [[61, "fr-003-mitre-att-ck-integration"]], "FR-004: Attack Scenario Modeling": [[61, "fr-004-attack-scenario-modeling"]], "FR-005: Asset Discovery and Management": [[61, "fr-005-asset-discovery-and-management"]], "False Positive Management": [[53, "false-positive-management"]], "Feature Coverage": [[1, "feature-coverage"]], "Feature Development": [[2, "feature-development"]], "Feature Development Workflow": [[21, "feature-development-workflow"]], "Feature-Specific Guides": [[72, "feature-specific-guides"]], "Features by Use Case": [[26, "features-by-use-case"]], "Feedback and Contributions": [[72, "feedback-and-contributions"]], "Field Selection": [[8, "field-selection"]], "File System Backup": [[30, "file-system-backup"]], "Filtering and Sorting": [[8, "filtering-and-sorting"]], "Financial Impact Assessment": [[10, "financial-impact-assessment"]], "First Login and Setup": [[36, "first-login-and-setup"]], "Fixtures and Factories": [[62, "fixtures-and-factories"]], "Fixtures and Test Data": [[63, "fixtures-and-test-data"]], "For Administrators:": [[0, "for-administrators"]], "For Purple Team Members:": [[0, "for-purple-team-members"]], "For Red Team Members:": [[0, "for-red-team-members"]], "For SOC Operators:": [[0, "for-soc-operators"]], "For Security Architects:": [[0, "for-security-architects"]], "Framework Implementation": [[38, "framework-implementation"]], "Framework Mapping": [[39, "framework-mapping"], [57, "framework-mapping"]], "Frequently Asked Questions (FAQ)": [[65, null]], "Frontend Debugging": [[20, "frontend-debugging"]], "Frontend Issues": [[64, "frontend-issues"]], "Frontend Tests": [[2, "frontend-tests"]], "Full Application Recovery": [[30, "full-application-recovery"]], "Full Pipeline (Recommended)": [[2, "full-pipeline-recommended"]], "Functional Requirements": [[61, "functional-requirements"]], "Future Enhancements": [[29, "future-enhancements"], [46, "future-enhancements"]], "Future Roadmap": [[61, "future-roadmap"]], "Future Security Roadmap": [[50, "future-security-roadmap"]], "GCP Authentication": [[29, "gcp-authentication"]], "GCP Configuration": [[29, "gcp-configuration"]], "GCP SDK": [[29, "gcp-sdk"]], "GCP Services": [[29, "gcp-services"]], "GDPR Compliance": [[38, "gdpr-compliance"], [40, "gdpr-compliance"]], "GDPR Compliance Checklist": [[49, "gdpr-compliance-checklist"]], "GDPR Compliance Framework": [[12, null]], "GDPR Compliance Framework - 100% Complete \u2705": [[28, "gdpr-compliance-framework-100-complete"]], "GDPR Impact Assessment": [[10, "gdpr-impact-assessment"]], "GDPR Service Architecture": [[12, "gdpr-service-architecture"]], "General Guidelines": [[72, "general-guidelines"]], "General Principles": [[18, "general-principles"]], "General Questions": [[65, "general-questions"]], "General Troubleshooting Approach": [[17, "general-troubleshooting-approach"]], "Generate Mitigations": [[10, "generate-mitigations"]], "Generate Navigator Layer": [[9, "generate-navigator-layer"]], "Get Asset": [[5, "get-asset"]], "Get Attack Scenario": [[6, "get-attack-scenario"]], "Get Data Status": [[9, "get-data-status"]], "Get Event Context": [[9, "get-event-context"]], "Get Simulation Results": [[10, "get-simulation-results"]], "Get Technique Information": [[9, "get-technique-information"]], "Get Threat Actor Profile": [[9, "get-threat-actor-profile"]], "Getting Additional Help": [[64, "getting-additional-help"]], "Getting Help": [[3, "getting-help"], [17, "getting-help"], [19, "getting-help"], [20, "getting-help"], [27, "getting-help"], [36, "getting-help"], [69, "getting-help"], [72, "getting-help"]], "Getting Help and Support": [[68, "getting-help-and-support"]], "Getting Started": [[19, "getting-started"], [68, "getting-started"], [69, "getting-started"], [73, "getting-started"], [78, "getting-started"]], "Getting Started Section": [[1, "getting-started-section"]], "Getting Started:": [[0, "getting-started"]], "Getting Started: SOC Operator Onboarding": [[77, "getting-started-soc-operator-onboarding"]], "Git Flow Model": [[21, "git-flow-model"]], "GitHub Actions Integration": [[53, "github-actions-integration"]], "GitHub Actions Workflow": [[14, "github-actions-workflow"]], "Grafana Dashboard Management": [[32, "grafana-dashboard-management"]], "Graph Algorithms": [[57, "graph-algorithms"]], "Graph Analysis Engine": [[60, "graph-analysis-engine"]], "Graph Data Structures": [[60, "graph-data-structures"]], "Graph Statistics": [[6, "graph-statistics"]], "GraphEngine": [[57, "graphengine"]], "HIPAA Impact Assessment": [[10, "hipaa-impact-assessment"]], "HTTP Status Code Usage": [[18, "id3"]], "HTTP Status Codes": [[8, "http-status-codes"]], "Health Check Commands": [[64, "health-check-commands"]], "Health Checks": [[2, "health-checks"], [27, "health-checks"]], "High (CVSS 7.0-8.9)": [[45, "high-cvss-7-0-8-9"]], "High (P1)": [[41, "high-p1"]], "High Availability Design": [[15, "high-availability-design"]], "High Priority Alerts (P1)": [[32, "high-priority-alerts-p1"]], "High Priority:": [[0, "high-priority"]], "High Vulnerabilities (CVSS 7.0-8.9)": [[41, "high-vulnerabilities-cvss-7-0-8-9"]], "HighMemoryUsage": [[32, "highmemoryusage"]], "Horizontal Scaling": [[33, "horizontal-scaling"]], "How can I contribute?": [[65, "how-can-i-contribute"]], "How do I add new users?": [[65, "how-do-i-add-new-users"]], "How do I backup my data?": [[65, "how-do-i-backup-my-data"]], "How do I configure audit logging?": [[65, "how-do-i-configure-audit-logging"]], "How do I configure cloud provider integrations?": [[65, "how-do-i-configure-cloud-provider-integrations"]], "How do I enable Multi-Factor Authentication (MFA)?": [[65, "how-do-i-enable-multi-factor-authentication-mfa"]], "How do I get API credentials?": [[65, "how-do-i-get-api-credentials"]], "How do I get help?": [[65, "how-do-i-get-help"]], "How do I install the Blast-Radius Security Tool?": [[65, "how-do-i-install-the-blast-radius-security-tool"]], "How do I integrate with my SIEM?": [[65, "how-do-i-integrate-with-my-siem"]], "How do I reset the admin password?": [[65, "how-do-i-reset-the-admin-password"]], "How do I update to a new version?": [[65, "how-do-i-update-to-a-new-version"]], "How does attack path analysis work?": [[65, "how-does-attack-path-analysis-work"]], "How long does attack path analysis take?": [[65, "how-long-does-attack-path-analysis-take"]], "How to Report": [[45, "how-to-report"]], "IOC Enhancement": [[73, "ioc-enhancement"]], "IR-001: SIEM Integration": [[61, "ir-001-siem-integration"]], "IR-002: SOAR Integration": [[61, "ir-002-soar-integration"]], "IR-003: Cloud Platform Integration": [[61, "ir-003-cloud-platform-integration"]], "ISO 27001 Compliance": [[38, "iso-27001-compliance"], [40, "iso-27001-compliance"]], "Identify (ID)": [[38, "identify-id"]], "Identity Verification Service": [[11, "identity-verification-service"]], "Immediate Actions": [[28, "immediate-actions"], [36, "immediate-actions"]], "Immediate Actions (0-1 hour)": [[41, "immediate-actions-0-1-hour"]], "Immediate Actions (Phase 2.5 - Next 4-6 weeks)": [[35, "immediate-actions-phase-2-5-next-4-6-weeks"]], "Implementation Achievements": [[28, "implementation-achievements"]], "Implementation Details": [[4, "implementation-details"], [12, "implementation-details"], [39, "implementation-details"]], "Implementation Gap Analysis - Blast-Radius Security Tool": [[25, null]], "Implementation Guidelines": [[11, "implementation-guidelines"]], "Implementation Path:": [[0, "implementation-path"]], "Implementation Tips": [[11, "implementation-tips"]], "In Scope": [[45, "in-scope"]], "Incident Classification": [[41, "incident-classification"], [43, "incident-classification"]], "Incident Commander (IC)": [[41, "incident-commander-ic"]], "Incident Response": [[24, "incident-response"], [40, "incident-response"], [69, "incident-response"]], "Incident Response Metrics": [[43, "incident-response-metrics"]], "Incident Response Objectives": [[43, "incident-response-objectives"]], "Incident Response Procedures - Blast-Radius Security Tool": [[41, null]], "Incident Response Process": [[41, "incident-response-process"], [43, "incident-response-process"]], "Incident Response Team": [[41, "incident-response-team"], [43, "incident-response-team"]], "Incident Response Training": [[43, "incident-response-training"]], "Incident Response Workflow": [[72, "incident-response-workflow"]], "Indexes and Performance Optimization": [[59, "indexes-and-performance-optimization"]], "Indices and Tables": [[26, "indices-and-tables"]], "Industry Standards Compliance": [[50, "industry-standards-compliance"]], "Information Security Management System (ISMS)": [[38, "information-security-management-system-isms"]], "Infrastructure Deployment": [[16, "infrastructure-deployment"]], "Infrastructure Enhancements": [[23, "infrastructure-enhancements"], [25, "infrastructure-enhancements"]], "Infrastructure Optimization": [[34, "infrastructure-optimization"]], "Infrastructure Provisioning": [[14, "infrastructure-provisioning"]], "Infrastructure Requirements": [[16, "infrastructure-requirements"]], "Infrastructure Security Layer": [[37, "infrastructure-security-layer"]], "Infrastructure Security Review": [[49, "id1"]], "Infrastructure as Code": [[15, "infrastructure-as-code"]], "Infrastructure as Code (IaC) - 100% Complete \u2705": [[28, "infrastructure-as-code-iac-100-complete"]], "Infrastructure as Code (IaC) Review": [[49, "infrastructure-as-code-iac-review"]], "Initial Login": [[36, "initial-login"]], "Initial Platform Setup": [[68, "initial-platform-setup"]], "Initial Platform Setup and Configuration": [[77, "initial-platform-setup-and-configuration"]], "Initial Setup": [[27, "initial-setup"]], "Input Validation Tests": [[51, "input-validation-tests"]], "Installation Guide": [[20, "installation-guide"], [27, null]], "Installation Issues": [[64, "installation-issues"]], "Installation Methods": [[27, "installation-methods"]], "Installation and Setup": [[29, "installation-and-setup"], [65, "installation-and-setup"]], "Integration Configuration": [[13, "integration-configuration"]], "Integration Examples": [[7, "integration-examples"]], "Integration Issues": [[64, "integration-issues"]], "Integration Management": [[68, "integration-management"]], "Integration Requirements": [[61, "integration-requirements"]], "Integration Setup": [[36, "integration-setup"]], "Integration Testing": [[62, "integration-testing"]], "Integration and API": [[65, "integration-and-api"]], "Integration and Automation": [[67, "integration-and-automation"]], "Integration with Existing Docs:": [[0, "integration-with-existing-docs"]], "Integration with External Tools": [[66, "integration-with-external-tools"]], "Integration with SDLC": [[48, "integration-with-sdlc"]], "Integrations": [[8, "integrations"]], "Interactive Debugging": [[2, "interactive-debugging"]], "Interactive Elements": [[3, "interactive-elements"]], "Internal Communications": [[43, "internal-communications"]], "Internal Resources": [[17, "internal-resources"]], "Internal Team": [[41, "internal-team"]], "Interpretation Guidelines": [[69, "interpretation-guidelines"]], "Introduction": [[46, "introduction"]], "Is my data secure?": [[65, "is-my-data-secure"]], "Is there a community or forum?": [[65, "is-there-a-community-or-forum"]], "Issue: Database Connection Timeout": [[17, "issue-database-connection-timeout"]], "Issue: Database Migration Failures": [[17, "issue-database-migration-failures"]], "Issue: High Response Times": [[17, "issue-high-response-times"]], "Issue: Memory Leaks": [[17, "issue-memory-leaks"]], "Issue: Missing Metrics": [[17, "issue-missing-metrics"]], "Issue: Persistent Volume Problems": [[17, "issue-persistent-volume-problems"]], "Issue: Pod Stuck in CrashLoopBackOff": [[17, "issue-pod-stuck-in-crashloopbackoff"]], "Issue: Pod Stuck in Pending State": [[17, "issue-pod-stuck-in-pending-state"]], "Issue: SSL/TLS Certificate Problems": [[17, "issue-ssl-tls-certificate-problems"]], "Issue: Service Not Accessible": [[17, "issue-service-not-accessible"]], "JSONB Structure for assessment_criteria": [[54, "jsonb-structure-for-assessment-criteria"]], "JWT Token Structure": [[7, "jwt-token-structure"]], "JavaScript SDK": [[5, "javascript-sdk"]], "JavaScript/TypeScript": [[7, "javascript-typescript"]], "JavaScript/TypeScript Standards": [[18, "javascript-typescript-standards"]], "JavaScript/TypeScript Style": [[19, "javascript-typescript-style"]], "June 2025 Security Review Results": [[50, "june-2025-security-review-results"]], "Just-in-Time Access Provisioning": [[4, "just-in-time-access-provisioning"]], "Key Capabilities": [[67, "key-capabilities"]], "Key Metrics": [[53, "key-metrics"]], "Key Performance Indicators": [[33, "key-performance-indicators"], [38, "key-performance-indicators"], [48, "key-performance-indicators"]], "Key Permissions": [[68, "key-permissions"]], "Key Security Fixes Implemented": [[50, "key-security-fixes-implemented"]], "Key Security Metrics": [[40, "id6"]], "Kubernetes Cluster Updates": [[31, "kubernetes-cluster-updates"]], "Kubernetes Health Check": [[32, "kubernetes-health-check"]], "Kubernetes Infrastructure": [[15, "kubernetes-infrastructure"]], "Kubernetes Network Policies": [[11, "kubernetes-network-policies"]], "Kubernetes Security Review": [[49, "kubernetes-security-review"]], "Latest Implementations Summary - Production Ready Release": [[28, null]], "Layered Security Model": [[40, "layered-security-model"]], "Learning Resources": [[36, "learning-resources"]], "Least Privilege Access Control - 100% Complete \u2705": [[28, "least-privilege-access-control-100-complete"]], "Least Privilege Access Control Framework": [[4, null]], "Legal Considerations": [[45, "legal-considerations"]], "Legal and Regulatory Considerations": [[43, "legal-and-regulatory-considerations"]], "Leveraging Existing Infrastructure": [[35, "leveraging-existing-infrastructure"]], "License": [[26, "license"]], "List Assets": [[5, "list-assets"]], "List Relationships": [[5, "list-relationships"]], "List Threat Actors": [[10, "list-threat-actors"]], "Load Balancer Issues": [[16, "load-balancer-issues"]], "Load Balancing": [[34, "load-balancing"]], "Load Testing": [[33, "load-testing"]], "Local Container Management": [[2, "local-container-management"]], "Local Deployment": [[3, "local-deployment"]], "Local Development": [[3, "local-development"], [62, "local-development"]], "Local Development & CI/CD Guide": [[2, null]], "Local Development Setup": [[14, "local-development-setup"]], "Local Performance Testing": [[2, "local-performance-testing"]], "Log Analysis": [[32, "log-analysis"]], "Log Collection": [[64, "log-collection"]], "Log Management Procedures": [[32, "log-management-procedures"]], "Log Retention Management": [[32, "log-retention-management"]], "Log Rotation and Cleanup (2:00 AM UTC)": [[31, "log-rotation-and-cleanup-2-00-am-utc"]], "Logging": [[29, "logging"]], "Logging Configuration": [[13, "logging-configuration"]], "Logical Backup Recovery": [[30, "logical-backup-recovery"]], "Login": [[7, "login"]], "Login Issues": [[36, "login-issues"]], "Login Problems": [[64, "login-problems"]], "Logout": [[7, "logout"]], "Logs and Metrics": [[2, "logs-and-metrics"]], "Long-Term Strategic Goals (2026)": [[50, "long-term-strategic-goals-2026"]], "Long-term Roadmap": [[28, "long-term-roadmap"]], "Low (CVSS 0.1-3.9)": [[45, "low-cvss-0-1-3-9"]], "Low (P3)": [[41, "low-p3"]], "Low Priority:": [[0, "low-priority"]], "MITRE ATT&CK Data Management": [[73, "mitre-att-ck-data-management"]], "MITRE ATT&CK Framework": [[40, "mitre-att-ck-framework"]], "MITRE ATT&CK Integration": [[8, "mitre-att-ck-integration"], [57, "mitre-att-ck-integration"], [67, "mitre-att-ck-integration"], [72, "mitre-att-ck-integration"]], "MITRE ATT&CK Integration API Reference": [[9, null]], "MITRE ATT&CK Integration User Guide": [[73, null]], "MITRE ATT&CK Mapping": [[6, "mitre-att-ck-mapping"]], "MITRE ATT&CK Mapping Decision Tree": [[58, "mitre-att-ck-mapping-decision-tree"]], "Machine Learning Capabilities": [[24, "machine-learning-capabilities"]], "Machine Learning Threat Prediction - Technical Specification": [[55, null]], "Maintenance": [[62, "maintenance"]], "Maintenance Procedures - Blast-Radius Security Tool": [[31, null]], "Maintenance Windows": [[31, "maintenance-windows"]], "Maintenance:": [[0, "maintenance"]], "Makefile Integration": [[51, "makefile-integration"], [53, "makefile-integration"]], "Manage Tags": [[5, "manage-tags"]], "Manifest Security Checklist": [[49, "manifest-security-checklist"]], "Manual Database Backup": [[30, "manual-database-backup"]], "Medium (CVSS 4.0-6.9)": [[45, "medium-cvss-4-0-6-9"]], "Medium (P2)": [[41, "medium-p2"]], "Medium Priority:": [[0, "medium-priority"]], "Medium-term Objectives (Phase 4 - Q4 2025)": [[35, "medium-term-objectives-phase-4-q4-2025"]], "Memory Issues": [[64, "memory-issues"]], "Mermaid Diagrams": [[3, "mermaid-diagrams"], [3, "id6"]], "Method 1: Quick Start with Docker (Recommended)": [[27, "method-1-quick-start-with-docker-recommended"]], "Method 2: Development Installation": [[27, "method-2-development-installation"]], "Method 3: Production Installation": [[27, "method-3-production-installation"]], "Metrics and Reporting": [[43, "metrics-and-reporting"], [48, "metrics-and-reporting"], [52, "metrics-and-reporting"]], "Minimum System Requirements": [[16, "minimum-system-requirements"]], "Missing or Incorrect Data": [[64, "missing-or-incorrect-data"]], "Mission Statement": [[61, "mission-statement"]], "Mitigation Strategies": [[10, "mitigation-strategies"]], "Mitigation Strategy Generation": [[78, "mitigation-strategy-generation"]], "Mocking and Patching": [[63, "mocking-and-patching"]], "Monitoring": [[60, "monitoring"]], "Monitoring & Dashboards": [[8, "monitoring-dashboards"]], "Monitoring Architecture": [[15, "monitoring-architecture"]], "Monitoring Dashboard Procedures": [[32, "monitoring-dashboard-procedures"]], "Monitoring Runbooks - Blast-Radius Security Tool": [[32, null]], "Monitoring Settings": [[13, "monitoring-settings"]], "Monitoring Setup": [[33, "monitoring-setup"]], "Monitoring and Alerting": [[11, "monitoring-and-alerting"], [29, "monitoring-and-alerting"], [39, "monitoring-and-alerting"], [41, "monitoring-and-alerting"]], "Monitoring and Alerting Issues": [[17, "monitoring-and-alerting-issues"]], "Monitoring and Analytics": [[4, "monitoring-and-analytics"]], "Monitoring and Logging": [[13, "monitoring-and-logging"]], "Monitoring and Logging Layer": [[37, "monitoring-and-logging-layer"]], "Monitoring and Maintenance": [[16, "monitoring-and-maintenance"], [68, "monitoring-and-maintenance"]], "Monitoring and Profiling": [[34, "monitoring-and-profiling"]], "Monitoring \ud83d\udd04": [[25, "monitoring"]], "Monthly Maintenance (First Sunday 4:00 AM UTC)": [[31, "monthly-maintenance-first-sunday-4-00-am-utc"]], "Morning Health Check (9:00 AM)": [[32, "morning-health-check-9-00-am"]], "Multi-Cloud Integration Guide": [[29, null]], "Multi-Environment Support": [[2, "multi-environment-support"]], "Multi-Factor Authentication": [[7, "multi-factor-authentication"]], "Multi-Factor Authentication (Recommended)": [[36, "multi-factor-authentication-recommended"]], "Multi-Factor Risk Assessment": [[57, "multi-factor-risk-assessment"]], "Multi-Framework Schema": [[25, "multi-framework-schema"]], "Multi-Layered Security Approach": [[50, "multi-layered-security-approach"]], "Multi-Level Caching": [[34, "multi-level-caching"]], "NFR-001: Performance": [[61, "nfr-001-performance"]], "NFR-002: Scalability": [[61, "nfr-002-scalability"]], "NFR-003: Security": [[61, "nfr-003-security"]], "NFR-004: Reliability": [[61, "nfr-004-reliability"]], "NFR-005: Usability": [[61, "nfr-005-usability"]], "NIST CSF Framework Seed Data": [[54, "nist-csf-framework-seed-data"]], "NIST Cybersecurity Framework": [[38, "nist-cybersecurity-framework"]], "Navigation": [[3, "navigation"]], "Neo4j Optimization": [[34, "neo4j-optimization"]], "Network Architecture": [[15, "network-architecture"]], "Network Connectivity Issues": [[64, "network-connectivity-issues"]], "Network Requirements": [[16, "network-requirements"]], "Network Security": [[11, "network-security"], [29, "network-security"]], "Network and Connectivity Issues": [[17, "network-and-connectivity-issues"]], "Network-Based Discovery": [[66, "network-based-discovery"]], "New Dependencies Required": [[23, "new-dependencies-required"], [25, "new-dependencies-required"]], "New Feature Workflow": [[2, "new-feature-workflow"]], "Next Steps": [[16, "next-steps"], [20, "next-steps"], [21, "next-steps"], [27, "next-steps"], [33, "next-steps"], [34, "next-steps"], [36, "next-steps"], [40, "next-steps"], [62, "next-steps"], [63, "next-steps"], [66, "next-steps"], [67, "next-steps"]], "Next Steps and Recommendations": [[28, "next-steps-and-recommendations"]], "Non-Functional Requirements": [[61, "non-functional-requirements"]], "Notification Requirements": [[41, "notification-requirements"]], "One-Command Setup": [[2, "one-command-setup"]], "Operational Benefits": [[47, "operational-benefits"]], "Operational Excellence": [[15, "operational-excellence"], [68, "operational-excellence"]], "Operational Guidelines": [[4, "operational-guidelines"], [12, "operational-guidelines"], [39, "operational-guidelines"]], "Operational Readiness": [[14, "operational-readiness"]], "Optimization Guidelines": [[34, "optimization-guidelines"]], "Optimization Recommendations": [[33, "optimization-recommendations"]], "Optimization Strategy": [[34, "optimization-strategy"]], "Original Phase 3 Goals": [[35, "original-phase-3-goals"]], "Original Phase 4 Goals": [[35, "original-phase-4-goals"]], "Out of Scope": [[45, "out-of-scope"]], "Overview": [[0, "overview"], [4, "overview"], [5, "overview"], [6, "overview"], [7, "overview"], [8, "overview"], [9, "overview"], [10, "overview"], [11, "overview"], [12, "overview"], [13, "overview"], [14, "overview"], [15, "overview"], [16, "overview"], [17, "overview"], [18, "overview"], [21, "overview"], [25, "overview"], [28, "overview"], [29, "overview"], [30, "overview"], [31, "overview"], [32, "overview"], [33, "overview"], [34, "overview"], [36, "overview"], [38, "overview"], [39, "overview"], [40, "overview"], [41, "overview"], [42, "overview"], [43, "overview"], [44, "overview"], [45, "overview"], [48, "overview"], [49, "overview"], [51, "overview"], [52, "overview"], [53, "overview"], [54, "overview"], [55, "overview"], [56, "overview"], [59, "overview"], [60, "overview"], [62, "overview"], [63, "overview"], [66, "overview"], [67, "overview"], [68, "overview"], [69, "overview"], [72, "overview"], [73, "overview"], [78, "overview"]], "PCI DSS Compliance": [[40, "pci-dss-compliance"]], "PCI DSS Compliance Assessment": [[67, "id3"]], "PCI DSS Implementation": [[40, "id5"]], "Pagination": [[8, "pagination"]], "Parallel Processing": [[57, "parallel-processing"]], "Parametrized Testing": [[63, "parametrized-testing"]], "Password Change": [[36, "password-change"]], "Password Management": [[7, "password-management"]], "Patching and Updates": [[48, "patching-and-updates"]], "Path Discovery Algorithm": [[57, "path-discovery-algorithm"]], "Path Parameters": [[5, "id2"], [5, "id4"], [5, "id5"], [5, "id8"]], "Pattern Recognition Engine": [[73, "pattern-recognition-engine"]], "Performance & Scalability": [[26, "performance-scalability"], [33, null]], "Performance Architecture": [[33, "performance-architecture"]], "Performance Benchmarks": [[33, "id3"]], "Performance Best Practices": [[13, "performance-best-practices"]], "Performance Characteristics": [[28, "performance-characteristics"]], "Performance Considerations": [[6, "performance-considerations"]], "Performance Impact Assessment": [[51, "performance-impact-assessment"]], "Performance Issues": [[17, "performance-issues"], [36, "performance-issues"], [64, "performance-issues"]], "Performance Methodology": [[34, "performance-methodology"]], "Performance Metrics": [[33, "id1"]], "Performance Monitoring": [[2, "performance-monitoring"], [33, "performance-monitoring"], [34, "performance-monitoring"], [59, "performance-monitoring"]], "Performance Monitoring Procedures": [[32, "performance-monitoring-procedures"]], "Performance Optimization": [[4, "performance-optimization"], [11, "performance-optimization"], [12, "performance-optimization"], [15, "performance-optimization"], [29, "performance-optimization"], [31, "performance-optimization"], [33, "performance-optimization"], [34, null], [66, "performance-optimization"], [67, "performance-optimization"], [68, "performance-optimization"], [69, "performance-optimization"], [73, "performance-optimization"], [78, "performance-optimization"]], "Performance Optimization Flow": [[58, "performance-optimization-flow"]], "Performance Optimization Procedures": [[32, "performance-optimization-procedures"]], "Performance Optimizations": [[57, "performance-optimizations"]], "Performance Profiling": [[20, "performance-profiling"]], "Performance Review": [[14, "performance-review"]], "Performance Specifications": [[15, "performance-specifications"]], "Performance Targets by Operation": [[34, "id1"]], "Performance Testing": [[27, "performance-testing"], [33, "performance-testing"], [62, "performance-testing"], [63, "performance-testing"]], "Performance Troubleshooting": [[33, "performance-troubleshooting"]], "Performance Tuning": [[39, "performance-tuning"], [60, "performance-tuning"]], "Performance and Operations": [[60, "performance-and-operations"]], "Performance and Troubleshooting": [[65, "performance-and-troubleshooting"]], "Periodic Assessment Coverage": [[44, "id3"]], "Periodic Security Assessments": [[44, "periodic-security-assessments"]], "Permission Errors": [[64, "permission-errors"]], "Permission Issues": [[2, "permission-issues"]], "Permission System": [[7, "permission-system"]], "Phase 1 Deliverables": [[24, "phase-1-deliverables"]], "Phase 1 Success Metrics": [[24, "phase-1-success-metrics"]], "Phase 1: Detection and Analysis": [[43, "phase-1-detection-and-analysis"]], "Phase 1: Detection and Initial Response (0-15 minutes)": [[41, "phase-1-detection-and-initial-response-0-15-minutes"]], "Phase 1: Infrastructure Setup (Weeks 1-2)": [[23, "phase-1-infrastructure-setup-weeks-1-2"]], "Phase 2 Deliverables": [[24, "phase-2-deliverables"]], "Phase 2 Status (96% Complete)": [[35, "phase-2-status-96-complete"]], "Phase 2 Success Metrics": [[24, "phase-2-success-metrics"]], "Phase 2.5 Integration Steps": [[35, "phase-2-5-integration-steps"]], "Phase 2.5: Enhanced Security Features (Current + 4-6 weeks)": [[24, "phase-2-5-enhanced-security-features-current-4-6-weeks"]], "Phase 2.5: Enhanced Security Features (Immediate - 4-6 weeks)": [[35, "phase-2-5-enhanced-security-features-immediate-4-6-weeks"]], "Phase 2.5: Enhanced Security Features (Immediate - 4-6 weeks) - HIGH PRIORITY": [[23, "phase-2-5-enhanced-security-features-immediate-4-6-weeks-high-priority"]], "Phase 2.5: Enhanced Security Features Integration": [[24, "phase-2-5-enhanced-security-features-integration"]], "Phase 2.5: Immediate Integration (Next 4-6 weeks) - HIGH PRIORITY": [[25, "phase-2-5-immediate-integration-next-4-6-weeks-high-priority"]], "Phase 2: Containment": [[43, "phase-2-containment"]], "Phase 2: Feature Deployment (Weeks 3-18)": [[23, "phase-2-feature-deployment-weeks-3-18"]], "Phase 2: Investigation and Containment (15 minutes - 2 hours)": [[41, "phase-2-investigation-and-containment-15-minutes-2-hours"]], "Phase 2: Threat Intelligence Integration (Q2 2025)": [[61, "phase-2-threat-intelligence-integration-q2-2025"]], "Phase 3 Deliverables": [[24, "phase-3-deliverables"]], "Phase 3 Success Metrics": [[24, "phase-3-success-metrics"]], "Phase 3: Advanced Analytics & Intelligence (Q3 2025 - 6-8 weeks)": [[24, "phase-3-advanced-analytics-intelligence-q3-2025-6-8-weeks"]], "Phase 3: Advanced Analytics & Intelligence (Q3 2025)": [[24, "phase-3-advanced-analytics-intelligence-q3-2025"], [35, "phase-3-advanced-analytics-intelligence-q3-2025"]], "Phase 3: Advanced Analytics (Q3 2025 - 6-8 weeks) - HIGH PRIORITY": [[23, "phase-3-advanced-analytics-q3-2025-6-8-weeks-high-priority"]], "Phase 3: Advanced Analytics (Q3 2025)": [[61, "phase-3-advanced-analytics-q3-2025"]], "Phase 3: Advanced Analytics (Q3 2025) - HIGH PRIORITY": [[25, "phase-3-advanced-analytics-q3-2025-high-priority"]], "Phase 3: Eradication": [[43, "phase-3-eradication"]], "Phase 3: Production Optimization (Weeks 19-24)": [[23, "phase-3-production-optimization-weeks-19-24"]], "Phase 3: Resolution and Recovery (2-8 hours)": [[41, "phase-3-resolution-and-recovery-2-8-hours"]], "Phase 4 Deliverables": [[24, "phase-4-deliverables"]], "Phase 4 Success Metrics": [[24, "phase-4-success-metrics"]], "Phase 4: Automation & Orchestration (Q4 2025)": [[35, "phase-4-automation-orchestration-q4-2025"]], "Phase 4: Automation and Orchestration (Q4 2025)": [[61, "phase-4-automation-and-orchestration-q4-2025"]], "Phase 4: Client Libraries & Automation (Q4 2025 - 6-8 weeks)": [[24, "phase-4-client-libraries-automation-q4-2025-6-8-weeks"]], "Phase 4: Client Libraries & Automation (Q4 2025 - 6-8 weeks) - MEDIUM PRIORITY": [[23, "phase-4-client-libraries-automation-q4-2025-6-8-weeks-medium-priority"]], "Phase 4: Client Libraries & Automation (Q4 2025)": [[24, "phase-4-client-libraries-automation-q4-2025"]], "Phase 4: Client Libraries & Automation (Q4 2025) - MEDIUM PRIORITY": [[25, "phase-4-client-libraries-automation-q4-2025-medium-priority"]], "Phase 4: Communication and Documentation (Ongoing)": [[41, "phase-4-communication-and-documentation-ongoing"]], "Phase 4: Recovery": [[43, "phase-4-recovery"]], "Phase 5: Enterprise Scaling (Q1 2026) - LOWER PRIORITY": [[25, "phase-5-enterprise-scaling-q1-2026-lower-priority"]], "Phase 5: Post-Incident Analysis": [[43, "phase-5-post-incident-analysis"]], "Phase 5: Post-Incident Review (24-72 hours after resolution)": [[41, "phase-5-post-incident-review-24-72-hours-after-resolution"]], "Phase 5: Production Optimization & Scaling (Q1 2026 - 4-6 weeks)": [[24, "phase-5-production-optimization-scaling-q1-2026-4-6-weeks"]], "Phase 5: Production Optimization & Scaling (Q1 2026)": [[24, "phase-5-production-optimization-scaling-q1-2026"]], "Phase 5: Production Optimization (Q1 2026 - 4-6 weeks) - MEDIUM PRIORITY": [[23, "phase-5-production-optimization-q1-2026-4-6-weeks-medium-priority"]], "Phase Integration Plan - Enhanced Features with Existing Roadmap": [[35, null]], "Physical Controls": [[40, "physical-controls"]], "Physical Security Controls": [[40, "id3"]], "Pipeline Commands": [[2, "pipeline-commands"]], "Pipeline Configuration": [[52, "pipeline-configuration"]], "Pipeline Options": [[2, "pipeline-options"]], "Pipeline Overview": [[2, "pipeline-overview"]], "Planned Features": [[29, "planned-features"]], "Planned Improvements": [[3, "planned-improvements"]], "Platform Capabilities and Features": [[26, "platform-capabilities-and-features"]], "Platform Updates and Maintenance": [[68, "platform-updates-and-maintenance"]], "Platform Won\u2019t Start": [[36, "platform-won-t-start"]], "Pod Startup Issues": [[16, "pod-startup-issues"]], "Point-in-Time Recovery": [[30, "point-in-time-recovery"]], "Policy Configuration": [[4, "policy-configuration"]], "Port Conflicts": [[2, "port-conflicts"]], "Post-Deployment Configuration": [[16, "post-deployment-configuration"]], "PostgreSQL Tuning": [[34, "postgresql-tuning"]], "PowerShell Module": [[25, "powershell-module"]], "Practical Focus": [[0, "practical-focus"], [1, "practical-focus"]], "Practical Scenarios:": [[0, "practical-scenarios"]], "Pre-Deployment Backup": [[30, "pre-deployment-backup"]], "Pre-Production Checklist": [[14, "pre-production-checklist"]], "Pre-Review Checklist": [[49, "pre-review-checklist"]], "Pre-commit Security Checks": [[52, "id1"]], "Pre-commit Security Hooks": [[52, "pre-commit-security-hooks"]], "Pre-loaded Threat Actors": [[78, "pre-loaded-threat-actors"]], "Preparedness Activities": [[43, "preparedness-activities"]], "Prerequisites": [[2, "prerequisites"], [3, "prerequisites"], [3, "id2"], [14, "prerequisites"], [16, "prerequisites"], [20, "prerequisites"], [27, "prerequisites"], [29, "prerequisites"], [36, "prerequisites"], [69, "prerequisites"], [73, "prerequisites"], [78, "prerequisites"]], "Preventive Maintenance": [[31, "preventive-maintenance"]], "Primary DAST Tools": [[51, "primary-dast-tools"]], "Primary Goals": [[24, "primary-goals"]], "Primary Responsibilities": [[77, "primary-responsibilities"]], "Primary SAST Tools": [[53, "primary-sast-tools"]], "Privacy (P1)": [[38, "privacy-p1"]], "Privacy Impact Assessment": [[12, "privacy-impact-assessment"]], "Privacy by Design Implementation": [[12, "privacy-by-design-implementation"]], "Privilege Escalation Management": [[4, "privilege-escalation-management"]], "Processing Integrity (PI1)": [[38, "processing-integrity-pi1"]], "Product Overview": [[61, "product-overview"]], "Product Requirements Document": [[60, "product-requirements-document"]], "Product Requirements Document (PRD)": [[61, null]], "Production Architecture Overview": [[15, null]], "Production Configuration": [[14, "production-configuration"]], "Production Deployment": [[3, "production-deployment"]], "Production Deployment Guide - Blast-Radius Security Tool": [[16, null]], "Production Deployment Infrastructure - 100% Complete \u2705": [[28, "production-deployment-infrastructure-100-complete"]], "Production Deployment Process": [[14, "production-deployment-process"]], "Production Environment": [[13, "production-environment"], [14, "production-environment"], [28, "production-environment"]], "Production Environment Setup": [[14, "production-environment-setup"]], "Production Infrastructure \u2705": [[25, "production-infrastructure"]], "Professional Communication": [[45, "professional-communication"]], "Professional Development and Excellence Framework": [[70, "professional-development-and-excellence-framework"]], "Professional Development and Executive Excellence": [[71, "professional-development-and-executive-excellence"]], "Professional Development and Support Resources": [[76, "professional-development-and-support-resources"]], "Professional Quality": [[0, "professional-quality"]], "Profiling Tools": [[34, "profiling-tools"]], "Program Enhancement": [[48, "program-enhancement"]], "Prometheus Configuration Management": [[32, "prometheus-configuration-management"]], "Protect (PR)": [[38, "protect-pr"]], "Public Disclosure": [[45, "public-disclosure"]], "Purple Team Collaboration": [[69, "purple-team-collaboration"]], "Purple Team Members": [[72, "purple-team-members"]], "Purple Team Members Comprehensive Guide": [[74, null]], "Python": [[7, "python"]], "Python Code Style": [[19, "python-code-style"]], "Python SDK": [[5, "python-sdk"], [25, "python-sdk"]], "Python SDK Usage": [[29, "python-sdk-usage"]], "Python Standards": [[18, "python-standards"]], "Qualitative Benefits": [[23, "qualitative-benefits"]], "Quality Assurance": [[3, "quality-assurance"], [3, "id8"], [18, "quality-assurance"], [60, "quality-assurance"]], "Quality Assurance and Improvement": [[44, "quality-assurance-and-improvement"]], "Quality Assurance:": [[0, "quality-assurance"]], "Quality Gates": [[21, "quality-gates"], [62, "quality-gates"]], "Quality Metrics": [[62, "quality-metrics"]], "Quantitative Benefits (Enhanced Timeline)": [[23, "quantitative-benefits-enhanced-timeline"]], "Quantitative Risk Assessment": [[78, "quantitative-risk-assessment"]], "Query Parameters": [[5, "id1"], [5, "id3"], [5, "id6"]], "Quick Diagnostics": [[64, "quick-diagnostics"]], "Quick Installation": [[36, "quick-installation"]], "Quick Reference": [[42, null]], "Quick Security Reference": [[42, "quick-security-reference"]], "Quick Start Example": [[69, "quick-start-example"]], "Quick Start Guide": [[36, null]], "Quick Wins": [[33, "quick-wins"]], "RBAC Implementation": [[11, "rbac-implementation"]], "REST API Design": [[18, "rest-api-design"]], "RESTful API": [[73, "restful-api"]], "Rate Limiting": [[5, "rate-limiting"], [6, "rate-limiting"], [8, "rate-limiting"], [9, "rate-limiting"], [10, "rate-limiting"]], "Real-Time Monitoring": [[36, "real-time-monitoring"], [39, "real-time-monitoring"]], "Real-Time Security Event Correlation": [[11, "real-time-security-event-correlation"]], "Real-time Event Correlation": [[73, "real-time-event-correlation"]], "Recent Security Achievements": [[50, "recent-security-achievements"]], "Recognition": [[19, "recognition"]], "Recognition and Rewards": [[45, "recognition-and-rewards"]], "Recognition and Standards": [[3, "recognition-and-standards"]], "Recommended Resources by Environment Size": [[33, "id2"]], "Recover (RC)": [[38, "recover-rc"]], "Recovery Procedures": [[30, "recovery-procedures"]], "Recovery Testing": [[30, "recovery-testing"]], "Red Team Analysis Results": [[67, "id1"]], "Red Team Best Practices and Operational Excellence": [[75, "red-team-best-practices-and-operational-excellence"]], "Red Team Exercise Workflow": [[72, "red-team-exercise-workflow"]], "Red Team Exercises": [[69, "red-team-exercises"]], "Red Team Members": [[72, "red-team-members"]], "Red Team Members Comprehensive Guide": [[75, null]], "Redis Optimization": [[34, "redis-optimization"]], "Reduced Risk": [[35, "reduced-risk"]], "Refresh Graph Data": [[6, "refresh-graph-data"]], "Regular Training": [[41, "regular-training"]], "Regulatory Compliance": [[43, "regulatory-compliance"], [46, "regulatory-compliance"]], "Regulatory Compliance Assessment": [[78, "regulatory-compliance-assessment"]], "Regulatory Compliance Status": [[50, "regulatory-compliance-status"]], "Regulatory Compliance and Governance Excellence": [[71, "regulatory-compliance-and-governance-excellence"]], "Regulatory Requirements": [[49, "regulatory-requirements"]], "Release Management": [[21, "release-management"]], "Release Notes": [[26, "release-notes"]], "Release Notes Section": [[1, "release-notes-section"]], "Release Process": [[21, "release-process"]], "Remediation Strategies": [[48, "remediation-strategies"]], "Remediation Tracking": [[48, "remediation-tracking"]], "Reporting Dashboard": [[53, "reporting-dashboard"]], "Reporting Schedule": [[38, "reporting-schedule"]], "Reporting and Analysis": [[43, "reporting-and-analysis"]], "Required Software": [[14, "required-software"], [20, "required-software"]], "Required Tools and Access": [[16, "required-tools-and-access"]], "Resource Limits": [[29, "resource-limits"]], "Resource Monitoring": [[2, "resource-monitoring"]], "Resource Requirements": [[15, "resource-requirements"]], "Respond (RS)": [[38, "respond-rs"]], "Response Engine": [[25, "response-engine"]], "Response Process": [[45, "response-process"]], "Response Time Performance": [[50, "id5"]], "Responsible Disclosure Principles": [[45, "responsible-disclosure-principles"]], "Responsive Design": [[3, "responsive-design"]], "Results Processing": [[53, "results-processing"]], "Review Criteria": [[49, "review-criteria"]], "Review Documentation Template": [[49, "review-documentation-template"]], "Review Documentation and Tracking": [[44, "review-documentation-and-tracking"]], "Review Guidelines": [[19, "review-guidelines"]], "Review Process": [[19, "review-process"], [21, "review-process"]], "Review Process Workflow": [[49, "review-process-workflow"]], "Review Quality Metrics": [[44, "review-quality-metrics"]], "Review Requirements": [[21, "review-requirements"]], "Review Tracking and Metrics": [[44, "review-tracking-and-metrics"]], "Review Types": [[49, "review-types"]], "Review Types and Triggers": [[44, "review-types-and-triggers"]], "Reviewer Quality Metrics": [[44, "id7"]], "Risk Assessment": [[10, "risk-assessment"], [61, "risk-assessment"]], "Risk Assessment Engine": [[4, "risk-assessment-engine"]], "Risk Assessment Framework": [[46, "risk-assessment-framework"]], "Risk Assessment Matrix": [[48, "risk-assessment-matrix"]], "Risk Assessment Summary": [[50, "id6"]], "Risk Calculation": [[46, "risk-calculation"]], "Risk Calculation Methodology": [[78, "risk-calculation-methodology"]], "Risk Forecasting": [[25, "risk-forecasting"]], "Risk Levels": [[29, "risk-levels"]], "Risk Management Excellence": [[50, "risk-management-excellence"]], "Risk Prioritization Framework": [[67, "id6"]], "Risk Reduction": [[47, "risk-reduction"]], "Risk Score Calculation": [[29, "risk-score-calculation"]], "Risk Scoring Decision Flow": [[58, "risk-scoring-decision-flow"]], "Risk Scoring Methodology": [[57, "risk-scoring-methodology"], [69, "risk-scoring-methodology"]], "Risk Scoring and Prioritization": [[67, "risk-scoring-and-prioritization"]], "Risk-Based Prioritization": [[48, "risk-based-prioritization"]], "Role Definition and Offensive Security Responsibilities": [[75, "role-definition-and-offensive-security-responsibilities"]], "Role Definition and Responsibilities": [[77, "role-definition-and-responsibilities"]], "Role Definition and Strategic Responsibilities": [[70, "role-definition-and-strategic-responsibilities"], [71, "role-definition-and-strategic-responsibilities"], [74, "role-definition-and-strategic-responsibilities"], [76, "role-definition-and-strategic-responsibilities"]], "Role Template Configuration": [[4, "role-template-configuration"]], "Role-Based Access Control (RBAC)": [[68, "role-based-access-control-rbac"]], "Role-Based Access Control and Permissions": [[77, "role-based-access-control-and-permissions"]], "Role-Based Access Templates": [[4, "role-based-access-templates"]], "Role-Based Guides": [[72, "role-based-guides"]], "Role-Specific Tips": [[72, "role-specific-tips"]], "Roles and Responsibilities": [[41, "roles-and-responsibilities"]], "Rollback Procedure": [[17, "rollback-procedure"]], "Rollback Procedures": [[16, "rollback-procedures"]], "Rollback Strategy": [[35, "rollback-strategy"]], "Routine Maintenance Schedule": [[31, "routine-maintenance-schedule"]], "Run Containers": [[2, "run-containers"]], "Running Attack Simulations": [[78, "running-attack-simulations"]], "Running Tests": [[2, "running-tests"], [62, "running-tests"], [63, "running-tests"]], "Runtime Security Monitoring": [[52, "runtime-security-monitoring"]], "SAST Benefits": [[53, "sast-benefits"]], "SAST Implementation Best Practices": [[53, "sast-implementation-best-practices"]], "SAST Integration in CI/CD": [[53, "sast-integration-in-ci-cd"]], "SAST Metrics and Reporting": [[53, "sast-metrics-and-reporting"]], "SAST Results Analysis": [[53, "sast-results-analysis"]], "SAST Rule Configuration": [[53, "sast-rule-configuration"]], "SAST Tools and Coverage": [[46, "id1"], [48, "id1"]], "SAST Tools and Implementation": [[53, "sast-tools-and-implementation"]], "SDK Examples": [[5, "sdk-examples"]], "SDK and Libraries": [[8, "sdk-and-libraries"]], "SIEM Integration": [[67, "siem-integration"]], "SOAR Integration": [[67, "soar-integration"]], "SOC 2 Compliance": [[38, "soc-2-compliance"]], "SOC 2 Compliance Checklist": [[49, "soc-2-compliance-checklist"]], "SOC 2 Implementation": [[40, "id4"]], "SOC 2 Type II Compliance": [[40, "soc-2-type-ii-compliance"]], "SOC Operations Best Practices and Excellence Framework": [[77, "soc-operations-best-practices-and-excellence-framework"]], "SOC Operators": [[72, "soc-operators"]], "SOC Operators Comprehensive Guide": [[77, null]], "SOLID Principles": [[18, "solid-principles"]], "SOLID Principles Application": [[18, "id2"]], "SSL/TLS Certificate Issues": [[64, "ssl-tls-certificate-issues"]], "STRIDE Threat Analysis": [[44, "id5"]], "Safe Harbor": [[45, "safe-harbor"]], "Scalability Considerations": [[24, "scalability-considerations"]], "Scalability Metrics": [[15, "scalability-metrics"]], "Scalability Patterns": [[33, "scalability-patterns"]], "Scenario 1: Multi-Cloud Asset Inventory": [[66, "scenario-1-multi-cloud-asset-inventory"]], "Scenario 1: Red Team Attack Simulation": [[67, "scenario-1-red-team-attack-simulation"]], "Scenario 2: Continuous Asset Monitoring": [[66, "scenario-2-continuous-asset-monitoring"]], "Scenario 2: Purple Team Validation Exercise": [[67, "scenario-2-purple-team-validation-exercise"]], "Scenario 3: Compliance Asset Reporting": [[66, "scenario-3-compliance-asset-reporting"]], "Scenario 3: Compliance Risk Assessment": [[67, "scenario-3-compliance-risk-assessment"]], "Scenario 4: Incident Response Planning": [[67, "scenario-4-incident-response-planning"]], "Scenario 5: Zero Trust Architecture Planning": [[67, "scenario-5-zero-trust-architecture-planning"]], "Scheduled Discovery": [[66, "scheduled-discovery"]], "Scheduled Maintenance Window": [[31, "scheduled-maintenance-window"]], "Schema Design": [[18, "schema-design"]], "Scope": [[45, "scope"]], "Search Features": [[3, "search-features"]], "Security": [[21, "security"]], "Security & Compliance": [[26, "security-compliance"], [26, null]], "Security & Compliance Section": [[1, "security-compliance-section"]], "Security (CC6)": [[38, "security-cc6"]], "Security Administration": [[68, "security-administration"]], "Security Analysis \u2705": [[25, "security-analysis"]], "Security Architects": [[72, "security-architects"]], "Security Architects Comprehensive Guide": [[76, null]], "Security Architecture": [[15, "security-architecture"], [40, "security-architecture"], [42, "security-architecture"]], "Security Architecture Excellence": [[50, "security-architecture-excellence"]], "Security Architecture Layers": [[37, "security-architecture-layers"]], "Security Architecture Overview": [[37, null]], "Security Assessment": [[69, "security-assessment"]], "Security Assessment Overview": [[46, null]], "Security Assessment Workflow": [[72, "security-assessment-workflow"]], "Security Automation Architecture": [[52, "security-automation-architecture"]], "Security Automation Best Practices": [[52, "security-automation-best-practices"]], "Security Automation KPIs": [[52, "id2"]], "Security Automation Metrics": [[52, "security-automation-metrics"]], "Security Automation Performance": [[50, "id3"]], "Security Best Practices": [[13, "security-best-practices"], [15, "security-best-practices"]], "Security Certifications and Compliance": [[42, "security-certifications-and-compliance"]], "Security Commands": [[2, "security-commands"]], "Security Configuration": [[13, "security-configuration"]], "Security Considerations": [[29, "security-considerations"], [66, "security-considerations"]], "Security Contact Information": [[42, "security-contact-information"]], "Security Controls Framework": [[40, "security-controls-framework"]], "Security Controls Implementation": [[37, "security-controls-implementation"]], "Security Documentation": [[42, null]], "Security Documentation Sections": [[42, "security-documentation-sections"]], "Security Documentation Structure": [[50, "security-documentation-structure"]], "Security Documentation Summary": [[50, null]], "Security Framework": [[40, null], [42, "security-framework"]], "Security Gates": [[46, "security-gates"]], "Security Improvement Metrics": [[50, "id1"]], "Security Improvements Implemented": [[47, "security-improvements-implemented"]], "Security Incident Categories": [[43, "security-incident-categories"]], "Security Incident Response": [[43, null]], "Security Investment ROI": [[50, "security-investment-roi"]], "Security Layer Assessment": [[50, "id2"]], "Security Layer Implementation": [[40, "id1"]], "Security Lead": [[41, "security-lead"]], "Security Maturity Assessment": [[40, "security-maturity-assessment"]], "Security Maturity Levels": [[40, "id8"]], "Security Metrics": [[46, "id3"]], "Security Metrics Comparison": [[47, "id1"]], "Security Metrics and KPIs": [[46, "security-metrics-and-kpis"], [49, "security-metrics-and-kpis"]], "Security Model": [[60, "security-model"]], "Security Monitoring": [[11, "security-monitoring"], [40, "security-monitoring"]], "Security Monitoring and Incident Response": [[50, "security-monitoring-and-incident-response"]], "Security Operations Excellence": [[50, "security-operations-excellence"]], "Security Patches": [[31, "security-patches"]], "Security Principles": [[11, "security-principles"], [42, "security-principles"]], "Security Quality Gates": [[52, "security-quality-gates"]], "Security Recommendations": [[7, "security-recommendations"]], "Security Researcher Recognition": [[45, "security-researcher-recognition"]], "Security Review": [[14, "security-review"]], "Security Review - June 14, 2025": [[47, null]], "Security Review Best Practices": [[44, "security-review-best-practices"]], "Security Review Framework": [[44, "security-review-framework"], [49, "security-review-framework"]], "Security Review Metrics": [[44, "id6"]], "Security Review Procedures": [[44, "security-review-procedures"]], "Security Review Process": [[44, null]], "Security Review Processes - Blast-Radius Security Tool": [[49, null]], "Security Review Templates": [[44, "security-review-templates"]], "Security Review Tools and Automation": [[49, "security-review-tools-and-automation"]], "Security Review Training": [[49, "security-review-training"]], "Security Scan Results": [[47, "security-scan-results"]], "Security Specifications": [[28, "security-specifications"]], "Security Standards": [[2, "security-standards"], [18, "security-standards"]], "Security Test Coverage": [[47, "security-test-coverage"]], "Security Testing": [[62, "security-testing"]], "Security Testing Automation": [[46, "security-testing-automation"], [52, null]], "Security Testing Excellence": [[50, "security-testing-excellence"]], "Security Testing Tools Integration": [[52, "security-testing-tools-integration"]], "Security Testing and Validation": [[37, "security-testing-and-validation"]], "Security Training and Awareness": [[40, "security-training-and-awareness"]], "Security Updates Check": [[31, "security-updates-check"]], "Security Validation": [[11, "security-validation"]], "Security and Authorization": [[7, "security-and-authorization"]], "Security and Compliance": [[60, "security-and-compliance"], [65, "security-and-compliance"], [68, "security-and-compliance"], [68, "id1"]], "Security-Specific Procedures": [[41, "security-specific-procedures"]], "SecurityThreatSpike": [[32, "securitythreatspike"]], "Service Account Management": [[11, "service-account-management"]], "Service Discovery": [[24, "service-discovery"]], "Service Discovery Benefits": [[23, "service-discovery-benefits"]], "ServiceNow Integration": [[13, "servicenow-integration"], [36, "servicenow-integration"]], "Setup Monitoring": [[10, "setup-monitoring"]], "Severity Classification": [[45, "severity-classification"]], "Severity Levels": [[41, "severity-levels"], [43, "severity-levels"]], "Short-Term Objectives (Q3-Q4 2025)": [[50, "short-term-objectives-q3-q4-2025"]], "Short-term Goals (Phase 3 - Q3 2025)": [[35, "short-term-goals-phase-3-q3-2025"]], "Simulate Attack": [[10, "simulate-attack"]], "Slow Response Times": [[64, "slow-response-times"]], "Snapshot Recovery": [[30, "snapshot-recovery"]], "Software Dependencies": [[27, "software-dependencies"]], "Specific Stages": [[2, "specific-stages"]], "Staging Configuration": [[14, "staging-configuration"]], "Staging Environment": [[13, "staging-environment"], [14, "staging-environment"]], "Staging Environment Setup": [[14, "staging-environment-setup"]], "Standard Authentication Flow": [[7, "standard-authentication-flow"]], "Start AWS Discovery": [[29, "start-aws-discovery"]], "Start Azure Discovery": [[29, "start-azure-discovery"]], "Start Discovery": [[5, "start-discovery"]], "Start GCP Discovery": [[29, "start-gcp-discovery"]], "Starting Development Services": [[20, "starting-development-services"]], "Statement of Applicability (SoA)": [[38, "statement-of-applicability-soa"]], "Static Application Security Testing (SAST)": [[46, "static-application-security-testing-sast"], [52, "static-application-security-testing-sast"], [53, null]], "Status Tracking": [[48, "status-tracking"]], "Step 10: Backup Configuration": [[16, "step-10-backup-configuration"]], "Step 11: Ongoing Monitoring": [[16, "step-11-ongoing-monitoring"]], "Step 1: Add Sample Assets": [[36, "step-1-add-sample-assets"]], "Step 1: Automated Security Checks": [[49, "step-1-automated-security-checks"]], "Step 1: Clone Repository": [[20, "step-1-clone-repository"]], "Step 1: Environment Configuration": [[16, "step-1-environment-configuration"]], "Step 1: Find or Create an Issue": [[19, "step-1-find-or-create-an-issue"]], "Step 1: Get the Code": [[36, "step-1-get-the-code"]], "Step 2: Backend Setup": [[20, "step-2-backend-setup"]], "Step 2: Fork and Clone": [[19, "step-2-fork-and-clone"]], "Step 2: Infrastructure Provisioning": [[16, "step-2-infrastructure-provisioning"]], "Step 2: Manual Security Review": [[49, "step-2-manual-security-review"]], "Step 2: Run Attack Path Analysis": [[36, "step-2-run-attack-path-analysis"]], "Step 2: Start the Platform": [[36, "step-2-start-the-platform"]], "Step 3: Access the Platform": [[36, "step-3-access-the-platform"]], "Step 3: Create a Branch": [[19, "step-3-create-a-branch"]], "Step 3: Database Setup": [[16, "step-3-database-setup"], [20, "step-3-database-setup"]], "Step 3: Security Testing": [[49, "step-3-security-testing"]], "Step 3: View Results": [[36, "step-3-view-results"]], "Step 4: Container Image Preparation": [[16, "step-4-container-image-preparation"]], "Step 4: Frontend Setup": [[20, "step-4-frontend-setup"]], "Step 4: Make Changes": [[19, "step-4-make-changes"]], "Step 5: Development Tools Setup": [[20, "step-5-development-tools-setup"]], "Step 5: Kubernetes Deployment": [[16, "step-5-kubernetes-deployment"]], "Step 5: Test Your Changes": [[19, "step-5-test-your-changes"]], "Step 6: SSL/TLS Configuration": [[16, "step-6-ssl-tls-configuration"]], "Step 6: Submit Pull Request": [[19, "step-6-submit-pull-request"]], "Step 7: Application Configuration": [[16, "step-7-application-configuration"]], "Step 8: Security Hardening": [[16, "step-8-security-hardening"]], "Step 9: Deployment Verification": [[16, "step-9-deployment-verification"]], "Storage Issues": [[17, "storage-issues"]], "Strategic Security Communication and Stakeholder Management": [[71, "strategic-security-communication-and-stakeholder-management"]], "Strategic Security Investment and Resource Management": [[71, "strategic-security-investment-and-resource-management"]], "Strategic Security Risk Management Framework": [[71, "strategic-security-risk-management-framework"]], "Structure and Organization": [[1, "structure-and-organization"]], "Style Guide": [[3, "style-guide"]], "Success Criteria for Month 1": [[23, "success-criteria-for-month-1"]], "Success Metrics and KPIs": [[61, "success-metrics-and-kpis"]], "Success Response": [[8, "success-response"]], "Support": [[26, "support"]], "Support Channels": [[64, "support-channels"]], "Support Resources": [[72, "support-resources"]], "Support and Community": [[65, "support-and-community"]], "Support and Resources": [[8, "support-and-resources"], [60, "support-and-resources"]], "Supported Cloud Services": [[29, "supported-cloud-services"]], "Sync ATT&CK Data": [[9, "sync-att-ck-data"]], "System Architecture": [[60, "system-architecture"]], "System Configuration and Management": [[68, "system-configuration-and-management"]], "System Health Check (6:00 AM UTC)": [[31, "system-health-check-6-00-am-utc"]], "System Health Monitoring": [[32, "system-health-monitoring"]], "System Monitoring": [[68, "system-monitoring"]], "System Overview": [[57, "system-overview"]], "System Requirements": [[14, "system-requirements"], [20, "system-requirements"], [27, "system-requirements"]], "System Updates and Patches": [[31, "system-updates-and-patches"]], "TAR-001: Graph Processing Engine": [[61, "tar-001-graph-processing-engine"]], "TAR-002: Database Architecture": [[61, "tar-002-database-architecture"]], "TAR-003: API Architecture": [[61, "tar-003-api-architecture"]], "TAR-004: Security Architecture": [[61, "tar-004-security-architecture"]], "TLS 1.3+ Enforcement": [[11, "tls-1-3-enforcement"]], "Table Definitions": [[59, "table-definitions"]], "Table of Contents": [[5, "table-of-contents"], [18, "table-of-contents"], [19, "table-of-contents"], [20, "table-of-contents"], [21, "table-of-contents"], [22, "table-of-contents"], [33, "table-of-contents"], [34, "table-of-contents"], [40, "table-of-contents"], [62, "table-of-contents"], [63, "table-of-contents"], [64, "table-of-contents"], [66, "table-of-contents"], [67, "table-of-contents"], [70, "table-of-contents"], [71, "table-of-contents"], [74, "table-of-contents"], [75, "table-of-contents"], [76, "table-of-contents"], [77, "table-of-contents"]], "Tamper-Proof Audit Chain": [[39, "tamper-proof-audit-chain"]], "Target Users": [[24, "target-users"], [61, "target-users"]], "Technical Architecture Requirements": [[61, "technical-architecture-requirements"]], "Technical Controls": [[40, "technical-controls"]], "Technical Coverage": [[1, "technical-coverage"]], "Technical Documentation": [[26, "technical-documentation"], [60, null]], "Technical Documentation (technical/)": [[3, "id5"]], "Technical Documentation Section": [[1, "technical-documentation-section"]], "Technical Implementation": [[12, "technical-implementation"]], "Technical KPIs": [[23, "technical-kpis"]], "Technical Lead": [[41, "technical-lead"]], "Technical Metrics": [[24, "technical-metrics"], [61, "technical-metrics"]], "Technical Performance Targets": [[35, "technical-performance-targets"]], "Technical Risks": [[61, "technical-risks"]], "Technical Specifications": [[28, "technical-specifications"]], "Technical Support": [[60, "technical-support"]], "Technique Correlation": [[9, "technique-correlation"]], "Technique Correlation Engine": [[73, "technique-correlation-engine"]], "Terms and Conditions": [[45, "terms-and-conditions"]], "Terraform Security Checklist": [[49, "terraform-security-checklist"]], "Test Automation": [[62, "test-automation"]], "Test Categories": [[2, "test-categories"], [62, "test-categories"]], "Test Data Management": [[51, "test-data-management"], [62, "test-data-management"]], "Test Design Principles": [[63, "test-design-principles"]], "Test Environment Management": [[51, "test-environment-management"]], "Test Environment Setup": [[62, "test-environment-setup"]], "Test Execution": [[63, "test-execution"]], "Test Organization": [[18, "test-organization"]], "Test Quality Metrics": [[63, "test-quality-metrics"]], "Test Reporting": [[62, "test-reporting"]], "Test Results": [[47, "test-results"]], "Test Structure": [[19, "test-structure"], [63, "test-structure"]], "Test Writing Guidelines": [[62, "test-writing-guidelines"]], "Test-Driven Development": [[21, "test-driven-development"]], "Testing & Quality Assurance": [[26, "testing-quality-assurance"], [62, null]], "Testing Async Code": [[63, "testing-async-code"]], "Testing Framework": [[60, "testing-framework"], [63, "testing-framework"]], "Testing Guidelines": [[19, "testing-guidelines"]], "Testing Philosophy": [[62, "testing-philosophy"]], "Testing Standards": [[18, "testing-standards"]], "Testing Strategy": [[21, "testing-strategy"]], "Testing and Development": [[8, "testing-and-development"]], "Testing and Quality Assurance": [[60, "testing-and-quality-assurance"]], "Testing and Validation": [[47, "testing-and-validation"]], "Testing in Development": [[20, "testing-in-development"]], "TheHive Connector": [[25, "thehive-connector"]], "TheHive Integration - Technical Specification": [[56, null]], "Threat Actor Attribution": [[9, "threat-actor-attribution"], [73, "threat-actor-attribution"]], "Threat Actor Profiles": [[78, "threat-actor-profiles"]], "Threat Actor Simulation": [[10, "threat-actor-simulation"]], "Threat Intelligence": [[8, "threat-intelligence"], [36, "threat-intelligence"]], "Threat Intelligence Enrichment": [[9, "threat-intelligence-enrichment"], [73, "threat-intelligence-enrichment"]], "Threat Intelligence Integration": [[40, "threat-intelligence-integration"], [72, "threat-intelligence-integration"], [78, "threat-intelligence-integration"]], "Threat Intelligence Settings": [[13, "threat-intelligence-settings"]], "Threat Modeling": [[46, "threat-modeling"], [72, "threat-modeling"]], "Threat Modeling & Risk Assessment": [[8, "threat-modeling-risk-assessment"]], "Threat Modeling API Reference": [[10, null]], "Threat Modeling User Guide": [[78, null]], "Threat Modeling Workflow": [[78, "threat-modeling-workflow"]], "Threat Prediction Models": [[25, "threat-prediction-models"]], "Token Refresh": [[7, "token-refresh"]], "Tools and Configuration": [[18, "tools-and-configuration"]], "Tools and Resources": [[41, "tools-and-resources"]], "Track Campaigns": [[9, "track-campaigns"]], "Training Program": [[40, "training-program"]], "Training Program Components": [[40, "id7"]], "Training and Awareness": [[49, "training-and-awareness"]], "Training and Certification": [[72, "training-and-certification"]], "Training and Drills": [[41, "training-and-drills"]], "Training and Preparedness": [[43, "training-and-preparedness"]], "Troubleshooting": [[4, "troubleshooting"], [11, "troubleshooting"], [12, "troubleshooting"], [13, "troubleshooting"], [20, "troubleshooting"], [26, "troubleshooting"], [27, "troubleshooting"], [29, "troubleshooting"], [39, "troubleshooting"], [66, "troubleshooting"], [69, "troubleshooting"]], "Troubleshooting Common Issues": [[16, "troubleshooting-common-issues"]], "Troubleshooting Guide - Blast-Radius Security Tool": [[17, null]], "Troubleshooting Section": [[1, "troubleshooting-section"]], "Troubleshooting and Support": [[68, "troubleshooting-and-support"]], "Trust Boundary Analysis": [[67, "id5"]], "Trust Service Criteria": [[38, "trust-service-criteria"]], "UI Not Loading": [[64, "ui-not-loading"]], "Understanding the Visualization": [[36, "understanding-the-visualization"]], "Unit Testing": [[62, "unit-testing"]], "Unit Testing Guide": [[63, null]], "Update Asset": [[5, "update-asset"]], "Update Management": [[68, "update-management"]], "Update Metadata": [[5, "update-metadata"]], "Updates and Changes": [[45, "updates-and-changes"]], "Usability Features": [[1, "usability-features"]], "Usage": [[29, "usage"]], "Use Case Scenarios": [[66, "use-case-scenarios"], [67, "use-case-scenarios"]], "Use Cases": [[26, null]], "Use Cases Section": [[1, "use-cases-section"]], "Use Cases and Workflows": [[69, "use-cases-and-workflows"]], "User Account Management": [[68, "user-account-management"]], "User Adoption Metrics": [[61, "user-adoption-metrics"]], "User Experience:": [[0, "user-experience"]], "User Guides": [[72, null]], "User Guides (user-guides/)": [[3, "id4"]], "User Guides Section": [[1, "user-guides-section"]], "User Profile Setup": [[36, "user-profile-setup"]], "User Role Coverage": [[1, "user-role-coverage"]], "User and Access Management": [[68, "user-and-access-management"]], "User and Role Management": [[36, "user-and-role-management"]], "User-Centric Organization": [[0, "user-centric-organization"]], "User-Friendly Format": [[1, "user-friendly-format"]], "Using the API": [[69, "using-the-api"]], "Using the Web Interface": [[69, "using-the-web-interface"]], "Verification": [[27, "verification"]], "Verification and Testing": [[16, "verification-and-testing"]], "Versioning Strategy": [[21, "versioning-strategy"]], "Vertical Scaling": [[33, "vertical-scaling"]], "Vision Statement": [[61, "vision-statement"]], "Visualization \ud83d\udd04": [[25, "visualization"]], "Vulnerability Assessment": [[48, "vulnerability-assessment"], [51, "vulnerability-assessment"]], "Vulnerability Classification": [[46, "vulnerability-classification"], [53, "vulnerability-classification"]], "Vulnerability Database": [[48, "vulnerability-database"]], "Vulnerability Disclosure Policy": [[45, null]], "Vulnerability Discovery": [[48, "vulnerability-discovery"]], "Vulnerability KPIs": [[48, "id8"]], "Vulnerability Management": [[46, "vulnerability-management"], [48, null]], "Vulnerability Management Lifecycle": [[48, "vulnerability-management-lifecycle"]], "Vulnerability Management Program": [[50, "vulnerability-management-program"]], "Vulnerability Prioritization": [[48, "vulnerability-prioritization"]], "Vulnerability Reporting Process": [[45, "vulnerability-reporting-process"]], "Vulnerability Response": [[41, "vulnerability-response"]], "Vulnerability Risk Assessment": [[48, "id4"]], "Vulnerability Status Definitions": [[48, "id7"]], "Vulnerability Tracking Fields": [[48, "id6"]], "Ways to Contribute": [[19, "ways-to-contribute"]], "Webhooks": [[8, "webhooks"]], "Week 1 Actions": [[35, "week-1-actions"]], "Week 1-2 Priorities": [[23, "week-1-2-priorities"]], "Week 2 Actions": [[35, "week-2-actions"]], "Weekly Maintenance (Sunday 3:00 AM UTC)": [[31, "weekly-maintenance-sunday-3-00-am-utc"]], "Weekly Performance Review (Monday 10:00 AM)": [[32, "weekly-performance-review-monday-10-00-am"]], "What are the default login credentials?": [[65, "what-are-the-default-login-credentials"]], "What are the system requirements?": [[65, "what-are-the-system-requirements"]], "What is the Blast-Radius Security Tool?": [[65, "what-is-the-blast-radius-security-tool"]], "What\u2019s the roadmap for future features?": [[65, "what-s-the-roadmap-for-future-features"]], "Who should use this tool?": [[65, "who-should-use-this-tool"]], "Why am I not seeing any attack paths?": [[65, "why-am-i-not-seeing-any-attack-paths"]], "Why can\u2019t I access the web interface?": [[65, "why-can-t-i-access-the-web-interface"]], "Why is the platform running slowly?": [[65, "why-is-the-platform-running-slowly"]], "Writing Guidelines": [[19, "writing-guidelines"]], "Writing Unit Tests": [[63, "writing-unit-tests"]], "Your First Attack Path Analysis": [[36, "your-first-attack-path-analysis"]], "Zero Trust Implementation": [[40, "zero-trust-implementation"]], "Zero-Trust Architecture - 100% Complete \u2705": [[28, "zero-trust-architecture-100-complete"]], "Zero-Trust Architecture Implementation": [[11, null]], "Zero-Trust Security Implementation": [[15, "zero-trust-security-implementation"]], "pytest Configuration": [[63, "pytest-configuration"]], "\u2696\ufe0f Governance and Leadership": [[3, "governance-and-leadership"]], "\u2705 Completed Documentation Files": [[1, "completed-documentation-files"]], "\ud83c\udf1f Industry Leadership": [[3, "industry-leadership"]], "\ud83c\udf1f Industry Leadership and Recognition": [[22, "industry-leadership-and-recognition"]], "\ud83c\udf89 Conclusion": [[0, "conclusion"], [1, "conclusion"]], "\ud83c\udf89 Latest Production Ready Release": [[26, "latest-production-ready-release"]], "\ud83c\udf89 Latest Release": [[26, null]], "\ud83c\udf89 NEW: Complete User Guide Ecosystem": [[26, null]], "\ud83c\udfa8 Features": [[3, "features"]], "\ud83c\udfa8 Styling and Customization": [[3, "styling-and-customization"]], "\ud83c\udfaf Core Documentation": [[3, "core-documentation"]], "\ud83c\udfaf Current State Analysis": [[35, "current-state-analysis"]], "\ud83c\udfaf Enhanced Vision & Objectives": [[24, "enhanced-vision-objectives"]], "\ud83c\udfaf Enhancement Overview": [[23, "enhancement-overview"]], "\ud83c\udfaf Framework Data Population": [[54, "framework-data-population"]], "\ud83c\udfaf Key Deliverables by Phase": [[24, "key-deliverables-by-phase"]], "\ud83c\udfaf Key Documentation Features": [[1, "key-documentation-features"], [22, "key-documentation-features"]], "\ud83c\udfaf Key Features": [[3, "key-features"]], "\ud83c\udfaf Key Improvements": [[0, "key-improvements"]], "\ud83c\udfaf Next Immediate Actions": [[23, "next-immediate-actions"]], "\ud83c\udfaf Next Immediate Steps": [[35, "next-immediate-steps"]], "\ud83c\udfc5 Professional Excellence": [[3, "professional-excellence"]], "\ud83c\udfc6 Documentation Excellence": [[3, "documentation-excellence"]], "\ud83c\udfc6 Documentation Excellence Achievements": [[22, "documentation-excellence-achievements"]], "\ud83c\udfd7\ufe0f Development Workflow": [[2, "development-workflow"]], "\ud83c\udfd7\ufe0f Enhanced Technical Architecture": [[24, "enhanced-technical-architecture"]], "\ud83c\udfd7\ufe0f Technical Architecture Enhancements": [[23, "technical-architecture-enhancements"]], "\ud83d\udc0d SQLAlchemy Models": [[54, "sqlalchemy-models"]], "\ud83d\udc33 Container Development": [[2, "container-development"]], "\ud83d\udc65 User Impact": [[0, "user-impact"]], "\ud83d\udcc1 New Documentation Structure": [[0, "new-documentation-structure"]], "\ud83d\udcc8 Content Metrics": [[0, "content-metrics"]], "\ud83d\udcc8 Expected Business Impact - Accelerated Through Integration": [[23, "expected-business-impact-accelerated-through-integration"]], "\ud83d\udcc8 Measurable Impact and Value": [[22, "measurable-impact-and-value"]], "\ud83d\udcc8 Performance and SEO": [[3, "performance-and-seo"]], "\ud83d\udcc8 Success Criteria": [[24, "success-criteria"]], "\ud83d\udcc8 Success Metrics Integration": [[35, "success-metrics-integration"]], "\ud83d\udcca API Endpoints": [[55, "api-endpoints"]], "\ud83d\udcca Analytics and Monitoring": [[3, "id7"]], "\ud83d\udcca Assessment Criteria Schema": [[54, "assessment-criteria-schema"]], "\ud83d\udcca Documentation Excellence": [[26, null]], "\ud83d\udcca Documentation Metrics": [[1, "documentation-metrics"]], "\ud83d\udcca Documentation Overview": [[26, "documentation-overview"]], "\ud83d\udcca Effort Estimation Summary - Integrated Timeline": [[25, "effort-estimation-summary-integrated-timeline"]], "\ud83d\udcca Expansion Statistics": [[0, "expansion-statistics"]], "\ud83d\udcca Implementation Priority Matrix - Integrated with Existing Phases": [[23, "implementation-priority-matrix-integrated-with-existing-phases"]], "\ud83d\udcca Integration Benefits": [[35, "integration-benefits"]], "\ud83d\udcca Key Performance Indicators": [[24, "key-performance-indicators"]], "\ud83d\udcca Key Statistics": [[3, "key-statistics"]], "\ud83d\udcca Measurable Impact": [[3, "measurable-impact"]], "\ud83d\udcca Monitoring & Debugging": [[2, "monitoring-debugging"]], "\ud83d\udcca Success Metrics": [[0, "success-metrics"]], "\ud83d\udccb Documentation Coverage": [[1, "documentation-coverage"]], "\ud83d\udccb Enterprise-Grade Content": [[3, "enterprise-grade-content"]], "\ud83d\udccb Executive Summary": [[23, "executive-summary"], [35, "executive-summary"]], "\ud83d\udccb Future Expansion Opportunities": [[0, "future-expansion-opportunities"]], "\ud83d\udccb Implementation Priority Matrix - Integrated with Existing Phases": [[25, "implementation-priority-matrix-integrated-with-existing-phases"]], "\ud83d\udccb Success Metrics": [[23, "success-metrics"]], "\ud83d\udcd6 Documentation Quality": [[1, "documentation-quality"]], "\ud83d\udcd6 Documentation Sections": [[3, "documentation-sections"]], "\ud83d\udcd6 User Guides (user-guides/)": [[3, "user-guides-user-guides"]], "\ud83d\udcda Additional Resources": [[2, "additional-resources"]], "\ud83d\udcda Blast-Radius Security Tool Documentation": [[3, null]], "\ud83d\udcda Comprehensive User Guide Ecosystem": [[22, "comprehensive-user-guide-ecosystem"]], "\ud83d\udcda Comprehensive User Guides": [[26, "comprehensive-user-guides"]], "\ud83d\udcda Documentation Structure": [[1, "documentation-structure"], [3, "documentation-structure"], [3, "id1"]], "\ud83d\udcda Enterprise User Guides": [[26, null]], "\ud83d\udcde Support": [[3, "support"]], "\ud83d\udd04 API Endpoints": [[56, "api-endpoints"]], "\ud83d\udd04 Celery Tasks for Batch Processing": [[55, "celery-tasks-for-batch-processing"]], "\ud83d\udd04 Implementation Roadmap Details - Integrated Timeline": [[24, "implementation-roadmap-details-integrated-timeline"]], "\ud83d\udd04 Migration Strategy": [[35, "migration-strategy"], [54, "migration-strategy"]], "\ud83d\udd04 Migration and Rollout Plan": [[23, "migration-and-rollout-plan"]], "\ud83d\udd04 Next Steps": [[24, "next-steps"]], "\ud83d\udd0d Search and Navigation": [[3, "search-and-navigation"]], "\ud83d\udd12 Security-First Development": [[2, "security-first-development"]], "\ud83d\udd17 Integration Architecture": [[56, "integration-architecture"]], "\ud83d\udd17 Integration Strategy": [[35, "integration-strategy"]], "\ud83d\udd27 Additional Build Features": [[3, "additional-build-features"]], "\ud83d\udd27 Build Commands": [[3, "id3"]], "\ud83d\udd27 Implementation Roadmap - Integrated with Existing Phases": [[24, "implementation-roadmap-integrated-with-existing-phases"]], "\ud83d\udd27 Technical Dependencies": [[23, "technical-dependencies"], [25, "technical-dependencies"]], "\ud83d\udd27 Technical Documentation (technical/)": [[3, "technical-documentation-technical"]], "\ud83d\udd27 Technical Implementation": [[0, "technical-implementation"]], "\ud83d\udd27 Technical Operations": [[3, "technical-operations"]], "\ud83d\udd27 Troubleshooting": [[2, "troubleshooting"]], "\ud83d\udd2e Future Enhancements": [[3, "future-enhancements"]], "\ud83d\udd34 New Features to Implement": [[25, "new-features-to-implement"]], "\ud83d\udd34 Offensive Security Operations": [[3, "offensive-security-operations"]], "\ud83d\udd35 Defensive Security Operations": [[3, "defensive-security-operations"]], "\ud83d\uddc4\ufe0f Database Schema Design": [[54, "database-schema-design"]], "\ud83d\ude80 Building the Documentation": [[3, "building-the-documentation"]], "\ud83d\ude80 Deployment": [[3, "deployment"]], "\ud83d\ude80 Deployment Architecture": [[24, "deployment-architecture"]], "\ud83d\ude80 Deployment Strategy": [[23, "deployment-strategy"]], "\ud83d\ude80 Future Enhancements and Roadmap": [[22, "future-enhancements-and-roadmap"]], "\ud83d\ude80 Getting Started": [[26, null]], "\ud83d\ude80 Implementation Priorities": [[35, "implementation-priorities"]], "\ud83d\ude80 Next Steps": [[1, "next-steps"]], "\ud83d\ude80 Quick Start": [[2, "quick-start"], [3, "quick-start"], [26, "quick-start"]], "\ud83d\ude80 Usage Recommendations": [[0, "usage-recommendations"]], "\ud83d\udee0\ufe0f Development": [[3, "development"]], "\ud83d\udee0\ufe0f Local CI/CD Pipeline": [[2, "local-ci-cd-pipeline"]], "\ud83d\udee1\ufe0f Enhanced Security Features": [[24, "enhanced-security-features"]], "\ud83d\udee1\ufe0f Security Documentation (security/)": [[3, "security-documentation-security"]], "\ud83d\udfe1 Partially Implemented Features": [[25, "partially-implemented-features"]], "\ud83d\udfe2 Already Implemented Features": [[25, "already-implemented-features"]], "\ud83e\udde0 ML Architecture Design": [[55, "ml-architecture-design"]], "\ud83e\uddea Testing Strategy": [[2, "testing-strategy"]]}, "docnames": ["DOCUMENTATION_EXPANSION_SUMMARY", "DOCUMENTATION_SUMMARY", "LOCAL-DEVELOPMENT", "README", "access-control/least-privilege-framework", "api/asset-management", "api/attack-path-analysis", "api/authentication", "api/index", "api/mitre-attack-integration", "api/threat-modeling", "architecture/zero-trust-architecture", "compliance/gdpr-compliance-framework", "configuration", "deployment/environment-setup", "deployment/production-architecture", "deployment/production-deployment-guide", "deployment/troubleshooting-guide", "development/code-standards", "development/contributing", "development/setup", "development/workflow", "documentation-overview", "enhanced-features-summary", "enhanced-prd-v2", "implementation-gap-analysis", "index", "installation", "latest-implementations-summary", "multi-cloud-integration", "operations/runbooks/backup-recovery-runbooks", "operations/runbooks/maintenance-procedures", "operations/runbooks/monitoring-runbooks", "performance/index", "performance/optimization", "phase-integration-plan", "quick-start-guide", "security/architecture/overview", "security/compliance-documentation", "security/enhanced-audit-logging", "security/framework", "security/incident-response-procedures", "security/index", "security/operations/incident-response", "security/procedures/security-review-process", "security/procedures/vulnerability-disclosure", "security/reviews/security-assessment", "security/reviews/security-review-2025-06-14", "security/reviews/vulnerability-management", "security/security-review-processes", "security/security-summary", "security/testing/dynamic-testing", "security/testing/security-automation", "security/testing/static-analysis", "technical-specifications/compliance-framework-schema", "technical-specifications/ml-threat-prediction", "technical-specifications/thehive-integration", "technical/attack-path-architecture", "technical/attack-path-flows", "technical/database-design", "technical/index", "technical/product-requirements", "testing/index", "testing/unit-tests", "troubleshooting/common-issues", "troubleshooting/faq", "use-cases/asset-discovery", "use-cases/attack-path-analysis", "user-guides/administrators", "user-guides/attack-path-analysis", "user-guides/compliance-officers", "user-guides/executive-leadership", "user-guides/index", "user-guides/mitre-attack-integration", "user-guides/purple-team-members", "user-guides/red-team-members", "user-guides/security-architects", "user-guides/soc-operators", "user-guides/threat-modeling"], "envversion": {"sphinx": 62, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.intersphinx": 1, "sphinx.ext.todo": 2, "sphinx.ext.viewcode": 1}, "filenames": ["DOCUMENTATION_EXPANSION_SUMMARY.md", "DOCUMENTATION_SUMMARY.md", "LOCAL-DEVELOPMENT.md", "README.md", "access-control/least-privilege-framework.rst", "api/asset-management.rst", "api/attack-path-analysis.rst", "api/authentication.rst", "api/index.rst", "api/mitre-attack-integration.rst", "api/threat-modeling.rst", "architecture/zero-trust-architecture.rst", "compliance/gdpr-compliance-framework.rst", "configuration.rst", "deployment/environment-setup.md", "deployment/production-architecture.rst", "deployment/production-deployment-guide.md", "deployment/troubleshooting-guide.md", "development/code-standards.rst", "development/contributing.rst", "development/setup.rst", "development/workflow.rst", "documentation-overview.rst", "enhanced-features-summary.md", "enhanced-prd-v2.md", "implementation-gap-analysis.md", "index.rst", "installation.rst", "latest-implementations-summary.rst", "multi-cloud-integration.md", "operations/runbooks/backup-recovery-runbooks.md", "operations/runbooks/maintenance-procedures.md", "operations/runbooks/monitoring-runbooks.md", "performance/index.rst", "performance/optimization.rst", "phase-integration-plan.md", "quick-start-guide.rst", "security/architecture/overview.rst", "security/compliance-documentation.md", "security/enhanced-audit-logging.rst", "security/framework.rst", "security/incident-response-procedures.md", "security/index.rst", "security/operations/incident-response.rst", "security/procedures/security-review-process.rst", "security/procedures/vulnerability-disclosure.rst", "security/reviews/security-assessment.rst", "security/reviews/security-review-2025-06-14.rst", "security/reviews/vulnerability-management.rst", "security/security-review-processes.md", "security/security-summary.rst", "security/testing/dynamic-testing.rst", "security/testing/security-automation.rst", "security/testing/static-analysis.rst", "technical-specifications/compliance-framework-schema.md", "technical-specifications/ml-threat-prediction.md", "technical-specifications/thehive-integration.md", "technical/attack-path-architecture.rst", "technical/attack-path-flows.rst", "technical/database-design.rst", "technical/index.rst", "technical/product-requirements.rst", "testing/index.rst", "testing/unit-tests.rst", "troubleshooting/common-issues.rst", "troubleshooting/faq.rst", "use-cases/asset-discovery.rst", "use-cases/attack-path-analysis.rst", "user-guides/administrators.rst", "user-guides/attack-path-analysis.rst", "user-guides/compliance-officers.rst", "user-guides/executive-leadership.rst", "user-guides/index.rst", "user-guides/mitre-attack-integration.rst", "user-guides/purple-team-members.rst", "user-guides/red-team-members.rst", "user-guides/security-architects.rst", "user-guides/soc-operators.rst", "user-guides/threat-modeling.rst"], "indexentries": {}, "objects": {}, "objnames": {}, "objtypes": {}, "terms": {"": [0, 7, 14, 16, 18, 19, 21, 25, 26, 27, 30, 31, 32, 36, 38, 42, 45, 46, 48, 51, 52, 53, 63, 64, 68, 69, 74, 75, 76, 77], "0": [4, 5, 6, 8, 9, 12, 13, 14, 16, 17, 18, 19, 20, 21, 23, 25, 26, 27, 29, 30, 31, 32, 33, 34, 39, 40, 44, 46, 47, 48, 50, 51, 52, 53, 54, 55, 56, 57, 59, 62, 63, 64, 65, 66, 67, 69, 72, 73, 77, 78], "00": [5, 16, 18, 30, 54], "000": [3, 15, 22, 26, 32, 39, 45, 65], "000000": [18, 54], "000_initi": 18, "001": [5, 9, 67, 73], "001_attack_path": 18, "002": 67, "0044": 6, "00z": [5, 6, 7, 8, 18, 73], "01": [4, 5, 7, 18, 30, 33, 39, 54, 57, 63, 73], "01t00": 5, "02": 5, "03": [16, 39], "04": [5, 16, 19, 20, 27], "041": [3, 22, 26], "05": [16, 78], "06": 45, "07": 16, "0_linux_amd64": 16, "0f": [69, 78], "0m": 52, "1": [2, 3, 4, 5, 6, 8, 9, 12, 13, 15, 18, 21, 22, 26, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 42, 44, 46, 48, 50, 51, 52, 53, 57, 59, 60, 61, 62, 63, 64, 65, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78], "10": [0, 5, 6, 8, 12, 13, 17, 18, 19, 20, 21, 22, 26, 27, 29, 30, 31, 33, 34, 36, 39, 40, 42, 44, 46, 48, 50, 51, 52, 54, 55, 57, 59, 61, 62, 63, 64, 65, 66, 67, 75, 76, 77, 78], "100": [0, 1, 3, 5, 8, 10, 12, 13, 15, 16, 17, 18, 22, 29, 31, 32, 33, 34, 38, 39, 41, 44, 46, 47, 50, 54, 55, 57, 59, 60, 61, 62, 66, 69, 73], "1000": [5, 6, 8, 9, 10, 13, 15, 16, 17, 26, 28, 31, 33, 34, 39, 55, 57, 60, 61, 73, 75, 76], "10000": [9, 10, 15, 28, 31, 33, 34, 39, 78], "100000": 28, "10000000": 28, "100000000": [28, 78], "1000m": [17, 33], "10021": 51, "100gb": [20, 27, 65], "100k": [15, 23, 24, 26, 33], "100m": [28, 33, 59, 60, 61, 78], "100mb": 13, "101": 5, "1024": 34, "10485760": 13, "10k": 33, "10m": [15, 23, 24, 26, 28, 33, 34, 35, 61, 69], "10mb": 13, "10tb": 28, "10x": 61, "11": [2, 3, 14, 18, 20, 27, 31, 34, 40, 51, 52, 53, 62, 63, 65], "11223344": 7, "11t22": [6, 8], "12": [6, 8, 12, 13, 16, 18, 25, 30, 36, 40, 45, 46, 47, 50, 57, 61, 62, 66, 67, 74, 75, 76], "120": [6, 45], "123": [4, 18, 21, 39, 66], "1234": [5, 21, 29], "12345": 29, "123456": 7, "12345678": [7, 29], "123456789012": [29, 64], "1234567890abcdef0": [5, 63], "123e4567": 7, "1247": 5, "1248": 5, "1249": 5, "1250": [6, 8], "125000": 6, "127": [5, 14, 20], "128": [33, 39], "128k": [33, 34], "12d3": 7, "12gb": [33, 34], "13": [23, 24, 25, 45, 61], "133": 5, "14": [25, 35, 42, 45, 46, 48, 50, 65, 72, 73, 76, 77], "145": 6, "15": [0, 1, 5, 6, 12, 16, 18, 20, 22, 26, 27, 29, 31, 32, 33, 39, 40, 43, 45, 50, 51, 54, 62, 65, 66, 67, 70, 72, 76, 77], "150": 12, "15000": 6, "150m": 33, "156": [5, 66], "15t10": [5, 7, 18, 73], "16": [5, 12, 16, 24, 27, 33, 54, 63, 66, 77], "1639234567": [6, 8], "1642248000": [5, 10], "168": [4, 5, 18, 34, 51, 62, 63, 64, 66, 73, 78], "16gb": [14, 20, 27, 34, 65], "17": [12, 29], "1705312200": 7, "1705314000": 7, "172": [63, 66], "18": [2, 6, 12, 14, 20, 25, 27, 31, 35, 40, 50, 65, 76], "180": 26, "1800": [7, 13, 34], "183": [3, 22, 26], "192": [5, 18, 34, 51, 62, 63, 64, 66, 73], "1_hour_crit": 40, "1f": [69, 78], "1gb": [33, 34, 64], "1gi": 17, "1h": 32, "1k": 33, "1x": 76, "1y": 34, "2": [2, 3, 4, 5, 6, 8, 9, 15, 18, 21, 26, 29, 30, 32, 33, 34, 37, 39, 42, 44, 45, 46, 48, 50, 52, 57, 59, 62, 63, 65, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78], "20": [0, 5, 6, 8, 12, 13, 14, 15, 17, 19, 20, 21, 27, 29, 31, 32, 33, 34, 39, 48, 55, 56, 57, 59, 61, 63, 78], "200": [7, 8, 18, 33, 34, 36, 51, 52, 62, 63], "2000": [9, 15, 33], "200k": 50, "200m": [23, 24, 33, 34, 35, 60, 61], "201": [8, 18, 51], "2012": 29, "2018": 54, "2021": 16, "2023": 5, "2024": [5, 6, 7, 8, 18, 21, 30, 39, 54, 73], "2025": [42, 43, 44, 45, 46, 48], "2026": 35, "204": 18, "20m": 33, "21": [12, 16], "22": [5, 27, 66], "23": [5, 6, 18, 52, 62], "24": [5, 6, 8, 12, 13, 22, 25, 26, 32, 33, 35, 39, 40, 43, 45, 46, 48, 49, 50, 62, 64, 66, 67, 70, 71, 72, 74, 75, 76, 77], "245": 8, "247": 66, "24h": [30, 31, 73], "24x7": 40, "25": [1, 6, 23, 25, 29, 34, 50, 67], "250": 45, "255": [18, 54, 56, 59, 63], "256": [11, 13, 27, 28, 37, 39, 40, 42, 47, 50, 52, 60, 61, 63, 65], "256k": [33, 34], "256mb": [33, 34, 64], "27001": [22, 23, 24, 25, 26, 37, 39, 42, 44, 46, 50, 54, 61, 65, 67, 70, 76], "27002": 76, "27005": 76, "276": [3, 22, 26], "28": [16, 47], "285": 6, "295": 66, "2e": 51, "2f": [51, 56], "2fetc": 51, "2fpasswd": 51, "2g": [34, 64], "2gb": 61, "2gi": 17, "2h": 31, "2x": 33, "2xlarg": 15, "3": [2, 3, 4, 5, 6, 8, 13, 15, 18, 21, 26, 28, 29, 30, 32, 33, 34, 37, 38, 39, 40, 42, 44, 46, 48, 50, 51, 52, 53, 57, 59, 60, 62, 63, 64, 65, 69, 70, 71, 72, 73, 74, 75, 76, 77], "30": [4, 5, 6, 7, 8, 12, 13, 15, 16, 18, 20, 21, 23, 24, 27, 28, 29, 30, 31, 32, 33, 34, 36, 38, 39, 40, 41, 43, 45, 46, 48, 50, 52, 54, 56, 57, 59, 61, 62, 63, 66, 73, 76, 77], "300": [0, 1, 5, 6, 8, 13, 30, 31, 33, 34, 61, 64, 66, 69, 77], "3000": [13, 14, 20, 27, 32, 36, 62, 64, 65], "3001": 27, "30d": 73, "31": [23, 25, 39], "312": [3, 22, 26], "32": [16, 33, 34, 47, 50, 64], "327": [47, 50], "32gb": [20, 27], "3306": 5, "3389": 66, "34": 61, "3420": 6, "35": [5, 29, 76], "36": 76, "3600": [13, 33, 34, 57, 66], "365": [12, 39, 40, 64], "370": [3, 22, 26], "374": [3, 22], "39": [29, 69], "3d": 75, "3f": 55, "4": [2, 4, 5, 6, 8, 13, 15, 17, 21, 27, 28, 30, 32, 33, 34, 38, 40, 44, 46, 48, 50, 52, 57, 59, 62, 63, 65, 66, 69, 70, 71, 72, 74, 75, 76, 77], "40": [0, 3, 5, 22, 23, 24, 29, 33, 34, 35, 44, 48, 57, 59, 61, 69, 77], "400": [6, 8, 18, 33, 34, 49, 51, 56], "400m": 33, "401": [8, 18, 51], "403": [8, 18, 51], "404": [6, 8, 18, 51, 55, 56, 64], "409": [8, 18], "4096": 64, "42": 55, "422": [3, 8, 18, 22, 26, 51], "423": [6, 8, 51], "426614174000": 7, "429": [8, 51], "443": [5, 16, 17, 31, 64, 66], "446": [3, 22, 26], "45": [6, 29, 40, 44, 50, 66, 76], "450": 67, "456": 4, "470": [3, 22, 26], "48": [45, 48, 49, 50, 67], "480": [13, 64], "488": [22, 26], "4g": [33, 34, 64], "4gb": [33, 34], "4gi": [17, 32], "4h": 67, "4m": 43, "4xlarg": 15, "5": [1, 2, 5, 6, 8, 13, 17, 18, 21, 22, 27, 29, 30, 31, 32, 33, 34, 36, 39, 40, 44, 45, 46, 47, 48, 50, 51, 52, 55, 57, 59, 61, 62, 63, 64, 65, 66, 69, 70, 71, 74, 75, 76, 77, 78], "50": [0, 1, 3, 5, 12, 13, 22, 29, 32, 33, 34, 36, 45, 52, 54, 55, 56, 57, 59, 61, 77], "500": [6, 8, 9, 16, 18, 33, 54, 56, 62, 64, 75], "5000": [33, 78], "50000": 78, "500000": 78, "500gb": 27, "500k": 78, "500m": [17, 33, 34], "502": [47, 50], "503": 8, "5050": 20, "50gb": [14, 27, 65], "50m": 28, "51": 5, "512": 34, "514": [22, 26], "519": [3, 22, 26], "522": [3, 22, 26], "53": 16, "5432": [2, 13, 14, 16, 17, 20, 27, 32, 64], "551": [3, 22, 26], "569": [22, 26], "57": 47, "58": [61, 66], "59": [29, 69], "5g": 76, "5k": 33, "5m": [33, 34, 52], "6": [3, 6, 13, 15, 18, 21, 22, 26, 30, 32, 40, 46, 48, 50, 52, 61, 62, 63, 67, 69, 72, 74, 76, 77, 78], "60": [4, 5, 6, 8, 13, 21, 22, 23, 24, 29, 33, 34, 35, 45, 52, 59, 60, 61, 62, 64, 65, 69, 71, 75, 76, 77], "600": [5, 33], "61": 5, "62": [46, 47, 50], "63": 5, "6379": [13, 14, 16, 20, 27, 64], "64": [33, 34, 39], "64mb": [33, 34], "65": [5, 34, 74], "65535": 59, "69": 54, "6m": 73, "7": [3, 4, 5, 13, 19, 20, 22, 25, 26, 27, 30, 31, 32, 35, 38, 39, 40, 44, 46, 48, 50, 52, 55, 59, 62, 63, 65, 66, 67, 70, 71, 72, 73, 74, 75, 76, 77, 78], "70": [21, 22, 33, 54, 71], "72": [12, 38, 43, 45, 46, 48, 50, 66, 77], "720h": 31, "75": [3, 6, 13, 22, 34, 50, 70, 71, 74, 75, 78], "7687": [13, 20, 27, 64], "79": [29, 69], "7d": 73, "8": [4, 5, 6, 13, 18, 19, 20, 25, 27, 31, 33, 34, 35, 40, 46, 48, 52, 57, 64, 65, 66, 67, 73, 74, 76, 78], "80": [3, 22, 23, 24, 29, 31, 33, 34, 35, 49, 50, 52, 54, 59, 61, 66, 69, 70, 73, 74, 76], "800": [0, 72, 73, 74, 75, 76, 77], "8000": [2, 3, 13, 14, 16, 17, 20, 23, 27, 29, 30, 31, 33, 34, 36, 51, 53, 64, 65], "802": 76, "8080": [3, 11], "8088": 66, "80m": 33, "8125": 52, "85": [3, 6, 18, 22, 23, 24, 32, 35, 44, 50, 70, 71, 75, 77, 78], "8601": 30, "86400": [13, 31], "87": 6, "87654321": 7, "88": [6, 18, 65, 77], "892": 66, "8g": [33, 34], "8gb": [14, 27, 33, 34, 36, 65], "9": [2, 3, 15, 20, 23, 27, 28, 30, 33, 34, 35, 40, 44, 46, 48, 50, 54, 55, 57, 59, 61, 64, 67, 78], "90": [1, 4, 12, 13, 18, 21, 22, 23, 33, 44, 45, 46, 48, 50, 54, 55, 59, 60, 61, 62, 63, 65, 70, 71, 74, 75, 76, 77, 78], "900": [33, 34], "9000": 3, "9090": [3, 13, 17, 32], "90d": 73, "91": 19, "9121": 33, "9187": 33, "92": [6, 44, 46, 50, 52], "94": [46, 50, 54], "95": [1, 5, 6, 8, 18, 23, 24, 25, 33, 35, 38, 40, 44, 46, 48, 49, 50, 52, 54, 60, 61, 62, 63, 70, 74, 75, 76, 77], "96": [23, 24, 25, 46, 50], "97": [40, 50], "98": [35, 44, 46, 48, 50, 52], "99": [15, 23, 28, 35, 40, 50, 61], "999": 10, "A": [3, 8, 14, 17, 19, 23, 29, 32, 43, 46, 59, 76], "AND": [18, 31, 34, 59, 66], "AS": [31, 32, 59], "AT": 38, "As": [41, 67, 68, 70, 71, 74, 75, 76, 77], "BE": 38, "BY": [17, 20, 31, 32, 33, 34, 59, 64], "Be": [19, 21, 45], "By": [52, 62, 69], "FOR": [34, 59], "For": [2, 3, 5, 7, 9, 10, 13, 16, 18, 19, 20, 21, 25, 26, 27, 33, 34, 36, 37, 40, 42, 43, 45, 51, 53, 57, 62, 63, 65, 67, 72, 73, 78], "IF": 59, "IN": [34, 59], "INTO": 51, "IT": [5, 40, 70, 76, 77], "If": [6, 18, 19, 27, 32, 36, 42, 51, 64], "In": [3, 24, 26, 34, 35, 36, 40, 48, 57], "It": [25, 50, 53, 65], "NOT": [12, 18, 31, 39, 45, 54, 59, 64], "No": [3, 5, 17, 18, 19, 21, 23, 25, 29, 38, 40, 43, 45, 49, 51, 55, 56, 62, 64, 65, 67, 69], "Not": [6, 8, 18, 34], "ON": [16, 17, 18, 34, 39, 54, 59, 64], "OR": [20, 51, 52, 59, 62], "On": [7, 17, 32, 41, 60, 66, 77], "One": [10, 19, 62, 77], "Or": [2, 19, 20, 65], "THEN": 59, "TO": 16, "The": [0, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 18, 21, 22, 23, 24, 26, 28, 29, 32, 33, 35, 36, 38, 39, 40, 42, 43, 44, 45, 46, 47, 48, 50, 52, 54, 55, 56, 57, 58, 59, 60, 61, 62, 65, 66, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78], "Then": 19, "There": 19, "These": [18, 44, 58, 63], "To": [19, 36, 45, 61], "WITH": [16, 18, 39, 54, 59], "Will": [20, 63], "With": [50, 64, 77], "_": [51, 59, 63], "__aenter__": [34, 56], "__aexit__": [34, 56], "__class__": 56, "__init__": [7, 19, 25, 34, 40, 44, 51, 52, 53, 55, 56, 57], "__main__": 63, "__name__": [18, 19, 34, 55, 56, 63], "__post_init__": 56, "__pycache__": 20, "__repr__": 63, "__table_args__": 54, "__tablename__": [25, 54, 55, 56], "_access_ord": 34, "_add_incident_observ": 56, "_add_incident_task": 56, "_analyze_single_target": 57, "_build": 3, "_build_case_descript": 56, "_cach": 34, "_calculate_behavioral_featur": 55, "_calculate_edge_likelihood": 57, "_calculate_impact_metr": 57, "_calculate_network_featur": 55, "_calculate_path_risk": 34, "_calculate_propagation_prob": 57, "_calculate_vulnerability_featur": 55, "_categorize_risk": 55, "_count_by_sever": 51, "_count_by_tool": 51, "_encode_categor": 55, "_evaluate_model_perform": 55, "_fetch_mitre_techniqu": 34, "_generate_case_tag": 56, "_generate_recommend": [51, 53], "_generate_threat_alert": 55, "_get_active_asset_id": 55, "_get_asset_data": 55, "_get_files_with_critical_issu": 53, "_get_thehive_config": 56, "_get_user_token": 51, "_id": 56, "_internal_method": 18, "_is_false_posit": 51, "_load_mitre_map": 57, "_load_model": 55, "_map_bandit_sever": 53, "_map_indicator_to_observable_typ": 56, "_map_semgrep_sever": 53, "_perform_attack_analysi": 34, "_precompute_shortest_path": 34, "_prepare_training_data": 55, "_process_case_update_webhook": 56, "_rank_path": 57, "_save_model": 55, "_search": 56, "_store_case_map": 56, "_store_l1": 34, "_store_prediction_result": 55, "_templat": 3, "_update_incident_from_case_data": 56, "_update_incident_from_case_statu": 56, "_validate_asset": 19, "_validate_input": 18, "_validate_webhook_signatur": 56, "a1b2c3d4e5f6": 73, "a456": 7, "aaa": [19, 62, 63], "abac": 76, "abil": 77, "abort": 14, "about": [5, 7, 9, 18, 19, 37, 42, 45, 51, 53, 65, 77], "abov": [32, 41], "abstract": 18, "abus": [6, 60, 74, 75, 77], "ac": [38, 48], "academ": [74, 75, 76, 77], "acceler": [22, 25, 71], "accept": [14, 19, 21, 29, 38, 45, 48, 51, 60, 61, 63, 68, 76], "access": [0, 3, 6, 7, 8, 11, 12, 14, 15, 19, 20, 25, 26, 27, 29, 32, 34, 37, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 57, 59, 60, 61, 64, 66, 67, 69, 70, 71, 72, 73, 74, 75, 76, 78], "access_certif": 40, "access_control": [28, 40, 78], "access_control_polici": 40, "access_key_id": [29, 66], "access_log": 34, "access_method": 39, "access_pattern_anomali": 55, "access_performance_metr": 77, "access_raw_data": 77, "access_request": 4, "access_review": 4, "access_reviews_quarterli": 40, "access_token": [7, 8, 62], "access_typ": 4, "accomplish": [0, 47], "accord": 68, "accordingli": [11, 66], "account": [6, 7, 8, 12, 13, 16, 27, 28, 29, 36, 38, 39, 40, 43, 44, 45, 48, 51, 56, 64, 65, 66, 67, 69, 70, 75, 76, 77, 78], "account_id": 14, "account_lockout_mechan": 40, "accumul": 53, "accur": [1, 12, 61, 67, 69, 75, 77], "accuraci": [0, 1, 12, 23, 24, 32, 33, 35, 38, 40, 44, 53, 55, 61, 74, 75, 76, 77], "achiev": [3, 24, 26, 47, 61, 70, 71, 74, 75, 76, 77], "acid": 61, "acknowledg": [45, 77], "acknowledge_alert": 77, "aclos": 56, "acm": [16, 17, 31], "acquir": 76, "acquisit": [71, 75, 76], "across": [0, 1, 3, 5, 18, 22, 23, 26, 28, 29, 33, 36, 40, 44, 47, 48, 50, 54, 61, 62, 66, 67, 69, 70, 72, 73, 74, 75, 76, 77], "act": [19, 45, 62, 63, 70, 76, 78], "action": [0, 1, 3, 11, 18, 19, 21, 22, 24, 29, 32, 39, 40, 44, 45, 46, 49, 51, 54, 56, 60, 61, 62, 63, 64, 65, 66, 69, 72, 74, 75, 76, 77], "activ": [0, 2, 4, 5, 7, 8, 11, 12, 14, 20, 24, 27, 28, 29, 31, 33, 36, 38, 39, 40, 45, 48, 54, 56, 59, 60, 61, 63, 66, 67, 68, 70, 72, 74, 75, 76, 77], "active_connect": 31, "active_job": 29, "actor": [6, 8, 22, 26, 36, 37, 39, 40, 57, 61, 65, 67, 68, 69, 72, 74, 75, 76, 77], "actor_id": 78, "actor_prob": 73, "actor_scor": 67, "actor_techniqu": 67, "actual": [0, 21, 50, 51, 64, 65, 69, 72, 77], "acumen": [70, 74, 75, 76, 77], "ad": [1, 19, 21, 23, 24, 25, 29, 35, 40, 44, 56, 62, 64, 67, 68], "adapt": [37, 61, 74, 75, 76], "add": [2, 3, 5, 17, 18, 19, 20, 21, 23, 24, 29, 31, 32, 35, 54, 56, 57, 62, 63, 66, 77], "add_argu": 62, "add_asset": 57, "add_compliance_framework": 54, "add_find": 44, "add_head": 34, "add_network_rang": 66, "add_observ": 56, "add_provid": 66, "add_relationship": 57, "add_task": 56, "addit": [16, 19, 27, 28, 29, 34, 36, 42, 43, 45, 49, 51, 53, 57, 65, 66, 67, 69, 75, 77], "additional_context": 73, "additional_depend": [18, 52], "addon": 31, "addopt": [62, 63], "address": [1, 3, 14, 18, 19, 21, 30, 36, 45, 49, 50, 52, 61, 63, 69, 76, 78], "adequ": [38, 40, 45, 77], "adequaci": [76, 77], "adher": [45, 50, 70, 75, 77], "adjac": 48, "adjust": [13, 17, 32, 33, 34, 39, 65, 68, 71, 73, 75, 76, 77], "admin": [4, 6, 13, 14, 16, 20, 32, 36, 45, 51, 62, 63, 64, 69], "admin123": 14, "admin_endpoint": 51, "admin_us": 63, "admin_workst": 6, "administ": 70, "administr": [1, 3, 4, 7, 13, 16, 22, 26, 27, 36, 39, 43, 45, 48, 60, 65, 75, 76, 77], "admiss": 43, "admonit": 3, "adopt": [0, 1, 22, 23, 25, 37, 48, 70, 71, 74, 75, 76, 77], "advanc": [0, 1, 6, 7, 8, 11, 22, 26, 28, 29, 46, 48, 50, 53, 63, 65, 69, 72], "advanced_attack_path_analysi": 75, "advanced_compliance_manag": 70, "advanced_evasion_techniqu": 75, "advanced_exercise_coordin": 74, "advantag": [3, 22, 50, 70, 71, 74, 75, 76], "adversari": [6, 74], "advisor": 43, "advisori": [31, 42, 45, 48, 70, 72, 74, 75, 76, 77], "ae": [11, 13, 28, 37, 38, 40, 42, 47, 50, 60, 61, 65], "aes_decrypt": 40, "aes_encrypt": 40, "aes_encrypt_hsm": 40, "affect": [23, 30, 41, 43, 45, 48, 49, 56, 61, 65, 69, 73, 77], "affected_asset": [6, 40, 54, 56, 57, 69, 73, 78], "affected_assets_count": 56, "affected_data_subject": 12, "affected_servic": 43, "affected_system": 49, "after": [14, 16, 20, 21, 27, 30, 34, 36, 38, 45, 47, 50, 51, 65, 66, 67, 69, 77], "afternoon": 76, "ag": [14, 27], "against": [0, 10, 11, 38, 40, 45, 46, 50, 73, 74, 75, 76, 77], "agenc": 43, "agenda": 41, "agent": [0, 5, 77, 78], "aggreg": [14, 15, 57, 71, 76, 77], "agil": 76, "agnost": [23, 24], "ago": [30, 32], "agre": 69, "agreement": [38, 45, 49, 70, 76], "ahead": [34, 74, 75, 76], "ai": [9, 10, 22, 26, 46, 50, 65, 70, 72, 73, 74, 75, 76, 77], "aicpa": 54, "aiohttp": 34, "air": [60, 75], "ak": 29, "akia": 29, "akto": 66, "alb": [16, 17, 31, 32], "alemb": [14, 16, 17, 18, 20, 25], "alert": [3, 4, 7, 8, 12, 14, 15, 22, 23, 24, 26, 28, 31, 36, 37, 38, 42, 43, 44, 48, 50, 51, 52, 55, 60, 61, 62, 66, 67, 68, 69, 70, 72, 74, 75, 76, 77], "alert_manag": 40, "alert_nam": 32, "alert_threshold": 78, "alert_triag": 40, "alertmanag": [16, 41], "alertmanagerservic": 40, "alertnam": 16, "algorithm": [3, 6, 18, 24, 33, 49, 53, 55, 58, 60, 61, 65, 74, 75, 77], "alias": 73, "alibaba": 29, "align": [3, 22, 23, 24, 25, 26, 35, 37, 39, 40, 42, 43, 48, 50, 61, 65, 70, 72, 74, 75, 76], "all": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 18, 19, 20, 21, 22, 24, 26, 27, 28, 30, 34, 36, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 53, 54, 55, 57, 58, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 74, 75, 76, 77, 78], "all_actor": 40, "all_cloud": 66, "all_endpoint": 40, "all_network": 66, "all_simple_path": 34, "all_techniqu": 40, "allkei": [32, 33, 34], "alloc": [0, 15, 17, 22, 28, 32, 33, 43, 65, 68, 70, 71, 72, 74, 75, 76, 77], "allocated_storag": 15, "allow": [2, 11, 13, 17, 29, 53, 65, 69], "allow_failur": 52, "allowed_valu": [5, 18], "allowprivilegeescal": 49, "alpin": 31, "alreadi": [1, 5, 10, 18, 20, 22, 23, 56], "alter": [18, 20, 64], "altern": [16, 17, 19, 31, 43, 50, 68, 75, 77], "alwai": [7, 8, 11, 15, 19, 34, 37, 40, 42, 59], "am": [5, 25, 30, 38, 54, 55, 66], "amazon": 16, "amazonaw": [14, 16], "amber": 56, "amd64": 16, "amtool": 16, "an": [3, 5, 6, 10, 18, 26, 33, 34, 38, 46, 48, 50, 51, 55, 56, 61, 63, 66, 67, 68, 69, 71, 77], "analys": [34, 36, 61, 77], "analysi": [0, 1, 2, 3, 4, 11, 12, 13, 14, 15, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 31, 33, 34, 37, 38, 39, 40, 41, 46, 48, 49, 55, 56, 61, 62, 63, 64, 66, 68, 70, 71, 74, 75], "analysis_metadata": 18, "analysis_result": 67, "analysis_tim": 63, "analysiserror": [18, 19], "analysisopt": 18, "analysisresult": 18, "analyst": [4, 26, 43, 59, 61, 70, 71, 72, 73, 74, 77], "analyt": [15, 22, 26, 28, 29, 50, 65, 67, 70, 71, 72, 74, 75, 77], "analyz": [3, 8, 17, 18, 19, 20, 25, 31, 32, 34, 38, 40, 51, 53, 56, 57, 59, 61, 64, 66, 67, 68, 69, 72, 73, 75, 77, 78], "analyze_api_secur": 66, "analyze_asset_blast_radiu": 56, "analyze_attack_path": [18, 21, 57, 67], "analyze_attack_path_async": 63, "analyze_attack_pattern": 73, "analyze_behavior": 73, "analyze_blast_radiu": [25, 67], "analyze_boundary_cross": 67, "analyze_bulk_path": 63, "analyze_compliance_risk": 67, "analyze_container_secur": 66, "analyze_dast_result": 51, "analyze_detection_coverag": 67, "analyze_incid": 40, "analyze_multiple_target": 57, "analyze_nuclei_result": 51, "analyze_path": [18, 19, 63], "analyze_technique_trend": 73, "analyze_threat_actor": 40, "analyze_threat_actor_fit": 67, "analyze_zap_result": 51, "analyzeattackpath": [18, 19], "android": 75, "angular": 14, "ani": [7, 18, 19, 26, 34, 45, 51, 52, 53, 55, 56, 77], "annot": [16, 17, 18, 19, 31, 50, 52], "announc": 45, "annual": [38, 40, 41, 43, 44, 48, 49, 50, 70, 71, 76, 77], "annual_revenu": 78, "anomal": [55, 77], "anomali": [4, 11, 24, 25, 29, 35, 37, 38, 39, 55, 57, 61, 73, 74, 75, 76, 77], "anomalies_detect": 55, "anomaly_detect": [4, 39, 55], "anomaly_scor": [55, 73], "anonym": [29, 37, 47, 76], "answer": [19, 65], "anti": [70, 75, 76, 77], "anticip": 77, "antiviru": [36, 40, 66], "apach": 3, "api": [0, 2, 11, 13, 14, 15, 16, 17, 19, 21, 23, 25, 27, 28, 31, 32, 33, 34, 35, 36, 37, 40, 41, 44, 45, 46, 48, 49, 51, 52, 59, 62, 63, 64, 68, 71, 72, 74, 75, 76, 77, 78], "api_bas": [8, 69], "api_base_url": 18, "api_discoveri": 66, "api_kei": [25, 52, 56], "api_key_encrypt": 56, "api_rate_limit": 13, "api_requests_per_second": 28, "api_response_time_p95": 28, "api_secur": 40, "api_security_architectur": 76, "api_token": [3, 5, 8, 25, 73, 78], "api_url": [25, 56], "api_v1_str": [13, 62], "api_workflow": 18, "apidiscoveryservic": 66, "apierror": 18, "apirout": [20, 56], "apitoken": [5, 8], "apivers": [14, 30, 33, 49, 66], "apm": 52, "app": [2, 13, 14, 16, 17, 18, 19, 20, 27, 29, 30, 31, 32, 33, 34, 36, 44, 46, 49, 51, 52, 53, 54, 55, 56, 62, 63, 64, 65, 66, 67, 77], "app_replica": 16, "app_server_001": 6, "appar": 51, "appear": [64, 77], "append": [34, 40, 44, 49, 51, 52, 53, 55, 56, 57, 62, 67], "appetit": [70, 71, 76], "appli": [0, 14, 16, 17, 20, 30, 32, 33, 34, 41, 45, 48, 49, 57, 59, 65, 77], "applianc": 77, "applic": [0, 5, 6, 7, 9, 10, 11, 19, 20, 22, 26, 27, 28, 29, 36, 39, 40, 42, 43, 44, 45, 47, 48, 49, 50, 56, 59, 61, 62, 64, 65, 67, 68, 70, 71, 73, 74, 75, 76, 77, 78], "application_awar": 40, "application_compliance_assess": 76, "application_layer_secur": 40, "application_log": 40, "application_memori": 33, "application_secur": 40, "application_security_design": 76, "application_security_group_id": 17, "application_threat_model": 76, "application_url": 52, "appreci": 45, "approach": [2, 21, 23, 26, 32, 35, 43, 44, 46, 48, 52, 61, 62, 72, 76, 77], "appropri": [1, 3, 4, 8, 12, 13, 19, 21, 29, 36, 39, 43, 45, 49, 52, 53, 54, 67, 68, 69, 73, 75, 77, 78], "approv": [4, 14, 19, 21, 28, 38, 41, 44, 45, 46, 49, 52, 68, 70, 71, 75, 76, 77], "approval_author": 40, "approval_record": 54, "approve_response_act": 77, "apt": [6, 8, 14, 22, 26, 27, 31, 69, 74, 75, 77, 78], "apt simul": 75, "apt1": 75, "apt28": [10, 72, 73, 74, 78], "apt29": [6, 8, 10, 69, 72, 73, 75, 78], "apt_simulation_author": 75, "aquasec": [14, 31, 52], "ar": [5, 6, 8, 9, 10, 15, 17, 18, 19, 21, 29, 32, 34, 36, 38, 40, 43, 45, 46, 48, 49, 50, 54, 63, 68, 69, 70, 71, 74, 75, 76, 77, 78], "architect": [1, 3, 22, 24, 26, 36, 44, 49, 61, 64, 65, 68, 71, 77], "architectur": [0, 1, 2, 3, 17, 19, 20, 21, 22, 25, 34, 35, 36, 41, 47, 65, 68, 69, 72, 74, 75], "architecture review": 44, "architecture_doc": 44, "archiv": [15, 32, 37, 39, 60, 64, 65, 68], "archive_retention_year": 39, "arcsight": 77, "area": [8, 40, 67, 76, 77], "area_scor": 40, "arg": [18, 19, 20, 34, 52, 62], "arn": [16, 17, 29, 31, 32, 64], "around": 67, "arrai": [6, 8, 54], "arrang": [19, 62, 63, 77], "arrivalr": 33, "arrow": 36, "articl": [12, 28], "artifact": [3, 46, 49, 51, 52, 53, 56, 77], "artifici": [71, 74, 75, 76], "artilleri": 33, "as_uuid": [54, 55, 56], "asc": [5, 8], "asdict": 55, "ask": [19, 20], "aspect": [0, 40, 44, 60, 62, 68, 76, 77], "asreproast": 75, "assembli": 77, "assert": [19, 21, 51, 62, 63], "assert_called_onc": 63, "assert_called_once_with": 19, "assert_us": [52, 53], "assertionerror": 63, "assess": [1, 3, 11, 15, 18, 19, 21, 22, 23, 24, 26, 28, 35, 36, 37, 38, 39, 42, 43, 45, 47, 52, 56, 60, 65, 68, 71, 73, 74, 75, 77], "assess_area_matur": 40, "assess_compliance_impact": 49, "assess_control_effect": 44, "assess_financial_impact": 78, "assess_framework": 25, "assess_gdpr_impact": 78, "assess_hipaa_impact": 78, "assess_implement": 44, "assess_monitor": 44, "assess_risk": 7, "assess_security_matur": 40, "assess_sox_impact": 78, "assess_test": 44, "assess_threat_coverag": 40, "assessment_area": 40, "assessment_criteria": 25, "assessment_d": 54, "assessment_id": [51, 54], "assessment_method": 54, "assessment_nam": 54, "assessment_typ": 54, "assessmentresult": 25, "assessor": [4, 54], "assessor_not": 54, "asset": [1, 3, 4, 6, 10, 13, 14, 15, 16, 17, 18, 19, 21, 23, 26, 31, 32, 33, 34, 35, 39, 40, 48, 49, 51, 52, 54, 55, 56, 57, 58, 59, 62, 63, 64, 65, 67, 69, 71, 73, 75, 76, 77, 78], "asset_ag": 66, "asset_configuration_chang": 66, "asset_count": 64, "asset_crit": 67, "asset_data": [33, 49, 57], "asset_discoveri": [18, 66], "asset_discovery_servic": 63, "asset_filt": 66, "asset_id": [5, 18, 25, 33, 34, 55, 56, 57], "asset_id_index": 34, "asset_inventory_complet": 54, "asset_manag": 40, "asset_metadata": 57, "asset_nam": 39, "asset_not_found": [5, 10], "asset_relationship": 59, "asset_relationships_confidence_rang": 59, "asset_relationships_no_self_refer": 59, "asset_relationships_port_rang": 59, "asset_remov": 66, "asset_risk": 57, "asset_status_enum": 59, "asset_typ": [5, 8, 17, 18, 34, 49, 54, 55, 56, 59, 63, 67], "asset_type_encod": 55, "asset_type_enum": 59, "asset_type_index": 34, "assetcr": 49, "assetcreaterequest": 18, "assetdiscoveryservic": 63, "assetfactori": [62, 63], "assetfeatur": 55, "assetid": 25, "assetmonitoringservic": 66, "assets_discov": [5, 66], "assets_found": 18, "assets_process": 55, "assets_risk_score_rang": 59, "assets_soft_delet": 59, "assetservic": 63, "assettyp": [5, 18], "assign": [21, 23, 36, 38, 41, 45, 48, 49, 63, 64, 65, 68, 69, 75, 77], "assigne": 56, "assist": [27, 60, 70, 74, 75, 77], "associ": [70, 71, 73, 76, 77], "assum": [11, 40, 63, 75], "assume_role_arn": 29, "assumerol": 29, "assur": [70, 71, 75, 76, 77], "ast": 52, "astext_typ": 54, "astyp": 34, "async": [7, 18, 19, 24, 25, 33, 34, 49, 54, 55, 56, 57, 67], "async_analysis_servic": 63, "asyncanalysisservic": 63, "asyncassetdiscoveri": 34, "asynccli": 56, "asynchron": [15, 34, 39, 63], "asyncio": [34, 55, 57, 63], "atm": 75, "atom": 34, "att": [3, 18, 22, 24, 25, 26, 33, 35, 42, 50, 60, 65, 69, 74, 75, 76, 77, 78], "attack": [0, 1, 3, 5, 7, 11, 13, 18, 19, 21, 22, 23, 24, 25, 26, 32, 33, 34, 35, 40, 41, 43, 44, 45, 46, 47, 48, 49, 51, 52, 60, 62, 63, 64, 66, 68, 74, 76], "attack path analysi": 26, "attack simul": 75, "attack_object": 67, "attack_path": [3, 6, 8, 18, 33, 34, 57, 59, 67, 69], "attack_path_result": 18, "attack_path_servic": [19, 62, 63, 67], "attack_path_timeout": 64, "attack_paths_detection_difficulty_rang": 59, "attack_paths_impact_score_rang": 59, "attack_paths_likelihood_rang": 59, "attack_paths_path_length_posit": 59, "attack_paths_per_minut": 33, "attack_paths_risk_score_rang": 59, "attack_scenario": [59, 78], "attack_scenarios_detection_probability_rang": 59, "attack_scenarios_impact_score_rang": 59, "attack_scenarios_likelihood_rang": 59, "attack_scenarios_risk_score_rang": 59, "attack_servic": 67, "attack_sophist": 73, "attack_techniqu": [6, 57, 59, 69], "attack_typ": 67, "attack_vector": 67, "attackpath": [8, 18, 57], "attackpathanalyz": [18, 19], "attackpathanalyzerprop": 18, "attackpatherror": 18, "attackpathprop": 19, "attackpathresult": [18, 19, 21], "attackpathservic": [18, 19, 62, 63, 67], "attackpathvisu": 18, "attackscenario": 57, "attackscenariorequest": 8, "attacktechniqu": 57, "attempt": [13, 41, 43, 51, 62, 75, 77], "attend": [69, 74, 75, 77], "attende": 41, "attent": [29, 36, 69, 77], "attract": 77, "attribut": [5, 8, 37, 45, 60, 61, 68, 72, 74, 75, 76, 77], "attribute_threat_actor": 73, "attributed_group": 73, "attribution_fail": 9, "attribution_threshold": 73, "auc": 55, "aud": 7, "audienc": [40, 76, 77], "audit": [2, 4, 7, 12, 13, 15, 22, 23, 24, 25, 26, 29, 37, 38, 40, 42, 43, 44, 47, 48, 49, 50, 59, 60, 61, 66, 67, 68, 71, 72, 74, 75, 76, 77, 78], "audit_alert": 39, "audit_all_access": 4, "audit_block": 39, "audit_configuration_chang": 13, "audit_coordination_access": 76, "audit_data_access": 13, "audit_databas": 78, "audit_en": 13, "audit_ev": 39, "audit_impl": 78, "audit_log": [28, 39, 49, 59, 66], "audit_log_all_request": 13, "audit_log_path": 13, "audit_log_sensitive_data": 13, "audit_logg": 40, "audit_login_ev": 13, "audit_permission_chang": 13, "audit_planning_and_execut": 70, "audit_support_act": 70, "auditloggingservic": 40, "auditor": [4, 67, 70, 76], "auth": [7, 8, 18, 19, 21, 23, 32, 36, 41, 51, 62, 66], "auth_head": 62, "auth_rate_limit": 13, "auth_respons": 8, "auth_token": 52, "authclient": 7, "authent": [0, 1, 3, 11, 15, 16, 18, 19, 21, 23, 24, 25, 26, 28, 37, 38, 39, 40, 41, 42, 44, 45, 47, 52, 53, 57, 59, 60, 61, 68, 69, 72, 73, 75, 76, 77], "authenticate_us": 40, "authentication_bypass": 52, "authi": 36, "author": [0, 1, 5, 6, 8, 9, 10, 11, 12, 17, 18, 19, 20, 21, 23, 26, 29, 31, 32, 37, 39, 40, 41, 42, 43, 44, 45, 52, 54, 56, 59, 60, 61, 62, 65, 68, 69, 70, 71, 73, 74, 75, 76, 77, 78], "authority_contact": 12, "authservic": 7, "auto": [0, 14, 15, 18, 20, 26, 28, 30, 31, 33, 44, 46, 49, 53, 60, 62, 63, 68], "auto_approve_threshold": 4, "auto_create_cas": 56, "auto_map": 39, "auto_notif": 12, "auto_remedi": 52, "auto_revert": 4, "autobuild": 3, "autocomplet": 3, "autodiscoveri": 16, "autodoc": 3, "autogener": 20, "autom": [0, 3, 4, 5, 8, 9, 11, 15, 16, 22, 26, 28, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 53, 54, 56, 59, 60, 68, 71, 72, 74, 75, 76, 77], "automat": [3, 4, 11, 12, 13, 15, 21, 23, 24, 28, 39, 49, 52, 61, 63, 69, 72, 77, 78], "automated test": 52, "automated_act": 56, "automated_block": 40, "automated_check": 54, "automated_decision_mak": 12, "automated_initi": 40, "automated_process": 12, "automatic_failov": 15, "automation_level": 54, "autonom": [23, 24, 35, 61, 75], "autonomous_attack_simul": 75, "autonomous_threat_hunt": 74, "autosc": [15, 33], "autoscal": 16, "aux": [32, 33], "auxiliari": 21, "av": [3, 48], "avail": [3, 5, 6, 7, 8, 10, 14, 17, 21, 22, 23, 26, 27, 28, 30, 34, 35, 36, 40, 42, 43, 45, 46, 48, 52, 60, 61, 64, 65, 67, 68, 69, 72, 76, 77], "available_actor": 10, "averag": [3, 22, 23, 24, 31, 44, 55, 59, 69, 74, 75, 77], "average_clust": 6, "average_review_tim": 44, "averageutil": 33, "avg": 50, "avg_review_tim": 44, "avg_time_per_cal": 34, "avg_vuln_age_dai": 55, "aviat": 75, "avoid": [19, 29, 44, 45, 57, 63, 66, 71, 74, 75], "aw": [0, 1, 5, 13, 17, 19, 22, 26, 27, 28, 30, 31, 32, 36, 40, 45, 61, 63, 64, 65, 68, 72, 74, 76, 77], "await": [5, 7, 8, 18, 19, 29, 34, 48, 49, 55, 56, 57, 63, 73], "awar": [4, 18, 26, 38, 47, 70, 71, 72, 74, 75, 76, 77], "award": 71, "awk": [30, 31, 32], "aws_access_key_id": [13, 27, 64], "aws_account_id": 16, "aws_config": 29, "aws_default_region": [13, 27], "aws_discoveri": 66, "aws_ec2": 5, "aws_enable_ec2": 13, "aws_enable_iam": 13, "aws_enable_rd": 13, "aws_enable_s3": 13, "aws_enable_vpc": 13, "aws_engin": 29, "aws_instance_id": 5, "aws_max_retri": 13, "aws_poll_interv": 13, "aws_profil": 14, "aws_region": [5, 14, 16, 30], "aws_result": 29, "aws_retry_delai": 13, "aws_secret_access_kei": [13, 27, 64], "aws_session_token": 13, "awsdiscoveryservic": 66, "awsregion": 16, "az": [14, 15, 28, 29, 64], "azur": [0, 1, 5, 13, 21, 22, 26, 27, 36, 40, 45, 61, 64, 65, 66, 68, 72, 74, 76, 77], "azure_client_id": [13, 27, 64], "azure_client_secret": [13, 27, 64], "azure_config": 29, "azure_enable_comput": 13, "azure_enable_network": 13, "azure_enable_secur": 13, "azure_enable_storag": 13, "azure_engin": 29, "azure_max_retri": 13, "azure_poll_interv": 13, "azure_result": 29, "azure_subscription_id": 13, "azure_tenant_id": [13, 27, 64], "b": [2, 3, 14, 19, 20, 21, 29, 43, 53], "b101": [52, 53], "b102": [52, 53], "b103": [52, 53], "b104": [52, 53], "b105": [52, 53], "b106": [52, 53], "b107": [52, 53], "b108": 52, "b110": 52, "b112": 52, "b999": 53, "baa": [70, 76], "back": [21, 31, 64], "back_popul": [25, 54, 55, 56], "backdoor": [43, 74, 75, 77], "backend": [0, 13, 16, 17, 24, 27, 30, 31, 32, 33, 34, 36, 38, 41, 49, 52, 64, 65], "backend_serv": [33, 34], "background": [6, 12, 19, 23, 24, 25, 34, 61, 67, 77], "backlog": 39, "backoff": 8, "backs_up": 59, "backup": [14, 17, 20, 24, 26, 27, 28, 29, 31, 32, 36, 37, 38, 40, 41, 43, 49, 51, 61, 67, 69, 72, 75, 76, 77], "backup_bucket": 30, "backup_cod": 7, "backup_d": 30, "backup_fil": 30, "backup_nam": 30, "backup_retent": 15, "backup_s": 30, "backup_schedul": 5, "backup_server_001": [6, 69], "backup_system": 59, "backup_typ": 30, "backward": [21, 60, 61], "bad": [6, 8, 18, 21, 39, 49, 63], "badg": 40, "balanc": [17, 23, 24, 25, 28, 29, 31, 32, 33, 39, 45, 53, 55, 66, 68, 69, 74, 75, 76, 77], "bandit": [0, 2, 18, 20, 21, 44, 46, 48, 49, 52, 53, 62], "bandit_result": 52, "banditen": 20, "bandwidth": [27, 33, 65], "bank": 75, "base": [0, 1, 2, 3, 7, 11, 12, 13, 15, 18, 22, 23, 24, 25, 26, 28, 29, 31, 34, 35, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 49, 53, 55, 56, 57, 60, 61, 62, 64, 65, 67, 69, 70, 71, 73, 74, 75, 76, 78], "base64": [7, 16, 17, 30, 32], "base64_payload": 73, "base_class": 54, "base_risk": 34, "base_url": [5, 7, 8, 25, 52, 56, 73, 78], "basel": [70, 76], "baselin": [33, 34, 38, 43, 48, 49, 51, 52, 62, 68, 69, 73, 75, 76, 77], "baseline_comparison": 66, "baseline_devi": 66, "baseline_scan": 51, "baseline_scor": 73, "basemodel": 18, "baseurl": [5, 7, 8], "bash": [0, 1, 2, 14, 27, 30, 31, 32, 66], "basi": [4, 12, 38, 40, 49], "basic": [0, 13, 14, 25, 26, 27, 32, 33, 40, 45, 51, 55, 70, 72, 74, 75, 77], "basic_attack_path_discoveri": 75, "basic_campaign_particip": 75, "basic_compliance_monitor": 70, "basic_exercise_particip": 74, "basic_metrics_access": 74, "basic_reporting_access": [70, 75], "basicconfig": 63, "batch": [5, 6, 16, 17, 23, 24, 29, 30, 35, 39, 60, 73, 78], "batch_siz": [29, 39, 66], "battl": [23, 35], "bcrypt": 13, "bear": [6, 74, 75, 78], "bearer": [5, 6, 7, 8, 9, 10, 20, 29, 51, 52, 56, 62, 65, 69, 73, 78], "beat": 14, "beat_schedul": 55, "becom": [36, 38], "been": [19, 28, 47, 77], "befor": [2, 7, 8, 12, 14, 21, 26, 34, 36, 47, 49, 50, 51, 53, 59, 61, 63, 64, 65, 69, 72, 73, 75, 78], "before_script": 52, "begin": [0, 6, 24, 35, 37, 41, 42, 45, 59, 62, 66, 69], "behavior": [4, 6, 11, 19, 23, 24, 25, 26, 35, 37, 39, 46, 50, 55, 57, 61, 62, 64, 65, 67, 74, 75, 76, 77], "behavior_analysi": 73, "behavioral_analysi": 40, "behavioral_featur": 55, "behavioral_scor": 73, "behind": 19, "being": [32, 64], "benchmark": [0, 21, 34, 44, 50, 60, 62, 68, 70, 71, 74, 75, 76, 77], "benefit": [6, 25, 70, 71, 74, 75, 76, 77, 78], "best": [0, 1, 3, 19, 22, 26, 27, 36, 37, 42, 43, 45, 46, 48, 49, 60, 70, 71, 74], "better": [6, 18, 19, 20, 34, 46, 47, 78], "between": [0, 1, 3, 18, 19, 23, 26, 36, 56, 62, 67, 69, 74, 76, 77], "bf": 57, "bgwriter_delai": 34, "bgwriter_lru_maxpag": 34, "bgwriter_lru_multipli": 34, "bi": [36, 40, 61, 65, 68], "bidirect": [23, 34, 56, 77], "biggest": 34, "bigint": 18, "biginteg": 18, "bigseri": 18, "billing_system": 78, "bin": [2, 14, 16, 18, 20, 27, 30, 31, 32], "bind": [17, 29, 34, 53, 62, 64], "bind_development_serv": 53, "biometr": [11, 15, 28, 40], "biometric_support": 28, "bit": [13, 27, 77], "black": [18, 20, 21, 52, 62, 77], "blank": 64, "blast": [0, 2, 4, 5, 7, 8, 9, 10, 11, 12, 13, 15, 18, 19, 20, 21, 22, 27, 28, 29, 33, 34, 35, 36, 37, 39, 40, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 53, 54, 55, 56, 59, 60, 62, 63, 64, 66, 68, 70, 71, 72, 73, 74, 75, 76, 77, 78], "blast-radiu": 47, "blast_dev": 20, "blast_neo4j": [27, 64], "blast_neo4j_password": 13, "blast_pass": [13, 27], "blast_radiu": [3, 6, 8, 13, 14, 16, 27, 31, 32, 41, 56, 59, 64, 65, 67, 73, 78], "blast_radius_analysi": 67, "blast_radius_app": 16, "blast_radius_cach": 57, "blast_radius_dev": [14, 20], "blast_radius_incident_id": 56, "blast_radius_restor": 30, "blast_radius_scor": 56, "blast_radius_sdk": [5, 25], "blast_radius_servic": 56, "blast_radius_sess": 13, "blast_respons": 8, "blast_result": 69, "blast_us": [13, 27, 64, 65], "blastradiu": [7, 8, 25, 27, 36, 64, 65], "blastradius2024": [27, 36, 65], "blastradiuscli": [3, 5, 8, 25, 73, 78], "blastradiusdast": 52, "blastradiusresult": [25, 57], "blastradiusrol": 29, "blastradiusservic": 56, "blastradiusus": 62, "blind": [72, 74], "bloat": 31, "blob": 29, "block": [3, 11, 19, 32, 33, 39, 41, 45, 46, 52], "block_hash": 39, "block_id": 39, "block_siz": 39, "block_threat_ind": 40, "blockchain": [28, 39, 71, 75, 76], "blue": [0, 26, 28, 68, 69, 72, 74, 75], "blue team": 26, "blue_team_detect": 67, "board": [22, 26, 43, 70, 71, 72, 75, 76, 77], "board govern": 71, "board_level_report": [70, 71], "board_reporting_access": 76, "bodi": [5, 6, 7, 19, 21, 38, 70, 71, 76, 77], "bolt": [13, 20, 27, 33, 34], "bool": [18, 25, 40, 56], "boolean": [5, 12, 54, 55, 56, 59], "boot": 75, "bootcamp": 77, "border": [37, 70, 76], "both": [34, 53], "boto3": [29, 63], "botocor": 29, "bottleneck": [33, 34, 62, 68, 77], "bound": 38, "boundari": [18, 44, 49, 51, 62, 75, 76, 77, 78], "boundary_analysi": 67, "bounti": 42, "box": 3, "bradlc": 20, "branch": [2, 3, 14, 20, 44, 51, 53, 60, 62], "branch_label": 54, "brand": [3, 68, 71, 75, 76, 77], "breach": [11, 28, 40, 43, 45, 46, 48, 49, 67, 70, 71, 75, 76, 77], "breach_manag": 12, "breach_notif": 28, "breach_scenario": 78, "breach_typ": 78, "breadcrumb": 3, "breadth": [4, 57], "break": [4, 19, 21, 48, 57], "breaker": 15, "breakout": 75, "breakpoint": 20, "bridg": [41, 59, 74, 77], "bridgecrew": 52, "brief": [19, 41, 43, 49, 71, 75, 76, 77], "broader": [45, 71, 75, 77], "broken": [3, 47], "broker": 76, "brows": 36, "browser": [0, 19, 20, 36, 62, 64, 65, 75, 77], "brute": [51, 68], "bruteforc": 66, "bside": 77, "bucket": [29, 66], "budget": [71, 74, 76, 77, 78], "budget_constraint": 78, "budget_planning_access": 76, "buffer": 34, "buffer_s": 39, "bug": [19, 20, 21, 26, 42, 60, 63, 65, 72], "bugfix": [19, 21], "build": [19, 20, 21, 23, 24, 25, 31, 34, 46, 48, 50, 52, 56, 64, 70, 71, 74, 75, 76, 77], "build_imag": 52, "builder": [3, 34], "building_system": 59, "built": [3, 26, 37, 42, 51, 57, 73, 77], "bulk": [4, 8, 24, 34, 35, 60, 63, 68], "bulk_operation_fail": 5, "bulk_risk_calcul": 34, "bundl": [0, 64], "burp": [46, 48], "busi": [3, 4, 10, 22, 26, 40, 41, 43, 44, 45, 46, 49, 57, 60, 63, 65, 66, 67, 68, 69, 70, 72, 74, 75, 76, 77, 78], "business enabl": 71, "business_context": 78, "business_continu": 40, "business_crit": [57, 78], "business_disrupt": 78, "business_impact": 44, "business_impact_scor": 44, "business_justif": 4, "business_multipli": 57, "business_object": 40, "button": 62, "by_sever": [51, 53], "by_tool": 51, "byod": 75, "bypass": [21, 45, 51, 52, 53, 74, 75, 76], "bypass_attempt": 51, "byte": 34, "c": [2, 3, 14, 17, 20, 22, 26, 27, 29, 30, 31, 32, 41, 64, 67, 71, 72, 75, 76, 77], "c-suit": 71, "ca": [13, 64], "cach": [0, 2, 3, 4, 11, 12, 13, 14, 15, 16, 20, 23, 24, 25, 26, 31, 33, 35, 39, 41, 47, 59, 60, 61, 64, 65, 66, 67, 68, 69, 73, 77, 78], "cache_attack_path": 57, "cache_hit": 6, "cache_kei": [34, 47, 57], "cache_level": 33, "cache_miss": 6, "cache_pattern": 33, "cache_result": 57, "cache_ttl": [13, 33], "cached_data": 34, "cached_result": 34, "cached_valu": 34, "cachemanag": 34, "cafil": 64, "calcul": [8, 10, 12, 24, 25, 26, 34, 44, 48, 55, 56, 57, 60, 65, 67, 72, 73, 75, 76, 77], "calculate_average_review_tim": 44, "calculate_blast_radiu": 57, "calculate_effectiveness_scor": 40, "calculate_metr": 44, "calculate_path_risk": 67, "calculate_path_risk_scor": 57, "calculate_resolution_tim": 40, "calculate_security_risk": 44, "calculateblastradiu": 8, "calendar": 70, "calibr": 74, "california": [61, 70], "call": [13, 17, 20, 29, 31, 32, 33, 34, 39, 41, 53, 59, 64, 72, 77], "call_function_name_qu": 53, "caller": [16, 64], "cam": 70, "camelcas": 18, "campaign": [8, 22, 69, 72, 73, 74, 76, 77, 78], "campaign_correl": 73, "campaign_id": 73, "campaign_planning_author": 75, "can": [3, 13, 29, 34, 45, 61, 63, 64, 69, 75], "canari": 15, "cannot": [17, 18, 63, 64], "cap": 67, "capabl": [0, 3, 4, 6, 7, 9, 10, 11, 15, 19, 20, 22, 25, 27, 28, 33, 35, 36, 39, 40, 50, 56, 57, 60, 61, 65, 66, 68, 69, 72, 73, 78], "capac": [15, 23, 24, 32, 35, 38, 61, 68, 72, 74, 76, 77], "capit": [71, 76], "captur": [71, 74, 76, 77], "car": 75, "carbanak": 78, "card": [39, 40, 61, 70, 75, 76], "cardhold": [40, 66, 70, 76], "cardholder_data": 67, "cardholder_data_asset": 67, "cardholder_data_environ": 66, "care": [31, 61, 77], "career": [3, 22, 26, 70, 74, 75, 76, 77], "carefulli": [11, 16], "carlo": [22, 76], "carrier": 77, "casb": 76, "cascad": [26, 54, 59, 76], "case": [19, 21, 22, 23, 24, 25, 30, 31, 35, 36, 41, 47, 51, 52, 59, 62, 71, 72, 75, 76], "case_data": 56, "case_id": [25, 56], "case_map": 56, "case_numb": 56, "caseid": 56, "cass": 75, "cat": [14, 16, 30, 31, 32, 51, 64], "catalog": 78, "catalogu": 38, "catalyst": 74, "catch": [18, 19, 62, 63], "categor": [38, 55, 63, 77], "categori": [19, 20, 25, 39, 45, 50, 53, 61, 66, 67, 76, 77], "caught": 18, "caus": [17, 19, 34, 41, 43, 65, 71, 77], "causal": 74, "cc6": 54, "cc6_1_logical_access": 40, "cc6_2_authent": 40, "cc7_1_threat_protect": 40, "cca": 76, "ccep": 70, "ccpa": [61, 70, 77, 78], "ccpto": 74, "ccrto": 75, "ccsa": 76, "ccsp": 74, "cctv": 40, "cd": [0, 1, 3, 13, 15, 16, 18, 19, 20, 21, 26, 27, 30, 35, 36, 47, 60, 64, 65, 75, 76], "cde": [70, 74, 76], "cdn": [13, 14, 33, 40, 45, 66, 75], "cdpo": 70, "ced": 74, "cef": 65, "ceh": 75, "celeri": [14, 23, 24, 25, 35], "celery_app": [14, 23, 55], "center": [17, 40, 50, 68, 70, 71, 72, 74, 75, 76, 77], "cento": 27, "central": [15, 53, 60, 68, 75, 76, 77], "central1": [13, 29, 66], "centric": [22, 50, 76], "ceo": [71, 76, 77, 78], "cert": [11, 13, 64], "cert_arn": 16, "cert_expiri": 31, "cert_non": 13, "cert_opt": 13, "cert_requir": 13, "certain": 64, "certbot": 27, "certif": [3, 11, 13, 14, 22, 23, 26, 27, 28, 36, 38, 44, 49, 60, 66, 68, 70, 71, 74, 75, 76, 77, 78], "certifi": [70, 72, 74, 75, 76, 77], "certificate_arn": 17, "certificate_author": 59, "certificatearn": [16, 31], "certification_impact": 78, "certonli": 27, "cesa": 76, "cessat": 38, "cfo": [76, 78], "cgrc": 70, "chacha20": 11, "chain": [11, 28, 38, 43, 61, 64, 69, 70, 71, 73, 74, 75, 76, 77, 78], "challeng": [7, 74, 75, 76], "chang": [0, 1, 2, 3, 4, 8, 13, 14, 16, 17, 18, 20, 21, 23, 25, 27, 29, 30, 34, 35, 38, 39, 40, 41, 44, 47, 48, 51, 52, 56, 60, 61, 63, 65, 66, 67, 68, 69, 70, 71, 72, 74, 75, 76, 77, 78], "change_descript": 49, "change_detect": 66, "changelog": [1, 3, 8, 19, 21, 26, 65], "channel": [19, 36, 41, 60, 65, 66, 70, 74, 75, 76, 77], "charact": [18, 36], "characterist": [15, 33, 39, 40, 45], "chart": [16, 38, 77], "chat": 65, "chc": 70, "chd": 66, "check": [0, 3, 4, 9, 11, 13, 15, 17, 19, 20, 21, 23, 24, 25, 29, 30, 33, 34, 35, 36, 38, 39, 40, 41, 44, 46, 48, 53, 56, 59, 60, 61, 62, 63, 65, 66, 67, 68, 69, 72, 73, 77], "check_custom_crypto": 53, "check_id": 53, "check_permiss": [18, 40], "check_request": 12, "check_risk_threshold": 52, "check_typ": 54, "checker": 18, "checklist": [21, 52, 77], "checkout": [2, 3, 14, 19, 20, 21, 51, 53, 62, 63], "checkov": [49, 52], "checkpoint_completion_target": [33, 34], "checksum": 39, "chief": [70, 71, 76, 77], "children": 18, "chmod": [2, 14, 20], "chokepoint": [67, 77], "choos": [19, 53, 69, 78], "chore": 21, "chown": [2, 17, 27], "chrome": [19, 20, 36, 62, 65, 77], "chromeopt": 62, "chronolog": [39, 77], "chronologi": 77, "chunk": 34, "chunk_siz": 34, "churn": 76, "ci": [0, 3, 15, 18, 21, 26, 35, 42, 47, 50, 60, 61, 75, 76], "ci/cd secur": 52, "ci_commit_branch": 52, "ci_commit_sha": 52, "ci_registry_imag": 52, "cia": 70, "cicd": 2, "cicst": 75, "cio": [76, 77], "cipher": 11, "cipp": 70, "cipt": 74, "circuit": 15, "cism": [70, 77], "ciso": [40, 43, 71, 77], "cissp": 77, "ck": [3, 18, 22, 24, 25, 26, 33, 35, 42, 50, 60, 65, 69, 74, 75, 76, 77, 78], "cl": 18, "claim": [7, 17, 30, 43, 76, 77], "clariti": [18, 63], "class": [7, 17, 18, 19, 21, 25, 30, 34, 40, 44, 51, 52, 53, 54, 55, 56, 57, 62, 63], "class_weight": 55, "classif": [4, 24, 26, 35, 37, 38, 39, 40, 42, 44, 55, 60, 61, 68, 69, 70, 72, 74, 75, 76, 77], "classification_report": 55, "classification_schem": 40, "classnam": [18, 19, 63], "clean": [2, 3, 4, 14, 20, 31, 32, 43, 51, 52, 64, 77], "cleanup": [28, 30, 59, 65, 68], "cleanup_attack_path": 59, "cleanup_audit_log": 59, "clear": [0, 3, 12, 17, 18, 19, 20, 29, 32, 34, 38, 43, 45, 61, 63, 64, 65, 66, 68, 69, 75, 76, 77], "clearli": [4, 77], "clever": 18, "cli": [13, 14, 19, 20, 23, 24, 25, 27, 31, 32, 35, 36, 41, 62, 63, 64, 65, 66], "click": [19, 23, 24, 25, 35, 36, 62, 65, 69], "click_rat": 40, "client": [3, 5, 7, 8, 13, 27, 29, 30, 35, 51, 60, 62, 63, 64, 65, 66, 69, 73, 75, 78], "client_addr": 32, "client_body_timeout": 34, "client_header_timeout": 34, "client_id": [29, 66], "client_max_body_s": 34, "client_secret": [29, 66], "clientsess": 34, "climat": 40, "clo": 77, "clock": 76, "clone": [2, 27, 36, 65], "close": [4, 18, 19, 29, 34, 48, 55, 62], "closur": [48, 68, 75, 76, 77], "cloud": [0, 1, 2, 5, 8, 21, 22, 26, 27, 41, 45, 57, 60, 68, 69, 71, 72, 74, 75, 76, 77], "cloud_automation_design": 76, "cloud_compliance_manag": 76, "cloud_cost_optim": 76, "cloud_discoveri": 29, "cloud_governance_framework": 76, "cloud_inst": 29, "cloud_secur": 59, "cloud_security_assess": 76, "cloud_servic": 59, "cloud_vendor_manag": 76, "clouddiscoveryengin": 29, "cloudflar": 40, "cloudfront": 66, "cloudtrail": [68, 77], "cloudwatch": [16, 17], "cluster": [14, 15, 17, 24, 28, 29, 30, 32, 66, 74, 75, 77], "cluster_s": 15, "clusternam": 16, "cm": 38, "cmd": [34, 51, 52, 53, 62], "cmdb": [0, 13, 36, 38, 45, 65, 66], "cmdb_sync": 66, "cni": 31, "co": [38, 63], "coach": [71, 76, 77], "cobalt": 78, "code": [1, 2, 6, 9, 10, 17, 24, 26, 35, 38, 42, 45, 46, 47, 48, 50, 51, 52, 53, 60, 62, 65, 72, 75, 76, 77], "code review": 44, "code_coverag": 18, "code_security_standard": 76, "codeactionsonsav": [18, 20], "codebas": [20, 47], "codecov": [62, 63], "codeql": [46, 48, 53], "col": 55, "cold_retention_year": 39, "collabor": [0, 1, 3, 19, 22, 26, 36, 44, 45, 61, 65, 67, 68, 71, 72, 75, 76, 77], "collaboration_result": 67, "collaborative secur": 74, "collect": [0, 3, 8, 12, 13, 15, 24, 25, 26, 33, 34, 38, 39, 43, 53, 60, 67, 70, 71, 72, 74, 75, 76, 77], "collis": 47, "color": [3, 77], "color_schem": 73, "column": [18, 25, 31, 54, 55, 56, 59], "com": [2, 5, 6, 7, 8, 9, 10, 12, 13, 14, 16, 17, 18, 19, 20, 23, 24, 27, 29, 31, 32, 34, 36, 39, 41, 42, 45, 49, 51, 52, 62, 63, 64, 65, 66, 67, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78], "combin": [0, 24, 46, 50, 57, 67, 74, 75, 76, 77], "come": [8, 26, 36, 42, 72], "comma": 5, "command": [21, 23, 24, 27, 37, 43, 44, 51, 52, 53, 62, 63, 65, 68, 70, 71, 72, 73, 74, 75, 76, 77, 78], "command_lin": 73, "command_payload": 51, "comment": [4, 19, 20, 21], "commerc": 76, "commerci": 53, "commercial_fe": 78, "commit": [0, 2, 3, 14, 18, 19, 20, 44, 45, 46, 47, 48, 50, 62, 74, 75, 76, 77], "committe": 76, "common": [1, 3, 9, 10, 13, 19, 26, 28, 45, 46, 48, 52, 53, 59, 60, 62, 65, 73, 74, 75], "common_techniqu": 73, "commonli": 65, "commun": [0, 8, 11, 20, 21, 22, 26, 36, 40, 44, 48, 51, 60, 61, 64, 67, 68, 69, 70, 72, 74, 75], "communicates_with": 59, "communication_plan": 40, "communication_protocol": 40, "communicationservic": 40, "compani": [5, 23, 24, 39, 66, 67, 74, 75, 76], "compar": [69, 72, 73, 76, 77], "compare_threat_actor": 73, "comparison": [44, 70, 73, 75, 76, 77], "compat": [0, 21, 60, 61, 74, 76, 77], "compens": [70, 75], "compet": [40, 74, 75, 76, 77], "competit": [3, 22, 50, 61, 70, 71, 74, 75, 76], "compil": 75, "complement": 51, "complementari": [0, 53], "complet": [0, 3, 4, 5, 8, 9, 12, 14, 15, 16, 18, 19, 20, 21, 22, 23, 24, 25, 31, 32, 36, 38, 39, 41, 43, 44, 45, 47, 48, 49, 50, 51, 53, 54, 55, 57, 58, 59, 60, 61, 62, 63, 64, 65, 67, 68, 69, 72, 73, 75, 76, 77, 78], "completed_at": 12, "completed_review": 44, "completion_r": [40, 44], "complex": [0, 1, 3, 6, 18, 19, 21, 25, 33, 34, 45, 48, 49, 51, 53, 57, 60, 61, 65, 67, 68, 69, 72, 73, 74, 75, 76, 77, 78], "complexity_factor": 57, "complexity_scor": 67, "compli": 75, "complianc": [0, 3, 4, 8, 11, 15, 22, 35, 41, 44, 45, 47, 48, 51, 57, 59, 69, 74, 75, 77], "compliance_analysi": 67, "compliance_control": 25, "compliance_documentation_access": 70, "compliance_framework": [25, 28, 39, 67], "compliance_framework_manag": 76, "compliance_impact": 6, "compliance_level": 54, "compliance_manag": 40, "compliance_monitor": 4, "compliance_program_oversight": 70, "compliance_risk_assess": 70, "compliance_risk_chang": 78, "compliance_servic": 67, "compliance_statu": 5, "compliance_track": 28, "compliance_training_access": 70, "complianceassess": 54, "complianceassessmentservic": 25, "compliancecontrol": [25, 54], "compliancecontrolassess": 54, "compliancedomain": 54, "complianceframework": [25, 54, 67], "compliancereportingservic": 66, "complianceservic": 67, "compliant": [3, 5, 44, 45, 49, 50, 54, 65, 67], "compon": [0, 1, 2, 3, 13, 18, 19, 20, 23, 26, 28, 33, 34, 35, 44, 45, 48, 49, 53, 60, 62, 64, 67, 68], "componentdidcatch": 18, "componentstatus": 32, "compos": [0, 2, 3, 14, 20, 23, 27, 34, 36, 60, 64, 65], "composit": [34, 59, 74, 75], "comprehens": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 21, 23, 24, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 72, 73], "comprehensive_risk_report": 71, "comprehensive_security_manag": 71, "compress": [13, 30, 33, 34], "compromis": [6, 9, 11, 24, 25, 37, 41, 43, 45, 47, 48, 56, 57, 61, 67, 69, 72, 73, 74, 75, 76, 77, 78], "compromised_serv": [6, 8, 69], "compromised_us": 43, "comput": [29, 65, 66, 71, 74, 76], "con": 77, "concept": [45, 72, 75, 76], "concern": [43, 44], "concis": [19, 63, 77], "conclus": [26, 72], "concret": 18, "concurr": [13, 17, 33, 34, 51, 59, 60, 61, 63, 66, 68, 78], "concurrent_request": 51, "concurrent_us": 28, "condit": [30, 34, 44, 49, 51, 62, 63, 66, 74, 76], "conduct": [16, 38, 40, 47, 68, 70, 72, 74, 75, 76, 77], "conf": [3, 32, 34, 55], "confer": [22, 41, 45, 49, 71, 74, 75, 76, 77], "confid": [5, 9, 21, 22, 28, 50, 51, 53, 59, 63, 67, 70, 71, 72, 73, 74, 75, 76, 77], "confidence_bas": 73, "confidence_scor": [55, 67, 73], "confidenti": [40, 42, 45, 46, 48, 76], "confidential_internal_publ": 40, "config": [2, 8, 13, 14, 17, 18, 20, 27, 29, 30, 31, 32, 33, 44, 46, 49, 51, 52, 53, 56, 62, 64, 66, 68], "config_backup": 30, "configmap": [14, 17, 30, 31, 32, 49, 66], "configur": [1, 7, 8, 10, 11, 17, 20, 21, 22, 23, 24, 26, 28, 33, 34, 37, 40, 42, 43, 44, 45, 49, 50, 51, 55, 56, 59, 60, 61, 62, 66, 67, 70, 72, 73, 74, 75, 76, 78], "configuration_chang": 66, "configuration_change_frequ": 55, "configure_detection_rul": 77, "configure_integr": 77, "configure_system": [7, 68], "confirm": [20, 36, 38, 43, 68, 69, 77], "confirm_password": 7, "conflict": [4, 8, 18, 27, 38, 74], "conftest": [62, 63], "congratul": 26, "connect": [0, 5, 11, 13, 14, 20, 21, 27, 29, 31, 32, 33, 34, 36, 39, 41, 56, 62, 63, 65, 66, 67, 68, 72, 73, 75, 76, 77], "connection_count": [34, 55], "connectionerror": 63, "connector": [23, 24, 33, 34, 35, 60], "connects_to": 5, "consent": [28, 37, 38, 39, 40], "consent_id": 12, "consent_manag": [12, 28, 40], "consent_text": 12, "consentmanagementservic": 40, "consequ": [46, 76, 77], "conserv": 69, "consid": [1, 6, 18, 19, 21, 27, 36, 48, 53, 65, 66, 69, 72, 77, 78], "consider": [8, 21, 39, 41, 42, 44, 49, 52, 57, 69, 75, 76, 78], "consider_supply_chain": 78, "consist": [0, 1, 3, 4, 8, 18, 21, 38, 39, 41, 43, 44, 51, 61, 63, 68, 75, 77], "consol": [18, 20, 64, 68], "consolid": 76, "const": [5, 7, 8, 18, 19, 25], "constant": [18, 19], "constantli": 77, "constraint": [16, 18, 34, 45, 59, 60, 64, 66, 76], "construct": [19, 72], "constructor": 18, "consult": [17, 27, 68, 70, 71, 74, 75, 76, 77], "consum": 70, "contact": [0, 19, 26, 36, 38, 43, 64, 68, 69, 70, 71, 72, 74, 75, 76, 77], "contact_info": 12, "contain": [0, 3, 8, 14, 17, 18, 19, 20, 24, 29, 31, 32, 33, 35, 36, 37, 40, 42, 44, 46, 48, 50, 52, 59, 64, 65, 67, 69, 72, 74, 75, 76, 77], "container": [0, 3, 20, 25, 60], "container_discoveri": 66, "container_nam": 17, "container_scan": 52, "container_secur": 52, "containerdiscoveryservic": 66, "containerservic": 29, "containerstatus": 32, "contamin": [43, 55], "content": [6, 7, 9, 10, 26, 29, 32, 45, 51, 52, 56, 73, 78], "context": [4, 16, 17, 18, 19, 30, 39, 44, 45, 47, 49, 51, 53, 56, 61, 63, 66, 67, 69, 72, 73, 76, 77], "context_of_organ": 40, "context_weight": 4, "contextu": [9, 46, 73, 76, 77], "conting": [68, 75, 76], "continu": [0, 1, 4, 8, 11, 12, 15, 17, 18, 22, 26, 28, 33, 34, 39, 41, 42, 43, 44, 50, 53, 54, 57, 60, 61, 64, 67, 68, 69, 70, 72, 74, 75, 76, 77], "continual_improv": 40, "continuous_authent": 40, "continuous_monitor": 28, "contract": [70, 71, 76], "contractu": 77, "contribut": [0, 20, 21, 22, 26, 50, 53, 55, 70, 71, 74, 75, 76, 77], "contributor": [1, 19, 26], "control": [1, 2, 3, 5, 6, 7, 8, 10, 11, 12, 15, 16, 17, 20, 23, 24, 25, 26, 27, 31, 34, 35, 36, 39, 42, 43, 44, 45, 46, 47, 49, 50, 51, 53, 54, 57, 60, 61, 64, 65, 66, 67, 69, 70, 71, 72, 73, 75, 76, 78], "control_assess": 54, "control_autom": 28, "control_effect": 67, "control_id": [25, 44, 54], "control_map": [28, 39], "control_nam": 78, "control_typ": 54, "convent": [18, 19, 60], "convert": [23, 55], "cooki": [7, 51, 64, 77], "cool": 33, "cooper": [43, 45, 76], "coordin": [23, 38, 41, 42, 43, 45, 68, 70, 71, 72, 74, 75, 76, 77], "copi": [0, 3, 14, 20, 27, 34, 57, 65, 77], "cor": [13, 27, 37, 64, 68], "core": [1, 14, 17, 20, 23, 26, 27, 33, 37, 40, 42, 52, 53, 60, 62, 63, 64, 65, 67, 72, 75], "coredn": [31, 32], "corner": 36, "corpor": [45, 71, 72, 75, 76], "correct": [12, 19, 21, 36, 38, 44, 49, 54, 61, 76], "correctli": [49, 77], "correl": [8, 12, 24, 26, 28, 33, 34, 36, 37, 38, 56, 60, 61, 65, 67, 68, 70, 71, 72, 74, 75, 76, 77], "correlate_ev": 73, "correlation_fail": 9, "correlation_window": 73, "correspond": 56, "corrupt": [41, 45, 64, 75], "cors_allow_credenti": 13, "cors_allow_head": 13, "cors_allow_method": 13, "cors_max_ag": 13, "cors_origin": [13, 14, 20, 27, 64], "cosmet": 48, "cosmo": 29, "cost": [10, 34, 43, 50, 53, 68, 69, 70, 71, 72, 74, 75, 76, 77, 78], "cost_cent": 5, "could": [47, 77], "council": 76, "counsel": [41, 43, 45], "count": [29, 30, 31, 32, 34, 52, 64, 73, 74, 77], "count_critical_issu": 44, "count_issues_by_sever": 52, "counter": [34, 51, 53, 75], "countermeasur": 75, "coupl": 60, "coursera": 77, "court": 43, "cov": [2, 18, 20, 49, 62, 63], "cover": [0, 1, 3, 7, 13, 19, 21, 26, 29, 34, 36, 38, 40, 44, 45, 60, 62, 63, 64, 65, 66, 67, 69, 72, 73, 74, 76, 77, 78], "coverag": [2, 3, 8, 9, 18, 19, 20, 21, 22, 23, 24, 26, 35, 37, 39, 40, 49, 50, 52, 53, 60, 61, 70, 72, 73, 74, 75, 76, 77, 78], "coverage_analysi": 40, "coverage_percentag": 40, "coveragerc": 63, "covert": [75, 77], "cozi": [6, 75, 78], "cp": [14, 20, 27, 30, 64, 65], "cpra": 70, "cprofil": [20, 34], "cpst": 75, "cptl": 74, "cptma": 74, "cptp": [74, 75], "cpu": [14, 16, 17, 20, 27, 31, 33, 34, 36, 60, 63, 64, 65, 68], "cpu_cor": 5, "cpu_impact": 51, "cpu_perc": 51, "cpu_usage_avg": 33, "cpu_usage_max": 33, "cpuperc": 33, "crash": 17, "crcm": 70, "creat": [0, 1, 2, 3, 4, 7, 8, 11, 12, 14, 17, 18, 20, 21, 22, 23, 24, 25, 29, 30, 32, 34, 35, 36, 39, 40, 41, 43, 47, 51, 53, 54, 56, 57, 59, 60, 61, 62, 63, 64, 65, 66, 68, 69, 71, 72, 73, 75, 77, 78], "create_al": 62, "create_alert_rul": 66, "create_asset": 49, "create_attack_scenario": 57, "create_cas": [25, 56], "create_case_from_incid": 56, "create_engin": [34, 62], "create_improvement_roadmap": 40, "create_index": [18, 54], "create_new": 66, "create_new_featur": 20, "create_response_playbook": 67, "create_review": 44, "create_t": [18, 54], "create_thehive_case_from_incid": 56, "createasset": 18, "created_at": [5, 6, 18, 34, 39, 54, 56, 59, 64], "created_bi": [30, 54, 59], "created_cas": 56, "created_d": [30, 44], "createscenario": 8, "creation": [0, 8, 16, 21, 23, 24, 35, 43, 56, 57, 60, 61, 68, 71, 72, 74, 75, 76, 77], "credenti": [7, 11, 13, 14, 16, 17, 22, 26, 27, 28, 30, 36, 38, 40, 41, 44, 48, 49, 51, 53, 64, 66, 68, 75, 77], "credential_harvesting_tool": 6, "credential_monitor": 40, "credentials_path": 29, "credibl": [74, 75], "credit": [45, 75], "creep": 75, "crime": 70, "crimin": [75, 77], "crise": [71, 76], "crisi": [22, 43, 72, 74, 75, 76, 77], "crisis_management_access": 76, "crisis_management_author": 71, "crisis_oversight_author": 71, "criteria": [21, 39, 40, 44, 45, 48, 60, 61, 69, 74, 75, 76, 77], "critic": [2, 4, 5, 6, 8, 13, 16, 21, 22, 26, 29, 31, 34, 38, 39, 43, 44, 46, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 59, 61, 62, 63, 65, 66, 67, 68, 69, 71, 72, 74, 75, 76, 77, 78], "critical_asset": 78, "critical_assets_affect": [6, 69], "critical_databas": 78, "critical_fil": 53, "critical_infrastructure_test": 75, "critical_issu": [44, 52], "critical_vuln": 31, "critical_vulns_count": 55, "criticality_level": [6, 8, 55, 56, 59, 69], "criticality_level_encod": 55, "criticality_level_enum": 59, "criticality_multipli": 34, "criticality_scor": [6, 59], "criticalvulnerabilitydetect": 52, "crl": 11, "crm": 75, "crma": 76, "crmp": 70, "cro": [71, 77], "cron": [65, 66], "cronjob": [16, 39], "crontab": 55, "cross": [0, 1, 13, 15, 19, 22, 23, 24, 25, 26, 28, 30, 37, 43, 45, 53, 54, 67, 71, 72, 73, 74, 75, 76, 77], "cross_domain_attack_plan": 75, "cross_functional_commun": 70, "cross_functional_coordin": 74, "cross_team_commun": 74, "cross_team_integration_manag": 74, "cross_val_scor": 55, "crowdstrik": [40, 77], "crown": [67, 74, 75, 76], "crown_jewel": [65, 67], "crtl": 75, "crto": 75, "crtp": 75, "crud": [8, 65], "crypto": 52, "cryptocurr": 75, "cryptograph": [38, 39, 49, 50, 52, 53, 77], "cryptographi": [11, 50, 71, 75, 76], "csa": 76, "csap": 74, "csep": 75, "csf": [23, 24, 25, 35, 39, 50, 70, 76], "csi": 30, "cspm": 76, "csrf": [42, 45], "csrf_trusted_origin": 14, "css": 34, "csv": [5, 68, 77], "cth": 74, "ctrl": 64, "cultiv": [74, 75, 76], "cultur": [22, 40, 50, 71, 74, 76], "cumul": 34, "curl": [2, 4, 5, 6, 9, 10, 11, 12, 14, 16, 17, 20, 27, 29, 30, 31, 32, 34, 39, 41, 64, 65, 66, 73, 78], "currenc": 61, "current": [0, 1, 6, 7, 9, 17, 19, 20, 21, 25, 31, 32, 34, 40, 41, 42, 43, 44, 48, 50, 51, 52, 53, 57, 61, 62, 65, 67, 68, 69, 72, 73, 74, 75, 76, 77], "current_boundari": 67, "current_level": 57, "current_password": 7, "current_risk_scor": 55, "current_scor": 73, "current_st": 67, "current_target": 5, "current_timestamp": 59, "current_us": [18, 20], "cursor": [8, 52], "custodi": [43, 70, 75, 76, 77], "custom": [0, 1, 2, 5, 18, 24, 27, 30, 31, 33, 36, 39, 40, 41, 43, 45, 46, 48, 50, 51, 52, 61, 63, 66, 67, 68, 70, 71, 72, 73, 74, 75, 76, 77, 78], "custom_apt": 78, "custom_bandit_rul": 53, "custom_threat_actor": 78, "customer_count": 78, "customer_databas": 78, "customfield": 56, "customiz": [4, 77], "cut": [3, 22, 26, 31, 74, 75], "cutoff": 34, "cve": [5, 21, 48, 51], "cve_id": 5, "cvss": [65, 75, 77], "cvss_base_scor": 44, "cvss_score": 44, "cvss_score_avg": 55, "cwd": 20, "cwe": [47, 50], "cwpp": 76, "cyber": [38, 43, 71, 75, 76, 77], "cyberark": 40, "cybercrimin": [74, 75], "cybersecur": [19, 37, 39, 42, 54, 61, 70, 71, 74, 75, 76, 77], "cybrari": 77, "cycl": [57, 67, 76], "cypher": [27, 34, 64], "czf": [30, 32, 41, 64], "d": [2, 3, 5, 6, 14, 16, 17, 19, 20, 21, 27, 29, 30, 31, 32, 36, 38, 41, 46, 49, 52, 64, 65, 73, 78], "d3": [23, 24, 35], "d_": 30, "daemon": 64, "dai": [4, 12, 22, 30, 31, 32, 33, 34, 38, 40, 41, 44, 45, 46, 48, 49, 50, 52, 59, 61, 64, 67, 73, 75, 77, 78], "daili": [5, 23, 24, 38, 46, 48, 55, 61, 66, 68, 76, 78], "daily_full_discoveri": 66, "damag": [70, 77], "danger": 50, "dark": [3, 74, 75], "dashboard": [0, 1, 11, 12, 15, 16, 17, 19, 21, 23, 24, 25, 26, 27, 30, 33, 35, 38, 41, 43, 44, 46, 48, 49, 50, 60, 64, 65, 66, 70, 71, 72, 74, 75, 76], "dashboard_refresh_second": 39, "dashboard_uid": 32, "dast": [0, 25, 37, 40, 42, 49, 50, 53, 62], "dast_autom": 52, "dast_requests_tot": 51, "dast_scan": 52, "dast_scan_dur": 51, "dast_scan_duration_second": 51, "dastperformancemonitor": 51, "dastresultsanalyz": 51, "data": [0, 1, 3, 4, 5, 7, 8, 10, 13, 17, 18, 19, 20, 21, 23, 24, 25, 26, 27, 28, 31, 32, 33, 34, 35, 36, 39, 40, 42, 43, 44, 45, 46, 48, 50, 53, 55, 56, 66, 67, 68, 69, 70, 71, 72, 74, 75, 76, 77, 78], "data_access": 39, "data_assets_affect": 6, "data_at_rest": 28, "data_breach": 78, "data_categories_affect": 12, "data_classif": 40, "data_encrypt": 67, "data_exfiltr": [59, 67], "data_in_transit": 28, "data_integr": 78, "data_loss_prevent": [40, 59], "data_processing_system": 66, "data_processor": 40, "data_protect": 40, "data_protection_polici": 40, "data_sensit": 78, "data_sourc": [57, 67, 73], "data_subject": 40, "data_subject_impact": 78, "data_subject_request": 12, "data_subject_right": 28, "data_subjects_affect": 78, "data_sync_in_progress": 9, "data_typ": 78, "databas": [0, 3, 4, 5, 6, 8, 11, 14, 15, 19, 21, 23, 25, 27, 29, 35, 36, 37, 41, 45, 49, 51, 57, 62, 63, 65, 66, 67, 68, 69, 74, 75, 77], "database_001": [6, 8, 69, 78], "database_cach": 33, "database_command_timeout": 13, "database_connect_timeout": 13, "database_echo": 13, "database_endpoint": 16, "database_host": [17, 30, 31, 32], "database_identifi": 30, "database_max_overflow": [13, 27, 33], "database_nam": [17, 30, 31, 32], "database_pool_recycl": 13, "database_pool_s": [13, 27, 33], "database_pool_timeout": 13, "database_query_time_p95": 28, "database_security_group_id": [17, 30], "database_ssl_mod": 13, "database_url": [13, 14, 20, 27, 34], "database_us": [17, 30, 31, 32], "database_usernam": 16, "database_vers": 30, "databaseerror": [18, 63], "dataclass": [55, 56], "datadog": 52, "datafram": [34, 55], "dataprocessingservic": 40, "dataset": [8, 60, 65, 73, 77], "datatyp": 56, "date": [2, 12, 16, 18, 21, 30, 31, 32, 41, 44, 45, 48, 54, 64, 68, 78], "datetim": [18, 40, 44, 52, 54, 55, 56], "datid": 64, "datnam": 64, "day_of_week": 55, "days_since_last_scan": 55, "days_since_last_upd": 55, "days_to_expiri": 31, "db": [2, 4, 14, 15, 16, 17, 18, 27, 29, 30, 32, 54, 55, 56, 57, 62, 63, 64, 67], "db_allocated_storag": 16, "db_endpoint": 16, "db_host": 41, "db_instance_class": 16, "db_max_allocated_storag": 16, "db_password": 16, "db_security_group_id": 16, "db_session": [55, 56, 62], "db_user": 41, "db_usernam": 16, "dbeaver": 2, "dbinstanc": [17, 30], "dbinstancestatu": 17, "dbm": [33, 34], "dbname": 27, "dbsnapshot": 30, "dbsnapshotidentifi": 30, "dcsync": 75, "dd": [41, 43, 44], "ddo": [40, 68], "de": [4, 53, 54], "deactiv": 68, "dead_percentag": 31, "deb": 14, "debt": 53, "debug": [0, 13, 14, 17, 19, 27, 60, 68], "debug_mod": 63, "debugg": [2, 63], "decentr": 75, "deception_technologi": 59, "decim": [18, 54, 55, 59], "decis": [3, 4, 12, 19, 21, 35, 41, 43, 53, 60, 68, 69, 71, 72, 74, 75, 76, 77], "decision_funct": 55, "decod": 47, "decomposit": 60, "decor": [18, 34], "decrypt": [40, 77], "decrypt_sensitive_data": 40, "dedic": [15, 16, 27, 41, 51, 65, 67, 74, 75, 76, 77], "dedupl": 77, "deep": [29, 46, 48, 53, 60, 65, 74, 76, 77], "def": [7, 18, 19, 20, 21, 25, 34, 40, 44, 49, 51, 52, 53, 54, 55, 56, 57, 62, 63, 67, 77], "default": [3, 5, 6, 8, 11, 12, 13, 16, 18, 27, 34, 36, 39, 40, 44, 48, 49, 51, 54, 55, 56, 57, 59, 64, 66, 68, 69, 70, 76, 77], "default_duration_hour": 4, "default_expiry_dai": 12, "default_pap": 56, "default_timeout_second": 18, "default_tlp": 56, "default_ttl": 34, "defaultinterpreterpath": [18, 20], "defend": [40, 77], "defens": [4, 11, 15, 22, 26, 28, 37, 40, 42, 49, 60, 67, 69, 72, 74, 75, 76, 77], "defense valid": 74, "defici": [24, 39, 76], "defin": [4, 15, 21, 38, 40, 49, 54, 55, 56, 62, 65, 67, 68, 69, 71, 72, 75, 76, 77, 78], "definit": [3, 4, 22, 23, 24, 26, 35, 41, 60, 68, 69, 72], "degrad": [41, 43, 45, 48, 61], "degre": [6, 22, 25, 26, 34, 57, 61, 65, 69, 75, 77], "del": [31, 32, 34], "delai": [4, 12, 34, 38, 47, 77], "deleg": [59, 68, 77], "delet": [4, 6, 12, 13, 14, 17, 18, 20, 21, 24, 26, 31, 32, 34, 38, 39, 41, 49, 54, 56, 59, 60, 61, 62, 63, 68, 77], "deleted_at": [54, 59], "deliv": [3, 22, 23, 24, 74], "deliver": [44, 67], "deliveri": [16, 38, 45, 50, 71, 75, 76, 77], "demand": [15, 28, 39], "demo": 0, "demonstr": [0, 3, 12, 22, 26, 45, 50, 61, 67, 70, 71, 74, 75, 76, 77], "deni": [11, 64], "denial": [43, 44, 45, 46, 49], "densiti": [6, 46, 48], "deny_all_default": 40, "dep": [18, 20], "depart": 39, "depend": [2, 3, 5, 14, 18, 19, 20, 21, 36, 38, 44, 45, 47, 49, 50, 51, 53, 55, 56, 60, 62, 63, 65, 67, 68, 69, 72, 74, 75, 76, 77], "dependabot": 52, "dependency_scan": 49, "depends_on": [5, 54, 59], "deploi": [1, 2, 3, 11, 21, 23, 24, 27, 28, 30, 31, 34, 40, 41, 50, 52, 66, 67], "deploy": [0, 2, 4, 12, 13, 17, 21, 25, 27, 31, 32, 33, 35, 36, 39, 41, 43, 44, 45, 46, 49, 50, 51, 53, 59, 61, 66, 68, 70, 71, 72, 74, 75, 76, 77, 78], "deployment_d": 55, "deployment_vers": 30, "deploymenttest": 16, "deprec": [54, 76], "deprovis": 68, "depth": [3, 4, 11, 15, 18, 19, 26, 28, 33, 37, 40, 42, 46, 49, 60, 64, 65, 67, 69, 76, 77], "deriv": 18, "desc": [5, 8, 17, 18, 20, 31, 32, 33, 34, 51, 55, 59, 64], "describ": [11, 16, 17, 18, 19, 29, 30, 31, 32, 41, 63, 64], "describe_inst": 63, "describeinst": 64, "descript": [0, 3, 4, 5, 6, 12, 13, 18, 19, 20, 21, 25, 32, 34, 38, 39, 40, 41, 43, 44, 45, 48, 49, 51, 52, 54, 56, 59, 61, 62, 63, 69, 72, 73, 75, 77], "description_part": 56, "deseri": [50, 53], "design": [1, 11, 19, 21, 23, 24, 28, 33, 35, 36, 37, 38, 42, 43, 44, 46, 48, 53, 57, 61, 65, 68, 70, 71, 72, 74, 75, 77], "design_security_control": 7, "desired_capac": 15, "desk": 77, "desktop": 2, "destin": 40, "destroi": 14, "destruct": [45, 68, 75], "detail": [0, 1, 2, 3, 5, 6, 8, 9, 10, 11, 13, 14, 16, 17, 18, 19, 26, 27, 28, 29, 32, 33, 36, 37, 38, 41, 42, 43, 45, 49, 53, 55, 56, 58, 60, 62, 64, 65, 66, 67, 68, 69, 72, 73, 74, 75, 76, 77, 78], "detect": [1, 3, 4, 8, 11, 12, 15, 22, 23, 24, 25, 26, 28, 29, 32, 35, 36, 37, 39, 40, 44, 46, 48, 50, 51, 52, 53, 54, 55, 56, 57, 60, 61, 66, 69, 70, 72, 73, 75, 76, 78], "detect_anomali": [25, 55], "detection_analysi": 67, "detection_cap": 40, "detection_difficulti": [6, 59], "detection_en": 12, "detection_engineering_author": 74, "detection_engineering_expertis": 74, "detection_enhanc": 78, "detection_method": [6, 57], "detection_prob": [6, 59, 78], "detection_queri": 67, "detection_rul": 67, "detection_rule_test": 74, "detection_tim": 56, "determin": [38, 41, 43, 48, 57, 69, 77], "determinist": 63, "dev": [2, 3, 14, 20, 27, 31, 43, 53], "develop": [1, 17, 19, 22, 23, 25, 28, 29, 37, 40, 41, 42, 44, 45, 46, 48, 50, 53, 54, 61, 63, 64, 65, 68, 69, 72, 74, 75], "developer_security_train": 76, "deviat": [70, 74, 76, 77], "devic": [3, 11, 38, 40, 45, 54, 64, 74, 75, 76], "device_certif": 40, "device_compli": 40, "device_health_check": 40, "device_id": 40, "device_verif": 40, "devop": [41, 49, 75], "devpassword123": 20, "devsecop": [48, 52, 71, 76], "devtool": 20, "df": [31, 34, 36, 39, 64], "dfir": 76, "dgvzddp0zxn0": 51, "diagnos": [17, 64], "diagnosi": 17, "diagnost": [0, 41, 75], "diagram": [0, 1, 17, 41, 44, 49, 58, 60], "dialect": 54, "dict": [7, 18, 25, 34, 40, 49, 51, 52, 53, 55, 56, 57, 64], "differ": [0, 1, 3, 12, 14, 15, 19, 27, 33, 36, 54, 63, 65, 68, 72, 76, 77], "differenti": [25, 50, 71, 74, 75, 76], "difficulti": [57, 69], "digest": 77, "digit": [22, 39, 43, 44, 70, 72, 74, 75, 76, 77], "digraph": [34, 57], "dilig": 76, "dimens": 74, "dimension": [70, 74, 75, 76, 77], "diplomat": 75, "dir": [34, 52], "direct": [3, 4, 34, 36, 38, 49, 59, 61, 65, 68, 69, 71, 76, 77, 78], "direct_cost": 78, "direction_enum": 59, "directli": [63, 69], "director": [70, 71, 75, 76, 77], "directori": [3, 14, 17, 20, 27, 52, 63, 68, 75, 77], "disabl": [13, 31, 34, 43, 47, 48, 50, 56, 62, 63, 65, 68, 77], "disast": [24, 26, 36, 38, 40, 41, 49, 60, 61, 71, 72, 75, 76], "disc_1234567890": 5, "disciplinari": 77, "disclos": 42, "disclosur": [42, 43, 44, 46, 49, 53, 71, 75], "disconnect": 43, "discord": 65, "discov": [5, 6, 8, 16, 18, 19, 29, 34, 42, 48, 61, 64, 65, 66, 69, 72, 75, 77, 78], "discover_api": 66, "discover_api_endpoint": 63, "discover_asset": [18, 34], "discover_assets_batch": 34, "discover_aws_asset": 63, "discover_docker_contain": 66, "discover_ec2_inst": 66, "discover_rds_inst": 66, "discover_s3_bucket": 66, "discovered_api": 66, "discoveri": [3, 6, 8, 12, 16, 18, 19, 21, 22, 25, 26, 32, 33, 34, 35, 36, 45, 60, 62, 63, 64, 65, 67, 70, 76, 77, 78], "discovery_config": 29, "discovery_fail": 5, "discovery_job": [64, 66], "discovery_opt": 5, "discovery_orchestr": 29, "discovery_respons": 8, "discovery_result": 66, "discovery_sourc": 5, "discovery_typ": [5, 66], "discoveryorchestr": 29, "discoveryschedul": 66, "discret": 77, "discretionari": 45, "discuss": [3, 8, 18, 19, 20, 21, 26, 44, 45, 60, 65, 70, 72, 78], "disgruntl": 75, "disk": [14, 29, 31, 33, 64], "disk_gb": 5, "disk_read_lat": 33, "disk_write_lat": 33, "diskpressur": 32, "dispar": 76, "displai": [13, 29, 69, 77], "dispos": [37, 38, 40, 42, 68, 75], "disposal_procedur": 40, "disput": 45, "disrupt": [43, 48, 50, 61, 71, 74, 75, 76, 77, 78], "dist": 20, "distinct": 59, "distinguish": 77, "distribut": [15, 23, 28, 33, 57, 71, 75, 76, 77], "distributedcach": 57, "div": [18, 19], "dive": [60, 76, 77], "divers": [59, 60, 76], "diversif": 71, "dkr": [14, 16], "dl": 16, "dlp": [11, 40, 75, 76], "dmz": [36, 40], "dn": [16, 17, 30, 31, 45, 75, 76, 77], "do": [31, 42, 45], "doc": [1, 3, 5, 8, 13, 17, 19, 20, 21, 24, 27, 36, 38, 41, 64, 65, 72], "docker": [0, 1, 2, 14, 16, 17, 19, 20, 21, 25, 31, 33, 34, 36, 41, 51, 60, 65, 66], "docker_contain": 59, "dockerfil": [14, 16, 31], "docs_url": [13, 20], "docstr": [18, 19, 20], "document": [4, 5, 7, 8, 9, 10, 11, 12, 15, 16, 17, 20, 21, 23, 24, 25, 27, 30, 31, 32, 34, 35, 36, 37, 39, 40, 43, 45, 46, 47, 51, 52, 53, 54, 55, 56, 57, 58, 59, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 78], "document_incid": 40, "documentation_coverag": 18, "documented_inform": 40, "doe": [5, 7, 9, 18, 73], "doesn": 40, "dogstatsdcli": 52, "domain": [5, 9, 13, 16, 17, 22, 27, 31, 36, 45, 46, 54, 56, 63, 64, 67, 69, 72, 73, 74, 75, 76, 77], "domain_control": 59, "domain_id": 54, "domain_nam": 16, "don": [19, 21, 34, 49, 64], "done": [31, 41], "down": [2, 14, 17, 19, 31, 32, 33, 34, 36, 64, 65], "down_revis": 54, "downgrad": [17, 18, 20, 54], "download": [8, 14, 16, 27, 29, 30, 36, 65, 77], "downstream": 75, "downtim": [15, 23, 28, 71], "downwardapi": 49, "dpi": 76, "dpia": [70, 76], "dpo": 12, "dr": [30, 38], "draft": 54, "drift": [36, 66, 76, 77], "drill": [26, 38, 43], "drive": [74, 76, 77], "driven": [10, 15, 22, 26, 40, 44, 52, 71, 72, 74, 75, 76, 77], "driver": [62, 75, 76], "drop": [43, 51, 52, 54, 62, 63], "drop_al": 62, "drop_tabl": [18, 54], "dry": [19, 21, 30], "dss": [0, 22, 26, 35, 39, 49, 61, 66, 70, 72, 76], "du": 32, "due": [18, 41, 76], "dump": [30, 34, 57, 65, 77], "duplic": [8, 19, 56, 77], "duplicate_asset": 5, "durabl": [33, 34], "durat": [4, 18, 31, 32, 33, 34, 41, 51, 53, 63, 66, 73, 75], "duration_dai": 73, "duration_hour": [4, 67], "duration_m": 18, "dure": [3, 17, 18, 27, 38, 41, 48, 51, 53, 61, 63, 64, 69, 71, 72, 74, 75, 76, 77], "duti": [4, 38], "dwell": 77, "dynam": [4, 11, 15, 24, 28, 35, 37, 39, 42, 48, 49, 50, 53, 60, 62, 66, 74, 75, 76, 77], "dynamodb": [29, 66], "e": [8, 9, 14, 18, 19, 20, 25, 29, 30, 31, 32, 46, 54, 55, 56, 57, 64, 69, 70, 76], "e2": [0, 2, 18, 21, 62], "e203": 18, "e89b": 7, "each": [1, 3, 13, 18, 22, 26, 41, 43, 46, 47, 57, 59, 62, 63, 68, 72, 77], "earli": [21, 52, 53, 62, 70, 74], "early_stop": 55, "eas": [46, 48, 63], "easi": [0, 1, 8, 12, 15, 18, 25], "east": [5, 13, 27, 29, 30, 63, 66, 76], "east1": [29, 66], "eastern_europ": 73, "eastu": 29, "eb": [29, 30, 66], "ec": [29, 62, 66], "ec2": [5, 16, 17, 29, 64, 65, 66], "ec2_inst": 66, "echo": [14, 21, 30, 31, 32, 34, 51, 53, 64], "economi": [71, 74, 75, 76], "ecosystem": [3, 48, 52, 70, 71], "ecr": [14, 16], "ecr_repo": 31, "edg": [3, 6, 19, 21, 22, 25, 26, 36, 51, 57, 60, 62, 71, 74, 75, 76, 77], "edge_data": [34, 57], "edge_likelihood": 57, "edit": [2, 3, 14, 20, 27, 64], "editor": [18, 20], "edr": [40, 66, 73, 75, 76, 77], "educ": [18, 22, 53, 67, 70, 71, 74, 75, 76, 77], "ef": 66, "effect": [3, 4, 13, 18, 19, 20, 21, 22, 26, 29, 33, 38, 39, 40, 41, 43, 44, 45, 46, 49, 51, 53, 57, 65, 66, 68, 69, 70, 71, 72, 74, 75, 76, 77, 78], "effective_cache_s": [33, 34], "effective_d": 54, "effective_io_concurr": [33, 34], "effectiveness_scor": 40, "effici": [0, 3, 4, 5, 18, 21, 22, 23, 24, 29, 34, 35, 39, 43, 44, 57, 60, 68, 70, 71, 74, 75, 76, 77, 78], "efficientdataprocessor": 34, "effort": [21, 23, 45, 70, 71, 77], "ehr": 75, "ek": [14, 16, 28, 29, 31, 66], "eks_cluster_vers": 16, "eks_desired_capac": 16, "eks_max_capac": 16, "eks_node_instance_typ": 16, "el": 76, "elaps": 52, "elasticach": [14, 16, 28, 66], "elb": 66, "elbv2": [16, 17, 32], "electr": 75, "electron": 75, "element": [0, 1, 24], "element_to_be_click": 62, "elev": [44, 46, 49, 74, 77], "elif": [34, 40, 44, 47, 55], "elimin": [47, 50, 77], "els": [31, 34, 40, 44, 51, 55, 56, 59, 63, 67], "elsif": 59, "email": [7, 11, 12, 14, 16, 19, 20, 27, 36, 41, 42, 45, 51, 56, 59, 62, 63, 64, 65, 66, 67, 75, 77], "email_secur": 59, "email_serv": 69, "embed": [40, 75], "embezzl": 75, "embrac": [71, 77], "emerg": [3, 4, 22, 26, 32, 42, 43, 46, 48, 71, 74, 75, 76, 77], "emergency_approval_bypass": 4, "emergency_duration_hour": 4, "empir": 78, "employe": [40, 45, 68, 69, 70, 75, 76, 77], "employee_id": 40, "empow": [3, 22, 61, 70, 71, 74, 75, 76, 77], "empti": [18, 63, 64], "emptydir": 49, "emul": [22, 26, 74, 75], "enabl": [3, 9, 10, 11, 13, 16, 17, 18, 20, 22, 23, 24, 26, 29, 31, 33, 34, 36, 40, 48, 49, 51, 53, 56, 61, 63, 66, 68, 70, 72, 74, 75, 76, 77], "enable_external_api": 14, "enable_profil": 14, "enc": 73, "encod": [17, 37, 42, 44, 47, 53, 55], "encompass": [42, 76], "encount": [5, 17, 27, 36], "encourag": 77, "encrypt": [15, 27, 28, 29, 37, 38, 39, 40, 42, 44, 45, 47, 49, 50, 57, 59, 60, 61, 65, 66, 68, 69, 75, 76, 77], "encrypt_personal_data": 40, "encrypt_sensitive_data": 40, "encrypted_commun": 40, "encrypted_data": 40, "encryption_algorithm": 13, "encryption_at_rest": 40, "encryption_in_transit": 40, "encryption_system": 59, "encryptionservic": 40, "end": [0, 15, 18, 19, 21, 29, 31, 34, 37, 45, 47, 51, 59, 60, 61, 70, 74, 75, 76], "end_dat": 39, "end_monitor": 51, "end_tim": [55, 63], "enddat": 31, "endpoint": [1, 3, 5, 9, 10, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26, 30, 32, 33, 34, 40, 45, 51, 52, 60, 63, 64, 65, 66, 67, 69, 73, 75, 76, 77], "endpoint_act": 40, "endpoint_detect": 73, "endpoint_protect": 59, "energi": 75, "enforc": [28, 43, 45, 49, 60, 68, 74, 75, 76, 77], "engag": [3, 22, 26, 61, 71, 74, 75, 76, 77], "engin": [6, 17, 22, 23, 24, 26, 32, 34, 35, 41, 45, 49, 57, 58, 62, 65, 69, 72, 75, 76, 78], "english": 34, "enhanc": [0, 9, 19, 21, 26, 43, 44, 50, 54, 61, 65, 69, 70, 71, 72, 74, 75, 76, 77], "enough": [19, 40], "enrich": [8, 23, 24, 25, 35, 72, 77], "enrich_ioc": 73, "enriched_ioc": 73, "ensembl": [23, 55], "ensemble_proba": 55, "ensemble_scor": 55, "ensur": [1, 4, 9, 10, 11, 12, 18, 19, 20, 21, 26, 32, 34, 35, 36, 38, 39, 42, 43, 44, 46, 48, 49, 51, 52, 61, 63, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78], "enter": [6, 36, 65], "enterpris": [0, 6, 8, 9, 10, 15, 22, 23, 27, 28, 33, 34, 36, 42, 57, 59, 60, 61, 62, 64, 65, 66, 71, 72, 73, 74, 75, 77, 78], "enterprise secur": [22, 76], "enterprise_architecture_design": 76, "enterprise_count": 73, "enterprise_risk_integr": 71, "entir": [18, 26, 47, 48], "entiti": [3, 8, 50, 60, 74, 75, 76], "entity_id": 73, "entity_typ": 73, "entri": [34, 56, 57, 69, 72, 77], "entropi": 49, "entry_point": [6, 59, 61, 69], "entrypoint": 8, "enum": [52, 59], "enumer": [55, 60, 72, 75, 77], "env": [2, 13, 14, 16, 17, 18, 20, 27, 34, 41, 51, 62, 63, 64, 65], "environ": [1, 5, 7, 8, 17, 19, 21, 22, 23, 24, 25, 26, 29, 30, 34, 35, 36, 38, 40, 41, 43, 44, 45, 52, 54, 55, 59, 61, 66, 68, 69, 70, 73, 74, 75, 76, 77], "environment": [40, 48, 71, 73, 75], "environment_encod": 55, "eof": [14, 16, 30, 31], "eq": 31, "equip": [74, 75, 76, 77], "equival": 54, "erad": [40, 77], "erasur": 12, "erm": 76, "erp": 75, "err": [8, 18, 19], "error": [0, 3, 7, 13, 17, 18, 19, 20, 21, 24, 25, 27, 31, 32, 33, 36, 38, 40, 41, 44, 47, 50, 51, 52, 53, 55, 56, 57, 60, 61, 62, 63, 65, 66, 68], "error_count": 31, "error_messag": 63, "error_r": [31, 33], "error_typ": 18, "errorboundari": 18, "errorfallback": 18, "errorinfo": 18, "errormessag": 18, "esac": [30, 31], "esbenp": 20, "escal": [0, 11, 15, 24, 25, 28, 39, 43, 44, 45, 49, 51, 53, 61, 67, 68, 69, 70, 72, 74, 75, 76], "escalate_incid": 25, "escalation_id": 4, "escalation_level": 25, "escalation_procedur": 40, "escap": [74, 75], "esg": 71, "eslint": 18, "especi": 7, "espionag": [75, 78], "essenti": [26, 31, 40, 77], "establish": [1, 18, 22, 23, 33, 34, 35, 38, 40, 43, 68, 69, 71, 74, 75, 76, 77], "estim": [21, 32, 35, 43, 61, 67, 69, 72, 75, 77, 78], "estimated_complet": 5, "estimated_dur": [5, 6, 59], "estimated_tim": [6, 59], "estimated_time_to_compromis": 78, "eta": 32, "etc": [3, 21, 27, 31, 32, 51, 64, 70, 72, 74, 78], "eth0": 66, "ethic": [26, 70, 74, 75], "eu": [29, 61, 66], "europ": 29, "european": 42, "evalu": [4, 23, 24, 27, 38, 41, 43, 44, 46, 48, 51, 52, 53, 55, 69, 71, 72, 74, 75, 76, 77], "evaluate_security_postur": 52, "evaluation_interv": 33, "evas": [61, 72, 74, 75], "event": [7, 8, 13, 15, 16, 17, 23, 24, 25, 26, 28, 32, 33, 35, 36, 37, 38, 41, 43, 52, 54, 60, 61, 65, 66, 71, 72, 74, 75, 76, 77], "event_count": [39, 73], "event_data": [25, 73], "event_id": [39, 73], "event_process": 39, "event_processing_time_p95": 28, "event_typ": [39, 52, 73], "events_per_second": 28, "ever": [71, 74, 75, 76, 77], "everi": [2, 3, 11, 21, 22, 26, 34, 41, 46, 48, 50, 52, 60, 66, 77], "everyon": [19, 42, 75], "everyth": [1, 2, 3, 8, 20, 72], "everywher": 15, "evict": 61, "evid": [24, 34, 36, 37, 38, 39, 41, 49, 54, 67, 70, 72, 73, 74, 75, 76, 77], "evidence_collect": 28, "evidence_requir": 54, "evolut": [24, 25, 60, 61, 71, 74, 75, 76, 77], "evolv": [3, 11, 12, 18, 21, 22, 24, 37, 44, 46, 48, 50, 52, 71, 74, 75, 76, 77], "evt_12345": 73, "evt_67890": 73, "ex": 73, "examin": [69, 70, 76, 77], "exampl": [1, 9, 10, 14, 18, 19, 20, 21, 26, 27, 28, 34, 36, 41, 45, 46, 48, 51, 53, 62, 63, 64, 65, 71, 72, 73, 78], "exc_info": 63, "exc_tb": [34, 56], "exc_typ": [34, 56], "exc_val": [34, 56], "exce": [18, 37, 42, 50, 52], "exceed": [8, 57], "excel": [0, 24, 35, 39, 44, 46, 48, 61, 66, 72], "except": [4, 18, 19, 26, 32, 34, 41, 47, 50, 55, 56, 57, 68, 76], "excerpt": 49, "exchang": 74, "exclud": [20, 45], "exclude_dir": [52, 53], "exclude_lin": 63, "exclus": 66, "exec": [2, 11, 12, 14, 16, 17, 30, 31, 32, 36, 39, 41, 64, 65], "exec_us": [52, 53], "execut": [2, 3, 4, 5, 15, 16, 18, 22, 25, 34, 38, 39, 40, 43, 44, 45, 51, 52, 53, 56, 57, 59, 60, 62, 67, 68, 69, 78], "execute_automated_respons": 25, "execute_valid": 67, "executive leadership": 71, "executive overview": 50, "executive_reporting_access": [70, 74, 75, 76], "executive_security_report": 71, "executive_summari": 54, "executor": 57, "exercis": [22, 37, 40, 41, 43, 49, 61, 74, 75, 76, 77], "exercise_design_author": 74, "exercise_result": 67, "exfiltr": [6, 43, 61, 69, 74, 75, 77], "exhaust": [18, 21, 29], "exist": [1, 5, 9, 18, 19, 27, 36, 56, 62, 63, 64, 65, 69, 72, 74, 76, 77], "existing_map": 56, "exit": [14, 17, 30, 31, 34, 36, 56, 64], "exp": [7, 55], "expand": [0, 3, 26, 28, 36, 50, 66], "expans": [3, 22, 24, 26, 28, 48, 75], "expect": [19, 63, 64, 65, 66, 67, 76, 77], "expected_condit": 62, "expected_path": 19, "expected_result": 63, "expens": 76, "expensive_attack_analysi": 34, "expensive_attack_path_calcul": 34, "experi": [3, 19, 20, 23, 64, 71, 76, 77], "experienc": 0, "expert": [43, 46, 70, 74, 75, 76, 77], "expertis": [21, 68, 70, 72, 74, 75, 76, 77], "expir": [4, 7, 12, 21, 28, 29, 31, 32, 34, 64, 65], "expire_in": 52, "expires_at": 12, "expires_in": 7, "expiri": [21, 31], "explain": [19, 34, 62], "explan": [19, 45], "explicit": [18, 38], "explicitli": [40, 45], "exploit": [41, 44, 45, 46, 48, 61, 65, 67, 69, 72, 74, 76, 77, 78], "exploit_development_access": 75, "exploitability_scor": 44, "explor": [0, 18, 19, 20, 36, 57, 75, 77], "exponenti": 8, "export": [2, 4, 7, 12, 14, 18, 24, 25, 32, 33, 36, 38, 57, 60, 66, 68, 69, 72, 73, 77], "export_navigator_lay": 73, "expos": [8, 19, 51, 62, 77], "exposur": [19, 29, 41, 43, 45, 48, 50, 75, 76, 77, 78], "exposure_level": 67, "exposure_risk": 34, "expr": 52, "extend": [12, 18, 23, 24, 25, 35, 53, 56, 57, 59, 65, 67, 71, 76], "extens": [0, 2, 3, 4, 18, 20, 35, 57, 59, 60, 75, 76, 77], "extern": [0, 1, 7, 11, 13, 14, 18, 19, 36, 37, 38, 39, 44, 50, 60, 62, 63, 65, 67, 68, 69, 70, 72, 73, 74, 75, 76, 77, 78], "external_asset": 67, "external_exposur": 34, "external_factor": 40, "extort": 75, "extra": [29, 52, 53], "extract": [30, 55], "extract_asset_featur": 55, "extract_batch_featur": 55, "extract_ioc": 40, "eyjhbgcioijiuzi1niisinr5cci6ikpxvcj9": 7, "eyjhbgcioijiuzi1nij9": 51, "eyjpzci6mtizndu2nzg5mh0": 8, "f": [2, 5, 6, 7, 8, 14, 16, 17, 18, 19, 20, 27, 29, 30, 31, 32, 33, 34, 40, 41, 44, 46, 49, 51, 52, 53, 55, 56, 57, 62, 63, 64, 66, 67, 69, 73, 78], "f1_score": 55, "f2": 31, "f5": 64, "faa": 75, "face": [43, 45, 69, 72, 76, 78], "facet": 3, "facial": 11, "facil": [40, 45, 74, 75, 76], "facilit": [74, 76], "factor": [3, 4, 6, 11, 13, 15, 28, 29, 37, 38, 40, 42, 44, 46, 47, 48, 49, 60, 61, 67, 68, 69, 72, 74, 75, 76, 77, 78], "factor_scor": 67, "factori": [0, 63], "fail": [2, 5, 6, 7, 9, 10, 17, 18, 19, 21, 32, 36, 37, 41, 47, 49, 51, 52, 53, 55, 56, 57, 62, 63, 64, 77], "fail_on_crit": 52, "fail_timeout": [33, 34], "failed_login_spik": 39, "failov": [15, 23, 28, 38, 60, 68], "failur": [11, 15, 21, 29, 34, 36, 39, 41, 43, 63, 64, 76], "fair": [9, 10, 38, 44], "faith": 45, "faker": [62, 63], "fallback": [47, 50], "fals": [3, 4, 5, 7, 12, 13, 14, 16, 18, 19, 20, 22, 23, 25, 27, 31, 32, 34, 40, 43, 44, 46, 47, 48, 49, 50, 51, 52, 54, 55, 56, 57, 59, 63, 66, 67, 73, 74, 75, 77], "false_posit": [51, 53], "false_positive_r": [40, 67], "false_positives_tot": 53, "fame": 45, "famili": 77, "familiar": [36, 42], "fanci": [74, 75, 78], "faq": 1, "far": 76, "fast": [4, 21, 51, 52, 53, 60, 62, 63], "fastapi": [20, 23, 24, 25, 51, 56, 62], "faster": [0, 2, 23, 24, 34, 47, 76], "fastest": 27, "fault": [15, 26, 74, 76], "fc": [18, 19, 25], "fda": 70, "feasibl": 78, "feat": [2, 19, 21], "featur": [0, 4, 10, 11, 12, 14, 15, 19, 28, 39, 41, 44, 48, 49, 57, 60, 61, 62, 63, 64, 69, 73, 75, 77], "feature_column": 55, "feature_count": 55, "feature_dict": 55, "feature_engin": 55, "feature_import": 55, "feature_scal": 55, "feature_vector": 55, "featureengin": 55, "features_df": 55, "features_list": 55, "feder": [59, 75, 76, 77], "feed": [11, 36, 37, 43, 45, 49, 60, 61, 65, 68, 72, 73, 74, 77, 78], "feedback": [0, 1, 3, 19, 21, 22, 37, 43, 51, 53, 62, 63, 74, 76, 77], "fetch": [7, 21, 34, 63], "fetch_func": 34, "fetchal": 18, "few": 21, "fi": [14, 30, 31], "field": [5, 11, 13, 18, 24, 26, 31, 36, 77], "field_encryption_kei": 13, "file": [0, 2, 3, 5, 13, 14, 15, 16, 17, 19, 20, 21, 26, 27, 31, 34, 41, 49, 51, 52, 53, 62, 63, 64, 65, 66, 67, 68, 69, 75, 77], "file_hash": 56, "file_path": 52, "fileless": 75, "filenam": [53, 56], "filesystem": 30, "fill": 65, "filter": [3, 5, 18, 26, 29, 31, 32, 36, 55, 56, 65, 66, 67, 77], "filter_bi": 62, "fin7": [10, 72, 78], "final": [18, 19, 21, 23, 24, 34, 35, 51, 55, 71, 74, 75, 76, 77], "financ": [76, 78], "financi": [6, 22, 26, 57, 61, 69, 70, 71, 72, 74, 75, 76, 77, 78], "financial_data_affect": 78, "financial_impact": [6, 69, 78], "financial_reporting_system": 78, "financially_motiv": 67, "find": [18, 20, 31, 32, 34, 38, 43, 44, 53, 54, 56, 57, 64, 65, 67, 68, 69, 70, 72, 74, 75, 76, 77], "find_attack_path": [57, 62], "find_attack_paths_optim": 34, "find_el": 62, "find_path": [19, 63], "fine": [7, 65, 66, 68, 76, 77, 78], "fingerprint": 11, "fire": [40, 75], "firefox": [36, 65, 77], "firestor": 29, "firewal": [15, 36, 37, 40, 42, 44, 48, 59, 64, 65, 66, 67, 68, 76, 77], "firmwar": [59, 75], "first": [15, 18, 19, 20, 21, 26, 27, 34, 42, 45, 48, 50, 55, 56, 57, 62, 63, 64, 65, 66, 68, 69, 71, 73, 77], "first_nam": [7, 59, 62, 63], "first_seen": 73, "fit": 55, "fit_transform": 55, "fix": [2, 17, 19, 21, 44, 45, 48, 53], "fix_hardcoded_secret": 52, "fix_weak_crypto": 52, "fixat": 51, "fixer": 62, "fixtur": [16, 19], "fk_attack_paths_source_asset": 18, "flag": [20, 35, 63, 74, 75], "flake8": [18, 20, 21, 49, 52, 62], "flake8en": [18, 20], "flaw": [45, 75], "flexibl": [54, 59, 60, 61], "float": [25, 34, 55, 57], "flow": [1, 3, 8, 12, 15, 28, 33, 37, 38, 44, 49, 60, 66, 72, 76, 77], "flushal": [64, 65], "focu": [19, 34, 36, 44, 49, 59, 66, 67, 69, 70, 72, 74, 75], "focus": [0, 1, 19, 21, 22, 36, 39, 44, 47, 55, 63, 67, 71, 75], "follow": [0, 1, 3, 4, 8, 13, 16, 18, 19, 21, 36, 37, 42, 43, 44, 45, 46, 48, 49, 52, 58, 62, 64, 68, 69, 70, 72, 75, 77], "font": 3, "footer": 21, "foothold": [6, 75], "footprint": [75, 76], "forbidden": [8, 18], "forc": [20, 34, 51, 68, 73], "force_upd": 73, "forecast": [24, 70, 71, 74, 76, 77], "forecast_risk_trend": 25, "forefront": [75, 77], "foreign": 18, "foreignkei": [25, 54, 55, 56], "foreignkeyconstraint": 18, "forens": [24, 28, 38, 39, 40, 43, 67, 69, 72, 74, 75, 76, 77], "forensicsservic": 40, "forest": [23, 24, 35, 55, 75], "forget": 34, "fork": [3, 60], "forkrul": [2, 19, 20, 27, 36, 64, 65], "form": [51, 63], "formal": [3, 38, 40, 48, 68, 76, 77], "format": [0, 3, 6, 9, 12, 16, 18, 19, 20, 21, 26, 30, 31, 33, 39, 40, 46, 52, 53, 57, 60, 61, 63, 65, 66, 67, 68, 73, 77], "formatonsav": [18, 20], "formatt": [18, 20], "formula": 46, "forum": [8, 19, 36, 60, 64, 68, 72, 74, 75, 76, 77], "forward": [17, 32, 33, 34, 65, 68, 77], "foster": [71, 74, 75, 76], "found": [6, 8, 10, 14, 18, 19, 31, 44, 52, 53, 55, 56, 62, 64, 66, 67, 69], "foundat": [1, 23, 25, 28, 35, 50, 54, 55, 57, 59, 61, 63, 66, 70, 74, 75, 76], "fp_rate": 67, "framework": [0, 3, 6, 8, 9, 22, 26, 35, 37, 43, 47, 48, 52, 62, 65, 66, 69, 72, 73, 78], "framework_funct": 28, "framework_id": [25, 54], "fraud": [74, 75], "fraudul": 75, "free": [6, 20, 27, 64, 69], "frequenc": [4, 38, 40, 43, 44, 46, 48, 74, 75, 76, 77], "frequent": [4, 11, 12, 19, 34, 39, 63, 64, 67, 73, 75], "fresh": 34, "fresh_valu": 34, "fridai": 76, "friendli": [3, 72], "from": [0, 1, 3, 5, 6, 7, 8, 9, 10, 13, 14, 16, 17, 18, 19, 20, 21, 22, 26, 29, 30, 31, 32, 33, 34, 36, 37, 38, 39, 41, 42, 43, 46, 49, 51, 52, 53, 54, 55, 56, 58, 59, 60, 61, 62, 63, 64, 66, 67, 68, 69, 70, 72, 73, 77, 78], "front": 75, "frontend": [0, 5, 16, 19, 23, 25, 27, 35, 36, 65], "frontlin": 77, "fsgroup": [17, 49], "fssl": 14, "full": [3, 4, 11, 12, 14, 19, 24, 25, 28, 35, 36, 41, 42, 43, 44, 50, 52, 54, 62, 65, 66, 68, 71, 75, 77], "full_nam": 51, "full_network": 40, "fulli": [15, 40, 46, 48, 50], "func": [18, 34, 52, 54, 55, 56], "function": [2, 3, 4, 8, 17, 18, 19, 21, 22, 23, 24, 25, 29, 34, 35, 38, 39, 41, 43, 45, 48, 52, 53, 59, 60, 63, 65, 66, 68, 71, 72, 74, 75, 76, 77, 78], "functool": [18, 34], "fundament": [53, 70, 71, 72, 74, 75, 76, 77], "further": [38, 77], "fusion": [74, 77], "futur": [24, 26, 54, 60, 69, 71, 74, 75, 76, 77], "fuzz": 75, "g": [8, 9, 14, 18, 19, 25, 29, 54, 69], "g0016": 73, "g0028": 73, "ga": 75, "gain": [6, 69, 75, 77], "game": 75, "gang": 74, "gap": [0, 23, 24, 39, 40, 43, 44, 48, 60, 69, 70, 72, 74, 75, 76, 77], "gapanalysi": 25, "garbag": [33, 34, 39], "gate": [0, 18, 50], "gate_statu": 52, "gatewai": [28, 40, 45, 76], "gather": [24, 34, 45, 63, 72, 74, 75, 76, 77], "gaug": [34, 52, 53], "gb": [16, 33], "gc": 34, "gcfa": 77, "gcih": 77, "gcloud": 29, "gcm": [11, 13, 28, 40], "gcp": [0, 1, 5, 13, 22, 26, 27, 36, 45, 61, 65, 66, 72, 74, 76, 77], "gcp_config": 29, "gcp_default_region": 13, "gcp_enable_comput": 13, "gcp_enable_iam": 13, "gcp_enable_network": 13, "gcp_enable_storag": 13, "gcp_engin": 29, "gcp_poll_interv": 13, "gcp_project_id": 13, "gcp_result": 29, "gdpr": [0, 6, 22, 26, 37, 39, 41, 42, 43, 44, 46, 50, 61, 65, 66, 69, 70, 76, 77, 78], "gdpr_complianc": 12, "gdpr_consent_record": 12, "gdpr_data_subject_request": 12, "gdpr_impact": 78, "gdpr_report": 66, "gdprcomplianceservic": 40, "gen_random_uuid": [54, 59], "gener": [0, 1, 3, 7, 8, 12, 13, 16, 19, 21, 22, 23, 24, 26, 28, 31, 32, 39, 40, 41, 42, 46, 49, 50, 51, 52, 53, 55, 56, 59, 60, 61, 62, 64, 66, 67, 68, 69, 70, 73, 74, 75, 76, 77], "generate_asset_report": 66, "generate_basic_report": 77, "generate_collaboration_report": 67, "generate_compliance_report": 67, "generate_detection_rul": 67, "generate_executive_report": 77, "generate_executive_summari": 52, "generate_gap_analysi": 25, "generate_improvement_recommend": 40, "generate_mitig": 78, "generate_navigator_lay": 73, "generate_playbook": 67, "generate_recommend": [44, 52], "generate_red_team_report": 67, "generate_report": [7, 51], "generate_review_id": 44, "generate_summari": 53, "generate_visu": 73, "generate_zero_trust_roadmap": 67, "genuin": 77, "geograph": [4, 15, 40, 68, 76, 77, 78], "geographic_region": 73, "geoloc": 39, "geopolit": [75, 76], "geospati": 77, "get": [4, 8, 11, 12, 13, 14, 16, 18, 25, 29, 30, 31, 32, 33, 34, 39, 41, 51, 52, 53, 55, 56, 57, 61, 62, 63], "get_active_job": 29, "get_alert": 67, "get_all_techniqu": 40, "get_asset": [18, 63], "get_asset_threat_predict": 55, "get_assets_by_classif": 67, "get_assets_by_typ": 18, "get_assets_by_type_orm": 18, "get_attack_analysi": 34, "get_attack_path": [34, 57], "get_by_id": [18, 63], "get_by_nam": 67, "get_cached_result": 57, "get_cas": 56, "get_compliance_scop": 67, "get_correlation_rul": 73, "get_critical_issu": 52, "get_current_us": [18, 20], "get_data_statu": 73, "get_database_password": 53, "get_db": [55, 56], "get_discovery_statist": 29, "get_edge_data": [34, 57], "get_event_context": 73, "get_event_loop": 57, "get_factor_scor": 67, "get_high_issu": 52, "get_incident_involv": 40, "get_incident_playbook": 40, "get_job_statu": 29, "get_kei": 40, "get_legal_basi": 40, "get_logg": 18, "get_mitre_attack_map": 57, "get_mitre_techniqu": 34, "get_or_set": 34, "get_phishing_test_result": 40, "get_remediation_r": 52, "get_response_team": 40, "get_review": 44, "get_role_permiss": 40, "get_security_postur": 52, "get_statu": 66, "get_techniqu": 63, "get_techniques_for_step": 67, "get_threat_actor": 40, "get_threat_actor_profil": 73, "get_total_scan": 52, "get_training_histori": 40, "get_training_recommend": 40, "get_us": 40, "get_user_rol": 40, "get_vulnerability_trend": 52, "getasset": 18, "getbucketloc": 29, "getderivedstatefromerror": 18, "getenv": 52, "getlogg": [19, 29, 52, 55, 56], "gh": [3, 19, 21], "giac": 77, "gin": [34, 59], "git": [2, 14, 15, 19, 20, 27, 30, 36, 44, 52, 60, 65], "githook": 20, "github": [0, 1, 2, 3, 8, 16, 18, 19, 20, 21, 26, 27, 36, 45, 49, 51, 52, 60, 62, 63, 64, 65], "github_token": 3, "gitlab": 52, "give": [12, 19], "given_at": 12, "gke": 29, "glass": 4, "global": [15, 28, 33, 71, 74, 75, 76, 77], "global_architecture_author": 76, "gnfa": 77, "go": [8, 19, 36, 60, 61, 63, 65, 69, 77], "goal": [40, 74, 75, 77], "golden": 75, "good": [17, 18, 19, 21, 36, 44, 45, 49, 63], "googl": [5, 13, 29, 36, 66, 68], "google_application_credenti": [13, 27, 66], "govern": [22, 26, 38, 40, 68, 72, 73, 74, 75, 77, 78], "governance_and_risk_manag": 40, "grace": [61, 65], "gracefulli": [8, 21, 63], "grade": [0, 1, 9, 10, 15, 22, 23, 24, 25, 26, 28, 42, 57, 60, 61, 62, 73, 75, 77, 78], "gradual": [11, 15, 35, 36, 41, 43, 66, 69, 77], "graduat": 77, "grafana": [0, 16, 17, 23, 24, 25, 30, 33, 35, 38, 41], "grafana_en": 14, "grafana_password": 32, "grain": [7, 65, 68], "grant": [4, 11, 16], "granular": [12, 28, 60, 61, 68, 73], "graph": [3, 8, 13, 15, 18, 19, 20, 21, 23, 24, 25, 26, 27, 29, 33, 34, 35, 36, 63, 64, 65, 67, 69, 72, 74, 75, 77], "graph_data": 25, "graph_engin": 57, "graph_servic": 19, "graph_statist": 6, "graphql": [29, 60, 66], "graphservic": 19, "grc": [26, 70], "green": [28, 36, 56, 68], "grep": [4, 12, 17, 27, 31, 32, 36, 39, 41, 64, 65], "grid": 75, "griffon": 78, "group": [6, 9, 11, 14, 15, 16, 17, 28, 29, 30, 31, 32, 34, 39, 49, 52, 66, 68, 69, 73, 74, 75, 77, 78], "group1_uniqu": 73, "group2_uniqu": 73, "group_id": 73, "group_not_found": 9, "grow": [19, 34, 77], "growth": [31, 32, 33, 50, 61, 68, 71, 72, 73, 76, 77], "growth_rat": 73, "gserviceaccount": 29, "gt": 31, "guarante": 4, "guardian": [70, 71], "guest_network": 40, "gui": 2, "guid": [7, 9, 10, 21, 23, 24, 25, 28, 34, 42, 49, 60, 61, 62, 64, 65, 66, 67], "guidanc": [0, 1, 3, 14, 22, 26, 31, 33, 41, 43, 44, 61, 62, 70, 72, 74, 75, 76, 77], "guided_attack_simul": 75, "guidelin": [0, 2, 3, 18, 21, 26, 33, 49, 53, 65, 75, 76], "gunicorn": 34, "gv": 38, "gz": [16, 30, 32, 41, 64], "gzip": 34, "gzip_min_length": 34, "gzip_typ": 34, "gzip_vari": 34, "h": [5, 6, 9, 10, 16, 17, 20, 27, 29, 30, 31, 32, 39, 41, 64, 65, 73, 78], "ha": [1, 15, 18, 19, 23, 28, 50, 52, 66, 69, 70, 71, 74, 75, 76, 77], "hack": [26, 75], "hacker": [51, 75], "hall": 45, "hand": [0, 3, 40, 49, 76], "handl": [0, 3, 4, 8, 12, 13, 18, 19, 21, 23, 24, 25, 33, 34, 37, 38, 40, 41, 42, 44, 51, 56, 60, 61, 63, 65, 68, 75, 77], "handle_security_incid": 40, "handle_thehive_case_upd": 56, "handleanalysi": 18, "handler": [4, 40, 77], "handov": [22, 77], "handshak": 64, "happen": 19, "happi": 2, "harass": 45, "hard": 64, "hard_delet": 5, "hardcod": [13, 46, 48, 49, 50, 52, 53], "hardcoded_bind_all_interfac": [52, 53], "hardcoded_password_default": [52, 53], "hardcoded_password_funcarg": [52, 53], "hardcoded_password_str": [52, 53], "hardcoded_tmp_directori": 52, "harden": [22, 23, 24, 35, 42, 43, 44, 50, 68, 74, 76, 77], "hardwar": [11, 20, 66, 68, 74, 75], "harmon": 76, "harvest": 75, "has_edg": 34, "has_lawful_basi": 40, "has_mor": 8, "has_next": 5, "has_prev": 5, "haserror": 18, "hash": [34, 39, 50, 52, 56, 73, 75, 77], "hash_algorithm": 39, "hashicorp": 16, "hashlib": [47, 52], "hat": 77, "have": [0, 4, 21, 26, 28, 36, 47, 68, 69, 73, 77, 78], "head": [1, 14, 17, 20, 30, 31, 32, 33], "header": [3, 5, 6, 7, 8, 10, 11, 21, 31, 45, 51, 52, 56, 62, 64, 65, 68, 69], "headless": 62, "heal": [24, 35, 61], "health": [0, 11, 13, 14, 15, 17, 23, 24, 25, 30, 34, 35, 36, 41, 51, 60, 61, 62, 68, 70, 72, 75, 76, 77, 78], "health_check_databas": 13, "health_check_en": 13, "health_check_external_api": 13, "health_check_interv": 13, "health_check_neo4j": 13, "health_check_redi": 13, "health_check_timeout": 13, "healthcar": [10, 22, 39, 61, 70, 75, 76, 78], "healthcheck": 34, "heap": [33, 34], "heat": [9, 72, 73, 76, 77], "heavi": 39, "heavili": 59, "hec": 66, "helm": 16, "help": [21, 26, 45, 53, 67, 74, 75, 77], "helper": 63, "here": [27, 36, 65, 69], "hesit": 64, "hexdigest": 47, "hhmmss": 41, "hidden": 75, "hidden_layer_s": 55, "hierarch": [3, 4, 13, 76], "hierarchi": [0, 1, 3, 68], "high": [4, 5, 6, 8, 12, 19, 21, 24, 26, 27, 28, 29, 31, 33, 34, 36, 37, 38, 39, 40, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 59, 60, 61, 62, 63, 64, 66, 67, 68, 69, 72, 73, 75, 76, 77, 78], "high_degree_nod": 34, "high_issu": 52, "high_risk_asset": 55, "high_risk_trigg": 12, "high_vuln": 31, "high_vulns_count": 55, "higher": [18, 27, 55, 57, 69, 77], "highest": [13, 45, 67, 71, 74, 75], "highli": 75, "highlight": [0, 3, 19, 60], "hijack": [45, 75], "hint": 19, "hipaa": [22, 26, 39, 61, 69, 70, 76, 77, 78], "hipaa_impact": 78, "histogram": [34, 51, 53], "histor": [4, 24, 39, 60, 76, 77, 78], "histori": [1, 12, 17, 45, 60, 75, 77], "historian": 75, "historical_weight": 4, "hit": [31, 32, 33, 57, 60], "hit_perc": 59, "hit_rat": 32, "hmac": 39, "hmi": 75, "hoc": [40, 44], "hold": [19, 43, 70, 77], "hole": 75, "homepag": 3, "hook": [0, 14, 18, 20, 21, 47, 62], "hookspath": 20, "hop": [6, 18, 36, 61, 65, 69, 72, 75, 77, 78], "horizont": [0, 15, 17, 24, 32, 39, 60, 61, 77], "horizontalpodautoscal": 33, "hospit": [75, 78], "host": [1, 2, 13, 14, 16, 17, 20, 23, 24, 27, 30, 33, 34, 51, 52, 53, 59, 64, 77], "host_id": 40, "hosted_zone_id": [16, 17, 30], "hostnam": [5, 16], "hot_retention_dai": 39, "hotfix": [21, 48], "hotlin": [74, 75, 76, 77], "hotspot": [46, 48, 53], "hour": [9, 10, 12, 13, 15, 28, 30, 32, 33, 34, 38, 40, 43, 45, 46, 48, 49, 50, 52, 55, 57, 66, 67, 69, 72, 76, 77, 78], "hour_of_dai": 55, "hourli": [41, 66, 78], "hourly_increment": 66, "housekeep": 68, "how": [35, 49, 51, 63, 69, 72, 77], "hpa": [31, 33], "hr": [68, 77], "hs256": [13, 27], "hsm": [11, 40, 47], "hst": 11, "html": [1, 3, 20, 39, 49, 51, 53, 62, 63], "htmlcov": [62, 63], "htop": 2, "http": [2, 3, 5, 6, 7, 9, 10, 11, 13, 14, 17, 19, 20, 23, 24, 27, 29, 30, 31, 32, 33, 34, 36, 41, 49, 51, 52, 62, 63, 64, 65, 66, 69, 72, 73, 77, 78], "http_request_duration_second": 34, "http_requests_tot": [32, 34], "httperror": 56, "httpexcept": [49, 55, 56], "httpie": 20, "httponli": 7, "httpuser": 62, "httpx": [23, 25, 56], "hub": [70, 74, 75], "human": [71, 74, 75, 76, 77], "humint": [74, 75], "hunt": [11, 22, 24, 26, 35, 37, 40, 50, 61, 74, 75, 76, 77], "hunter": [74, 77], "hvt": 75, "hybrid": [54, 60, 66, 75, 76], "hybrid_architecture_design": 76, "hyperparamet": 55, "hypervisor": 75, "hypothes": 77, "hypothesi": [26, 74, 77], "i": [1, 2, 5, 6, 8, 10, 12, 13, 15, 16, 17, 18, 19, 20, 21, 24, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 37, 38, 40, 41, 42, 43, 44, 46, 47, 48, 50, 51, 52, 53, 55, 56, 57, 59, 61, 62, 63, 64, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78], "iac": [15, 75, 76], "iam": [16, 29, 64, 65, 66, 68, 74, 75, 76, 77], "iast": [37, 46, 50], "iat": 7, "ibm": 29, "ic": [9, 72, 73, 75], "icon": 36, "ics_count": 73, "id": [0, 2, 5, 6, 7, 8, 9, 12, 13, 16, 17, 18, 19, 20, 25, 27, 29, 30, 34, 40, 43, 44, 48, 51, 52, 53, 54, 55, 56, 57, 59, 60, 62, 63, 64, 65, 66, 67, 75, 76, 77], "idea": [20, 72], "ident": [12, 13, 15, 16, 22, 27, 28, 29, 38, 40, 44, 46, 50, 64, 67, 74, 75, 76, 77], "identif": [4, 9, 10, 11, 24, 25, 37, 38, 39, 40, 41, 43, 48, 49, 53, 60, 61, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78], "identifi": [3, 5, 8, 9, 16, 17, 19, 25, 30, 32, 33, 34, 35, 41, 43, 45, 47, 48, 49, 51, 53, 54, 61, 62, 65, 67, 68, 69, 72, 73, 74, 75, 76, 77, 78], "identify_affected_asset": 40, "identify_risk_pattern": 25, "identify_trust_boundari": 67, "identity_provid": 59, "identity_verif": 40, "ideologi": 78, "ids_ip": 59, "idx": 55, "idx_asset_relationships_act": 59, "idx_asset_relationships_sourc": 59, "idx_asset_relationships_target": 59, "idx_asset_relationships_typ": 59, "idx_assets_config_gin": 59, "idx_assets_created_at": [34, 59], "idx_assets_discovery_composit": 34, "idx_assets_environment_statu": 59, "idx_assets_ip_address": 34, "idx_assets_properties_gin": 59, "idx_assets_risk_scor": 59, "idx_assets_soft_delet": 59, "idx_assets_typ": 17, "idx_assets_type_act": 34, "idx_assets_type_env_statu": 59, "idx_assets_type_provid": 59, "idx_attack_paths_created_at": 59, "idx_attack_paths_crit": 59, "idx_attack_paths_nodes_gin": 59, "idx_attack_paths_path_typ": 59, "idx_attack_paths_risk_scor": [18, 34, 59], "idx_attack_paths_source_asset": 18, "idx_attack_paths_source_risk_crit": 59, "idx_attack_paths_source_target": [34, 59], "idx_attack_paths_techniques_gin": 59, "idx_attack_scenarios_entry_points_gin": 59, "idx_attack_scenarios_objectives_gin": 59, "idx_audit_events_event_typ": 39, "idx_audit_events_resourc": 39, "idx_audit_events_timestamp": 39, "idx_audit_events_user_id": 39, "idx_compliance_assessments_assessment_d": 54, "idx_compliance_assessments_deleted_at": 54, "idx_compliance_assessments_framework_id": 54, "idx_compliance_assessments_statu": 54, "idx_compliance_control_assessments_assessment_id": 54, "idx_compliance_control_assessments_control_id": 54, "idx_compliance_control_assessments_deleted_at": 54, "idx_compliance_control_assessments_risk_r": 54, "idx_compliance_control_assessments_statu": 54, "idx_compliance_control_mappings_deleted_at": 54, "idx_compliance_control_mappings_mapping_typ": 54, "idx_compliance_control_mappings_source_control_id": 54, "idx_compliance_control_mappings_target_control_id": 54, "idx_compliance_controls_automation_level": 54, "idx_compliance_controls_control_id": 54, "idx_compliance_controls_deleted_at": 54, "idx_compliance_controls_domain_id": 54, "idx_compliance_controls_framework_id": 54, "idx_compliance_domains_deleted_at": 54, "idx_compliance_domains_framework_id": 54, "idx_compliance_frameworks_deleted_at": 54, "idx_compliance_frameworks_nam": 54, "idx_compliance_frameworks_statu": 54, "idx_mitre_techniques_search": 34, "idx_mitre_techniques_tact": 34, "idx_relationships_source_type_act": 59, "idx_scan": [31, 34], "idx_tup_fetch": [31, 34], "idx_tup_read": [31, 34], "iga": 76, "ignor": [14, 18, 34, 44, 51, 52, 56, 64], "ii": [37, 42, 46, 50, 61, 65, 76], "iii": [70, 76], "illustr": 58, "im": 38, "imag": [3, 7, 21, 23, 29, 31, 34, 36, 37, 43, 46, 48, 49, 51, 52, 62, 64, 65, 75, 76, 77], "image_vulner": 66, "img": [51, 62], "immedi": [0, 1, 12, 24, 27, 29, 32, 38, 43, 44, 46, 48, 64, 65, 67, 69, 77], "immut": [34, 39], "impact": [6, 8, 11, 19, 21, 25, 26, 28, 32, 34, 38, 41, 43, 45, 46, 56, 57, 60, 61, 65, 67, 69, 70, 71, 72, 74, 75, 76, 77], "impact_area": 49, "impact_by_degre": [6, 57], "impact_scor": [6, 59], "impair": 41, "imperson": [44, 75], "implant": [74, 75], "implement": [1, 2, 3, 5, 7, 8, 13, 14, 18, 19, 20, 21, 22, 26, 29, 32, 33, 34, 36, 42, 43, 44, 46, 48, 49, 52, 54, 57, 59, 60, 61, 62, 66, 67, 68, 69, 70, 71, 72, 75, 77, 78], "implementation_dai": 78, "implementation_guid": 54, "implementation_phas": 67, "implementation_scor": 44, "implementation_timelin": 78, "implic": [18, 19, 21, 43, 53, 69, 71, 72, 75, 76, 77, 78], "implicit": 11, "import": [2, 3, 6, 7, 8, 14, 16, 17, 18, 19, 20, 21, 29, 31, 32, 34, 36, 44, 45, 51, 52, 53, 54, 55, 56, 62, 63, 64, 65, 66, 67, 68, 69, 73, 77, 78], "improv": [1, 8, 18, 19, 21, 22, 23, 24, 26, 29, 33, 34, 35, 38, 39, 41, 43, 52, 54, 57, 60, 61, 62, 65, 67, 69, 70, 71, 72, 74, 75, 76, 77, 78], "improvement_program_manag": 74, "improvement_roadmap": 40, "in_progress": [44, 54], "inaccess": 29, "inact": 63, "inadequ": 44, "inbound_connect": 55, "inc": [34, 43, 51], "incent": 71, "incid": [1, 3, 4, 7, 8, 10, 11, 12, 13, 14, 15, 22, 23, 26, 28, 31, 32, 35, 36, 37, 39, 42, 44, 45, 48, 49, 54, 60, 61, 65, 68, 70, 71, 74, 75, 76], "incident respons": 77, "incident_classif": 40, "incident_data": 25, "incident_detail": 40, "incident_frequ": 40, "incident_id": [25, 31, 32, 40, 56], "incident_involv": 40, "incident_nam": 32, "incident_respons": [4, 40], "incident_response_polici": 40, "incident_response_servic": 67, "incident_scenario": 67, "incidentcasemapp": 56, "incidentrespons": 40, "incidentresponseengin": 25, "incidentresponseservic": [40, 67], "incidentscenario": 67, "includ": [0, 2, 3, 5, 6, 7, 8, 10, 13, 14, 16, 18, 19, 20, 21, 22, 26, 30, 31, 32, 36, 39, 41, 45, 52, 53, 57, 59, 60, 61, 62, 64, 65, 69, 70, 71, 73, 75, 77, 78], "include_access_control": 67, "include_application_boundari": 67, "include_attack_path": [5, 8], "include_baselin": 73, "include_blast_radiu": 8, "include_business_impact": 67, "include_campaign": 73, "include_communication_plan": 67, "include_containment_step": 67, "include_control": 66, "include_data_flow": 66, "include_evid": 67, "include_financial_impact": 67, "include_group": 73, "include_histor": 73, "include_identity_boundari": 67, "include_insider_threat": 78, "include_lateral_mov": 67, "include_legend": 73, "include_mitr": [18, 67], "include_monitor": 67, "include_network_seg": 67, "include_network_segment": [66, 67], "include_ongo": 73, "include_predict": 73, "include_privilege_escal": 67, "include_recommend": 67, "include_recovery_procedur": 67, "include_recovery_tim": 67, "include_relationship": 5, "include_stop": 5, "include_sub_techniqu": 73, "include_techniqu": [67, 73], "include_technology_recommend": 67, "include_tool": 67, "include_vulner": 5, "includemitr": 18, "inclus": 19, "incom": [25, 77], "incompat": 38, "incomplet": 63, "inconsist": [17, 40, 44, 64], "incorpor": [0, 3, 38, 44, 52, 65, 76], "incorrect": [13, 44], "increas": [4, 6, 17, 18, 22, 28, 29, 32, 36, 48, 64, 65, 66, 69, 74, 75, 77, 78], "increment": [21, 35, 52, 66, 73, 78], "independ": [45, 60, 63, 74, 75, 76, 77], "index": [1, 3, 4, 12, 13, 15, 17, 18, 24, 26, 31, 32, 33, 34, 39, 54, 60, 61, 62, 63, 64, 65, 68], "indexnam": [31, 34], "indic": [3, 4, 9, 17, 37, 39, 41, 56, 62, 63, 67, 70, 71, 72, 73, 74, 75, 76, 77], "indicator_typ": 56, "indirect": [76, 77, 78], "indirect_cost": 78, "individu": [2, 36, 39, 40, 57, 65, 77], "industri": [1, 26, 37, 39, 40, 42, 43, 44, 45, 46, 48, 49, 61, 68, 70, 71, 73, 74, 75, 76, 77], "industrial_control": 59, "industry_collaboration_access": [70, 74, 76], "industry_represent": 71, "ineffect": 44, "ineffici": 34, "inet": [39, 59], "infect": 43, "infer": 24, "influenc": 76, "info": [3, 13, 14, 16, 17, 18, 23, 27, 29, 31, 32, 34, 41, 51, 55, 56, 64], "infograph": 76, "inform": [0, 5, 7, 8, 10, 12, 19, 26, 27, 29, 36, 37, 39, 40, 41, 43, 44, 46, 49, 51, 53, 54, 56, 60, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77], "information_schema": 30, "information_security_control": 28, "information_security_polici": 40, "infrastructur": [3, 6, 22, 24, 26, 27, 29, 30, 36, 38, 39, 42, 43, 44, 45, 46, 48, 50, 52, 54, 55, 60, 61, 66, 67, 68, 69, 72, 74, 75, 76, 77, 78], "infrastructure_secur": 52, "ingest": [24, 68, 73, 77], "ingress": [17, 30, 31, 32], "inherit": [4, 59, 68], "ini": [49, 62, 63], "init": [14, 16, 53], "initi": [0, 1, 2, 6, 12, 14, 20, 22, 24, 26, 32, 35, 40, 43, 45, 51, 53, 56, 57, 58, 66, 67, 69, 70, 71, 72, 73, 74, 75, 76], "initial_access": [6, 57], "initial_compromis": 67, "initial_cpu": 51, "initial_memori": 51, "initial_s": [33, 34], "initial_techniqu": 67, "inject": [18, 19, 21, 37, 42, 44, 45, 49, 51, 52, 53, 62, 75, 78], "inlin": 19, "innov": [22, 50, 61, 72, 74, 75, 76, 77], "innovation_program_access": 76, "input": [5, 8, 18, 19, 21, 37, 38, 42, 43, 44, 50, 53, 62, 63], "inquiri": [42, 50], "insecur": [46, 53], "insert": [49, 51, 74, 75], "insid": [43, 61, 69, 74, 75, 76, 77, 78], "insider_threat": [10, 59], "insight": [4, 23, 24, 35, 39, 75], "inspect": 76, "inspir": [28, 39, 71, 75], "instal": [0, 1, 2, 3, 8, 14, 18, 19, 21, 26, 34, 43, 51, 52, 53, 60, 62, 63, 66, 77], "instanc": [8, 13, 14, 16, 17, 18, 19, 23, 27, 29, 30, 32, 36, 51, 63, 64, 66, 69, 73, 78], "instance_class": 15, "instance_typ": 15, "instanceid": 63, "instanceof": [18, 19], "instancetyp": 63, "instant": [4, 15, 77], "instead": [0, 8, 13], "institut": [74, 75, 76], "instruct": [1, 2, 3, 14, 16, 19, 65], "instrument": 75, "insuffici": [5, 8, 17, 18, 64, 65], "insufficient_permiss": 10, "insur": [43, 70, 71, 75, 76, 77, 78], "int": [18, 19, 25, 34, 51, 55, 56, 57], "integ": [5, 6, 8, 39, 54, 55, 56, 59], "integr": [1, 2, 6, 11, 15, 16, 18, 19, 20, 22, 26, 28, 30, 37, 41, 42, 43, 44, 45, 47, 50, 59, 69, 71, 74, 75, 76, 77], "integrate_threat_intellig": 78, "integratedtermin": 20, "integrity_viol": 39, "intel": 48, "intellectu": [38, 71, 74, 75, 76, 77], "intellig": [1, 4, 6, 11, 15, 22, 23, 25, 26, 28, 34, 37, 38, 39, 43, 45, 46, 48, 49, 50, 52, 57, 58, 60, 65, 67, 68, 69, 70, 71, 74, 75, 76], "intelligentcach": 57, "intend": [19, 21], "intens": [32, 33, 69, 77], "intent": [63, 76], "inter": 11, "interact": [0, 5, 8, 19, 22, 23, 25, 35, 36, 37, 46, 48, 50, 51, 60, 63, 65, 75, 76, 77], "interconnect": 76, "interdepend": [70, 71, 76], "interest": 77, "interfac": [13, 18, 19, 25, 28, 38, 45, 60, 66, 68, 70, 71, 72, 74, 75, 76, 77], "intern": [0, 1, 3, 6, 8, 18, 36, 42, 44, 45, 48, 68, 69, 70, 74, 75, 76, 77, 78], "internal_audit": 40, "internal_controls_compromis": 78, "internal_extern": 40, "internal_factor": 40, "internal_firewal": 40, "internal_network": 40, "internet": [20, 27, 36, 60, 69, 71, 73, 76], "internet_gatewai": [6, 8, 69], "interoper": [74, 76], "interpret": [67, 70, 73, 78], "intersect": 76, "interv": [32, 34, 51, 52, 59, 62, 66, 72, 75], "interview": [71, 77], "introduc": [19, 21, 69], "introduct": 45, "intrus": [37, 48, 75, 76], "intrusion_detect": 40, "intset": 34, "invalid": [5, 6, 7, 8, 9, 13, 18, 19, 29, 34, 36, 48, 49, 51, 63, 64], "invalid_asset": 19, "invalid_email": 63, "invalid_id": 63, "invalid_paramet": 10, "invalid_sourc": 63, "invalid_threat_actor": 10, "invalid_timefram": 9, "invalid_typ": 63, "invalidate_pattern": 34, "invalidtransitionerror": 63, "inventori": [0, 5, 8, 36, 38, 54, 59, 61, 64, 65, 67, 72, 74, 75, 76, 77], "invers": 18, "invert": 55, "invest": [22, 61, 63, 70, 72, 74, 75, 76, 77, 78], "investig": [4, 7, 11, 23, 28, 31, 32, 38, 39, 43, 45, 56, 70, 72, 74, 76], "investigate_incid": [7, 77], "investor": [71, 72], "invit": 65, "invok": 25, "involv": [44, 72, 77, 78], "io": [16, 17, 30, 31, 32, 75], "ioc": [8, 13, 23, 36, 37, 56, 61, 68, 72, 74, 77], "ioc_auto_correl": 13, "ioc_confidence_threshold": 13, "ioc_correl": 78, "ioc_retention_dai": 13, "iot": [71, 75, 76], "iot_devic": 59, "ip": [5, 13, 18, 29, 32, 33, 34, 38, 39, 40, 41, 48, 56, 63, 73, 75, 76, 77], "ip_address": [5, 18, 34, 39, 56, 59, 62, 63], "ipaddress": [5, 18], "ipdb": 20, "iptabl": [43, 64], "ipv4": [62, 63], "ir_playbook": 67, "ir_servic": 67, "is_act": [5, 18, 34, 55, 56, 59, 62, 63, 64], "is_complet": 66, "is_compromis": 55, "is_connect": 6, "is_data_accur": 40, "is_delet": 59, "is_new": 5, "is_purpose_compat": 40, "is_retention_period_exceed": 40, "is_superus": 63, "is_train": 55, "is_valid": 63, "is_valid_asset_typ": 49, "is_verifi": 59, "isaca": 77, "isanalyz": [18, 19], "isc": 77, "ism": [40, 70, 76], "iso": [22, 23, 24, 25, 26, 30, 37, 39, 42, 44, 46, 50, 54, 61, 65, 67, 70, 76], "iso27001": [24, 28, 35, 37, 39, 72], "iso27001_": 40, "iso_proba": 55, "iso_scor": 55, "isoformat": [52, 55, 56], "isol": [15, 16, 19, 23, 24, 25, 35, 37, 40, 41, 43, 51, 55, 62, 63, 64, 67, 69, 72, 74, 75, 76, 77], "isolate_affected_system": 40, "isolated_asset": 64, "isolation_forest": [25, 55], "isolation_forest_scor": 55, "isolationforest": [25, 55], "isort": [18, 20, 21, 62], "iss": 7, "issu": [1, 3, 8, 13, 14, 18, 21, 26, 28, 31, 32, 34, 38, 41, 42, 43, 44, 45, 46, 48, 49, 50, 51, 52, 53, 60, 62, 65, 70, 72, 74, 75, 76, 77], "issue_confid": 53, "issue_sever": 53, "issue_text": 53, "istio": 11, "item": [4, 5, 8, 16, 34, 36, 41, 49, 52, 53, 55, 64, 67], "iter": [2, 33, 34, 62, 63, 72], "itgc": 70, "its": [28, 51], "ivborw0kggoaaaansuheugaa": 7, "j": [2, 3, 14, 20, 23, 24, 27, 29, 35, 43, 48, 49, 51, 52, 65], "javascript": [1, 8, 20, 34, 46, 48, 51, 60, 61, 62, 64], "jbswy3dpehpk3pxp": 7, "jewel": [67, 74, 75, 76], "jinja2": 52, "jira": 32, "jira_token": 32, "jit": 4, "jitter": 34, "job": [3, 4, 5, 14, 16, 17, 24, 25, 51, 53, 62, 63, 64, 65, 66, 68, 75, 77], "job_id": [5, 29, 62], "job_nam": 33, "job_statu": 29, "joblib": [23, 55], "john": [7, 21, 65, 73], "join": [3, 6, 8, 19, 26, 47, 51, 56, 64, 65, 69, 73], "joint": [61, 69, 71, 72, 75], "jop": 75, "journalctl": [17, 41], "journei": 50, "jq": [16, 32, 49], "json": [5, 6, 7, 8, 9, 10, 13, 16, 17, 18, 20, 23, 25, 27, 29, 30, 31, 32, 33, 34, 39, 44, 46, 47, 49, 50, 51, 52, 53, 56, 57, 61, 62, 63, 65, 66, 69, 73, 77, 78], "jsonb": [12, 25, 39, 55, 59, 61], "jsondecodeerror": 47, "jsonpath": [16, 17], "jsx": [18, 19], "juli": 47, "june": [42, 46, 48], "junior": [21, 70, 74, 75, 76, 77], "jurisdict": 76, "just": [21, 28, 40, 76], "just_in_tim": 28, "justif": [4, 38, 53, 68, 71], "justifi": 4, "jwt": [3, 5, 6, 8, 13, 14, 16, 19, 20, 21, 27, 53, 61, 62, 64, 69], "jwt_algorithm": [13, 27], "jwt_audienc": 13, "jwt_expire_minut": [13, 27, 64], "jwt_issuer": 13, "jwt_refresh_expire_dai": 13, "jwt_secret_kei": [13, 14, 20, 27, 64], "k": [11, 29, 34, 62, 63], "k8": [14, 16, 17, 30, 31, 32, 52], "keep": [0, 1, 2, 4, 12, 13, 19, 20, 21, 31, 45, 51, 52, 62, 67, 69, 72, 77], "keepal": [33, 34], "keepalive_timeout": [13, 34], "kei": [11, 12, 13, 14, 16, 18, 19, 20, 23, 26, 27, 28, 29, 30, 32, 34, 36, 37, 39, 41, 42, 44, 45, 47, 49, 54, 55, 57, 59, 60, 62, 63, 64, 65, 66, 70, 72, 73, 74, 75, 76, 77], "kerbero": 75, "kerberoast": 75, "kernel": 75, "key_manag": [28, 40, 59], "key_part": 47, "keymanagementservic": 40, "keyout": 64, "keyspace_hit": [31, 32], "keyspace_miss": [31, 32], "keyword": [1, 73], "kill": [2, 20, 27, 32, 64, 73], "kind": [14, 19, 30, 33, 49, 66], "kit": 75, "kiterunn": 66, "kloc": [46, 48], "km": [28, 66], "know": [4, 38, 40], "knowledg": [3, 21, 22, 44, 65, 68, 71, 74, 75, 76, 77], "known": [10, 17, 39, 48, 66, 73, 77, 78], "kpi": [22, 24, 26, 60, 70, 71, 74, 76, 77], "kube": [16, 17, 32], "kubeconfig": [14, 16], "kubectl": [4, 11, 12, 14, 17, 30, 31, 32, 39, 41], "kubelet": 17, "kubernet": [0, 17, 23, 24, 26, 28, 29, 30, 33, 52, 60, 66, 74, 75], "kubernetes_clust": 15, "kubernetes_pod": 59, "kwarg": [18, 34], "l": [14, 16, 29, 30, 31, 32, 43, 51, 53, 63, 64], "l0": 34, "l1": [33, 34], "l1_cach": 34, "l1_max_siz": 34, "l1_ttl": 34, "l2": [33, 34], "l3": 33, "la": 51, "label": [3, 17, 21, 23, 24, 34, 51, 52, 60, 76], "labelencod": 55, "laboratori": [74, 75], "lack": [10, 44], "lambda": [29, 34, 40, 55, 62, 63, 66, 67], "lambda_funct": 59, "land": [75, 77], "landscap": [37, 42, 44, 46, 71, 72, 73, 74, 75, 76, 77], "languag": [0, 1, 3, 8, 12, 19, 45, 46, 48, 52, 53, 59, 62, 74, 76], "language_vers": [18, 52], "larg": [5, 6, 8, 12, 15, 16, 27, 33, 36, 45, 60, 62, 65, 66, 69, 73, 74, 75, 76, 77], "large_scale_process": 12, "larger": 36, "last": [17, 20, 27, 31, 32, 45, 64, 65, 71, 73], "last_act": 40, "last_analysis_tim": 6, "last_assess": 54, "last_login": [7, 59], "last_nam": [7, 59, 62, 63], "last_patch": 5, "last_seen": [5, 34], "last_sync": 56, "last_test_d": 56, "last_test_statu": 56, "last_upd": 73, "lasttimestamp": [16, 17, 32], "latenc": [15, 24, 28, 39, 64, 65, 68, 76], "later": [6, 57, 61, 67, 69, 74, 75, 77], "lateral_mov": [6, 59, 67], "latest": [2, 3, 11, 14, 16, 23, 27, 30, 31, 36, 42, 49, 50, 51, 52, 53, 55, 62, 63, 77], "latest_backup": 30, "latest_snapshot": 30, "latexpdf": 3, "launch": 20, "launder": 70, "law": [38, 40, 41, 43, 45, 49, 74, 75, 77], "lax": 13, "layer": [0, 3, 4, 11, 15, 18, 28, 42, 46, 52, 57, 60, 62, 73, 75, 76, 77], "layer_id": 73, "layer_json": 73, "layer_nam": 73, "layout": 3, "lazaru": 75, "ldap": 68, "lead": [3, 17, 18, 22, 26, 32, 43, 47, 49, 50, 67, 71, 74, 75, 76, 77], "leader": [50, 71, 75, 76, 77], "leadership": [26, 43, 50, 67, 70, 74, 75, 77], "leadership_commit": 40, "leak": [21, 32, 33, 38], "leakag": [38, 45, 76, 77], "learn": [3, 11, 19, 21, 22, 29, 35, 37, 38, 40, 41, 43, 44, 46, 50, 53, 61, 67, 69, 70, 71, 72, 73, 74, 75, 76, 77], "least": [11, 13, 15, 21, 26, 29, 34, 37, 40, 42, 44, 48, 49, 66, 68], "least_conn": [33, 34], "least_privileg": [28, 40], "least_privilege_config": 4, "ledger": [71, 76], "leef": 65, "left": [48, 52, 62, 64], "legaci": 76, "legal": [12, 38, 41, 68, 70, 71, 74, 75, 76, 77], "legal_basi": [12, 40], "legisl": 38, "legitim": [32, 38, 43, 45, 75, 77, 78], "legitimate_interest": 12, "len": [18, 19, 31, 34, 40, 44, 51, 53, 55, 56, 57, 62, 63, 66, 67, 69, 73], "length": [6, 18, 34, 54, 57, 59, 69, 75], "less": 55, "lesson": [37, 38, 40, 41, 43, 44, 67, 69, 71, 72, 74, 75, 76, 77], "lessons_learn": 40, "let": 27, "letsencrypt": 27, "level": [3, 4, 11, 12, 13, 15, 17, 19, 22, 26, 33, 36, 39, 44, 49, 50, 51, 52, 53, 55, 57, 60, 63, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78], "leverag": [6, 8, 23, 24, 25, 44, 57, 69, 70, 71, 73, 74, 75, 76, 77, 78], "liabil": 43, "liaison": 76, "lib": [16, 32], "librari": [12, 26, 35, 44, 49, 60, 76, 77], "licens": [44, 72], "life": 75, "lifecycl": [4, 5, 11, 12, 26, 37, 52, 60, 68, 71, 72, 74, 75, 76, 77], "lifetim": 64, "light": 3, "lightweight": 66, "like": [2, 19, 28, 34, 51, 69, 72, 77], "likelihood": [6, 10, 36, 38, 46, 57, 59, 61, 69, 72, 73, 75, 76, 77, 78], "likely_consequ": 12, "limit": [0, 3, 4, 12, 13, 17, 18, 20, 21, 24, 25, 26, 31, 32, 33, 34, 37, 38, 39, 40, 43, 44, 45, 48, 49, 51, 52, 60, 61, 63, 64, 65, 66, 67, 68, 69, 75, 76, 77], "line": [0, 3, 18, 22, 24, 26, 36, 51, 53, 65, 75, 77], "line_length": 18, "line_numb": 53, "linear": [15, 33, 60], "lineno": 53, "link": [0, 3, 19, 73, 75, 77], "linkag": 39, "linkcheck": 3, "linkedin": 45, "lint": [2, 18, 19, 20, 21, 49, 60], "linter": [18, 21, 53, 62], "linux": [0, 14, 16, 20, 27, 57], "liskov": 18, "list": [8, 17, 18, 19, 25, 29, 30, 31, 34, 40, 45, 48, 49, 51, 52, 53, 55, 56, 57, 64, 75, 77], "listallmybucket": 29, "listen": [33, 34], "liter": 16, "litig": [43, 70], "live": [3, 4, 11, 14, 17, 18, 19, 21, 26, 27, 28, 36, 39, 46, 63, 74, 75, 77], "livenessprob": 17, "ll": [36, 62, 68], "lo": 16, "load": [0, 2, 13, 14, 15, 17, 20, 23, 24, 25, 27, 28, 29, 31, 32, 35, 36, 38, 45, 47, 51, 53, 57, 60, 61, 62, 65, 66, 68, 69, 72, 74], "load_bal": 59, "load_balanc": 59, "load_bandit_result": 52, "load_safety_result": 52, "load_security_control": 44, "loadbalanc": [16, 23], "local": [1, 16, 19, 20, 26, 27, 30, 34, 35, 36, 45, 48, 56, 60, 63, 65, 68, 75, 77, 78], "localhost": [2, 3, 13, 14, 17, 20, 27, 29, 30, 31, 32, 33, 34, 36, 51, 52, 62, 64, 65], "localstorag": 7, "locat": [4, 33, 34, 36, 38, 39, 44, 65, 77], "lock": [6, 8, 15, 20, 51], "lockdown": 77, "lockout": [44, 51, 68], "locust": [0, 2, 62], "locustfil": [2, 62], "log": [0, 3, 4, 7, 11, 12, 14, 15, 16, 18, 19, 20, 21, 23, 24, 25, 26, 27, 30, 34, 35, 36, 38, 40, 41, 42, 44, 46, 47, 48, 50, 51, 52, 55, 56, 57, 59, 60, 61, 63, 66, 67, 68, 73, 74, 75, 76, 77], "log_backup_count": 13, "log_file_path": 13, "log_format": 13, "log_include_level": 13, "log_include_logger_nam": 13, "log_include_thread_id": 13, "log_include_timestamp": 13, "log_level": [2, 13, 14, 20, 27], "log_max_s": 13, "log_processing_act": 40, "log_sourc": 73, "log_stat": 20, "logger": [18, 19, 29, 47, 52, 55, 56, 57], "logging_system": 59, "logic": [1, 3, 8, 18, 19, 21, 24, 29, 38, 40, 45, 49, 63, 75], "login": [8, 13, 14, 16, 26, 27, 32, 39, 51, 62, 68, 77], "login_respons": 51, "login_url": 51, "logist": 75, "loglevel": [14, 23], "logout": 39, "logrot": [31, 32], "long": [6, 17, 18, 24, 31, 32, 39, 43, 44, 61, 63, 64, 68, 71, 73, 74, 75, 76, 77], "long_term": 67, "longer": 12, "look": [19, 77], "lookback_dai": 55, "lookup": 12, "loop": [53, 57, 62, 77], "loos": 60, "loss": [10, 68, 71, 72, 74, 75, 76, 77, 78], "low": [29, 32, 34, 36, 43, 44, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 59, 67, 69, 77], "lower": [51, 52, 62, 63, 69], "lowest": 13, "lru": [32, 33, 34, 57, 61], "lru_cach": 34, "lru_kei": 34, "lrucach": 57, "lsass": 67, "lsof": [2, 20, 64], "lt": 31, "m": [2, 13, 14, 16, 20, 21, 27, 30, 31, 32, 36, 63, 64, 65, 76], "m5": 15, "machin": [11, 22, 29, 35, 46, 50, 61, 71, 72, 73, 74, 75, 76, 77], "machine_learn": 40, "maco": [0, 14, 20, 27, 57], "made": 77, "magicmock": 63, "mai": [13, 18, 36, 45, 51, 65, 77], "mail": 56, "mailbox": 75, "main": [2, 3, 14, 20, 21, 34, 36, 44, 51, 53, 62, 63, 65, 69, 77], "maintain": [0, 4, 11, 12, 15, 18, 19, 21, 32, 35, 37, 38, 39, 40, 43, 44, 45, 46, 48, 50, 51, 52, 53, 54, 61, 62, 63, 66, 67, 68, 70, 71, 72, 74, 75, 76, 77], "mainten": [17, 22, 26, 28, 34, 39, 48, 51, 60, 61, 70, 72, 74, 75, 76], "maintenance_mod": 31, "maintenance_reindex": 59, "maintenance_typ": 31, "maintenance_vacuum_analyz": 59, "maintenance_window": 5, "maintenance_work_mem": [33, 34], "major": [0, 1, 21, 26, 28, 41, 43, 45, 58, 61, 70, 71, 73, 78], "make": [0, 1, 2, 3, 12, 20, 21, 26, 27, 35, 41, 43, 62, 69, 71, 74, 75, 76, 77], "makefil": 20, "maker": 76, "malform": 21, "malici": [38, 47, 51, 73, 74, 75, 77], "malicious_payload": 62, "malwar": [43, 75, 76, 77], "manag": [0, 1, 3, 6, 13, 15, 16, 22, 23, 24, 26, 28, 31, 34, 35, 37, 39, 40, 41, 42, 43, 44, 45, 47, 49, 54, 56, 59, 60, 63, 64, 65, 66, 67, 69, 74, 75, 76], "manage_backup": 68, "manage_incident_respons": 77, "manage_integr": [7, 68], "manage_rol": 68, "manage_soc_oper": 77, "manage_us": [7, 68], "management_network": 40, "management_review": 40, "mani": [0, 8, 10, 18, 19, 21, 34], "manifest": [30, 41, 51], "manipul": 75, "manner": 45, "manual": [2, 5, 16, 17, 18, 19, 21, 23, 24, 35, 41, 44, 45, 46, 48, 52, 54, 62, 64, 65, 73, 75, 77], "manual_check": 54, "manufactur": [74, 75], "map": [3, 5, 7, 9, 12, 18, 22, 23, 24, 25, 26, 28, 34, 36, 38, 49, 54, 60, 61, 65, 67, 68, 69, 70, 72, 73, 74, 75, 76, 77], "map_attack_path": 57, "map_attack_path_to_mitr": 67, "mapper": 56, "mapping_strength": 54, "mapping_typ": 54, "maritim": 75, "mark": [17, 62, 63], "marker": [62, 63], "market": [22, 38, 61, 71, 75, 76], "markup": 1, "mass": [75, 77], "massiv": [3, 22, 26], "master": [2, 51, 52, 53, 74, 75, 76, 77], "masteri": [74, 75, 76, 77], "match": [19, 34, 37, 40, 51, 62, 63, 64, 72, 73, 77], "match_field": 5, "matching_techniqu": 73, "materi": [0, 24, 42, 70, 76, 78], "material_weak": 78, "mathemat": [10, 69, 72, 76, 78], "matplotlib": 25, "matrix": [44, 53, 67, 76, 77], "matter": [19, 70, 76], "matur": [48, 70, 71, 74, 75, 76, 77], "maturity_assess": 28, "maturity_scor": 40, "max": [5, 8, 18, 34, 36, 54, 64, 69], "max_access_duration_hour": 4, "max_allocated_storag": 15, "max_attack_dur": 78, "max_attack_path_depth": [18, 64], "max_attempt": 34, "max_concurrent_request": [13, 29], "max_concurrent_sess": 13, "max_connect": [33, 34], "max_critical_age_dai": 54, "max_degre": [6, 8, 57, 61, 69], "max_depth": [18, 19, 55, 56, 62, 67], "max_duration_hour": 4, "max_escalation_duration_hour": 4, "max_fail": [33, 34], "max_fin": 78, "max_high_age_dai": 54, "max_hop": 65, "max_it": 55, "max_length": 57, "max_nb_char": [62, 63], "max_nod": 28, "max_overflow": 34, "max_path": 34, "max_path_length": [6, 8, 61, 69], "max_paths_per_target": [6, 61, 69], "max_relationship": 28, "max_request": [33, 34], "max_request_s": 13, "max_requests_jitt": 33, "max_retri": [29, 39], "max_retry_attempt": 18, "max_siz": [15, 33, 34, 57], "max_wal_s": [33, 34], "max_work": 57, "maxdegre": 8, "maxdepth": [18, 25], "maxfail": 62, "maxim": [74, 77], "maximum": [6, 13, 18, 19, 34, 69, 72, 74, 75], "maxmemori": [32, 33, 34], "maxreplica": 33, "maxsiz": 34, "md": [19, 21, 38], "md5": [47, 50, 52, 53], "mdm": 76, "me": 51, "mean": [3, 22, 23, 24, 40, 43, 46, 48, 49, 50, 52, 74, 77], "mean_tim": [17, 20, 31, 32, 33, 34, 59, 64], "mean_time_to_detect": 40, "mean_time_to_respond": 40, "meaning": [2, 19, 21], "measur": [12, 21, 26, 33, 34, 38, 40, 43, 49, 60, 65, 68, 70, 71, 72, 74, 75, 76, 77], "measures_taken": 12, "mechan": [12, 15, 25, 37, 38, 44, 49, 50, 51, 60, 75, 76, 77], "media": [43, 45, 71, 75, 76, 77, 78], "mediat": 45, "medic": 75, "medical_devic": 59, "medium": [21, 24, 29, 32, 33, 34, 43, 44, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 59, 65, 67, 69, 77, 78], "medium_vulns_count": 55, "meet": [19, 21, 36, 37, 42, 49, 62, 76, 77], "mem": [32, 33], "mem_limit": 64, "member": [1, 3, 17, 21, 22, 24, 26, 29, 32, 36, 41, 44, 49, 61, 65, 68, 71, 76, 77], "memori": [6, 13, 14, 16, 21, 27, 29, 31, 32, 33, 34, 41, 57, 60, 61, 65, 68, 69, 75, 77], "memory_gb": 5, "memory_impact": 51, "memory_info": 34, "memory_profil": 17, "memory_usag": [33, 34], "memory_usage_byt": 34, "memoryefficientcach": 34, "memorypressur": 32, "memusag": 33, "mention": 19, "mentor": [21, 22, 71, 74, 75, 76, 77], "mentor_junior_analyst": 77, "mentorship": [76, 77], "menu": [36, 62, 69], "merg": [2, 18, 21, 44, 60], "merge_request": 52, "merger": [75, 76], "merger_acquisition_assess": 76, "merkl": 39, "merkle_root": 39, "mermaid": [0, 1], "mesh": [11, 15, 76], "messag": [2, 5, 6, 7, 8, 9, 10, 13, 15, 18, 19, 36, 43, 44, 49, 52, 53, 56, 61, 64, 65, 71, 76, 77], "met": [21, 49, 61], "meta": [3, 5, 18, 62, 63], "metadata": [8, 12, 14, 16, 17, 26, 30, 31, 33, 39, 49, 52, 54, 55, 56, 60, 61, 62, 66, 69, 72], "metamorph": 75, "metavers": 71, "method": [1, 7, 8, 16, 17, 18, 19, 26, 31, 34, 45, 47, 51, 52, 54, 61, 63, 64, 75, 77], "methodologi": [3, 22, 26, 60, 70, 72], "methodology_develop": [74, 75], "metric": [4, 6, 8, 13, 14, 16, 22, 25, 26, 31, 32, 34, 36, 38, 39, 51, 55, 57, 60, 67, 68, 70, 71, 72, 74, 76, 77], "metrics_analytics_expertis": 74, "metrics_en": 13, "metrics_endpoint": 13, "metrics_include_appl": 13, "metrics_include_system": 13, "metrics_path": 33, "metrics_report": 40, "mfa": [11, 13, 25, 36, 37, 38, 40, 42, 50, 76, 77], "mfa_backup_codes_count": 13, "mfa_en": [7, 13], "mfa_method": 40, "mfa_require_for_admin": 13, "mfa_require_for_al": 13, "mfa_system": 59, "mfa_token": [7, 40], "mfa_totp_digit": 13, "mfa_totp_issu": 13, "mfa_totp_period": 13, "mfaservic": 40, "mfatoken": 7, "mgmt": 29, "micro": [37, 50, 63, 76], "micro_segment": 40, "microsegment": [11, 15, 28], "microservic": [15, 26, 59, 60, 71, 76], "microsoft": [29, 36, 40, 77], "middai": 76, "middlewar": [24, 48, 59], "migrat": [0, 14, 18, 20, 24, 25, 27, 60, 63, 65, 76], "mileston": [21, 74, 75, 76, 77], "militari": 78, "millisecond": 39, "mimicri": 75, "min": [33, 34, 54, 57, 67], "min_confid": 73, "min_duration_dai": 73, "min_fin": 78, "min_siz": 15, "min_wal_s": 34, "mine": 74, "minim": [11, 12, 21, 34, 40, 41, 42, 43, 45, 48, 49, 50, 63, 66, 69, 70, 71], "minimis": 38, "minimize_data": 40, "minimized_data": 40, "minimum": [4, 11, 13, 14, 15, 20, 27, 36, 62, 63, 65], "minimum_complet": 54, "minor": [19, 21, 41, 43, 45, 53, 77], "minreplica": 33, "minut": [5, 6, 8, 13, 23, 24, 32, 33, 34, 36, 40, 43, 55, 60, 61, 62, 64, 65, 66, 76, 77], "mirror": 18, "misconfigur": [45, 51, 66, 74, 75, 76, 77], "misp": 78, "miss": [0, 13, 18, 25, 29, 31, 32, 44, 51, 57, 62, 63, 67, 77], "missing_var": 17, "mission": [38, 47, 60], "mistak": 44, "mit": 26, "mitig": [6, 8, 12, 15, 36, 41, 44, 48, 49, 50, 57, 58, 61, 67, 69, 70, 71, 72, 74, 75, 76, 77], "mitigation_cost": [6, 59], "mitigation_strategi": [6, 59, 69], "mitr": [3, 18, 21, 22, 24, 25, 26, 33, 34, 35, 42, 50, 60, 63, 65, 69, 74, 75, 76, 77, 78], "mitre att&ck": 26, "mitre_attack": 73, "mitre_correlations_per_second": 33, "mitre_coverag": 40, "mitre_data": [33, 57], "mitre_map": 57, "mitre_servic": [40, 63, 67], "mitre_techniqu": [18, 34, 67, 78], "mitreattackmap": 57, "mitreattackservic": 40, "mitredataerror": 63, "mitreservic": 63, "mkdir": [27, 30, 51, 52, 53, 64], "ml": [22, 23, 25, 28, 35, 39, 77], "ml_model": 55, "ml_model_metadata": 55, "mlmodelmetadata": 55, "mlpclassifi": [25, 55], "mm": [41, 44], "mobil": [3, 9, 28, 38, 45, 72, 73, 74, 75, 76, 77], "mobile_count": 73, "mobile_devic": 59, "mock": [8, 18, 19, 62], "mock_boto_cli": 63, "mock_ec2": 63, "mock_get": 63, "mock_graph_servic": [19, 63], "mock_sess": 63, "mockup": 19, "mode": [20, 31], "model": [3, 6, 7, 18, 19, 20, 23, 26, 35, 37, 39, 44, 50, 57, 58, 62, 63, 67, 70, 71, 74, 75, 76, 77], "model_nam": 55, "model_scor": 55, "model_select": 55, "model_typ": 55, "model_vers": 55, "moder": [13, 29, 36, 43, 48, 69, 77], "modern": [26, 36, 65, 75, 76], "modif": [18, 19, 44, 49, 65, 68, 74, 75, 76, 77], "modifi": [3, 4, 7, 16, 18, 21, 29, 45, 57, 65, 68, 77], "modify_incident_statu": 77, "modul": [15, 23, 24, 26, 28, 35, 39, 40, 47, 62, 73], "modular": [0, 15, 60], "module_nam": 63, "mondai": 76, "monei": 70, "monetari": [10, 72, 78], "monitor": [0, 1, 6, 7, 9, 14, 19, 20, 21, 22, 23, 24, 26, 27, 28, 31, 35, 42, 43, 44, 46, 47, 48, 49, 51, 57, 61, 62, 64, 65, 67, 69, 71, 72, 73, 74, 75, 76], "monitor_detect": 67, "monitor_for_reoccurr": 40, "monitor_perform": 34, "monitoring_coverag": 40, "monitoring_frequ": 78, "monitoring_measur": 40, "monitoring_scor": 44, "monitoring_system": 59, "mont": [22, 76], "month": [24, 25, 30, 44, 50, 61, 67, 76], "monthli": [30, 38, 39, 40, 41, 43, 44, 46, 48, 49, 76, 77], "more": [0, 4, 7, 10, 18, 19, 34, 36, 37, 54, 59, 71, 74, 75, 76, 77], "morn": [76, 77], "most": [3, 22, 26, 34, 42, 50, 60, 61, 64, 65, 67, 76, 77], "motiv": [19, 75, 76, 78], "mount": 17, "move": [34, 75], "movement": [6, 61, 67, 69, 74, 75, 77], "msp": 75, "mtime": [31, 32], "mtl": 11, "mtta": [48, 77], "mttc": [43, 77], "mttd": [3, 22, 43, 46, 48, 50, 74, 77], "mttr": [3, 22, 43, 46, 48, 50, 74, 77], "mttrec": 77, "much": 34, "multi": [0, 3, 4, 6, 11, 13, 14, 15, 21, 22, 23, 24, 26, 28, 33, 37, 38, 40, 42, 44, 46, 47, 48, 49, 52, 53, 54, 59, 60, 61, 62, 67, 68, 69, 70, 72, 73, 74, 75, 76, 77], "multi_az": 15, "multi_cloud_architecture_design": 76, "multi_factor": 28, "multi_factor_authent": 40, "multi_line_output": 18, "multiclouddiscoveryorchestr": 66, "multilevelcach": 34, "multin": [75, 76], "multipl": [0, 1, 3, 4, 5, 9, 11, 12, 15, 23, 28, 29, 34, 37, 39, 42, 48, 51, 53, 55, 57, 62, 63, 66, 67, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78], "multivari": 74, "must": [2, 8, 18, 21, 49, 65], "mustrunasnonroot": 49, "mutual": [45, 75], "mv": 16, "my": [12, 29], "mypi": [18, 21, 49], "myst": 3, "n": [4, 11, 12, 14, 16, 17, 30, 31, 32, 34, 39, 41, 55, 56, 62, 63, 64], "n_dead_tup": 31, "n_estim": 55, "n_live_tup": 31, "n_tup_del": 32, "n_tup_in": 32, "n_tup_upd": 32, "nac": 76, "name": [3, 5, 6, 8, 11, 14, 16, 17, 18, 19, 20, 25, 27, 29, 30, 31, 32, 33, 34, 36, 40, 44, 45, 49, 51, 52, 53, 54, 56, 57, 59, 62, 63, 64, 65, 66, 67, 68, 69, 73, 78], "namespac": [14, 30, 66], "nano": [20, 27], "narr": 76, "nat": 28, "nation": [74, 75, 77], "nativ": [24, 25, 26, 60, 61, 74, 76, 77, 78], "natur": [74, 77], "navig": [0, 1, 8, 14, 20, 32, 36, 62, 65, 69, 72, 77], "nc": [11, 16, 17], "ndarrai": 55, "necessari": [4, 12, 16, 32, 34, 38, 43, 77], "need": [0, 1, 3, 4, 6, 8, 12, 14, 16, 17, 19, 20, 21, 27, 31, 32, 40, 41, 43, 45, 66, 67, 68, 69, 72, 74, 75, 76, 77], "neg": [43, 74, 75, 77], "negoti": [74, 76], "neighbor": 57, "neo4j": [0, 13, 15, 20, 27, 33, 36, 64, 65], "neo4j_connection_acquisition_timeout": 13, "neo4j_databas": 13, "neo4j_max_connection_lifetim": 13, "neo4j_max_connection_pool_s": 13, "neo4j_password": [13, 20, 27], "neo4j_uri": [13, 20, 27], "neo4j_us": [13, 20, 27], "nessu": [46, 48], "nest": 18, "net": 23, "netstat": [27, 36, 64, 65], "network": [0, 3, 4, 5, 6, 13, 14, 18, 20, 22, 23, 24, 26, 27, 28, 33, 34, 35, 36, 37, 38, 40, 41, 42, 43, 44, 45, 46, 48, 49, 50, 51, 55, 57, 63, 65, 67, 68, 69, 72, 73, 74, 75, 76, 77], "network_access": 6, "network_access_control": 40, "network_centr": 55, "network_devic": [5, 59, 63], "network_discoveri": 66, "network_discovery_retri": 64, "network_discovery_timeout": 64, "network_featur": 55, "network_id": 40, "network_lat": 33, "network_secur": 40, "network_segment": [40, 59], "network_traff": 40, "networkdiscoveryservic": 66, "networkgraph": 25, "networkgraphprop": 25, "networkpolici": [11, 30], "networkx": [23, 25, 29, 34, 57, 60, 61], "networkxnopath": 34, "neural": [23, 24, 35, 55], "neural_network": [25, 55], "neural_network_scor": 55, "never": [8, 11, 15, 37, 40, 42], "new": [5, 7, 8, 11, 14, 16, 17, 19, 21, 22, 27, 28, 30, 31, 32, 35, 36, 41, 44, 48, 49, 51, 52, 53, 56, 59, 62, 63, 64, 66, 68, 69, 71, 72, 76, 77, 78], "new_assess": 51, "new_asset": 5, "new_asset_discov": 66, "new_attack_path": 78, "new_cert_arn": 31, "new_certificate_arn": 17, "new_endpoint": 30, "new_featur": 20, "new_instance_id": 30, "new_likelihood": 57, "new_password": 7, "new_statu": 56, "new_valu": 39, "new_vers": 31, "newasset": 5, "newfeaturecr": 20, "newfeaturerespons": 20, "newkei": 64, "newli": 69, "newpassword123": 64, "newpassword456": 7, "newsecurepassword123": 65, "newserv": 5, "next": [8, 26, 32, 41, 43, 45, 47, 50, 57, 73, 74, 75, 76, 77], "next_assessment_du": 54, "next_cursor": 8, "next_gener": 40, "next_level": 57, "nf": 32, "nfv": 76, "ng": 31, "ngfw": 76, "nginx": [3, 34], "nist": [22, 23, 24, 25, 26, 35, 37, 39, 42, 50, 61, 70, 74, 76, 77], "nist_csf": [28, 39, 54], "nitpick": 19, "nmap": [0, 5, 64], "nmap_opt": 66, "nn_proba": 55, "no_map": 56, "no_modif": 4, "node": [2, 6, 14, 15, 16, 17, 20, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 41, 48, 53, 57, 60, 61, 64, 65, 69, 77], "node_group": [15, 31], "node_modul": 20, "node_typ": 15, "nodegroup": 31, "nodeid": 25, "nodej": 14, "nodeselector": 17, "nodesourc": 14, "non": [14, 18, 19, 21, 31, 33, 41, 44, 48, 51, 60, 63, 67, 77], "non_compli": 54, "nonconformity_corrective_act": 40, "none": [7, 13, 18, 19, 34, 47, 48, 51, 54, 55, 56, 57, 62, 63, 67], "noout": [11, 31, 64], "normal": [24, 43, 55, 75, 77], "normal_data": 55, "northeurop": 29, "nosec": 53, "not_applic": 54, "notaft": 31, "note": [3, 7, 8, 18, 19, 21, 42, 63, 65, 68, 72, 77], "notic": [36, 38, 45, 65, 70], "notif": [4, 8, 12, 17, 23, 28, 29, 32, 36, 42, 43, 46, 49, 60, 65, 66, 68, 70, 71, 74, 75, 76, 77, 78], "notifi": [12, 17, 32, 77], "notification_timelin": 78, "notimplementederror": 63, "now": [1, 13, 18, 21, 26, 27, 28, 31, 32, 34, 36, 39, 40, 44, 52, 54, 55, 56, 66], "np": 55, "npm": [2, 8, 14, 18, 20, 27, 48], "nr": [31, 32], "nslookup": [17, 41], "nta": 76, "nuclear": 75, "nuclei": 51, "null": [6, 12, 13, 17, 18, 19, 31, 39, 54, 59], "nullabl": [18, 25, 54, 55, 56], "nullif": [31, 59], "num_cache_nod": 15, "number": [5, 8, 13, 18, 21, 34, 44, 49, 65, 69, 74, 75, 77, 78], "number_of_compon": 6, "numer": [18, 63], "numpi": [23, 25, 55], "nx": [34, 57], "o": [14, 16, 17, 19, 20, 29, 30, 31, 32, 33, 34, 44, 46, 49, 51, 52, 53, 62, 64, 66, 78], "oauth": [31, 32], "obfusc": 75, "object": [6, 8, 12, 15, 48, 54, 56, 57, 59, 61, 68, 69, 71, 72, 74, 75, 76], "objectview": 29, "oblig": [76, 77], "observ": [15, 23, 25, 28, 34, 41, 43, 45, 51, 56, 60, 77], "observable_data": 56, "obsolet": [62, 69], "obtain": [7, 8], "obviou": 21, "occur": 38, "occurr": 77, "oci": 29, "ocr": 78, "ocr_reporting_requir": 78, "ocsp": 11, "octav": 76, "octet": 63, "off": [34, 69, 75, 76, 77], "offens": [22, 26, 72, 74], "offensive secur": 75, "offer": 53, "offic": [3, 22, 26, 43, 49, 71, 76, 77], "offici": [8, 9, 60, 61, 73], "offlin": 65, "often": [34, 62], "oid": 64, "oidc": [7, 45, 77], "oil": 75, "ok": [7, 8], "okta": 40, "old": [31, 32, 59], "old_valu": 39, "oldest": 34, "oldest_kei": 34, "oldpassword123": 7, "omit": 63, "on_start": 62, "onanalysiscomplet": [18, 19], "onboard": [0, 3, 18, 49, 72], "one": [18, 21, 27, 51, 62, 67], "onerror": [51, 62], "ongo": [1, 10, 12, 18, 34, 40, 43, 50, 66, 68, 70, 72, 73, 74, 75, 76, 77], "onli": [2, 3, 4, 11, 12, 13, 20, 21, 26, 29, 34, 38, 44, 45, 51, 52, 53, 55, 62, 64, 65, 75, 77, 78], "onlin": [40, 76, 77], "onload": 51, "onnodeclick": 25, "ons": 31, "oomkil": 17, "op": [18, 23, 54], "open": [3, 5, 18, 19, 22, 31, 36, 44, 45, 50, 51, 52, 53, 56, 62, 63, 64, 65, 74, 75, 77], "openapi": [0, 13, 15, 19, 25, 26, 60, 61, 65, 66], "openapi_url": 13, "openssl": [11, 16, 17, 31, 64], "oper": [1, 6, 8, 16, 17, 18, 20, 22, 23, 24, 27, 29, 30, 32, 33, 35, 36, 37, 40, 41, 42, 43, 48, 49, 56, 57, 61, 64, 65, 69, 70, 71, 76], "operating_system": [5, 59], "operation": 76, "operational_plan": 40, "opportun": [3, 19, 22, 26, 45, 67, 68, 71, 74, 75, 76, 77], "opsec": 75, "opsgeni": 77, "opt": [27, 38, 66], "optim": [0, 1, 3, 8, 14, 16, 17, 18, 22, 25, 26, 27, 28, 35, 39, 40, 43, 48, 60, 61, 62, 64, 65, 70, 71, 72, 75, 76, 77], "optimizedattackpathfind": 34, "option": [1, 3, 5, 6, 7, 13, 14, 18, 20, 21, 34, 51, 53, 55, 56, 57, 60, 61, 62, 63, 64, 73, 78], "oracl": 29, "orchestr": [0, 22, 24, 29, 37, 44, 48, 50, 60, 66, 70, 74, 75, 76], "order": [5, 13, 17, 20, 31, 32, 33, 34, 38, 39, 54, 59, 64], "order_bi": 55, "org": [14, 38], "organ": [3, 4, 8, 12, 22, 26, 34, 40, 43, 50, 54, 56, 63, 66, 67, 68, 69, 70, 71, 73, 74, 75, 76, 77], "organiz": [3, 22, 24, 38, 54, 68, 70, 71, 74, 75, 76, 77], "organization_ev": 73, "organization_profil": 40, "organization_wid": 40, "organizational_transformation_leadership": 74, "organizeimport": [18, 20], "orient": [0, 19], "origin": [2, 13, 19, 21, 23, 24, 25, 38, 64, 65, 77, 78], "original_purpos": 40, "orm": [18, 54, 56, 62], "orphan": 54, "os_detect": [5, 66], "osint": [74, 75], "ot": [74, 75, 76], "other": [3, 19, 21, 28, 38, 45, 51, 54, 56, 63, 65, 67, 70, 73, 74, 77], "our": [2, 3, 19, 21, 22, 26, 27, 37, 42, 43, 44, 45, 46, 48, 50, 51, 52, 53, 63, 65, 72], "out": [2, 7, 14, 16, 19, 23, 30, 38, 49, 63, 64, 65], "outag": [41, 43], "outbound": 59, "outbound_connect": 55, "outbreak": 77, "outcom": [75, 76], "outdat": [3, 29, 31], "outlier": 77, "outlin": [35, 41, 42, 43, 45, 47, 61], "outlook": 76, "outofdisk": 32, "output": [16, 30, 31, 37, 39, 42, 43, 44, 46, 49, 51, 52, 53, 62, 63, 64, 77], "outsid": 77, "outsourc": [71, 76], "outstand": 19, "outward": 11, "over": [5, 9, 17, 18, 34, 50, 53, 69, 70, 71, 73, 74, 75, 76, 77, 78], "overal": [41, 43, 44, 46, 47, 48, 50, 52, 53, 62, 63, 64, 70, 71, 74, 75, 76, 77, 78], "overall_matur": 40, "overall_scor": 54, "overall_statu": 54, "overdu": 4, "overdue_notification_dai": 4, "overflow": 65, "overhead": 33, "overlap": [67, 74], "overnight": [76, 77], "overse": [70, 71, 76], "oversight": [22, 26, 70, 71, 72, 75, 76, 77], "overview": [3, 19, 47, 71, 76, 77], "owasp": [16, 37, 40, 42, 45, 46, 48, 50, 51, 52], "own": 19, "owner": [5, 21, 41, 54], "ownership": [38, 68], "oxlei": [70, 78], "p": [2, 14, 16, 17, 20, 27, 30, 31, 32, 33, 34, 36, 51, 52, 53, 64, 65], "p0": 61, "p1": [43, 61, 77], "p2": [32, 43, 77], "p3": [32, 43, 77], "p4": [43, 77], "p95": 33, "pace": 75, "packag": [20, 21, 23, 25, 30, 31, 48, 52, 67], "packet": 76, "packet_loss": 33, "page": [1, 3, 5, 8, 19, 22, 26, 31, 32, 36, 41, 64], "page_id": [31, 32], "pagecach": [33, 34], "pagerduti": [17, 41, 77], "pagin": [5, 26, 29], "pair": 77, "pam": [40, 75, 76], "pan": 36, "panda": [23, 25, 34, 55], "panel": 33, "pap": 56, "paragraph": 77, "parallel": [33, 60, 61, 63, 66, 67, 73, 78], "parallel_work": 66, "parallelanalyz": 57, "param": [18, 25, 52, 62], "paramet": [6, 8, 49, 54, 65, 68, 69, 78], "parameter": 18, "parent_control": 54, "parent_control_id": 54, "parent_process": 73, "pars": [13, 30, 68, 72], "parse_invalid_mitre_data": 63, "parser": 3, "parti": [8, 38, 40, 41, 43, 44, 45, 49, 50, 60, 69, 70, 74, 75, 76, 77], "partial": [44, 54, 67], "partially_compli": 54, "particip": [19, 22, 43, 50, 60, 68, 71, 72, 74, 75, 76, 77], "partner": [43, 71, 74, 75, 76, 77], "partnership": [71, 74, 75, 76], "pascalcas": 18, "pass": [2, 18, 19, 20, 21, 25, 27, 31, 34, 47, 50, 52, 63, 75], "pass_threshold": 54, "passiv": [66, 72], "passive_asset": 66, "passive_discoveri": 66, "passwd": 51, "password": [8, 12, 13, 14, 16, 20, 27, 30, 38, 40, 44, 51, 52, 53, 62, 64, 66, 68, 75], "password123": 51, "password_complexity_requir": 40, "password_field": 51, "password_hash": 59, "password_hash_algorithm": 13, "password_hash_round": 13, "password_rotation_polici": 40, "passwordless": 76, "past": 0, "pasta": 76, "patch": [8, 14, 16, 17, 19, 21, 30, 32, 41, 42, 43, 46, 56, 66, 75, 76, 77], "patch_manag": 59, "patch_vulner": 40, "patent": 75, "path": [1, 3, 7, 13, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 33, 34, 45, 51, 52, 53, 60, 62, 63, 64, 66, 68, 74, 76, 78], "path_001": [6, 8], "path_001_web_server_001_database_001": 6, "path_cach": 57, "path_edg": [57, 59], "path_id": [6, 8, 59, 61], "path_length": [6, 59], "path_nod": [6, 57, 59, 69], "path_techniqu": 67, "path_typ": [6, 59], "path_type_enum": 59, "pathfind": [33, 60], "paths_found": 21, "paths_respons": 8, "pathwai": [3, 22, 26, 70, 74, 75, 76, 77], "patient": [19, 45, 75, 77, 78], "patient_notification_requir": 78, "patient_record": 78, "pattern": [0, 1, 4, 7, 8, 11, 18, 19, 24, 25, 28, 31, 32, 34, 39, 44, 52, 53, 60, 62, 68, 70, 72, 74, 75, 76, 77], "payload": [8, 21, 51, 52, 53, 62, 75], "payment": [39, 40, 61, 70, 75, 76, 77], "payment_data": 49, "payment_system": 78, "pci": [0, 22, 26, 35, 39, 49, 61, 66, 70, 72, 76], "pci dss": 70, "pci_dss": [66, 67], "pci_report": [66, 67], "pci_scop": 67, "pd": [34, 55], "pdb": [2, 20, 63], "pdf": [3, 36, 39, 66, 77], "peaceiri": 3, "peak": 69, "peer": [3, 60, 70, 71, 75, 76, 77], "pem": [11, 13, 27, 64], "penalti": [10, 57, 70, 71, 76, 77, 78], "pend": [4, 54], "penetr": [1, 2, 3, 14, 16, 37, 38, 42, 44, 50, 51, 53, 60, 61, 65, 67, 68, 70, 72, 74, 75, 76], "penetration test": 75, "peopl": 19, "pep": [18, 19], "per": [5, 6, 8, 9, 10, 13, 15, 16, 19, 33, 34, 38, 39, 46, 48, 60, 61, 69, 74, 75, 77], "percent": [17, 51], "percentag": [44, 46, 53, 63, 74, 75, 77], "percentil": 33, "perf": 21, "perfect": 35, "perform": [1, 8, 9, 10, 18, 19, 21, 22, 23, 25, 37, 41, 43, 44, 45, 52, 53, 55, 56, 70, 72, 74, 76, 77], "perform_analysi": 18, "performance_evalu": 40, "performance_metr": [6, 55], "performance_regress": 18, "performance_target": 28, "perimet": [40, 67, 76], "perimeter_firewal": 40, "period": [4, 6, 11, 16, 28, 34, 38, 45, 65, 68, 69, 76], "perman": 5, "permiss": [3, 4, 5, 8, 10, 11, 13, 15, 16, 17, 18, 21, 28, 29, 32, 38, 39, 40, 44, 45, 48, 60, 61, 65, 66, 69, 70, 71, 72, 73, 74, 75, 76, 78], "permission_deni": 5, "permissionerror": 18, "permissionservic": 40, "persist": [6, 16, 22, 29, 30, 33, 34, 63, 69, 74, 75, 76, 77, 78], "persistence_level": 78, "persistentvolumeclaim": 49, "persistentvolumeclaimnam": 30, "person": [37, 43, 45, 60, 75, 77], "persona": [72, 75], "personal_data": 78, "personal_identifi": 12, "personaldata": 40, "personnel": [38, 71, 72, 74, 75, 77], "perspect": [19, 76], "persuas": 76, "pfsens": 40, "pg_databas": 64, "pg_database_s": [31, 32], "pg_dump": [20, 30, 65], "pg_isreadi": [16, 17, 32, 51, 62, 64], "pg_reload_conf": [20, 64], "pg_restor": 30, "pg_rotate_logfil": 31, "pg_size_pretti": [31, 32, 59], "pg_stat_act": [31, 32, 33, 34, 36], "pg_stat_databas": 64, "pg_stat_stat": [17, 20, 31, 32, 33, 34, 59, 64], "pg_stat_statements_reset": 31, "pg_stat_user_index": [31, 34], "pg_stat_user_t": [31, 32], "pg_tabl": [31, 59], "pg_terminate_backend": 32, "pg_total_relation_s": [31, 59], "pgadmin": [2, 20], "pgp": [42, 45], "pgpassword": 16, "pgrep": 33, "phantom": 40, "pharmaceut": 75, "phase": [21, 22, 31, 33, 40, 45, 72, 74, 75, 76, 77], "phi": [70, 76], "phi_records_affect": 78, "philosophi": [], "phish": [40, 45, 74, 75, 76, 77, 78], "phishing_click_r": 40, "phishing_resili": 40, "phishing_result": 40, "phone": [40, 41, 75, 77], "phone_system": 59, "phoni": [51, 53], "physic": [38, 45, 48, 54, 74, 75, 76, 77], "physical_security_test": 75, "pia": [12, 28, 70], "pickl": [50, 53], "pictur": 77, "pid": [2, 20, 27, 31, 32, 33], "pii": [38, 44, 47, 49, 69, 75, 78], "pilot": [11, 23, 76, 77], "pin": [11, 28], "ping": [14, 27, 41, 62, 64], "pinnacl": [3, 22], "pip": [2, 3, 8, 14, 18, 20, 29, 31, 34, 51, 52, 53, 62, 63], "pipelin": [0, 15, 18, 21, 23, 24, 26, 28, 35, 46, 47, 48, 49, 53, 60, 75, 76, 77], "pki": [75, 76], "place": [38, 44, 49], "placement": [61, 72, 76], "plai": 45, "plain": [12, 34], "plan": [0, 14, 15, 16, 21, 22, 24, 25, 30, 32, 38, 39, 40, 41, 43, 44, 45, 46, 48, 49, 53, 54, 61, 65, 68, 69, 70, 71, 72, 74, 75, 76, 77, 78], "plane": 31, "planned_valu": 49, "planner": 34, "platform": [0, 1, 3, 5, 8, 13, 19, 22, 23, 24, 27, 28, 29, 38, 41, 45, 50, 56, 57, 60, 67, 72, 73], "playbook": [40, 61, 67, 72, 76, 77], "playbook_step": 67, "plc": 75, "pleas": [3, 18, 19, 26, 37, 42, 45, 65], "plpgsql": 59, "plugin": [48, 60], "plural": 18, "pluralsight": 77, "png": 7, "po": 75, "pod": [11, 12, 14, 15, 30, 31, 32, 39, 41, 49, 66], "podsecuritypolici": 49, "point": [15, 18, 19, 20, 23, 29, 35, 44, 49, 60, 67, 68, 69, 72, 75, 76, 77], "poison": 75, "polici": [7, 14, 15, 26, 28, 29, 32, 33, 34, 37, 39, 40, 41, 43, 44, 54, 59, 60, 64, 65, 68, 70, 71, 72, 74, 75, 76, 77], "policy_development_author": 70, "policy_docu": 54, "policy_review": 54, "poll": [8, 13], "poly1305": 11, "polymorph": 75, "pool": [0, 11, 13, 21, 33, 34, 39, 68], "pool_pre_p": 34, "pool_recycl": 34, "pool_siz": 34, "pool_timeout": 34, "poolclass": 34, "poor": 44, "pop": [34, 57], "popul": 69, "popular": [0, 3, 8], "port": [3, 5, 13, 14, 16, 17, 20, 23, 27, 32, 36, 51, 52, 53, 59, 64, 65, 66], "port_scan": [5, 66], "portabl": [12, 70, 76, 78], "portain": 2, "portal": [38, 42, 72, 77], "portfolio": [71, 74, 76], "posit": [3, 22, 23, 32, 40, 43, 44, 46, 47, 48, 50, 51, 52, 67, 71, 73, 74, 75, 76, 77], "possibl": [6, 29, 43, 48, 52, 69, 73], "post": [4, 5, 6, 7, 8, 11, 12, 13, 14, 18, 20, 26, 29, 31, 32, 33, 34, 39, 45, 49, 51, 55, 56, 61, 62, 65, 69, 70, 71, 73, 75, 76, 77, 78], "postgr": [14, 30, 31, 33, 36, 51, 62, 64, 65], "postgres_password": [51, 62], "postgresql": [0, 2, 13, 14, 15, 16, 20, 23, 24, 25, 27, 28, 29, 33, 54, 59, 61, 64, 65], "postman": [2, 8], "postur": [23, 24, 35, 36, 40, 43, 44, 47, 48, 50, 51, 52, 53, 61, 65, 67, 69, 71, 72, 74, 75, 76, 77, 78], "potenti": [0, 10, 18, 36, 38, 41, 43, 45, 49, 52, 53, 56, 65, 67, 69, 70, 72, 75, 76, 77, 78], "potential_penalti": 78, "power": [9, 22, 24, 25, 26, 35, 36, 40, 46, 50, 61, 63, 65, 69, 70, 72, 73, 74, 75, 76, 77], "powerpoint": 77, "powershel": [23, 24, 35, 73, 75, 78], "powersourc": 78, "pr": [19, 21, 48, 54], "practic": [3, 18, 19, 22, 26, 27, 36, 37, 38, 40, 41, 42, 43, 45, 46, 47, 48, 49, 50, 60, 70, 71, 74], "practition": [74, 75], "pragma": 63, "prd": [3, 25, 60], "pre": [0, 18, 20, 21, 31, 44, 47, 48, 62, 65, 69, 72, 73, 75, 77], "precis": [55, 74], "precomput": 34, "predecessor": 57, "predefin": [4, 68, 77], "predeploy": 30, "predict": [23, 26, 28, 29, 35, 40, 47, 61, 69, 70, 72, 73, 74, 76, 77], "predict_asset_compromis": 25, "predict_proba": 55, "predict_threat": 25, "predict_threat_prob": 55, "predicted_count": 73, "prediction_d": 55, "prefer": [2, 13, 16, 18, 19, 20, 36, 45, 67, 68, 76, 77, 78], "preferred_techniqu": 78, "prefix": [18, 56], "preliminari": 77, "prematur": [18, 34], "premier": 76, "premis": [5, 36, 60, 66, 75, 76], "premium": [5, 6, 8, 9, 10, 71], "prepar": [11, 15, 21, 22, 27, 38, 40, 41, 43, 48, 50, 51, 55, 67, 69, 72, 74, 75, 76, 77], "prepared": 77, "preprocess": 55, "prerequisit": 26, "presenc": [76, 77], "presence_of_element_loc": 62, "present": [45, 65, 70, 71, 74, 75, 76, 77], "preserv": [0, 41, 43, 71, 76, 77], "preserve_evid": 40, "pressur": 33, "pretext": 75, "prettier": [18, 20], "preval": 76, "prevent": [6, 13, 18, 19, 21, 29, 37, 38, 41, 42, 43, 44, 46, 48, 49, 50, 51, 54, 60, 63, 68, 71, 74, 75, 76, 77], "previou": [16, 17, 45, 77], "previous": [10, 77], "previous_block_hash": 39, "previous_revis": 54, "price": 75, "primari": [12, 13, 15, 18, 20, 25, 39, 40, 41, 45, 54, 59, 61, 63, 73, 74, 75, 76, 78], "primarili": 68, "primary_kei": [25, 54, 55, 56], "primary_motiv": 78, "primary_techniqu": 73, "primarykeyconstraint": [18, 54], "princip": [29, 64, 68, 76], "principl": [19, 21, 29, 40, 44, 47, 48, 49, 60, 62, 68, 75, 76], "print": [3, 6, 14, 17, 18, 19, 20, 30, 31, 32, 34, 55, 63, 64, 66, 69, 73, 78], "print_stat": 34, "printer": 59, "prior": 45, "priorit": [4, 13, 22, 36, 38, 43, 45, 46, 53, 54, 61, 66, 68, 69, 70, 71, 72, 74, 75, 76, 77], "prioriti": [13, 24, 29, 36, 38, 44, 46, 48, 61, 67, 69, 72, 74, 75, 76, 77, 78], "privaci": [28, 37, 39, 40, 42, 43, 45, 46, 49, 50, 51, 61, 66, 70, 71, 75, 76], "privacy_by_design": 28, "privacy_impact_assess": 12, "privat": [7, 15, 16, 18, 19, 28, 29, 45, 76], "privateipaddress": 63, "privileg": [6, 11, 13, 15, 21, 26, 29, 36, 37, 38, 40, 42, 43, 44, 45, 46, 48, 49, 51, 53, 61, 66, 67, 68, 69, 74, 75, 76, 77], "privilege_escal": [4, 39, 59, 67], "privileged_access": 59, "privileged_access_manag": 40, "privileged_user_frequency_dai": 4, "privileges_requir": 67, "pro": 36, "proactiv": [15, 26, 37, 43, 50, 60, 61, 65, 67, 68, 69, 70, 71, 74, 75, 76, 77], "probabilist": 76, "probabl": [10, 24, 25, 26, 36, 55, 57, 61, 69, 72, 75, 76, 77, 78], "probe": 17, "problem": [0, 1, 3, 11, 12, 19, 68, 72, 74, 75, 76, 77], "procedur": [0, 1, 3, 4, 11, 14, 15, 22, 26, 28, 36, 37, 39, 40, 42, 45, 46, 49, 51, 53, 59, 60, 61, 65, 67, 68, 70, 72, 73, 74, 75, 76], "proceed": 43, "process": [0, 1, 2, 3, 4, 5, 11, 13, 15, 18, 20, 23, 24, 25, 26, 27, 28, 29, 32, 33, 34, 35, 37, 39, 40, 42, 46, 47, 48, 50, 52, 58, 60, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 73, 74, 75, 76, 77, 78], "process_asset_discoveri": 18, "process_assets_chunk": 34, "process_bandit_result": 53, "process_defin": 40, "process_execut": 73, "process_nam": 73, "process_personal_data": 40, "process_sast_result": 53, "process_security_ev": 25, "process_semgrep_result": 53, "process_webhook": 25, "processing_interval_m": 39, "processing_tim": 55, "processing_time_second": 55, "procur": [68, 76], "prod": [4, 27], "produc": 63, "product": [0, 1, 5, 7, 8, 17, 20, 21, 29, 30, 34, 36, 39, 41, 46, 50, 51, 53, 63, 65, 66, 69, 74, 75, 76, 77], "production_resourc": 15, "profess": [75, 77], "profession": [1, 19, 22, 26, 27, 36, 43, 50, 60, 61, 65, 68, 72, 74, 75], "professional develop": 22, "profici": [72, 74, 75, 76, 77], "profil": [0, 6, 8, 10, 14, 17, 18, 19, 26, 33, 40, 51, 61, 65, 67, 69, 72, 73, 74, 75, 76, 77], "profile_funct": 34, "profit": 75, "profound": [70, 71, 74, 75], "program": [1, 3, 8, 20, 22, 34, 42, 43, 46, 60, 61, 68, 70, 71, 72, 74, 75, 76, 77], "programm": 75, "programmat": [8, 77], "progress": [0, 3, 5, 9, 16, 22, 24, 26, 35, 43, 45, 48, 66, 69, 70, 71, 72, 74, 75, 76, 77], "prohibit": 45, "project": [1, 13, 16, 18, 19, 21, 24, 26, 29, 48, 49, 53, 60, 63, 66, 75, 76, 77], "project_id": 29, "project_nam": [13, 16, 20, 27], "projectkei": 53, "projectnam": 53, "projectvers": 53, "prometheu": [0, 13, 16, 17, 23, 24, 25, 30, 33, 35, 41], "prometheus_cli": [34, 51, 53], "prometheus_en": [13, 14], "prometheus_namespac": 13, "prometheus_port": 13, "promis": 18, "prompt": 36, "promptli": [19, 21, 45], "promtool": 32, "proof": [28, 38, 45, 75, 76], "prop": 18, "propag": [57, 60, 61, 69, 75, 76, 77], "propagation_prob": 57, "proper": [1, 7, 8, 13, 16, 26, 36, 39, 42, 43, 44, 49, 50, 51, 53, 69, 72, 77], "properli": [16, 44, 49, 51, 68, 75, 77], "properti": [38, 53, 59, 60, 71, 74, 75, 76, 77], "propos": [25, 41, 49, 69, 72, 76], "proposit": [60, 70, 71, 74], "proprietari": 77, "prosper": [71, 74, 75, 76], "protect": [1, 12, 15, 18, 26, 28, 30, 37, 39, 40, 42, 44, 45, 46, 48, 50, 51, 54, 59, 60, 66, 67, 68, 70, 71, 72, 74, 75, 76, 77, 78], "proto": 34, "protocol": [5, 17, 43, 48, 59, 68, 74, 75, 77], "proven": [23, 35], "provid": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 22, 25, 26, 27, 28, 29, 30, 31, 32, 33, 37, 38, 39, 40, 41, 42, 43, 45, 46, 48, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 63, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78], "provider_enum": 59, "provis": [28, 38, 43, 45, 68, 76], "proxi": [32, 51, 59], "proxy_add_x_forwarded_for": [33, 34], "proxy_buff": [33, 34], "proxy_buffer_s": [33, 34], "proxy_busy_buffers_s": [33, 34], "proxy_connect_timeout": 34, "proxy_pass": [33, 34], "proxy_read_timeout": 34, "proxy_send_timeout": 34, "proxy_set_head": [33, 34], "prune": [2, 31, 36, 57, 64], "psf": [18, 52, 62], "psm1": 25, "psp": 49, "psql": [16, 17, 20, 27, 30, 31, 32, 41, 64], "pstat": 34, "psutil": [17, 32, 34, 51], "psychologi": 75, "ptw": 20, "public": [16, 18, 19, 28, 29, 30, 31, 34, 38, 40, 43, 59, 60, 69, 71, 72, 74, 75, 76, 77, 78], "publication_d": 54, "publicli": 42, "publish": 24, "publish_dir": 3, "pull": [2, 3, 16, 20, 21, 31, 49, 52, 60, 65], "pull_request": [3, 14, 51, 53, 62, 63], "purpl": [1, 3, 22, 24, 26, 36, 61, 65, 68, 75], "purple team": [26, 74], "purple team collabor": 75, "purple_servic": 67, "purple_team_collabor": 75, "purple_team_coordin": 75, "purple_team_servic": 67, "purpleteam": 74, "purpleteamservic": 67, "purpos": [7, 12, 14, 30, 38, 40, 68, 71, 77], "pursu": [74, 75, 76, 77], "pursuit": [74, 75, 76], "purview": 40, "push": [2, 3, 19, 21, 31, 40, 51, 53, 62, 63, 77], "push_servic": 40, "pushnotificationservic": 40, "put": [5, 8, 13, 18, 31, 32], "pv": [17, 30], "pvc": [17, 30, 31], "pwa": 20, "py": [2, 3, 12, 14, 16, 19, 20, 21, 25, 31, 32, 49, 51, 52, 53, 54, 62, 63], "py311": 18, "pyc": 20, "pycqa": [18, 52, 62], "pydant": [13, 18, 24, 25, 49], "pyi": 18, "pylint": [20, 49], "pylinten": 20, "pylintrc": 49, "pyproject": [18, 21], "pyramid": [21, 62], "pytest": [0, 2, 18, 19, 20, 21, 41, 49, 51, 62], "python": [0, 1, 2, 3, 6, 8, 12, 13, 14, 16, 17, 20, 23, 24, 27, 31, 32, 34, 35, 36, 46, 48, 49, 51, 52, 53, 60, 61, 62, 63, 64, 65, 73, 78], "python3": [14, 18, 20, 27, 31, 52], "python_class": [62, 63], "python_fil": [62, 63], "python_funct": [62, 63], "pythondontwritebytecod": 34, "pythonhashse": 34, "pythonpath": 20, "pythonunbuff": 34, "q": [16, 31, 63], "q1": 50, "q4": [73, 78], "qr": [36, 65], "qr_code": 7, "qradar": 77, "qualit": 70, "qualiti": [19, 38, 45, 46, 48, 49, 53, 67, 71, 74, 75, 76, 77], "quality_g": 18, "quantif": [67, 71, 72, 75, 76, 77], "quantifi": [3, 26, 69, 72, 74, 75, 76, 77], "quantit": [8, 10, 12, 22, 26, 69, 70, 71, 72, 74, 75], "quantum": [11, 50, 71, 76], "quarterli": [38, 40, 41, 43, 44, 46, 48, 49, 50, 52, 70, 71, 73, 75, 76, 77, 78], "queri": [3, 6, 8, 11, 12, 13, 15, 16, 17, 18, 20, 24, 30, 31, 32, 33, 34, 37, 39, 52, 53, 55, 56, 59, 60, 61, 62, 64, 65, 67, 68, 77], "query_cache_s": [33, 34], "query_start": [32, 33], "question": [0, 1, 2, 3, 18, 19, 20, 45, 72, 76, 77], "queue": [4, 15, 23, 25, 57, 77], "queuepool": 34, "quick": [0, 1, 34, 63, 77], "quickest": 65, "quickli": [19, 36, 43, 63, 64], "quiet": 63, "quit": 62, "quota": [24, 25, 68], "r": [2, 3, 14, 16, 17, 18, 20, 27, 31, 34, 44, 46, 49, 51, 52, 53, 54, 62, 63, 64], "r5": 15, "r6g": 16, "ra": 38, "race": 51, "radiu": [0, 2, 4, 5, 7, 8, 9, 10, 11, 12, 13, 15, 18, 19, 20, 21, 22, 27, 28, 29, 33, 34, 35, 36, 37, 39, 40, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 53, 54, 55, 56, 59, 60, 62, 63, 64, 66, 68, 70, 71, 72, 73, 74, 75, 76, 77, 78], "radius_postgres_data": 64, "railwai": 75, "rais": [18, 19, 49, 55, 56, 63], "raise_for_statu": [7, 56], "ram": [14, 16, 20, 27, 34, 36, 65], "rand": 16, "random": [23, 24, 34, 35, 49, 50, 55], "random_forest": [25, 55], "random_forest_scor": 55, "random_page_cost": [33, 34], "random_st": 55, "randomforestclassifi": [25, 55], "randomint": 33, "rang": [9, 34, 51, 57, 78], "rank": 77, "ransom": 77, "ransomwar": [67, 74, 75, 77], "ransomware_scenario": 67, "rapid": [43, 48, 61, 77], "rapidli": [69, 74, 75, 76], "rasp": [48, 76], "rate": [3, 4, 13, 21, 22, 23, 24, 25, 26, 29, 31, 32, 33, 37, 38, 40, 43, 44, 45, 46, 48, 49, 50, 51, 52, 53, 60, 61, 62, 65, 66, 67, 68, 73, 74, 75, 76, 77, 78], "rate_limit_burst": 13, "rate_limit_en": 13, "rate_limit_exceed": 10, "rate_limit_request": 13, "rate_limit_requests_per_minut": 13, "rate_limit_storag": 13, "rate_limit_window": 13, "ratelimit": [5, 6, 8, 10], "rather": 35, "ratio": 31, "rational": [4, 53, 72, 76], "raw": [16, 49, 77], "rbac": [7, 28, 29, 37, 40, 42, 47, 49, 50, 60, 61, 76], "rbac_analysi": 66, "rbacservic": 40, "rc": 54, "rc4": 53, "rcfile": 49, "rd": [5, 14, 16, 17, 28, 29, 30, 32, 66], "rdp": [67, 75], "rds_instanc": 66, "rds_snapshot": 30, "re": [18, 36, 49, 52, 64, 69], "reach": [2, 17, 18, 19, 46, 53, 63, 64, 65, 76], "react": [18, 19, 20, 23, 24, 25, 35], "react_app_api_url": [18, 20], "react_app_api_vers": 20, "react_app_debug": 20, "react_app_enable_debug_tool": 20, "react_app_enable_mock_data": 20, "react_app_environ": 20, "reactiv": 40, "read": [1, 4, 14, 15, 18, 19, 20, 21, 24, 27, 28, 29, 33, 39, 52, 62, 65], "readabl": [18, 63], "reader": [29, 65], "readi": [1, 17, 19, 20, 21, 22, 23, 24, 30, 31, 32, 36, 43, 65, 68, 70, 71, 74, 75, 76, 77], "readinessprob": 17, "readytous": 30, "real": [0, 1, 3, 4, 8, 9, 12, 15, 19, 21, 22, 23, 24, 26, 27, 28, 29, 33, 34, 35, 37, 38, 40, 42, 46, 47, 50, 51, 56, 59, 60, 61, 65, 66, 67, 68, 69, 70, 71, 72, 74, 75, 76], "real_tim": 40, "real_time_alert": [4, 28, 39], "realist": [8, 26, 34, 51, 61, 67, 69, 72, 74, 75, 78], "realiti": 71, "realiz": 71, "reason": [18, 19, 45, 47, 51, 56], "reassess": 12, "rebas": 21, "reboot": 17, "rebuild": [2, 31, 64, 71], "recal": 55, "receipt": 45, "receiv": 38, "recent": [17, 26, 32, 34, 36, 40, 48, 55], "recept": 77, "recipi": [39, 66], "recogn": [19, 22, 26, 45], "recognit": [9, 11, 24, 25, 26, 70, 71, 72, 74, 75, 76, 77], "recommend": [1, 6, 10, 14, 16, 18, 20, 25, 26, 39, 40, 44, 45, 48, 49, 50, 51, 52, 53, 54, 60, 61, 65, 67, 69, 70, 71, 72, 74, 75, 76, 77], "reconnaiss": [74, 75, 77], "reconstruct": [43, 77], "record": [5, 16, 17, 28, 30, 34, 38, 40, 44, 49, 51, 61, 72, 75, 77], "record_request": 51, "recov": [54, 76, 77], "recover": 60, "recoveri": [10, 14, 24, 26, 28, 36, 38, 40, 49, 51, 60, 61, 67, 69, 71, 72, 74, 75, 76, 77, 78], "recovery_namespac": 30, "recovery_region": 30, "recovery_time_estim": [6, 69], "recreat": 17, "recruit": 75, "rectif": 12, "recur": 77, "recurr": [41, 43], "recycl": 34, "red": [1, 3, 22, 24, 26, 36, 37, 56, 61, 65, 68, 73, 74, 76], "red team": [26, 75], "red team blue team integr": 74, "red_team_find": 67, "red_team_report": 67, "redeploi": 17, "redesign": 21, "redhat": 20, "redi": [0, 13, 14, 15, 16, 20, 23, 24, 25, 27, 28, 31, 32, 33, 35, 36, 41, 57, 62, 64, 65], "redirect": [36, 64], "redis_cach": 33, "redis_cli": [34, 57], "redis_endpoint": 16, "redis_host": [31, 32, 41], "redis_pool_max_connect": 13, "redis_pool_s": [13, 27, 33], "redis_retry_on_timeout": 13, "redis_socket_connect_timeout": 13, "redis_socket_timeout": 13, "redis_url": [13, 14, 20, 27], "redoc": [13, 20], "redoc_url": [13, 20], "redteam": 75, "reduc": [0, 8, 17, 23, 29, 33, 47, 50, 61, 63, 64, 69, 73, 74, 77], "reduct": [3, 22, 23, 24, 35, 50, 61, 70, 71, 74, 75, 76, 77, 78], "redund": [15, 29, 37, 40, 44, 60, 66, 74, 75, 76, 77], "redux": 20, "ref": [3, 14], "refactor": [21, 63], "refer": [0, 1, 5, 18, 19, 28, 37, 44, 45, 54, 59, 60, 61, 69, 70, 72, 73, 78], "referenc": [0, 70], "referr": 45, "refin": [70, 73, 74, 75, 76], "reflect": [19, 21, 42, 45, 46], "refresh": [64, 69, 71, 76], "refresh_token": 7, "refus": [36, 64], "regard": 54, "regener": 64, "regex": 49, "region": [14, 15, 16, 28, 29, 30, 40, 63, 65, 66], "regist": 38, "registr": [24, 38, 66], "registri": [16, 56, 75, 77], "registry_kei": 56, "regress": [60, 63, 76], "regul": [12, 38, 42, 49, 70, 71, 75, 76, 77, 78], "regular": [0, 1, 3, 4, 7, 11, 12, 15, 16, 18, 21, 22, 31, 37, 38, 39, 40, 43, 44, 45, 46, 47, 48, 51, 52, 60, 61, 62, 65, 66, 67, 68, 69, 71, 72, 74, 75, 76, 77, 78], "regular_us": 51, "regularli": [13, 20, 21, 29, 53, 62, 66, 68, 72, 73, 77], "regulatori": [4, 8, 10, 12, 22, 23, 26, 38, 41, 42, 44, 45, 48, 59, 60, 61, 68, 69, 72, 74, 75, 76, 77], "regulatory_change_manag": 70, "regulatory_compliance_oversight": 71, "regulatory_context": 78, "regulatory_fin": 78, "regulatory_framework_administr": 70, "regulatory_liaison_access": 76, "regulatory_relationship_manag": 70, "regulatory_requir": [40, 78], "reindex": [32, 59], "reinfect": 77, "reinforc": 74, "reinstal": 20, "reject": [44, 49, 51, 63], "rel": [9, 10], "rel_data": 57, "relat": [0, 1, 3, 6, 7, 13, 19, 21, 38, 42, 43, 45, 54, 63, 71, 73, 76, 77], "related_techniqu": 73, "relationship": [3, 8, 15, 23, 24, 25, 26, 29, 36, 43, 54, 55, 56, 57, 60, 61, 64, 65, 67, 69, 70, 71, 72, 74, 75, 76, 77, 78], "relationship_count": 64, "relationship_typ": [5, 59], "relationship_type_enum": 59, "relationship_weight": 57, "relax": [13, 14], "releas": [3, 14, 15, 16, 19, 60, 61, 65, 66, 68], "relev": [2, 18, 39, 40, 42, 49, 56, 66, 67, 74, 75, 76, 77], "relevance_scor": 40, "relevant_actor": 40, "reliabl": [15, 21, 39, 60, 63, 68, 74, 75, 77], "relief": 32, "reload": [3, 14, 20, 21, 32, 64], "remain": [5, 6, 8, 10, 17, 18, 34], "remedi": [12, 23, 24, 29, 36, 38, 39, 40, 41, 43, 44, 45, 46, 49, 50, 53, 54, 65, 67, 68, 69, 70, 72, 74, 75, 76, 77], "remediation_r": 52, "remediation_step": 54, "rememb": [70, 71, 74, 75, 76, 77], "remember_m": 7, "remind": 4, "remot": [5, 6, 15, 19, 20, 21, 37, 38, 45, 47, 53, 75, 76], "remote_addr": [33, 34], "remote_sid": 54, "remov": [4, 5, 17, 28, 34, 38, 43, 48, 56, 62, 64, 69, 77], "render": [18, 52, 77], "renew": [4, 12, 17, 50, 75], "renewal_notice_dai": 12, "reoccurr": 77, "repair": [17, 38], "repeat": [6, 21, 69, 77, 78], "repeatedli": 17, "replac": [35, 47, 50, 52, 59, 76, 77], "replic": [15, 28, 59, 75], "replica": [14, 15, 17, 24, 28, 31, 32, 33, 39, 41], "repo": [16, 18, 52, 62], "report": [0, 1, 3, 4, 7, 8, 11, 15, 18, 19, 20, 21, 22, 23, 24, 25, 26, 28, 31, 32, 35, 36, 37, 40, 41, 44, 46, 49, 51, 60, 61, 63, 65, 67, 68, 69, 70, 71, 72, 73, 74, 76, 78], "report_respons": 8, "report_schedul": 39, "reportpath": 53, "reports_dir": 52, "repositori": [3, 9, 16, 18, 19, 26, 27, 36, 53, 60, 65, 70, 73, 75, 76, 77], "repres": [3, 22, 26, 70, 71, 74, 75, 76, 77], "represent": [56, 60, 69, 77], "reproduc": [19, 45, 64, 65], "reproduct": 75, "repudi": [44, 46, 49], "reput": [38, 70, 71, 75, 76, 77], "req": [33, 34, 64], "req_123456789": 18, "req_1234567890": 5, "req_1639234567": 8, "request": [0, 3, 5, 6, 7, 8, 9, 10, 12, 13, 15, 17, 18, 20, 21, 23, 25, 26, 31, 32, 33, 34, 37, 38, 39, 42, 44, 45, 49, 51, 52, 56, 58, 60, 61, 62, 63, 64, 65, 69, 70, 72, 75, 76], "request_count": [31, 34, 51], "request_dur": 34, "request_id": [4, 5, 8, 12, 18], "request_timeout": [13, 29], "request_typ": 12, "requests_per_second": 33, "requir": [0, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 18, 19, 28, 29, 30, 34, 36, 37, 38, 39, 40, 42, 43, 45, 46, 48, 51, 62, 64, 66, 68, 69, 70, 71, 72, 74, 75, 76, 77, 78], "require_approv": 4, "require_justif": 4, "require_permiss": 18, "required_field": 54, "required_privileg": [6, 59], "required_resourc": [6, 59], "requireddropcap": 49, "requires_approv": 4, "rerun": 69, "research": [22, 50, 70, 71, 74, 75, 76, 77], "research_innovation_access": 74, "reserv": [34, 45, 63], "reset": [2, 5, 6, 8, 10, 12, 20, 31, 32, 36, 64, 68, 77], "residu": [50, 76], "resili": [38, 54, 60, 71, 74, 75, 76, 77], "resist": [11, 50, 76], "resolut": [0, 1, 17, 32, 43, 47, 49, 60, 74, 75, 76, 77], "resolution_tim": 40, "resolv": [17, 18, 19, 21, 31, 32, 41, 45, 48, 50, 53, 56, 64], "resourc": [0, 3, 4, 11, 13, 14, 16, 18, 19, 22, 23, 24, 26, 28, 30, 31, 32, 34, 37, 38, 39, 40, 42, 43, 44, 48, 61, 64, 65, 66, 67, 68, 69, 70, 74, 75, 78], "resource_group": 29, "resource_id": [4, 39], "resource_level": 78, "resource_sensitivity_weight": 4, "resource_typ": [4, 39], "resources_alloc": 40, "respect": [19, 45, 66], "respond": [12, 19, 32, 40, 45, 50, 51, 54, 64, 69, 74, 75, 76, 77], "respons": [1, 4, 7, 10, 11, 12, 13, 14, 15, 18, 21, 22, 23, 26, 28, 31, 33, 34, 35, 36, 37, 38, 42, 46, 48, 51, 52, 56, 57, 60, 61, 62, 63, 65, 68, 78], "response1": 51, "response_act": 40, "response_data": 12, "response_model": [20, 55, 56], "response_procedur": 40, "response_team": 40, "response_tim": [31, 32, 40, 52], "response_time_avg": 31, "response_time_dai": 12, "response_time_p50": 33, "response_time_p95": 33, "response_time_p99": 33, "rest": [3, 7, 8, 11, 15, 23, 25, 26, 28, 29, 32, 37, 38, 39, 42, 44, 47, 49, 60, 61, 65, 66, 68, 78], "restart": [2, 13, 17, 20, 30, 32, 36, 64, 65], "restart_polici": 34, "restartcount": 32, "restaur": 78, "restor": [15, 16, 17, 20, 30, 38, 41, 43, 68, 71, 74, 75, 77], "restore_system": 40, "restore_tim": 30, "restrict": [4, 12, 40, 48, 49, 65, 68, 77], "restructuredtext": 3, "result": [0, 3, 7, 8, 14, 18, 19, 21, 23, 24, 31, 34, 38, 43, 44, 45, 48, 49, 52, 56, 57, 61, 62, 63, 64, 65, 68, 69, 70, 71, 74, 75, 76, 77, 78], "result_cache_s": 33, "results_fil": [51, 53], "resumpt": 43, "retail": 78, "retent": [12, 15, 16, 26, 29, 30, 37, 38, 39, 42, 49, 60, 65, 66, 68, 71, 76], "retention_period": 40, "retrain": [23, 55], "retrain_ml_model": 55, "retrain_model": 55, "retrained_at": 55, "retri": [8, 15, 29, 34, 51, 62], "retriev": [5, 6, 8, 9, 10, 18, 39, 62, 68], "retrospect": 44, "return": [6, 7, 9, 10, 18, 19, 21, 27, 34, 40, 43, 44, 47, 49, 51, 52, 53, 55, 56, 57, 59, 61, 62, 63, 64, 67, 70, 71, 74, 75, 76, 77], "return_except": 34, "return_valu": [19, 63], "reus": [19, 23, 25, 35, 62], "reusabl": [15, 23, 24, 54], "rev": [18, 30, 52, 62], "reveal": 49, "reveng": 78, "revenu": [71, 76], "revers": [4, 35, 40, 51, 54, 55, 67, 75, 76], "revert": 4, "review": [0, 1, 3, 12, 16, 18, 26, 27, 28, 29, 33, 35, 36, 37, 38, 42, 43, 45, 46, 48, 51, 52, 53, 54, 56, 60, 62, 63, 65, 66, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78], "review_frequ": 40, "review_frequency_month": 12, "review_id": [4, 44], "review_typ": [4, 44], "reviewer_id": 4, "revis": [18, 20, 41, 54, 76], "revoc": [4, 43], "revok": 4, "revolution": 26, "reward": [], "rf": [20, 30, 51], "rf_proba": 55, "rg": [29, 66], "right": [19, 28, 34, 36, 37, 42, 45, 46, 49, 61, 70, 76, 77, 78], "rigor": 2, "risk": [1, 3, 6, 11, 12, 15, 18, 22, 23, 24, 26, 28, 34, 36, 37, 38, 39, 40, 41, 44, 45, 49, 51, 52, 53, 54, 55, 56, 60, 66, 68, 72, 73, 74, 75, 77], "risk assess": [46, 48, 76], "risk manag": [70, 71], "risk_appetite_approv": 71, "risk_appetite_definit": 71, "risk_assess": [4, 40], "risk_based_authent": 40, "risk_factor": 67, "risk_level": [8, 12, 55, 73], "risk_manag": 28, "risk_management_leadership": 71, "risk_quantification_author": 71, "risk_rat": 54, "risk_reduct": 78, "risk_scor": [5, 6, 8, 18, 19, 21, 34, 44, 57, 59, 63, 66, 67, 69], "risk_score_increas": 78, "risk_strategy_develop": 71, "risk_threshold": 52, "risk_toler": 78, "risk_treat": 40, "risk_trend_30d": 55, "risk_trend_7d": 55, "risk_weight": 34, "riskdesc": 51, "riskforecastingservic": 25, "riskscor": 18, "rm": [3, 14, 20, 30, 31, 38, 51, 64], "roadmap": [1, 23, 25, 26, 40, 60, 67, 70, 71, 72, 74, 76, 77], "robust": [19, 24, 26, 28, 35, 46, 50, 52, 54, 55, 57, 59, 60, 76], "roc": 55, "roc_auc": 55, "roc_auc_scor": 55, "roe": 75, "roi": [3, 22, 26, 61, 70, 71, 72, 74, 75, 76, 77, 78], "role": [0, 7, 11, 16, 22, 25, 26, 27, 28, 29, 37, 38, 39, 40, 42, 43, 44, 45, 47, 49, 59, 60, 61, 64, 65, 67, 73, 78], "role_based_access_control": 40, "role_id": [4, 64], "role_permiss": 40, "role_templ": 4, "roles_respons": 40, "roll": [23, 68], "rollback": [4, 15, 20, 21, 26, 32, 41, 62, 68], "rollout": [14, 16, 17, 30, 31, 32, 35, 41], "root": [34, 41, 43, 71, 77], "root_caus": 40, "root_modul": 49, "rootkit": 75, "rop": 75, "rotat": [7, 11, 13, 28, 29, 32, 37, 38, 41, 51, 66, 68, 75], "round": [31, 76], "rout": [4, 11, 16, 20, 39, 59, 65, 66, 69, 72, 75, 77], "route53": [16, 17, 30], "router": [20, 23, 24, 56, 59, 66, 77], "routin": [4, 23, 43, 68, 77], "row": 59, "rp": 38, "rpc": 75, "rpo": [15, 28, 38, 68, 76], "rsa": [64, 77], "rss": 34, "rst": [1, 3], "rsync": 3, "rtd": [3, 18], "rto": [15, 28, 38, 68, 76], "rule": [4, 15, 16, 17, 18, 23, 24, 28, 35, 37, 38, 40, 44, 46, 48, 49, 51, 52, 60, 61, 64, 66, 67, 68, 73, 74, 75, 77], "rule_001": 67, "rule_002": 67, "rule_003": 67, "run": [3, 5, 6, 10, 14, 16, 17, 19, 20, 21, 27, 29, 30, 31, 32, 33, 34, 39, 41, 49, 51, 53, 55, 60, 61, 64, 66, 67, 69, 73], "run_analysi": 62, "run_batch_predict": 55, "run_batch_threat_predict": 55, "run_daily_threat_predict": 55, "run_in_executor": 57, "runasani": 49, "runasus": 49, "runbook": [14, 16, 17, 28, 31, 41], "runtim": [13, 29, 37, 46, 48, 50, 51, 76], "russia": 78, "s3": [5, 15, 16, 29, 30, 64, 66], "s3_bucket": 66, "s_client": [16, 17, 31, 64], "sa": [18, 29, 54], "saa": 75, "sabotag": [74, 75], "safari": [36, 65, 77], "safe": [37, 43, 49, 63], "safeguard": [37, 54, 75, 76, 77], "safer": 75, "safeti": [0, 2, 18, 20, 21, 31, 44, 45, 46, 48, 49, 52, 53, 62, 75, 77], "safety_result": 52, "sale": [72, 75], "sam": 51, "same": [2, 3, 5, 6], "saml": [7, 45, 77], "sampl": [14, 19, 20, 63], "sample_asset": [19, 62, 63], "sample_attack_path": 63, "san": [50, 77], "sandbox": [8, 59, 75], "sanit": [42, 44, 51, 62, 64], "sar": 44, "sarban": [70, 78], "sast": [0, 25, 37, 40, 42, 49, 50, 51, 62], "sast_scan_dur": 53, "sast_scan_duration_second": 53, "sast_scans_tot": 53, "sastresultsprocessor": 53, "satisfact": [3, 43, 61, 70, 71, 74, 76, 77], "satisfi": 50, "save": [25, 33, 34, 35, 36, 50, 63, 65, 69], "scada": 75, "scalabl": [23, 28, 34, 39, 55, 57, 59, 60, 62, 68, 74, 75, 76], "scale": [0, 1, 3, 4, 6, 11, 14, 15, 17, 23, 26, 27, 28, 31, 32, 34, 39, 41, 45, 55, 59, 60, 61, 68, 72, 73, 74, 75, 77], "scaler": 55, "scaletargetref": 33, "scan": [5, 8, 14, 16, 18, 20, 21, 29, 31, 32, 36, 37, 38, 44, 45, 50, 51, 53, 60, 62, 65, 66, 68, 70, 72, 76, 77], "scan_opt": 66, "scan_rang": 66, "scan_result": 52, "scan_subnet": 66, "scan_typ": 51, "scanner": [41, 51, 59, 62, 68], "scenario": [1, 3, 7, 8, 11, 16, 19, 22, 25, 26, 27, 33, 43, 45, 51, 57, 59, 60, 63, 70, 72, 76, 78], "scenario_1639234567": 6, "scenario_development_author": 74, "scenario_document": 74, "scenario_id": [6, 59], "scenario_nam": [6, 61, 69, 78], "scenarionam": 8, "schedul": [4, 12, 14, 15, 16, 30, 39, 44, 46, 48, 52, 55, 59, 68, 70, 75, 77], "schedule_data_delet": 40, "schedule_discoveri": 66, "schema": [3, 16, 17, 20, 23, 24, 35], "schemanam": [31, 32, 34, 59], "scheme": [3, 34], "scienc": [74, 75], "scikit": [23, 24, 25], "scope": [3, 4, 21, 26, 29, 40, 41, 43, 44, 48, 49, 62, 65, 66, 67, 69, 70, 72, 75, 77], "scope_descript": 54, "score": [3, 4, 5, 6, 9, 12, 24, 25, 26, 34, 36, 39, 40, 43, 45, 46, 50, 54, 55, 56, 60, 61, 66, 68, 72, 73, 74, 75, 77, 78], "scorecard": 76, "scr": 44, "scrape": [16, 17, 32], "scrape_config": 33, "scrape_interv": 33, "screen": [36, 64], "screenshot": [1, 19, 45], "script": [0, 2, 3, 17, 20, 26, 27, 30, 31, 32, 35, 37, 41, 45, 46, 49, 51, 52, 53, 60, 62, 73, 75, 78], "scroll": 19, "sda": 43, "sdk": [9, 10, 23, 26, 35, 60, 61, 73, 78], "sdlc": [38, 52], "sdn": 76, "sdp": 76, "seaborn": 25, "sealed_at": 39, "seamless": [0, 15, 26, 35, 53, 57, 70, 74, 75, 77], "search": [1, 5, 8, 15, 18, 19, 26, 27, 32, 34, 36, 52, 56, 57, 62, 64, 69, 77], "search_cas": 56, "searchabl": 77, "sec": [33, 34], "second": [6, 9, 11, 13, 15, 23, 24, 26, 30, 33, 34, 36, 39, 60, 61, 63, 64, 65, 69, 72, 73], "secondari": 61, "secret": [3, 7, 8, 11, 13, 14, 17, 20, 24, 27, 29, 30, 35, 38, 41, 44, 46, 47, 48, 50, 52, 53, 64, 65, 66, 75], "secret_access_kei": [29, 66], "secret_detect": 49, "secret_kei": [13, 14, 20, 27, 64], "secrets_manag": 59, "secretsmanag": 16, "secretstr": 16, "section": [0, 2, 8, 19, 26, 33, 37, 50, 60, 69, 72], "sector": [73, 75, 78], "secur": [4, 5, 6, 8, 10, 12, 19, 20, 22, 27, 33, 34, 36, 39, 48, 54, 55, 56, 57, 59, 63, 64, 67, 70, 73, 77], "secure_app_password": 16, "secure_development_lifecycl": 76, "secure_password": 8, "secure_random": 47, "securepassword123": [7, 27], "security architectur": 76, "security assess": 46, "security autom": 52, "security fix": 47, "security improv": 74, "security oper": 77, "security patch": 48, "security platform": 26, "security postur": 50, "security process": 44, "security review": [44, 47], "security summari": 50, "security test": 46, "security_admin": 4, "security_analysi": 66, "security_analyst": [4, 8], "security_approv": 52, "security_architect": 7, "security_assess": 8, "security_awar": 40, "security_control": 40, "security_controls_effect": 78, "security_critical_issu": 52, "security_devic": 59, "security_factor": 57, "security_featur": 28, "security_g": 52, "security_gate_check": 52, "security_governance_oversight": 71, "security_incid": 56, "security_investment_approv": 71, "security_monitor": [12, 40, 52], "security_object": 40, "security_polici": 40, "security_posture_chang": 66, "security_report_format": 52, "security_report_gener": 52, "security_scan": [18, 46, 49, 52, 66], "security_scan_success": 52, "security_standard_develop": 76, "security_strategy_develop": 71, "security_team_manag": 71, "security_testing_framework": 76, "security_vulnerabilities_crit": 52, "securityautofix": 52, "securitycontext": [16, 17], "securitycontrol": 40, "securitycontrolassess": 44, "securityg": 52, "securityincid": [25, 40, 56], "securitymaturityassess": 40, "securitymonitor": 52, "securityreportgener": 52, "securityreviewtrack": 44, "securityscanfailur": 52, "securitytrainingservic": 40, "sed": 21, "see": [3, 7, 9, 10, 12, 13, 16, 19, 26, 36, 37, 73, 77, 78], "seed_compliance_framework": 54, "seed_nist_csf": 54, "seen": 73, "segment": [6, 15, 36, 37, 40, 42, 44, 48, 50, 57, 65, 66, 67, 68, 69, 74, 75, 76, 77], "segmented_by_funct": 40, "segreg": [4, 18, 38], "select": [14, 16, 17, 18, 20, 26, 27, 30, 31, 32, 33, 34, 36, 41, 51, 52, 53, 59, 62, 64, 69, 70, 74, 75, 76, 78], "selector": [14, 17, 31], "selenium": [0, 62], "self": [0, 7, 18, 19, 24, 25, 34, 35, 38, 40, 44, 48, 51, 52, 53, 55, 56, 57, 61, 62, 63, 64, 76, 77], "selinux": 49, "semant": [21, 46, 48, 53], "semgrep": [0, 21, 44, 46, 48, 49, 52, 53, 62], "semi": 38, "semi_autom": 54, "semver": 21, "send": [4, 8, 17, 18, 31, 32, 65, 66], "send_discovery_ev": 66, "send_kei": 62, "send_timeout": 34, "senior": [17, 32, 38, 41, 49, 70, 74, 75, 76, 77], "sensit": [4, 11, 19, 21, 29, 37, 42, 43, 44, 45, 47, 49, 51, 61, 66, 67, 69, 75, 76, 77, 78], "sensitive_asset": 67, "sensitive_data": 12, "sensitive_personal_data": 78, "sensitive_t": 51, "sentinel": 77, "sentinelon": 77, "separ": [4, 5, 6, 12, 23, 25, 35, 38, 39, 44, 61, 69, 77], "seq_scan": 31, "seq_tup_read": 31, "sequenc": [62, 63, 69, 72, 73, 77], "seri": [24, 25, 74, 76, 77], "serial": [47, 60], "serv": [3, 14, 25, 26, 50, 63, 72], "server": [1, 3, 5, 6, 7, 8, 13, 16, 18, 19, 20, 21, 23, 27, 29, 33, 34, 36, 39, 45, 51, 53, 59, 62, 63, 65, 66, 67, 68, 69, 75], "server_default": [18, 54], "server_nam": 34, "serverless": [29, 74, 75, 76, 77], "servernam": [16, 17, 31], "servic": [0, 2, 4, 5, 8, 13, 15, 16, 18, 19, 25, 27, 28, 30, 31, 32, 34, 35, 36, 40, 41, 43, 44, 45, 46, 48, 49, 51, 60, 61, 62, 63, 64, 65, 67, 68, 69, 70, 72, 74, 75, 76, 77], "service_account_frequency_dai": 4, "service_detect": [5, 66], "service_disruption_scor": 6, "serviceaccount": [16, 29], "servicemonitor": [16, 17], "servicenow": [27, 45, 65, 66], "servicenow_api_vers": [13, 27], "servicenow_batch_s": 13, "servicenow_create_incid": 13, "servicenow_incident_categori": 13, "servicenow_incident_subcategori": 13, "servicenow_inst": [13, 27], "servicenow_password": [13, 27], "servicenow_sync_en": 13, "servicenow_sync_interv": 13, "servicenow_timeout": 13, "servicenow_usernam": [13, 27], "servicenowcmdbsync": 66, "sess_123456789": 7, "session": [7, 13, 14, 15, 18, 20, 27, 33, 34, 37, 42, 44, 45, 48, 49, 51, 52, 53, 55, 56, 57, 62, 63, 64, 68, 69, 71, 75, 76, 77], "session1": 51, "session2": 51, "session_cookie_httponli": [13, 27], "session_cookie_max_ag": 13, "session_cookie_nam": 13, "session_cookie_samesit": [13, 27], "session_cookie_secur": [13, 27], "session_id": [7, 33, 39, 51], "session_invalidate_on_logout": 13, "session_manag": 28, "session_regenerate_on_login": 13, "session_ttl": 13, "sessionloc": [14, 55, 62, 63], "sessionmak": 62, "set": [0, 1, 2, 3, 4, 7, 11, 14, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 40, 44, 49, 51, 52, 53, 55, 56, 57, 60, 62, 63, 64, 65, 66, 67, 69, 72, 74, 75, 76, 77, 78], "set_bad_file_permiss": [52, 53], "set_statu": 63, "set_trac": [2, 20, 63], "seterror": [18, 19], "setex": [34, 57], "setisanalyz": [18, 19], "setitem": 7, "setlevel": 29, "settlement": 75, "setup": [1, 3, 19, 21, 24, 25, 26, 28, 51, 53, 60, 63, 72, 75], "setup_18": 14, "setup_continuous_monitor": 78, "setuptool": 20, "sever": [5, 8, 16, 19, 21, 24, 25, 31, 32, 36, 38, 39, 40, 44, 46, 47, 48, 49, 50, 51, 52, 53, 56, 67, 74, 76, 77], "severity_bas": 40, "severity_map": 56, "severity_threshold": 56, "sh": [2, 3, 14, 16, 17, 30, 31, 32, 41, 66], "sha": [39, 47, 50, 52], "sha1": 53, "sha256": [8, 39, 47, 52], "shall": 61, "shape": 71, "shard": 33, "share": [3, 13, 20, 21, 22, 24, 38, 41, 44, 49, 61, 63, 65, 67, 68, 69, 70, 71, 72, 74, 75, 76, 77], "shared_blks_hit": 59, "shared_blks_read": 59, "shared_buff": [33, 34, 64], "sharepoint": 75, "shell": [0, 27, 52, 64, 75], "shield": 40, "shift": [15, 22, 48, 52, 62, 77], "ship": 75, "short": [24, 30, 31, 43, 44, 51, 62, 63, 77], "short_term": 67, "shortcut": 77, "shorter": 69, "shortest": [34, 67, 69, 77], "shortest_path": 34, "should": [2, 19, 20, 21, 36, 42, 51, 62, 63], "show": [3, 14, 16, 17, 36, 49, 58, 63, 64, 66, 77], "showcas": 76, "si": 75, "side": [8, 75], "side_by_sid": 73, "side_effect": 63, "siem": [0, 8, 28, 35, 38, 40, 41, 50, 59, 60, 66, 68, 72, 73, 75, 76, 77], "siem integr": 77, "siem_alert": 67, "siem_platform": 40, "siem_typ": 66, "siemintegr": [66, 67], "sig": 16, "sigma": 77, "sign": [7, 36, 37, 42, 64, 68, 75, 76, 77], "signatur": [8, 39, 44, 51, 56, 75, 77], "signature_algorithm": 39, "signature_and_anomali": 40, "signature_bas": 40, "signific": [2, 18, 40, 41, 43, 45, 48, 49, 74, 76, 77], "significantli": [0, 47, 77], "silver": 75, "sim_001": 78, "similar": [19, 73, 77, 78], "simpl": [34, 69], "simplifi": 77, "simul": [0, 1, 3, 8, 22, 24, 26, 36, 40, 41, 43, 51, 61, 64, 65, 68, 69, 72, 74, 76, 77], "simulate_attack": [67, 78], "simulate_attack_custom": 78, "simulation_id": 78, "simulation_in_progress": 10, "simulation_paramet": 78, "simultan": [60, 61, 77], "sinc": [30, 31, 32], "singl": [7, 9, 14, 18, 19, 21, 34, 37, 42, 55, 60, 68, 75, 76, 77], "single_source_shortest_path": 34, "site": [3, 29, 37, 45, 51, 53], "situat": [4, 72, 74, 75, 76, 77], "six": 69, "size": [0, 3, 8, 25, 29, 31, 34, 39, 40, 59], "size_byt": 59, "skill": [43, 70, 71, 72, 74, 75, 76, 77], "skip": [2, 20, 51, 52, 53], "sklearn": 55, "sla": [0, 15, 38, 40, 46, 48, 49, 50, 60, 62], "slack": [41, 66, 75, 77], "sleep": [51, 66], "slim": [31, 34, 52], "slow": [17, 20, 21, 31, 32, 33, 34, 36, 59, 62, 63], "slow_queri": 59, "slowest": 63, "slowli": 64, "sm": [11, 40, 77], "small": [6, 19, 20, 21, 33, 36, 60, 65, 66], "smaller": [6, 14, 34], "smart": [3, 4, 75], "smb": 75, "smoke": 14, "sms_servic": 40, "smsservic": 40, "sn": 64, "snake_cas": 18, "snakeviz": 20, "snapshot": 16, "snapshot_id": 30, "snapshot_identifi": 16, "snapshotcreatetim": 30, "snyk": 48, "soap": 66, "soar": [0, 8, 40, 59, 60, 72, 76, 77], "soc": [1, 3, 22, 23, 24, 25, 26, 36, 37, 39, 42, 43, 44, 46, 47, 50, 54, 61, 65, 66, 67, 68, 75, 76], "soc oper": 26, "soc2": [23, 24, 28, 35, 37, 39, 43, 49, 66, 72], "soc2_control": 40, "soc2_report": 66, "soc_oper": [7, 40, 65], "social": [45, 71, 74, 75, 76, 77], "social_engineering_oper": 75, "societ": [75, 76], "sock": [14, 31], "sofaci": 78, "soft": [5, 24, 26, 59, 60, 61], "soft_delete_asset": 59, "softwar": [1, 38, 40, 50, 52, 59, 63, 65, 66, 68, 73, 74, 75, 76, 77], "solid": [1, 23, 35], "solut": [0, 1, 2, 19, 26, 28, 36, 51, 60, 64, 65, 76, 77], "solv": 19, "some": [0, 13, 19, 21, 40, 41, 65], "some_funct": 63, "sonar": 53, "sonarqub": [40, 46, 48, 53], "soon": [8, 26, 36, 41, 42, 72], "sophist": [22, 26, 67, 69, 70, 71, 73, 74, 75, 76, 77, 78], "sophistication_level": [40, 67, 78], "sort": [5, 16, 17, 21, 26, 31, 32, 33, 34, 40, 55, 57, 67], "sort_bi": 30, "sort_ord": 54, "sort_stat": 34, "sortimport": 20, "sourc": [2, 3, 6, 14, 16, 17, 18, 19, 20, 21, 22, 27, 30, 32, 33, 34, 35, 36, 38, 43, 45, 50, 51, 53, 56, 57, 60, 61, 62, 63, 64, 65, 66, 67, 69, 72, 73, 74, 75, 77], "source_asset": [18, 19, 67], "source_asset_id": [6, 8, 18, 33, 34, 59, 61, 62, 69], "source_asset_typ": 67, "source_control_id": 54, "source_dropdown": 62, "source_id": [34, 62, 63], "source_system": 56, "sourceasset": [18, 19], "sourceassetid": [8, 18, 19], "sourceid": [18, 34], "sourcetyp": 66, "sox": [6, 22, 26, 61, 69, 70, 76, 78], "sox_impact": 78, "sp": [29, 64], "space": [3, 14, 20, 27, 36, 64], "spam": 45, "span": 75, "spawn": 62, "speak": [45, 71], "spear": [74, 75, 77], "spec": [14, 16, 17, 30, 31, 32, 33, 49], "special": [3, 15, 19, 26, 70, 72, 74, 76, 77], "specialist": [70, 74, 75, 77], "specif": [0, 1, 3, 4, 5, 6, 7, 8, 9, 10, 12, 16, 17, 18, 19, 20, 21, 22, 24, 26, 29, 33, 36, 37, 39, 43, 44, 51, 53, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 74, 75, 76, 77, 78], "specifi": [9, 10, 38, 77, 78], "speed": [21, 77], "speedup": 33, "spell": 3, "spent": 77, "sphinx": [0, 1, 18, 23, 25], "spike": [32, 33], "split": 55, "splunk": [40, 66, 77], "spoof": [44, 46, 49], "spot": [72, 74], "spread": 77, "sprint": [21, 23], "sql": [0, 13, 17, 18, 19, 20, 21, 29, 34, 37, 41, 42, 44, 45, 49, 51, 52, 53, 54, 62, 65, 66, 75], "sql_inject": 52, "sql_payload": 51, "sqlalchemi": [18, 34, 56, 62], "squash": 21, "src": [20, 51, 62], "ss": 66, "ssd": [16, 20, 27, 34], "ssh": 75, "ssl": [13, 14, 23, 24, 27, 31, 36, 66, 68], "ssl_ca_cert_path": 13, "ssl_cert_path": 13, "ssl_key_path": 13, "ssl_verify_mod": 13, "sso": [7, 37, 42, 45, 60, 68, 75, 76, 77], "st": [16, 29, 64], "stabil": [21, 70], "stabl": [16, 20, 21, 40, 51, 52], "stack": [14, 16, 23, 24, 25, 28, 35, 44, 48, 53, 65, 74, 75, 76, 77], "staff": 12, "stage": [0, 5, 21, 22, 26, 27, 29, 34, 41, 46, 49, 52, 66, 68, 74, 75, 77], "stai": [20, 68, 74, 75, 76, 77], "stakehold": [17, 21, 22, 26, 32, 38, 41, 43, 49, 50, 68, 69, 70, 72, 74, 75, 76, 77, 78], "stakeholder_commun": [71, 74, 75], "stamp": 17, "standalon": 27, "standard": [4, 5, 6, 8, 9, 10, 12, 15, 20, 22, 26, 33, 38, 39, 41, 42, 43, 44, 45, 46, 48, 49, 60, 61, 68, 70, 71, 74, 75, 76, 77], "standard_user_frequency_dai": 4, "standardscal": 55, "start": [6, 11, 13, 18, 21, 30, 31, 32, 33, 34, 41, 47, 51, 53, 55, 57, 62, 64, 65, 66, 67, 72], "start_continuous_monitor": 66, "start_dat": 39, "start_discoveri": [5, 66], "start_monitor": 51, "start_period": 34, "start_tim": [34, 39, 51, 55, 63], "started_at": 5, "starts_with": 30, "startswith": 63, "startup": [13, 75], "stat": [2, 20, 29, 30, 31, 32, 33, 34, 36, 64, 65], "state": [12, 15, 18, 20, 30, 31, 33, 34, 40, 51, 63, 67, 74, 75, 76, 77], "stateless": 15, "statement": [29, 63, 71], "static": [3, 13, 18, 21, 24, 33, 34, 35, 37, 42, 48, 49, 50, 51, 62, 75, 76], "static_config": 33, "staticmethod": 34, "statist": [4, 10, 17, 22, 29, 31, 32, 59, 60, 72, 73, 74, 76, 77, 78], "statsd": 52, "statu": [2, 4, 6, 7, 10, 11, 12, 14, 16, 17, 27, 29, 30, 31, 32, 34, 36, 38, 39, 43, 44, 47, 49, 51, 53, 54, 55, 56, 59, 61, 62, 63, 64, 66, 67, 68, 70, 71, 73, 76, 77], "status_cod": [51, 52, 55, 56, 62], "status_page_token": [31, 32], "status_upd": 32, "statuscod": 18, "statuspag": [31, 32], "stddev_tim": 31, "stdin": [14, 16], "stealth": [22, 26, 72, 74, 75], "stealth_operation_manag": 75, "steer": 76, "steganographi": 75, "step": [0, 3, 14, 22, 26, 30, 32, 41, 43, 44, 45, 51, 53, 60, 64, 65, 68, 75, 76, 77], "stewardship": 71, "stix": [9, 13, 26, 61, 65, 68, 72, 73, 78], "stop": [43, 63, 65, 75], "storag": [7, 12, 14, 15, 16, 20, 23, 27, 28, 29, 30, 31, 33, 38, 39, 40, 43, 55, 57, 59, 60, 61, 64, 65, 66, 68, 69, 74, 75, 77], "storage_capac": 28, "storage_s": 15, "storageaccount": 29, "storageclass": 17, "store": [7, 8, 13, 15, 23, 29, 34, 38, 40, 45, 49, 55, 56, 59, 65, 66, 69, 75], "stori": 48, "storytel": 76, "str": [7, 18, 25, 29, 34, 40, 51, 52, 53, 55, 56, 57, 63], "strateg": [3, 22, 26, 43, 44, 48, 60, 61, 72, 75, 77], "strategi": [0, 8, 14, 24, 26, 28, 33, 38, 41, 44, 53, 59, 60, 61, 62, 63, 68, 69, 70, 71, 72, 74, 75, 76, 77], "strategic secur": 71, "strategic_campaign_author": 75, "strategic_compliance_author": 70, "strategic_planning_access": 76, "strategic_program_author": 74, "strategic_risk_assess": 76, "strategic_security_approv": 71, "strategic_security_oversight": 71, "strategic_threat_analysi": 77, "stratifi": 55, "stream": [21, 29, 36, 64], "streamlin": [23, 76, 77], "strength": [44, 60], "strengthen": 69, "stress": [2, 33, 60, 62, 70], "strict": [11, 13, 27, 40, 62, 63, 75], "strictli": 75, "stride": [46, 49, 76], "strike": 78, "string": [5, 6, 7, 8, 13, 18, 19, 25, 52, 54, 55, 56, 61, 63, 68], "string_to_arrai": 59, "stringifi": 7, "strip": [18, 51], "strive": 77, "strong": [4, 7, 11, 13, 28, 36, 39, 40, 44, 49, 51, 65, 68, 71, 76, 77], "structlog": 18, "structur": [4, 12, 13, 18, 21, 23, 24, 25, 29, 35, 43, 44, 45, 48, 57, 61, 68, 69, 70, 71, 74, 75, 76, 77], "studi": [22, 76], "studio": [74, 75], "style": [20, 21], "sub": [7, 9, 11, 15, 26, 33, 34, 39, 52, 60, 61, 69, 72, 73], "subgraph": 29, "subject": [16, 17, 28, 31, 37, 45, 46, 49, 70, 76, 77, 78], "subject_id": 40, "submiss": 70, "submit": [3, 4, 7, 12, 49, 60, 65, 72], "submitted_at": 12, "subnet": [15, 16, 28, 30, 66], "subprocess": 52, "subscript": [13, 29, 64, 65, 66], "subscription_id": 29, "subsect": 19, "subsequ": [7, 77], "subset": 54, "substitut": 18, "succe": 51, "success": [3, 5, 10, 16, 18, 19, 21, 22, 26, 27, 60, 63, 68, 69, 70, 71, 72, 74, 75, 76, 77, 78], "success_count": 51, "success_prob": 78, "successfulli": [1, 5, 6, 7, 8, 14, 18, 30, 36, 47, 74, 75], "successor": 57, "sudo": [2, 14, 16, 27, 43, 64, 65], "suffici": 39, "suggest": [0, 3, 9, 10, 18, 19, 21, 25, 45, 64, 65, 69, 77], "suit": [0, 3, 11, 15, 19, 20, 22, 25, 26, 46, 47, 48, 62, 63, 71, 72, 75, 76, 77], "suitabl": 0, "sum": [31, 40, 51, 53, 57], "summar": [0, 1, 23, 28], "summari": [4, 5, 32, 39, 41, 42, 43, 44, 48, 51, 52, 53, 60, 67, 72], "sun": 16, "sundai": [5, 55], "super": [13, 18, 27], "superior": [70, 74, 75, 76, 77], "superset": 54, "superus": 14, "supervis": [55, 74, 75], "supervisori": 12, "suppli": [38, 61, 69, 71, 74, 75, 76, 77], "supplier": [74, 75, 76, 77], "supply_chain": 59, "supply_chain_attack_simul": 75, "support": [0, 1, 7, 9, 12, 15, 19, 21, 23, 24, 25, 33, 35, 36, 39, 40, 41, 43, 44, 47, 48, 50, 53, 54, 57, 59, 61, 62, 66, 67, 69, 73], "suppress": [40, 51, 53], "surfac": [47, 49, 67, 75, 76, 77], "suricata": 40, "survei": 61, "surveil": [40, 77], "suspect": 77, "suspici": [7, 41, 43, 74, 77], "sustain": 77, "sv": 66, "svg": [51, 73], "swagger": [5, 19, 60, 66], "swap": 64, "swap_usag": 33, "swarm": 24, "swift": 75, "switch": [3, 14, 15, 19, 59, 77], "sy": [20, 31, 52], "symptom": [17, 36, 64], "syn": 66, "sync": [13, 23, 56, 66, 73], "sync_asset": 66, "sync_result": 66, "sync_statu": 56, "sync_thehive_cas": 56, "synchron": [1, 9, 23, 36, 56, 60, 61, 65, 66, 68, 77], "syntax": [0, 3, 13, 64], "synthet": 51, "system": [0, 1, 2, 4, 6, 8, 13, 15, 22, 23, 24, 25, 26, 28, 29, 33, 35, 36, 40, 42, 43, 44, 45, 46, 47, 48, 49, 53, 54, 56, 58, 61, 63, 64, 66, 67, 69, 70, 72, 73, 74, 75, 76, 77, 78], "system32": 51, "system_imag": 43, "system_mainten": 68, "systemat": [17, 34, 40, 70, 74, 75, 77], "systemctl": [27, 43, 64, 66], "systemrandom": [47, 50], "t": [2, 14, 16, 19, 21, 30, 31, 33, 34, 40, 46, 49, 51, 52, 63, 64], "t1003": [67, 73], "t1005": 78, "t1021": 67, "t1055": [67, 73, 78], "t1059": [73, 78], "t1068": 67, "t1078": [67, 73, 78], "t1190": [67, 78], "t1204": 67, "t1566": [9, 67, 73, 78], "t2": 63, "t3": 16, "t4": 66, "t9999": 9, "ta0001": [6, 57], "ta0008": 6, "tab": 20, "tabl": [0, 1, 3, 12, 16, 29, 30, 31, 32, 35, 39, 51, 52, 60], "table_s": 59, "table_schema": 30, "tablenam": [31, 32, 34, 59], "tabletop": [40, 41, 43, 75, 77], "tactic": [6, 40, 57, 61, 67, 72, 73, 74, 75, 76, 77], "tactic_id": 34, "tag": [3, 8, 14, 16, 21, 30, 31, 49, 52, 56, 60, 63, 65, 66, 68], "tail": [17, 30, 31, 32, 41], "tailgat": 75, "tailor": [26, 72, 76], "tailwindcss": 20, "take": [13, 17, 18, 21, 34, 36, 59, 64], "takeawai": 41, "taken": [32, 41, 77], "talent": 71, "tamper": [28, 37, 44, 46, 49], "tar": [16, 30, 32, 41, 64, 65], "target": [0, 5, 6, 8, 10, 11, 15, 16, 17, 18, 19, 21, 30, 32, 33, 36, 38, 40, 44, 46, 48, 49, 50, 51, 52, 53, 55, 57, 60, 62, 63, 64, 65, 66, 67, 69, 72, 73, 75, 76, 77, 78], "target_15_minut": 40, "target_1_hour": 40, "target_95_perc": 40, "target_asset": [18, 19, 57, 67, 78], "target_asset_id": [5, 6, 8, 18, 33, 34, 59, 61, 62, 69], "target_asset_nam": 5, "target_asset_typ": 67, "target_column": 55, "target_control_id": 54, "target_databas": 30, "target_dropdown": 62, "target_group_arn": [16, 32], "target_id": [34, 62, 63], "target_industri": 40, "target_less_than_5_perc": 40, "target_matur": [40, 67], "target_region": 40, "target_rol": 4, "target_s": 40, "target_sector": [67, 73, 78], "targetasset": [18, 19], "targetassetid": [18, 19], "targetid": 34, "targets_complet": 5, "targets_tot": 5, "task": [0, 6, 17, 19, 23, 25, 28, 31, 34, 36, 56, 57, 61, 62, 63, 68, 75, 77], "task_data": 56, "taxii": [13, 61, 65, 68, 78], "taxii2": 13, "taxii_collection_id": 13, "taxii_password": 13, "taxii_server_url": 13, "taxii_usernam": 13, "tb": [29, 51, 62, 63], "tbd": 45, "tcp": [5, 17, 33, 34], "td": 3, "tdd": [2, 21], "team": [1, 2, 3, 5, 14, 16, 17, 21, 22, 23, 24, 25, 26, 27, 28, 32, 36, 37, 39, 40, 42, 44, 45, 46, 49, 50, 52, 60, 61, 65, 66, 68, 70, 71, 76, 77], "team_management_author": [74, 75], "teamer": 65, "techint": 74, "technic": [19, 21, 22, 36, 38, 43, 45, 48, 49, 57, 65, 67, 68, 69, 70, 72, 76, 77, 78], "techniqu": [0, 6, 8, 18, 25, 26, 33, 34, 40, 45, 49, 51, 52, 57, 58, 59, 60, 61, 63, 67, 69, 70, 72, 74, 75, 76, 77, 78], "technique_id": [9, 33, 34, 57, 67, 73], "technique_map": [57, 67], "technique_nam": 57, "technique_not_found": 9, "technique_overlap": 67, "technique_upd": 78, "technologi": [3, 22, 43, 48, 53, 61, 70, 71, 74, 75, 76, 77, 78], "telemedicin": 75, "templat": [1, 3, 12, 14, 16, 17, 19, 20, 24, 32, 41, 43, 45, 51, 52, 66, 67, 75, 76, 77], "tempor": [55, 73, 75, 76, 77], "temporari": [4, 13, 28, 31, 41, 75], "temporarili": 8, "tenant": [13, 23, 24, 25, 27, 29, 64, 65, 66, 75], "tenant_id": [29, 66], "term": [3, 24, 39, 43, 44, 62, 63, 68, 71, 73, 74, 75, 76, 77], "termin": [20, 38, 68], "terraform": [15, 16, 28, 30, 35, 52], "terraform_1": 16, "test": [1, 3, 11, 12, 13, 14, 15, 17, 22, 23, 24, 25, 28, 31, 32, 34, 35, 36, 38, 40, 41, 42, 43, 44, 45, 48, 61, 64, 65, 66, 67, 68, 69, 70, 72, 74, 76, 77], "test_": [62, 63], "test_analysi": 18, "test_analysis_endpoint": 63, "test_analyze_attack_path_raises_validation_error_for_invalid_asset": 18, "test_analyze_attack_path_respects_max_depth_paramet": 18, "test_analyze_attack_path_returns_valid_paths_for_connected_asset": 18, "test_analyze_path_invalid_asset": 19, "test_analyze_path_invalid_source_raises_validation_error": 63, "test_analyze_path_success": [19, 63], "test_api_integr": 62, "test_api_secur": [51, 62], "test_asset": [51, 63], "test_asset_cr": 63, "test_asset_crud_oper": 62, "test_asset_discovery_workflow": 62, "test_asset_endpoint": 63, "test_asset_servic": 63, "test_asset_status_transit": 63, "test_asset_stuff": 63, "test_asset_type_valid": 63, "test_asset_validation_accepts_valid_data": 63, "test_asset_validation_rejects_empty_nam": 63, "test_async_attack_path_analysi": 63, "test_attack_path": [18, 21, 62, 63], "test_attack_path_analysi": 21, "test_attack_path_analysis_perform": 63, "test_attack_path_servic": [19, 62, 63], "test_auth": 2, "test_auth_endpoint": 63, "test_authentication_bypass": [51, 52], "test_brute_force_protect": 51, "test_bulk_analysis_perform": 63, "test_cas": 52, "test_command_inject": 51, "test_complete_attack_path_analysi": 62, "test_concurrent_analysis_request": 63, "test_dat": 30, "test_database_connection_failur": 63, "test_database_integr": 62, "test_database_url": 62, "test_debug_exampl": 63, "test_discover_api_endpoints_handles_timeout": 63, "test_discover_aws_assets_success": 63, "test_e2": 2, "test_email_valid": 63, "test_find_attack_paths_no_path_exist": 62, "test_find_attack_paths_success": 62, "test_get_asset": 63, "test_get_technique_invalid_id_raises_validation_error": 63, "test_help": 63, "test_horizontal_privilege_escal": 51, "test_id": 53, "test_integration_": 2, "test_mitre_servic": 63, "test_multiple_exception_typ": 63, "test_nam": 53, "test_new_featur": 2, "test_password": 53, "test_path_travers": 51, "test_privilege_escal": 51, "test_properti": 53, "test_race_condit": 51, "test_session_fix": 51, "test_siz": 55, "test_sql_inject": 52, "test_sql_injection_attack": 51, "test_sql_injection_protect": 62, "test_typ": 30, "test_update_mitre_data_network_error_raises_mitre_data_error": 63, "test_us": 63, "test_user_workflow": 62, "test_valid": 63, "test_validate_ip_address": 63, "test_with_debugg": 63, "test_workflow_bypass": 51, "test_xss_prevent": 51, "test_xss_protect": 62, "testabl": [18, 63], "testalert": 16, "testapiintegr": 62, "testapisecur": 62, "testapisecuritydast": 51, "testassetdiscoveryservic": 63, "testasyncanalysisservic": 63, "testattackpathperform": 63, "testattackpathservic": [19, 62, 63], "testauthenticationsecur": 51, "testauthorizationsecur": 51, "testbusinesslog": 51, "testclient": [51, 62], "testdatabaseintegr": 62, "tester": 75, "testing_scor": 44, "testinputvalid": 51, "testipvalid": 63, "testmitreserviceexcept": 63, "testmon": 20, "testpassword": 62, "testpath": [62, 63], "testus": 51, "testuserworkflow": 62, "text": [3, 8, 11, 12, 13, 16, 18, 25, 30, 31, 34, 39, 51, 52, 53, 54, 56, 59, 62, 63, 64], "tfplan": [14, 16, 30, 49], "tfsec": 49, "tfstate": 30, "tfvar": [14, 16, 30], "than": [18, 34, 35, 59, 71, 74, 75, 76, 77], "thank": 19, "theft": [12, 43, 74, 75, 77], "thehiv": [24, 35], "thehive4pi": [23, 25], "thehive_cas": 56, "thehive_case_id": 56, "thehive_case_map": 56, "thehive_case_numb": 56, "thehive_connector": 56, "thehive_integration_config": 56, "thehivecas": 56, "thehivecasemap": 56, "thehiveconfig": 56, "thehiveconnector": [25, 56], "thehiveintegrationconfig": 56, "thei": [18, 19, 49, 53, 61, 62, 63, 69, 77], "them": [61, 75], "theme": [3, 18], "themselv": 42, "theoret": 0, "thi": [0, 1, 2, 3, 4, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 35, 36, 37, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 64, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78], "thick": 36, "thing": [34, 62, 63, 71, 76], "think": [74, 76, 77], "third": [8, 38, 40, 41, 43, 44, 45, 49, 50, 60, 69, 70, 74, 75, 76, 77], "thorough": 44, "thoroughli": [19, 21], "thought": [3, 22, 26, 70, 71, 74, 75, 76], "thread": [51, 57, 60, 61], "thread_pool_keep_al": 34, "thread_pool_max_s": [33, 34], "threadpoolexecutor": 57, "threat": [1, 4, 6, 11, 21, 22, 23, 26, 28, 32, 35, 37, 38, 39, 41, 42, 43, 45, 48, 50, 52, 58, 60, 65, 67, 68, 69, 71, 74, 75, 76], "threat hunt": 74, "threat model": [26, 76], "threat monitor": 77, "threat_actor": [6, 59, 61, 67, 69, 78], "threat_actor_emul": 75, "threat_actor_id": 78, "threat_actor_profil": 67, "threat_actor_upd": 78, "threat_fe": 40, "threat_group": 73, "threat_hunting_assist": 74, "threat_hunting_tool": 77, "threat_ind": 40, "threat_intel": 78, "threat_intel_en": 13, "threat_intel_update_interv": 13, "threat_intellig": [40, 59], "threat_intelligence_access": 77, "threat_intelligence_analysi": 74, "threat_landscap": 40, "threat_likelihood": 67, "threat_model": 78, "threat_predict": 55, "threat_threshold": 32, "threatactor": [8, 40], "threatfeedservic": 40, "threatintelligenceservic": 40, "threatpredict": [25, 55], "threatpredictionmodel": 55, "threatpredictionservic": [25, 55], "three": 24, "threshold": [32, 36, 38, 39, 52, 55, 57, 61, 67, 73, 77], "threshold_count": 39, "threshold_monitor": 39, "threshold_multipli": 39, "throttl": [24, 25, 29, 37, 60, 61, 68], "through": [0, 3, 6, 7, 8, 13, 18, 21, 25, 26, 27, 31, 34, 35, 37, 43, 44, 45, 46, 47, 48, 50, 51, 53, 58, 60, 61, 62, 65, 67, 69, 70, 71, 74, 75, 76, 77], "throughout": [1, 15, 38, 45, 50, 52, 62, 74, 75, 77], "throughput": [15, 28, 33, 34, 36, 39, 60, 68, 76], "throw": [7, 18], "thursdai": 76, "ti": 64, "ticket": [0, 32, 41, 46, 60, 66, 75, 77], "tier": [0, 5, 8, 15, 16, 22, 33, 39, 70, 74, 75, 76, 77], "time": [3, 6, 8, 9, 12, 15, 18, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 31, 32, 33, 34, 35, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 56, 59, 60, 61, 62, 63, 65, 66, 67, 68, 69, 70, 71, 72, 74, 75, 76, 78], "time_horizon": 25, "time_to_fix": 53, "time_window_minut": 39, "timedelta": [40, 55], "timefram": [67, 73, 77], "timelin": [21, 38, 41, 43, 45, 48, 49, 65, 67, 72, 74, 75, 76, 77, 78], "timeout": [5, 7, 21, 29, 30, 31, 33, 34, 36, 37, 51, 56, 62, 63, 64, 66, 68], "timeout_r": 33, "timeouterror": 18, "timer": 77, "timestamp": [5, 6, 8, 12, 15, 18, 34, 39, 40, 41, 43, 52, 54, 55, 59, 73, 75, 77], "timezon": [18, 54, 55, 56], "tip": [36, 69], "titl": [19, 21, 41, 56], "tl": [13, 23, 24, 28, 37, 38, 42, 47, 50, 60, 61, 65, 68], "tlp": 56, "tmp": [30, 31, 32, 41], "to_tsvector": 34, "toc": 3, "togeth": [19, 45], "token": [3, 5, 6, 8, 9, 10, 11, 19, 20, 21, 29, 44, 51, 62, 64, 65, 66, 69, 73, 75, 78], "token_typ": 7, "token_urlsaf": 64, "toler": [15, 26, 38, 70, 71, 74, 76], "toml": [18, 21], "too": [8, 10, 17, 18, 34, 64], "tool": [0, 4, 5, 7, 8, 9, 10, 11, 12, 13, 15, 19, 22, 27, 28, 29, 33, 35, 36, 37, 39, 40, 42, 43, 44, 45, 47, 50, 54, 55, 56, 59, 60, 61, 62, 63, 64, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78], "top": [2, 17, 31, 32, 33, 34, 36, 41, 42, 46, 48, 50, 55, 73], "top_degree_centr": 6, "top_risk_factor": 55, "top_trend": 73, "topic": [0, 3, 19, 43, 76], "topologi": [24, 65, 75, 76, 77], "total": [0, 5, 22, 26, 34, 41, 47, 50, 51, 52, 53, 56, 66, 69, 76, 77, 78], "total_affect": 56, "total_asset": 5, "total_count": 8, "total_edg": 6, "total_impact": 78, "total_impact_scor": 6, "total_issu": [52, 53], "total_nod": 6, "total_review": 44, "total_risk": [34, 67], "total_risk_scor": [6, 56, 59, 69], "total_scan": 52, "total_second": [52, 55], "total_tim": [31, 33, 34, 59, 64], "total_vulner": 51, "totp": [7, 11, 40], "totp_secret": 40, "totp_servic": 40, "totpservic": 40, "tour": 26, "trace": [15, 77], "track": [3, 4, 8, 11, 12, 23, 24, 25, 26, 28, 33, 34, 35, 36, 39, 40, 45, 46, 47, 52, 53, 60, 61, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78], "track_campaign": 73, "track_configur": 66, "track_relationship": 66, "track_security_ev": 52, "track_training_effect": 40, "track_vulner": 66, "track_vulnerability_metr": 52, "trade": [75, 76], "tradit": 67, "traefik": 25, "traffic": [11, 14, 15, 28, 29, 31, 32, 57, 59, 74, 75, 76, 77], "traffic_volume_anomali": 55, "trail": [4, 12, 24, 25, 26, 28, 37, 38, 39, 40, 43, 49, 50, 59, 60, 61, 62, 68, 70, 75, 76], "train": [0, 3, 11, 12, 14, 16, 18, 22, 23, 24, 25, 26, 28, 35, 38, 42, 44, 48, 50, 53, 55, 60, 61, 67, 68, 69, 70, 74, 75, 76], "train_model": 55, "train_test_split": 55, "trained_model": 55, "training_complet": 40, "training_d": 55, "training_data": 55, "training_environment_access": [74, 75], "training_histori": 40, "training_program": 40, "training_program_develop": 74, "training_sampl": 55, "transact": [11, 31, 38, 62, 75], "transfer": [37, 38, 65, 68, 70, 74, 75, 76, 77], "transform": [0, 22, 23, 28, 55, 70, 72, 74, 75, 76], "transit": [11, 29, 37, 38, 39, 42, 44, 47, 49, 60, 63, 65, 66, 68, 75, 77], "translat": [19, 76], "transmiss": [38, 40, 44, 60, 75, 77], "transmit": 77, "transpar": [12, 38, 43, 45, 71], "transport": [11, 75], "travers": [51, 53, 60, 65, 75, 77], "traversal_payload": 51, "treat": 19, "treatment": [46, 70, 75, 76], "tree": [3, 39, 60], "trend": [4, 12, 23, 24, 31, 33, 35, 39, 40, 43, 46, 48, 50, 52, 53, 60, 68, 70, 71, 73, 74, 75, 76, 77], "triag": [45, 53, 56, 74, 77], "trigger": [12, 18, 29, 49, 55, 59, 64, 65, 66, 67, 75, 77], "trigger_soft_delete_asset": 59, "trivi": [14, 31, 46, 48, 52], "troubleshoot": [8, 22, 28, 36, 41, 60, 72, 76, 77], "true": [2, 4, 5, 6, 7, 8, 12, 13, 14, 15, 16, 17, 18, 19, 20, 23, 24, 25, 27, 28, 31, 34, 39, 40, 51, 52, 53, 54, 55, 56, 59, 62, 63, 66, 67, 73, 78], "trufflehog": 49, "trust": [22, 23, 24, 26, 35, 37, 39, 42, 47, 49, 50, 59, 60, 61, 70, 71, 74, 75, 76, 77], "trust_boundari": 67, "trust_service_criteria": 28, "trustboundari": 67, "try": [18, 19, 34, 47, 55, 56, 57, 64], "try_except_continu": 52, "try_except_pass": 52, "tsx": 25, "ttl": [13, 33, 34, 57, 68], "ttp": [9, 73, 74, 75, 76, 77], "tuesdai": 76, "tulpn": [27, 36, 64, 65], "tune": [0, 3, 13, 16, 23, 24, 25, 33, 35, 51, 66, 67, 68, 69, 72, 73, 74, 75, 76, 77], "tunnel": [59, 75], "tupl": [34, 52, 57], "tutori": [0, 1, 3, 19, 77], "tvm": 76, "two": [18, 19], "txt": [3, 14, 16, 20, 31, 34, 51, 53, 62, 63, 64, 66], "type": [0, 1, 3, 6, 7, 8, 9, 10, 13, 15, 18, 20, 23, 26, 30, 31, 32, 33, 34, 36, 37, 39, 42, 45, 46, 50, 51, 52, 53, 55, 56, 57, 60, 61, 62, 63, 65, 66, 68, 73, 75, 76, 77, 78], "type_coverag": 18, "typehint": 3, "typescript": [20, 23, 25], "typic": [6, 60, 61, 65, 69, 72, 75, 77], "typographi": 3, "u": [5, 13, 14, 16, 17, 19, 21, 27, 29, 30, 31, 32, 41, 45, 51, 63, 64, 65, 66], "uac": 75, "uba": 75, "ubuntu": [3, 5, 14, 19, 20, 27, 51, 53, 62, 63], "ueba": [50, 74, 75, 76], "uefi": 75, "ufw": 65, "ui": [5, 19, 21, 41, 48, 60, 62], "uid": 32, "uk": 63, "ul": 76, "unam": 14, "unauthor": [8, 12, 18, 32, 38, 41, 43, 44, 45, 77], "unauthorized_access": 78, "unauthorized_disclosur": 78, "unavail": [8, 32, 41, 43, 44, 63], "uncertainti": 76, "unchang": 48, "unclear": 63, "under": [5, 6, 8, 19, 26, 34, 62, 63, 74, 75, 77], "undergo": [48, 76], "underground": [74, 75], "underli": 77, "underscor": 18, "understand": [3, 18, 19, 20, 21, 38, 45, 54, 61, 67, 72, 76, 77], "understood": 38, "underwai": 48, "undetect": [75, 77], "undo": [16, 17, 32], "undu": [12, 38], "unexpect": [18, 69, 76, 77], "unicodedecodeerror": 47, "unifi": [22, 61, 74, 76, 77], "uniform": 18, "union": [18, 42, 51, 52], "uniq": [31, 32], "uniqu": [5, 13, 40, 48, 54, 56, 59, 73, 77], "uniqueconstraint": 54, "unit": [0, 2, 18, 19, 20, 21, 47, 49, 52, 60, 71, 74, 75, 76, 77], "unittest": [19, 63], "unknown": [5, 9, 10, 30, 56, 74, 77, 78], "unknown_apt": 10, "unless": 45, "unlock": 68, "unnecessari": [4, 48, 68], "unnecessarili": 19, "unnest": 59, "unpatch": 76, "unpreced": 77, "unprocess": 8, "unreach": [34, 62, 64], "unsaf": [50, 53], "unsupervis": [55, 74], "untest": 62, "until": [31, 45, 62], "untrust": 47, "unus": [18, 34, 64], "unusu": [4, 11, 32, 39, 77], "unwav": [74, 75], "unzip": 16, "up": [2, 3, 4, 11, 12, 14, 19, 20, 21, 23, 24, 27, 31, 32, 33, 34, 35, 36, 41, 43, 44, 51, 52, 53, 60, 61, 62, 63, 64, 65, 66, 68, 69, 70, 72, 77, 78], "upcom": 65, "updat": [0, 1, 2, 3, 4, 8, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 25, 26, 30, 32, 35, 36, 38, 39, 40, 42, 43, 44, 46, 49, 50, 51, 52, 53, 56, 57, 59, 60, 61, 62, 63, 64, 66, 67, 69, 71, 72, 73, 75, 76, 77, 78], "update_attack_data": 73, "update_cas": [25, 56], "update_data_accuraci": 40, "update_exist": [5, 66], "update_frequ": 78, "update_incident_not": 77, "update_incident_statu": 7, "update_mitre_data": 63, "update_playbook": 40, "updated_asset": 5, "updated_at": [5, 18, 54, 59], "updated_cas": 56, "upgrad": [0, 14, 17, 18, 20, 27, 31, 54, 60, 68], "upload": [5, 30, 49, 51, 53, 62, 63], "upon": [24, 35, 45], "upper": [55, 56], "upper_snake_cas": 18, "uppercas": 19, "upstream": [19, 33, 34], "uptim": [15, 23, 35, 40, 50, 61], "uptime_sla": 28, "uq_control_framework_control_id": 54, "uq_domain_framework_domain_id": 54, "uq_framework_name_vers": 54, "ur": 64, "urgent": 77, "url": [18, 20, 26, 33, 51, 56, 65, 66], "us": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 18, 19, 20, 21, 23, 24, 27, 28, 29, 30, 31, 33, 34, 35, 36, 38, 39, 40, 41, 44, 45, 46, 47, 48, 49, 51, 52, 53, 55, 57, 59, 60, 61, 62, 63, 64, 68, 70, 71, 72, 73, 74, 75, 76, 77, 78], "usabl": [0, 37], "usag": [1, 2, 3, 8, 9, 10, 11, 13, 14, 16, 17, 28, 30, 31, 32, 33, 34, 36, 38, 39, 41, 44, 47, 49, 51, 53, 60, 61, 63, 64, 67, 68, 69, 75, 77, 78], "usage_track": 4, "usecallback": [18, 19], "useeffect": 18, "user": [2, 4, 5, 6, 7, 9, 10, 11, 12, 13, 14, 16, 18, 19, 20, 21, 23, 25, 28, 29, 33, 34, 38, 39, 40, 41, 42, 43, 44, 45, 48, 49, 50, 51, 52, 54, 56, 59, 60, 62, 63, 64, 66, 67, 74, 75, 76, 77], "user guid": 22, "user1": 51, "user123": 63, "user1_token": 51, "user2": 51, "user_account": 56, "user_ag": 39, "user_behavior": 40, "user_behavior_weight": 4, "user_data": 49, "user_id": [4, 12, 33, 39, 64], "user_rol": [40, 64], "user_role_enum": 59, "user_sess": 33, "user_token": 51, "user_workflow": 18, "useradd": 27, "userfactori": [62, 63], "usermod": [14, 27, 43], "usernam": [7, 8, 14, 16, 27, 40, 51, 59, 66], "username_field": 51, "users_email_format": 59, "users_username_length": 59, "usest": [18, 19], "usr": [14, 16], "utc": 30, "utcnow": 55, "util": [0, 18, 24, 31, 33, 34, 39, 42, 48, 63, 68, 73, 75, 76, 77], "uuid": [12, 25, 39, 54, 55, 56, 59], "uuid4": [54, 55, 56], "uvicorn": [13, 14, 20, 33, 34, 51], "uvicornwork": [13, 33, 34], "ux": [19, 41], "v": [0, 2, 14, 17, 18, 19, 20, 31, 33, 34, 49, 51, 53, 62, 63, 64, 65, 69, 72, 73, 77], "v1": [4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 18, 20, 21, 24, 27, 29, 30, 31, 32, 33, 39, 51, 52, 61, 62, 64, 65, 66, 69, 73, 78], "v1beta1": 49, "v2": [14, 25, 33, 53], "v3": [3, 14, 16, 45, 46, 48, 51, 53, 62, 63], "v4": [3, 51, 53, 62, 63], "vacuum": [31, 32, 59, 64], "valid": [0, 1, 3, 4, 5, 7, 8, 10, 12, 14, 16, 17, 18, 19, 21, 23, 24, 25, 26, 28, 29, 31, 32, 34, 35, 38, 40, 42, 43, 44, 45, 46, 48, 50, 52, 53, 54, 56, 59, 60, 61, 62, 63, 64, 65, 66, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78], "valid_asset": 19, "valid_email": 63, "valid_target": 63, "validate_asset_typ": 63, "validate_email": 63, "validate_ip": 18, "validate_ip_address": 63, "validate_nam": 18, "validate_technique_data": 63, "validated_bi": 54, "validated_data": 49, "validation_d": 54, "validation_error": [5, 8, 18], "validation_scenario": 67, "validationerror": [18, 19, 63], "valu": [1, 6, 13, 16, 17, 18, 23, 26, 29, 30, 34, 36, 40, 45, 48, 51, 52, 53, 55, 56, 60, 63, 64, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78], "valuabl": [19, 76], "valuat": [75, 76], "valueerror": [18, 56], "var": [3, 13, 14, 16, 18, 30, 31, 32, 41, 53, 65, 76], "varchar": [12, 39, 54, 59], "vari": 45, "variabl": [1, 13, 14, 17, 18, 21, 29, 52, 63], "variat": 77, "variou": [6, 9, 63, 65, 77], "varoni": 40, "vcpu": 16, "ve": [19, 26, 36], "vector": [25, 26, 34, 36, 41, 43, 44, 48, 49, 61, 69, 72, 75, 76, 77], "vehicl": [59, 75], "veloc": [44, 52, 53, 77], "vendor": [40, 41, 45, 49, 51, 68, 69, 71, 74, 75, 76, 77], "vendor_evaluation_access": 76, "vendor_relationship_manag": 71, "ventur": 75, "venv": [2, 14, 18, 20, 27, 52, 53, 63], "venv_bin": [51, 53], "verbos": [30, 31, 63], "veri": 78, "verif": [14, 15, 26, 28, 36, 38, 39, 40, 41, 43, 45, 47, 48, 65, 68, 73, 76, 77], "verifi": [0, 2, 11, 12, 15, 16, 17, 19, 27, 29, 30, 31, 32, 34, 36, 37, 38, 39, 40, 41, 42, 48, 56, 60, 62, 64, 65, 66, 67, 68, 69, 72, 77], "verification_complet": 12, "verification_interval_hour": 39, "verification_method": 12, "verification_requir": 12, "verify_password": 40, "verify_ssl": 56, "version": [1, 3, 4, 5, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 24, 25, 27, 29, 30, 31, 34, 42, 44, 45, 47, 51, 52, 53, 54, 60, 61, 62, 63, 64, 66, 68, 70, 74, 75, 76], "versu": 25, "vertic": [0, 15, 17, 60, 76], "via": [3, 5, 17, 19, 20, 21, 24, 41, 65, 66, 75, 77], "victim": 74, "video": [0, 1, 3, 41, 45, 77], "view": [2, 3, 7, 39, 59, 62, 73, 76, 77], "view_advanced_attack_path": 77, "view_asset": [39, 62], "view_attack_path": [7, 62], "view_audit_log": [7, 68], "view_basic_attack_path": 77, "view_security_ev": [7, 77], "viewer": [29, 65], "vigil": 70, "violat": [10, 12, 22, 39, 43, 45, 48, 69, 70, 72, 75, 76, 77, 78], "violation_prob": 78, "virtual": [14, 20, 27, 29, 61, 75, 76, 77], "virtual_machin": 59, "virtual_memori": [17, 32, 51], "virtualmachin": 29, "viru": 75, "visibl": [19, 23, 24, 60, 61, 66, 71, 77], "vision": [60, 71, 76], "visionari": 76, "visit": [5, 17, 32, 57, 75], "visual": [0, 1, 3, 4, 7, 8, 9, 11, 12, 21, 23, 35, 39, 58, 60, 61, 65, 67, 69, 72, 75, 76], "visualization_typ": 73, "vlan": [40, 75], "vm": [29, 64, 66], "void": [18, 19, 25, 59], "volum": [4, 16, 29, 30, 32, 34, 39, 49, 64, 66], "volume_anomali": 39, "volumesnapshot": 30, "volumesnapshotclassnam": 30, "voluntari": 38, "vpc": [15, 16, 28, 29, 30, 31, 65, 66, 68], "vpn": [29, 37, 38, 59, 67, 75, 76], "vpn_connect": 59, "vscode": 20, "vuln_featur": 55, "vuln_scan": 31, "vulner": [2, 5, 14, 18, 19, 21, 22, 23, 24, 25, 29, 31, 37, 38, 40, 42, 43, 44, 47, 49, 52, 55, 60, 61, 62, 65, 66, 67, 68, 69, 72, 74, 75, 76, 77, 78], "vulnerabilities_curr": 53, "vulnerabilities_found": 53, "vulnerabilities_found_tot": 53, "vulnerability assess": 47, "vulnerability manag": [46, 48], "vulnerability_count": 34, "vulnerability_fix_time_hour": 53, "vulnerability_manag": [40, 54], "vulnerability_research_access": 75, "vulnerability_scan": 66, "vulnerability_scann": 59, "vulnerability_sever": 67, "w": 32, "w503": 18, "wa": [34, 41, 48, 77], "waf": [15, 28, 32, 40, 48, 59], "wafv2": 32, "wai": [27, 34, 65], "wait": [14, 20, 30, 31, 41, 56, 57, 62], "wait_tim": 62, "wal_buff": [33, 34], "walk": 27, "want": [12, 19], "war": 75, "warehous": 15, "warm_retention_dai": 39, "warn": [3, 13, 14, 16, 18, 19, 31, 39, 46, 47, 52, 56, 62, 63, 70, 74], "watch": 20, "water": 75, "wc": [31, 32], "wcag": 3, "we": [18, 19, 21, 26, 36, 43, 44, 45, 46, 48, 52, 63, 72], "weak": [29, 38, 50, 52, 53, 74, 75, 76, 77, 78], "weak_crypto": 53, "weakref": 34, "weapon": 75, "web": [3, 5, 15, 16, 18, 19, 27, 29, 45, 46, 48, 51, 62, 63, 67, 72, 74, 75, 76, 77], "web01": 5, "web_appl": 78, "web_secur": 59, "web_serv": 3, "web_server_001": [6, 8, 69, 78], "web_servic": 59, "web_ui": 39, "webdriv": 62, "webdriverwait": 62, "webhook": [23, 24, 25, 26, 29, 60, 65, 66, 77], "webhook_data": [25, 56], "webinar": 76, "webroot": 20, "websit": 75, "websocket": [19, 21], "wednesdai": 76, "week": [4, 30, 45, 49, 67, 73, 74, 75, 76, 78], "weekli": [23, 24, 30, 46, 47, 48, 50, 52, 55, 68, 73, 76, 77], "weight": [33, 34, 48, 54, 55, 57, 60, 61, 65, 67, 69, 72], "weighted_asset_risk": 57, "weighted_averag": 54, "welcom": [3, 19, 26, 36, 42, 72], "well": [19, 21, 40, 63, 74, 75, 76, 77], "west": [5, 14, 16, 29, 30, 66, 76], "west1": 29, "westus2": 29, "wget": 16, "what": [0, 18, 19, 25, 26, 27, 34, 41, 62, 63, 69, 76, 77], "wheel": 20, "when": [2, 5, 6, 12, 18, 19, 21, 29, 38, 43, 48, 52, 59, 63, 64, 65, 67, 72, 73], "where": [18, 19, 30, 31, 32, 33, 34, 46, 49, 52, 59, 62, 64, 78], "whether": 18, "which": 19, "while": [0, 1, 3, 4, 15, 22, 23, 35, 37, 43, 44, 48, 52, 54, 57, 66, 70, 71, 74, 75, 76, 77], "white": [56, 64], "whitelist": [29, 76, 77], "whitespac": 62, "who": [45, 68, 70, 71, 74, 75, 76, 77], "whoami": [30, 51], "whose": 77, "why": 34, "wide": [32, 63, 68, 74, 75, 76], "widespread": 77, "widget": 77, "window": [0, 14, 16, 20, 23, 24, 25, 27, 35, 48, 51, 57, 68, 75, 77], "winrar": 78, "winword": 73, "wireless_access_point": 59, "wisdom": [71, 76], "wit": 43, "withdraw": [12, 28], "withdrawal_confirm": 12, "withdrawn_at": 12, "within": [6, 12, 29, 31, 38, 41, 49, 50, 54, 61, 63, 72, 77], "without": [2, 12, 13, 18, 21, 34, 38, 42, 45, 49, 52, 53, 60, 77], "wizard": 36, "wmi": 75, "won": 64, "word": 77, "wordlist": 66, "work": [3, 11, 19, 20, 34, 36, 45, 60, 64, 70, 74, 75, 76, 77], "work_mem": [33, 34, 64], "workdir": 34, "worker": [13, 14, 16, 23, 27, 31, 33, 34, 61, 64], "worker_class": [13, 33], "worker_connect": 33, "worker_replica": 16, "workflow": [0, 1, 4, 8, 18, 23, 25, 26, 28, 35, 40, 45, 48, 50, 51, 53, 56, 58, 60, 61, 62, 65, 67, 70, 74, 76], "workload": [15, 24, 25, 34, 35, 39, 76, 77], "workpap": 70, "workshop": [0, 40, 49, 76], "workspac": [14, 16, 30, 74, 75, 76, 77], "workspacefold": 20, "workstat": [5, 18, 59, 62, 63, 66, 67, 69], "world": [0, 1, 3, 22, 26, 51, 67, 74, 75, 76], "would": [19, 51, 77], "wrap": [18, 34], "wrapper": [18, 25, 34], "write": [2, 4, 18, 20, 21, 33, 34, 60, 64], "writer": 34, "written": [21, 52], "wrk": 52, "wrong_password": 51, "wrong_password_": 51, "wsl2": [14, 20], "www": 3, "x": [2, 4, 5, 6, 8, 10, 12, 14, 20, 29, 31, 32, 33, 34, 39, 40, 51, 55, 62, 63, 64, 67, 73, 78], "x509": [11, 31, 64], "x_scale": 55, "x_test": 55, "x_train": 55, "xact_start": 31, "xarg": [31, 32, 64], "xdr": [76, 77], "xlarg": [15, 16], "xml": [34, 53, 62, 63], "xpath": 62, "xr": 71, "xss": [18, 37, 42, 44, 45, 49, 51, 53, 62], "xss_payload": [51, 62], "xxx": [41, 42], "xxxx": [42, 43, 44], "xzf": 30, "y": [14, 16, 21, 30, 31, 32, 55, 64], "y_pred": 55, "y_pred_proba": 55, "y_test": 55, "y_train": 55, "yaml": [0, 14, 16, 17, 18, 20, 30, 32, 41, 52, 62, 64, 66], "yara": 77, "yarn": [20, 27], "ye": [3, 38, 52, 65, 67], "year": [38, 75], "yellow": 36, "yelp": 52, "yield": [34, 62], "yml": [14, 20, 21, 23, 27, 32, 33, 51, 52, 53, 62, 64, 65], "you": [26, 27, 36, 42, 64, 68, 69, 70, 71, 73, 74, 75, 76, 77, 78], "your": [3, 5, 6, 8, 13, 14, 16, 20, 26, 27, 34, 45, 53, 62, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78], "your_api_token": [9, 10], "your_jwt_token": 5, "your_usernam": 19, "yourapp": 13, "yourcompani": [27, 65], "yourself": 21, "yyyi": [41, 44], "z": [14, 30, 31, 59], "z0": 59, "za": 59, "zap": [40, 46, 48, 49, 51, 52], "zap2dock": [16, 51, 52], "zebroci": 78, "zed": 51, "zero": [2, 22, 23, 24, 26, 35, 37, 42, 47, 48, 49, 50, 60, 61, 62, 75, 76, 77], "zero trust": 76, "zero_day_simul": 75, "zero_trust": 59, "zero_trust_architectur": 40, "zero_trust_deploy": 11, "zero_trust_network_access": 40, "zero_trust_servic": 67, "zerotrustservic": 67, "zip": [16, 57], "ziplist": 34, "zone": [15, 16, 17, 18, 28, 30, 36, 39, 44, 54, 59, 68, 76], "zoom": [36, 77], "zt": 67, "zt_roadmap": 67, "zt_servic": 67, "ztna": 76, "zv": [11, 16, 17], "\u00b2": 77}, "titles": ["Documentation Expansion Summary", "Blast-Radius Security Tool - Complete User Guide Documentation", "Local Development &amp; CI/CD Guide", "\ud83d\udcda Blast-Radius Security Tool Documentation", "Least Privilege Access Control Framework", "Asset Management API", "Attack Path Analysis API", "Authentication API", "API Reference", "MITRE ATT&amp;CK Integration API Reference", "Threat Modeling API Reference", "Zero-Trust Architecture Implementation", "GDPR Compliance Framework", "Configuration Guide", "Environment Setup Documentation - Blast-Radius Security Tool", "Production Architecture Overview", "Production Deployment Guide - Blast-Radius Security Tool", "Troubleshooting Guide - Blast-Radius Security Tool", "Code Standards &amp; Style Guide", "Contributing Guide", "Development Environment Setup", "Development Workflow", "Documentation Overview and Achievements", "Enhanced Features Summary - Blast-Radius Security Tool", "Blast-Radius Security Tool - Enhanced PRD v2.0", "Implementation Gap Analysis - Blast-Radius Security Tool", "Blast-Radius Security Tool Documentation", "Installation Guide", "Latest Implementations Summary - Production Ready Release", "Multi-Cloud Integration Guide", "Backup and Recovery Runbooks - Blast-Radius Security Tool", "Maintenance Procedures - Blast-Radius Security Tool", "Monitoring Runbooks - Blast-Radius Security Tool", "Performance &amp; Scalability", "Performance Optimization", "Phase Integration Plan - Enhanced Features with Existing Roadmap", "Quick Start Guide", "Security Architecture Overview", "Compliance Documentation - Blast-Radius Security Tool", "Enhanced Audit Logging System", "Security Framework", "Incident Response Procedures - Blast-Radius Security Tool", "Security Documentation", "Security Incident Response", "Security Review Process", "Vulnerability Disclosure Policy", "Security Assessment Overview", "Security Review - June 14, 2025", "Vulnerability Management", "Security Review Processes - Blast-Radius Security Tool", "Security Documentation Summary", "Dynamic Application Security Testing (DAST)", "Security Testing Automation", "Static Application Security Testing (SAST)", "Compliance Framework Schema - Technical Specification", "Machine Learning Threat Prediction - Technical Specification", "TheHive Integration - Technical Specification", "Attack Path Analysis Architecture", "Attack Path Analysis Flows", "Database Design and Schema", "Technical Documentation", "Product Requirements Document (PRD)", "Testing &amp; Quality Assurance", "Unit Testing Guide", "Common Issues &amp; Troubleshooting", "Frequently Asked Questions (FAQ)", "Asset Discovery Use Cases", "Attack Path Analysis Use Cases", "Administrators Guide", "Attack Path Analysis User Guide", "Compliance Officers Comprehensive Guide", "Executive Leadership Comprehensive Guide", "User Guides", "MITRE ATT&amp;CK Integration User Guide", "Purple Team Members Comprehensive Guide", "Red Team Members Comprehensive Guide", "Security Architects Comprehensive Guide", "SOC Operators Comprehensive Guide", "Threat Modeling User Guide"], "titleterms": {"": 65, "0": [24, 41, 45], "00": [31, 32], "001": 61, "002": 61, "003": 61, "004": 61, "005": 61, "1": [0, 11, 14, 16, 17, 19, 20, 23, 24, 25, 27, 35, 36, 41, 43, 45, 47, 49, 54, 55, 56, 66, 67], "10": [16, 32, 38, 45], "100": 28, "11": 16, "12": 38, "13": 38, "14": [38, 47], "15": [38, 41], "16": 38, "17": 38, "18": [23, 38], "19": 23, "2": [0, 14, 16, 17, 19, 20, 23, 24, 25, 27, 31, 35, 36, 38, 40, 41, 43, 47, 49, 54, 55, 56, 61, 66, 67], "20": 38, "2025": [23, 24, 25, 35, 47, 50, 61], "2026": [23, 24, 25, 50], "21": 38, "24": [23, 41], "27001": [38, 40], "3": [0, 11, 14, 16, 17, 19, 20, 23, 24, 25, 27, 31, 35, 36, 41, 43, 45, 47, 49, 54, 55, 56, 61, 66, 67], "33": 38, "34": 38, "4": [0, 14, 16, 19, 20, 23, 24, 25, 31, 35, 41, 43, 45, 49, 54, 55, 56, 61, 67], "5": [0, 14, 16, 19, 20, 23, 24, 25, 35, 38, 41, 43, 49, 54, 67], "6": [0, 14, 16, 19, 23, 24, 25, 31, 35, 38, 45, 54], "7": [16, 24, 41, 45], "72": 41, "8": [16, 23, 24, 38, 41, 45], "9": [16, 32, 38, 41, 45], "96": 35, "A": 38, "For": 0, "In": 45, "Not": [17, 64], "One": 2, "_static": 3, "a1": 38, "acceler": [23, 35], "access": [4, 13, 16, 17, 28, 36, 38, 65, 68, 77], "account": [11, 68], "accuraci": 73, "achiev": [22, 28, 50], "acquisit": 38, "action": [14, 23, 28, 35, 36, 41, 53], "activ": 43, "actor": [9, 10, 73, 78], "ad": [0, 3, 20], "add": [16, 36, 65], "addit": [1, 2, 3, 64], "admin": [27, 65], "administr": [0, 40, 68, 72], "adopt": 61, "advanc": [3, 23, 24, 25, 33, 35, 36, 61, 66, 67, 70, 71, 73, 74, 75, 76, 77, 78], "advisori": 71, "after": [0, 41], "agent": 66, "ai": 78, "air": 65, "alemb": 54, "alert": [11, 16, 17, 29, 32, 39, 41], "algorithm": [34, 57], "align": 71, "alreadi": 25, "am": [31, 32, 65], "an": [19, 65], "analysi": [6, 8, 9, 10, 17, 25, 32, 35, 36, 43, 44, 51, 53, 57, 58, 59, 60, 65, 67, 69, 72, 73, 76, 77, 78], "analyt": [3, 4, 23, 24, 25, 35, 61, 73, 76], "analyz": [6, 9], "ani": 65, "api": [1, 3, 4, 5, 6, 7, 8, 9, 10, 12, 18, 20, 24, 26, 29, 39, 55, 56, 60, 61, 65, 66, 69, 73], "applic": [2, 13, 14, 15, 16, 17, 18, 30, 31, 32, 33, 34, 37, 38, 41, 46, 51, 52, 53], "approach": [17, 50], "ar": 65, "architect": [0, 72, 76], "architectur": [4, 11, 12, 15, 23, 24, 26, 28, 29, 33, 37, 39, 40, 42, 44, 49, 50, 52, 55, 56, 57, 59, 60, 61, 67, 76, 77], "archiv": 59, "area": 44, "articl": 38, "ask": 65, "assess": [4, 8, 10, 12, 25, 29, 40, 41, 44, 46, 48, 49, 50, 51, 54, 57, 61, 67, 69, 70, 72, 76, 78], "assessment_criteria": 54, "asset": [0, 5, 8, 24, 25, 29, 36, 38, 60, 61, 66, 72], "assur": [0, 3, 18, 26, 44, 60, 62], "async": 63, "att": [6, 8, 9, 40, 57, 58, 61, 67, 72, 73], "attack": [6, 8, 9, 10, 36, 57, 58, 59, 61, 65, 67, 69, 72, 73, 75, 77, 78], "attackpathanalyz": 57, "attribut": [9, 73], "audit": [28, 39, 46, 65, 70], "authent": [5, 6, 7, 8, 9, 10, 13, 29, 36, 49, 51, 64, 65], "author": [7, 38, 49, 51, 64], "auto": 52, "autom": [2, 12, 18, 21, 23, 24, 25, 30, 31, 32, 35, 46, 48, 49, 50, 51, 52, 61, 62, 65, 66, 67, 70, 73], "automat": 73, "avail": [15, 38], "aw": [14, 16, 29, 66], "awar": [40, 49], "azur": 29, "backend": [2, 14, 20], "backup": [15, 16, 30, 65, 68], "balanc": [16, 34], "base": [4, 5, 6, 8, 9, 10, 48, 54, 66, 68, 72, 77], "basic": [3, 36, 63, 69, 73, 78], "batch": [9, 55], "befor": [0, 19], "behavior": 73, "benchmark": 33, "benefit": [23, 35, 47, 51, 53], "best": [2, 4, 6, 7, 8, 11, 12, 13, 15, 21, 34, 39, 44, 51, 52, 53, 62, 63, 66, 67, 68, 69, 72, 73, 75, 76, 77, 78], "blast": [1, 3, 6, 14, 16, 17, 23, 24, 25, 26, 30, 31, 32, 38, 41, 49, 57, 58, 61, 65, 67, 69], "blastradiusapidown": 32, "blue": [14, 15], "boundari": 67, "bounti": 45, "branch": [19, 21], "breach": [12, 38, 41], "bug": 45, "build": [1, 2, 3, 14, 16, 35], "bulk": 5, "busi": [23, 24, 38, 47, 48, 50, 51, 61, 71], "c1": 38, "cach": [6, 29, 32, 34, 57, 58], "calcul": [6, 29, 46, 58, 61, 69, 78], "campaign": [9, 75], "can": [36, 65], "capabl": [23, 24, 26, 52, 67, 70, 71, 74, 75, 76, 77], "capac": 31, "case": [0, 1, 26, 56, 66, 67, 69], "categori": [2, 43, 51, 62], "cc6": 38, "cd": [2, 14, 46, 51, 52, 53, 62], "celeri": 55, "centric": 0, "certif": [4, 16, 17, 31, 42, 64, 72], "chain": 39, "chang": [7, 19, 36, 45, 49], "channel": 64, "characterist": 28, "check": [2, 12, 16, 18, 27, 31, 32, 49, 52, 64], "checklist": [11, 14, 19, 44, 49], "ci": [2, 14, 46, 51, 52, 53, 62], "ck": [6, 8, 9, 40, 57, 58, 61, 67, 72, 73], "classif": [11, 41, 43, 45, 46, 53], "cleanup": [14, 31], "clear": 6, "cli": 16, "client": [23, 24, 25], "clone": [14, 19, 20], "cloud": [13, 29, 36, 61, 64, 65, 66], "cluster": [16, 31], "code": [0, 3, 5, 8, 15, 18, 19, 20, 21, 28, 34, 36, 44, 49, 63], "collabor": [21, 69, 70, 74], "collect": 64, "command": [2, 3, 41, 64], "commit": [21, 52], "common": [0, 2, 4, 5, 8, 11, 12, 16, 17, 20, 27, 29, 33, 34, 36, 39, 44, 63, 64, 66, 68, 69, 72], "commun": [3, 19, 32, 38, 41, 43, 45, 65, 71, 76, 77], "comparison": 47, "compens": 48, "complet": [1, 17, 26, 28, 30, 35], "complianc": [1, 10, 12, 23, 24, 25, 26, 28, 37, 38, 39, 40, 42, 43, 46, 49, 50, 54, 60, 61, 65, 66, 67, 68, 70, 71, 72, 76, 78], "compliance_assess": 54, "compliance_control": 54, "compliance_control_assess": 54, "compliance_control_map": 54, "compliance_domain": 54, "compliance_framework": 54, "compon": [4, 11, 12, 15, 16, 24, 25, 39, 40, 55, 56, 57, 63], "comprehens": [0, 1, 22, 26, 70, 71, 74, 75, 76, 77], "concept": 69, "conclus": [0, 1, 4, 11, 12, 15, 28, 39, 43, 44, 45, 46, 47, 48, 50, 51, 52, 53, 68, 69, 70, 71, 74, 75, 76, 77], "concurr": 29, "condit": 45, "conduct": 19, "confidenti": 38, "configur": [0, 2, 3, 4, 12, 13, 14, 16, 18, 27, 29, 30, 32, 36, 39, 47, 48, 52, 53, 63, 64, 65, 68, 69, 77], "conflict": 2, "congratul": 36, "connect": [16, 17, 64], "connector": [25, 56], "consent": 12, "consider": [6, 24, 29, 43, 45, 66], "contact": [41, 42, 45], "contain": [2, 16, 34, 41, 43, 66], "content": [0, 1, 3, 5, 18, 19, 20, 21, 22, 33, 34, 40, 62, 63, 64, 66, 67, 70, 71, 74, 75, 76, 77], "context": 9, "continu": [3, 10, 21, 37, 38, 40, 46, 47, 48, 51, 52, 62, 63, 66, 71, 78], "contribut": [3, 19, 60, 65, 72], "control": [4, 13, 28, 37, 38, 40, 48, 68, 74, 77], "convent": 21, "core": [3, 4, 11, 12, 13, 15, 16, 18, 24, 25, 39, 43, 54, 55, 56, 57, 59, 61, 68, 69, 77], "correl": [9, 11, 73], "coverag": [0, 1, 28, 44, 46, 47, 48, 62, 63, 66], "cr": 61, "crashloopbackoff": 17, "creat": [5, 6, 16, 19, 27], "creation": [58, 69], "credenti": [29, 65], "crisi": 71, "criteria": [23, 24, 38, 49, 54], "critic": [32, 41, 45, 47], "cross": [3, 70], "crud": 5, "cryptograph": 47, "cryptographi": [38, 49], "csf": 54, "css": 3, "current": [23, 24, 35, 46], "custom": [3, 53, 60, 65], "cvss": [41, 45, 46, 48], "cybersecur": 38, "cycl": 2, "d3": 25, "daili": [2, 30, 31, 32, 77], "dashboard": [4, 8, 32, 36, 39, 53, 68, 77], "dast": [46, 48, 51, 52], "data": [6, 9, 11, 12, 14, 15, 16, 29, 30, 37, 38, 41, 47, 49, 51, 54, 59, 60, 61, 62, 63, 64, 65, 73], "databas": [2, 12, 13, 16, 17, 18, 20, 24, 30, 31, 32, 33, 34, 39, 48, 54, 55, 56, 59, 60, 61, 64], "databaseconnectionshigh": 32, "de": 38, "debug": [2, 20, 29, 63], "decis": 58, "default": 65, "defens": 3, "definit": [21, 48, 59, 70, 71, 74, 75, 76, 77], "delet": 5, "deliver": 24, "depend": [16, 23, 25, 27, 48, 52], "deploi": [14, 16], "deploy": [1, 3, 11, 14, 15, 16, 23, 24, 26, 28, 30, 52, 60], "deseri": 47, "design": [3, 12, 15, 18, 26, 49, 54, 55, 59, 60, 63, 76], "detail": [4, 12, 24, 35, 39], "detect": [38, 41, 43, 67, 74, 77], "develop": [0, 2, 3, 8, 13, 14, 16, 18, 20, 21, 24, 26, 27, 35, 38, 49, 52, 60, 62, 70, 71, 76, 77], "diagnost": 64, "diagram": [3, 59], "digit": 71, "disast": [15, 30, 68], "disclosur": 45, "discoveri": [0, 5, 23, 24, 29, 48, 57, 58, 61, 66, 69, 72, 75], "do": 65, "doc": 0, "docker": [3, 23, 24, 27, 64], "document": [0, 1, 2, 3, 14, 18, 19, 22, 26, 28, 38, 41, 42, 44, 49, 50, 60, 61, 77], "documentation_expansion_summari": 0, "doe": 65, "done": 21, "drill": 41, "driven": [21, 78], "dss": [40, 67], "dynam": [46, 51, 52], "ecosystem": [22, 26], "effect": 67, "effort": 25, "element": [3, 66], "emerg": [16, 17, 41], "enabl": [2, 7, 65, 71], "encrypt": [11, 13], "end": [2, 62], "endpoint": [4, 6, 7, 8, 12, 29, 39, 55, 56], "enforc": [11, 18], "engin": [4, 25, 29, 55, 60, 61, 73, 74, 77], "enhanc": [3, 22, 23, 24, 25, 28, 29, 35, 39, 46, 47, 48, 73], "enrich": [9, 39, 73], "enterpris": [3, 24, 25, 26, 50, 76], "entiti": 59, "enumer": 59, "environ": [0, 2, 13, 14, 16, 20, 27, 28, 33, 51, 60, 62, 64, 65], "erad": 43, "erasur": 38, "error": [5, 6, 8, 9, 10, 29, 49, 58, 64], "escal": [4, 17, 32, 77], "essenti": 36, "estim": 25, "event": [9, 11, 39, 73], "evid": 43, "evolut": 3, "exampl": [0, 3, 5, 6, 7, 8, 12, 49, 66, 69], "excel": [3, 15, 22, 26, 50, 68, 70, 71, 74, 75, 76, 77], "except": 63, "execut": [23, 24, 26, 35, 47, 48, 50, 61, 63, 70, 71, 72, 74, 75, 76, 77], "exercis": [67, 69, 72], "exist": [0, 23, 24, 25, 35], "expans": 0, "expect": 23, "experi": 0, "exploit": 75, "export": [6, 9], "extend": 43, "extens": 54, "extern": [16, 17, 41, 43, 66], "factor": [7, 36, 57, 65], "factori": 62, "failur": 17, "fals": 53, "faq": 65, "featur": [1, 2, 3, 20, 21, 22, 23, 24, 25, 26, 29, 35, 36, 55, 65, 66, 67, 68, 72, 78], "feedback": 72, "field": [8, 48], "file": [1, 30], "filter": 8, "final": 14, "financi": 10, "find": 19, "first": [2, 31, 36], "fix": [41, 47, 50, 52], "fixtur": [62, 63], "flow": [7, 21, 58], "focu": [0, 1], "forecast": 25, "fork": 19, "format": [1, 5, 8], "forum": 65, "foundat": 24, "fr": 61, "framework": [4, 12, 23, 24, 25, 28, 38, 39, 40, 42, 44, 46, 49, 50, 54, 57, 60, 61, 63, 67, 70, 71, 74, 75, 76, 77], "frequent": 65, "friendli": 1, "frontend": [2, 14, 20, 24, 64], "full": [2, 30], "function": [16, 61, 70], "futur": [0, 1, 3, 22, 29, 46, 50, 61, 65], "gap": [25, 65], "gate": [21, 46, 52, 62], "gather": 17, "gcp": 29, "gdpr": [10, 12, 28, 38, 40, 49], "gener": [9, 10, 17, 18, 47, 65, 72, 78], "get": [0, 1, 3, 5, 6, 9, 10, 17, 19, 20, 26, 27, 36, 64, 65, 68, 69, 72, 73, 77, 78], "git": 21, "github": [14, 53], "goal": [24, 35, 50], "govern": [3, 70, 71, 76], "grade": 3, "grafana": 32, "graph": [6, 57, 60, 61], "graphengin": 57, "green": [14, 15], "guid": [0, 1, 2, 3, 13, 16, 17, 18, 19, 20, 22, 26, 27, 29, 36, 63, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78], "guidelin": [4, 11, 12, 19, 34, 39, 44, 45, 52, 60, 62, 67, 69, 72], "handl": [5, 9, 10, 29, 43, 49, 58], "harbor": 45, "harden": [16, 48], "hash": 47, "health": [2, 16, 27, 31, 32, 64], "help": [3, 17, 19, 20, 27, 36, 64, 65, 68, 69, 72], "high": [0, 15, 17, 23, 25, 32, 41, 45], "highmemoryusag": 32, "hipaa": 10, "hook": 52, "horizont": 33, "hour": 41, "how": [45, 65], "http": [8, 16, 18], "i": 65, "iac": [28, 49], "ic": 41, "id": 38, "ident": 11, "identifi": 38, "ii": 40, "imag": [2, 14, 16], "immedi": [23, 25, 28, 35, 36, 41], "impact": [0, 3, 10, 12, 22, 23, 47, 48, 49, 50, 51, 78], "implement": [0, 4, 11, 12, 15, 23, 24, 25, 28, 35, 37, 38, 39, 40, 41, 47, 50, 51, 53, 55, 74, 76], "import": 5, "improv": [0, 3, 37, 40, 44, 46, 47, 48, 50, 51], "incid": [24, 25, 38, 40, 41, 43, 50, 56, 67, 69, 72, 77], "incorrect": 64, "increment": 29, "index": [0, 59], "indic": [24, 26, 33, 38, 48], "industri": [3, 22, 50], "inform": [9, 17, 38, 42, 45], "infrastructur": [14, 15, 16, 23, 25, 28, 34, 35, 37, 49], "ingress": 16, "initi": [16, 27, 36, 41, 68, 77], "innov": 71, "input": [49, 51], "instal": [16, 20, 27, 29, 36, 64, 65], "integr": [0, 3, 7, 8, 9, 13, 21, 23, 24, 25, 27, 29, 35, 36, 38, 39, 40, 46, 48, 49, 51, 52, 53, 56, 57, 60, 61, 62, 63, 64, 65, 66, 67, 68, 70, 72, 73, 78], "intellig": [8, 9, 13, 24, 35, 36, 40, 61, 72, 73, 77, 78], "interact": [2, 3, 24], "interfac": [36, 65, 69], "intern": [17, 41, 43], "interpret": 69, "introduct": 46, "inventori": 66, "invest": [50, 71], "investig": [41, 77], "ioc": [9, 73], "ir": 61, "ism": 38, "iso": [38, 40], "issu": [0, 2, 4, 11, 12, 16, 17, 19, 20, 27, 29, 33, 36, 39, 47, 64, 66, 68, 69], "j": 25, "javascript": [5, 7, 18, 19], "job": 29, "jsonb": 54, "june": [47, 50], "just": 4, "jwt": 7, "kei": [0, 1, 3, 22, 24, 33, 38, 40, 48, 50, 53, 67, 68], "kpi": [23, 35, 46, 48, 49, 52, 61], "kubectl": 16, "kubernet": [11, 14, 15, 16, 31, 32, 49], "latest": [26, 28], "layer": [9, 24, 37, 40, 50], "lead": 41, "leadership": [3, 22, 71, 72, 76], "leak": 17, "learn": [23, 24, 25, 36, 55], "least": [4, 28], "legal": [43, 45], "level": [29, 34, 40, 41, 43, 46, 48], "leverag": 35, "librari": [8, 23, 24, 25], "licens": 26, "lifecycl": 48, "limit": [5, 6, 8, 9, 10, 29], "list": [5, 10], "load": [16, 33, 34, 64, 78], "local": [2, 3, 14, 62], "log": [2, 13, 17, 28, 29, 31, 32, 37, 39, 49, 64, 65], "logic": [30, 51], "login": [7, 36, 64, 65], "logout": 7, "long": [28, 50, 65], "low": [0, 41, 45], "lower": 25, "machin": [23, 24, 25, 55], "mainten": [0, 1, 16, 31, 38, 59, 62, 68], "make": 19, "makefil": [51, 53], "manag": [2, 4, 5, 7, 8, 9, 11, 12, 14, 20, 21, 25, 29, 32, 36, 38, 46, 48, 50, 51, 53, 61, 62, 68, 70, 71, 72, 73, 77], "manifest": 49, "manual": [30, 49], "map": [6, 29, 39, 56, 57, 58], "matrix": [23, 25, 32, 38, 48], "matur": 40, "md": 0, "measur": [3, 22], "medium": [0, 23, 25, 35, 41, 45], "meet": 41, "member": [0, 72, 74, 75], "memori": [17, 64], "mermaid": 3, "messag": 21, "metadata": 5, "method": [27, 66], "methodologi": [34, 46, 57, 69, 74, 75, 76, 77, 78], "metric": [0, 1, 2, 3, 15, 17, 23, 24, 33, 35, 40, 43, 44, 46, 47, 48, 49, 50, 52, 53, 61, 62, 63, 75], "mfa": [7, 65], "migrat": [16, 17, 23, 35, 54], "minimum": 16, "minut": 41, "miss": [17, 64], "mission": 61, "mitig": [10, 78], "mitr": [6, 8, 9, 40, 57, 58, 61, 67, 72, 73], "ml": [24, 55], "mock": 63, "mode": [2, 29], "model": [8, 10, 21, 24, 25, 40, 46, 49, 54, 55, 56, 60, 61, 69, 72, 78], "modul": 25, "mondai": 32, "monitor": [2, 3, 4, 8, 10, 11, 12, 13, 15, 16, 17, 25, 29, 30, 32, 33, 34, 36, 37, 38, 39, 40, 41, 50, 52, 59, 60, 66, 68, 70, 77, 78], "month": 23, "monthli": 31, "morn": 32, "mortem": 41, "multi": [2, 7, 25, 29, 34, 36, 50, 57, 65, 66], "my": 65, "name": 21, "namespac": 16, "navig": [3, 9, 73], "neo4j": 34, "network": [11, 15, 16, 17, 29, 64, 66], "new": [0, 2, 3, 20, 23, 24, 25, 26, 65], "next": [1, 16, 20, 21, 23, 24, 25, 27, 28, 33, 34, 35, 36, 40, 62, 63, 66, 67], "nfr": 61, "nist": [38, 54], "non": 61, "note": [1, 26], "notif": [38, 41], "number": 47, "object": [24, 35, 38, 43, 50], "offens": [3, 75], "offic": [70, 72], "onboard": 77, "ongo": [16, 41], "ons": 16, "oper": [0, 3, 4, 5, 12, 14, 15, 26, 28, 34, 38, 39, 47, 50, 60, 68, 72, 74, 75, 77], "opportun": 0, "optim": [4, 11, 12, 15, 23, 24, 29, 31, 32, 33, 34, 57, 58, 59, 66, 67, 68, 69, 73, 74, 78], "option": 2, "orchestr": [35, 61], "organ": [0, 1, 18, 21, 38], "origin": 35, "out": 45, "overview": [0, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 21, 22, 23, 25, 26, 28, 29, 30, 31, 32, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 59, 60, 61, 62, 63, 66, 67, 68, 69, 72, 73, 78], "p0": [32, 41], "p1": [32, 38, 41], "p2": 41, "p3": 41, "pagin": 8, "parallel": 57, "paramet": 5, "parametr": 63, "partial": [24, 25], "password": [7, 36, 65], "patch": [31, 48, 63], "path": [0, 5, 6, 8, 36, 57, 58, 59, 61, 65, 67, 69, 72, 75, 77], "pattern": [9, 33, 49, 63, 73], "pci": [40, 67], "pend": 17, "perform": [0, 2, 3, 4, 6, 11, 12, 13, 14, 15, 16, 17, 20, 24, 26, 27, 28, 29, 31, 32, 33, 34, 35, 36, 38, 39, 48, 50, 51, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 71, 73, 75, 78], "period": 44, "permiss": [2, 7, 64, 68, 77], "persist": 17, "phase": [23, 24, 25, 35, 41, 43, 61], "philosophi": 62, "physic": 40, "pi1": 38, "pickl": 47, "pipelin": [2, 14, 39, 51, 52, 55, 62], "pitfal": [34, 44], "plan": [3, 23, 29, 31, 35, 67], "platform": [26, 36, 61, 65, 68, 70, 71, 74, 75, 76, 77], "pod": [16, 17], "point": 30, "polici": [4, 11, 16, 38, 45, 49], "popul": 54, "port": 2, "portabl": 38, "posit": 53, "post": [16, 41, 43], "postgresql": 34, "postur": 46, "powershel": 25, "pr": 38, "practic": [0, 1, 2, 4, 6, 7, 8, 11, 12, 13, 15, 21, 34, 39, 44, 51, 52, 53, 62, 63, 66, 67, 68, 69, 72, 73, 75, 76, 77, 78], "prd": [24, 61], "pre": [14, 30, 49, 52, 78], "predict": [24, 25, 55], "prepar": [14, 16, 70], "prepared": 43, "prerequisit": [2, 3, 14, 16, 20, 27, 29, 36, 69, 73, 78], "prevent": 31, "primari": [24, 51, 53, 77], "principl": [4, 11, 18, 37, 38, 42, 45, 63], "priorit": [48, 67], "prioriti": [0, 23, 25, 32, 35], "privaci": [12, 38], "privileg": [4, 28], "problem": [17, 64], "procedur": [16, 17, 30, 31, 32, 38, 41, 43, 44, 77], "process": [12, 14, 19, 21, 38, 41, 43, 44, 45, 49, 53, 55, 56, 57, 61], "product": [3, 13, 14, 15, 16, 23, 24, 25, 26, 27, 28, 60, 61], "profession": [0, 3, 45, 70, 71, 76, 77], "profil": [9, 20, 34, 36, 78], "program": [40, 45, 48, 50], "prometheu": 32, "proof": 39, "proposit": 61, "protect": [11, 29, 38, 47, 49, 61], "provid": [13, 36, 64, 65, 66], "provis": [4, 14, 16], "public": 45, "pull": 19, "purpl": [0, 67, 69, 72, 74], "purpos": 21, "push": [14, 16], "pytest": 63, "python": [5, 7, 18, 19, 25, 29], "q1": [23, 24, 25], "q2": 61, "q3": [23, 24, 25, 35, 50, 61], "q4": [23, 24, 25, 35, 50, 61], "qualit": 23, "qualiti": [0, 1, 3, 18, 20, 21, 26, 44, 52, 60, 62, 63], "quantit": [23, 76, 78], "queri": 5, "question": 65, "quick": [2, 3, 26, 27, 33, 36, 42, 64, 69], "r": 38, "radiu": [1, 3, 6, 14, 16, 17, 23, 24, 25, 26, 30, 31, 32, 38, 41, 49, 57, 58, 61, 65, 67, 69], "random": 47, "rate": [5, 6, 8, 9, 10], "rbac": [11, 68], "rc": 38, "readi": [14, 26, 28], "real": [11, 36, 39, 73, 77], "recent": 50, "recognit": [3, 19, 22, 45, 73], "recommend": [0, 2, 7, 27, 28, 33, 36, 78], "record": 12, "recov": 38, "recoveri": [15, 16, 17, 30, 41, 43, 58, 68], "rectif": 38, "red": [0, 67, 69, 72, 75], "redi": 34, "redirect": 16, "reduc": 35, "reduct": 47, "refer": [3, 8, 9, 10, 26, 42], "refresh": [6, 7], "regular": 41, "regulatori": [43, 46, 49, 50, 70, 71, 78], "relationship": [5, 59], "releas": [1, 21, 26, 28], "reliabl": 61, "remedi": [48, 52], "renew": 31, "report": [12, 38, 39, 43, 45, 48, 52, 53, 62, 66, 75, 77], "repositori": [14, 20], "request": [4, 16, 19], "requir": [14, 15, 16, 20, 21, 23, 25, 27, 41, 44, 49, 60, 61, 63, 65], "research": 45, "reset": 65, "resolut": 41, "resolv": 47, "resourc": [2, 8, 15, 17, 29, 33, 36, 41, 49, 60, 71, 72, 76, 77], "respond": 38, "respons": [3, 5, 6, 8, 17, 24, 25, 32, 40, 41, 43, 45, 49, 50, 64, 67, 69, 70, 71, 72, 74, 75, 76, 77], "rest": [18, 73], "restrict": 38, "result": [5, 6, 10, 33, 36, 46, 47, 50, 51, 53, 55, 66, 67], "retent": [32, 59], "review": [4, 14, 19, 21, 31, 32, 41, 44, 47, 49, 50], "reward": 45, "right": [12, 38], "risk": [4, 8, 10, 25, 29, 35, 46, 47, 48, 50, 57, 58, 61, 65, 67, 69, 70, 71, 76, 78], "roadmap": [22, 24, 28, 35, 50, 61, 65], "roi": 50, "role": [1, 3, 4, 36, 41, 68, 70, 71, 72, 74, 75, 76, 77], "rollback": [16, 17, 35], "rollout": 23, "room": 41, "rotat": 31, "routin": 31, "rst": 0, "rule": 53, "run": [2, 36, 62, 63, 65, 78], "runbook": [30, 32], "runtim": 52, "safe": 45, "sampl": 36, "sanit": 49, "sast": [46, 48, 52, 53], "scalabl": [0, 15, 24, 26, 33, 61], "scale": [24, 25, 33], "scan": [2, 47, 48, 49, 52], "scenario": [0, 6, 58, 61, 66, 67, 69, 74, 75, 77], "schedul": [31, 38, 66], "schema": [12, 18, 25, 39, 54, 59, 60], "scope": 45, "score": [29, 48, 57, 58, 65, 67, 69], "script": [14, 54], "sdk": [5, 8, 24, 25, 29], "sdlc": 48, "search": 3, "secret": [16, 49], "section": [1, 3, 42], "secur": [0, 1, 2, 3, 7, 9, 11, 13, 14, 15, 16, 17, 18, 21, 23, 24, 25, 26, 28, 29, 30, 31, 32, 35, 37, 38, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 52, 53, 60, 61, 62, 65, 66, 68, 69, 71, 72, 74, 75, 76, 78], "securitythreatspik": 32, "see": 65, "seed": 54, "select": 8, "seo": 3, "servic": [11, 12, 14, 17, 20, 23, 24, 29, 38, 39, 55, 56, 66, 71], "servicenow": [13, 36], "set": [13, 16, 68], "setup": [0, 2, 10, 14, 16, 20, 23, 27, 29, 33, 36, 41, 62, 65, 68, 77], "sever": [41, 43, 45], "short": [35, 50], "should": 65, "siem": [23, 24, 25, 61, 65, 67], "simul": [10, 67, 75, 78], "size": 33, "slow": 64, "slowli": 65, "snapshot": 30, "soa": 38, "soar": [61, 67], "soc": [0, 38, 40, 49, 72, 77], "softwar": [14, 20, 27], "solid": 18, "solut": 17, "sort": 8, "special": 75, "specif": [2, 13, 14, 15, 28, 41, 54, 55, 56, 72], "sphinx": 3, "sqlalchemi": 54, "ssl": [16, 17, 64], "stage": [2, 13, 14], "stakehold": 71, "standard": [0, 2, 3, 7, 18, 19, 21, 37, 50], "start": [0, 1, 2, 3, 5, 14, 19, 20, 26, 27, 29, 36, 68, 69, 73, 77, 78], "startup": [16, 17], "state": [17, 23, 35], "statement": [38, 61], "static": [46, 52, 53], "statist": [0, 3, 6], "statu": [5, 8, 9, 18, 24, 35, 41, 46, 48, 50], "step": [1, 16, 19, 20, 21, 24, 27, 28, 33, 34, 35, 36, 40, 49, 62, 63, 66, 67], "storag": 17, "strateg": [50, 70, 71, 74, 76], "strategi": [2, 10, 15, 21, 23, 34, 35, 39, 48, 54, 57, 58, 66, 78], "strength": 35, "stride": 44, "structur": [0, 1, 3, 7, 19, 39, 50, 54, 60, 63], "stuck": 17, "style": [3, 18, 19], "subject": [12, 38], "submit": 19, "success": [0, 8, 23, 24, 35, 61], "summari": [0, 1, 23, 24, 25, 26, 28, 35, 46, 47, 50, 61, 70, 71, 74, 75, 76, 77], "sundai": 31, "supervisori": 38, "support": [2, 3, 8, 17, 26, 29, 60, 64, 65, 68, 70, 71, 72, 74, 75, 76, 77], "sync": 9, "synchron": 73, "system": [3, 7, 12, 14, 16, 17, 20, 27, 30, 31, 32, 38, 39, 41, 57, 60, 65, 68], "t": [36, 65], "tabl": [5, 18, 19, 20, 21, 22, 26, 33, 34, 40, 54, 59, 62, 63, 64, 66, 67, 70, 71, 74, 75, 76, 77], "tag": 5, "take": 65, "tamper": 39, "tar": 61, "target": [24, 34, 35, 61], "task": [20, 55], "team": [0, 41, 43, 67, 69, 72, 74, 75], "technic": [0, 1, 3, 12, 23, 24, 25, 26, 28, 35, 40, 41, 54, 55, 56, 60, 61, 74, 75], "techniqu": [9, 73], "templat": [0, 4, 44, 49], "term": [28, 35, 45, 50], "terraform": [14, 49], "test": [0, 2, 8, 16, 18, 19, 20, 21, 26, 27, 30, 33, 37, 46, 47, 49, 50, 51, 52, 53, 60, 62, 63, 75], "thehiv": [23, 25, 56], "thi": 65, "threat": [8, 9, 10, 13, 24, 25, 36, 40, 44, 46, 49, 55, 61, 72, 73, 77, 78], "through": 23, "time": [4, 11, 17, 30, 36, 39, 50, 64, 73, 77], "timelin": [23, 24, 25, 35], "timeout": 17, "tip": [11, 72], "tl": [11, 16, 17, 64], "token": 7, "tool": [1, 2, 3, 14, 16, 17, 18, 20, 21, 23, 24, 25, 26, 30, 31, 32, 34, 38, 41, 46, 48, 49, 51, 52, 53, 65, 66], "tour": 36, "track": [9, 44, 48], "traefik": [23, 24], "train": [40, 41, 43, 49, 72, 77], "transform": 71, "tree": 58, "trigger": 44, "troubleshoot": [0, 1, 2, 4, 11, 12, 13, 16, 17, 20, 26, 27, 29, 33, 39, 64, 65, 66, 68, 69, 70, 74, 75], "trust": [11, 15, 28, 38, 40, 67], "tune": [34, 39, 60], "type": [5, 14, 19, 21, 29, 40, 44, 49, 59, 67, 69], "typescript": [7, 18, 19], "ui": 64, "understand": 36, "unit": [62, 63], "unsaf": 47, "up": 16, "updat": [5, 29, 31, 41, 45, 48, 65, 68], "url": [5, 6, 8, 9, 10], "us": [0, 1, 26, 65, 66, 67, 69], "usabl": [1, 61], "usag": [0, 4, 18, 29, 65], "user": [0, 1, 3, 8, 22, 24, 26, 27, 36, 61, 65, 68, 69, 72, 73, 78], "utc": 31, "v2": 24, "valid": [11, 13, 30, 37, 47, 49, 51, 67], "valu": [22, 50, 61], "variabl": [2, 16, 27, 64], "verif": [11, 16, 27, 30], "verifi": 14, "version": [21, 65], "vertic": 33, "view": 36, "vision": [24, 61], "visual": [24, 25, 36, 73, 77], "volum": [1, 17], "vulner": [41, 45, 46, 48, 50, 51, 53], "wai": 19, "war": 41, "weak": 47, "web": [36, 65, 69], "webhook": [8, 56], "week": [23, 24, 25, 35], "weekli": [31, 32], "what": 65, "who": 65, "why": 65, "win": 33, "window": 31, "won": 36, "work": 65, "workflow": [2, 3, 12, 14, 19, 20, 21, 24, 49, 68, 69, 72, 73, 77, 78], "workstat": 52, "write": [19, 62, 63], "you": 19, "your": [19, 36], "zero": [11, 15, 28, 40, 67]}})