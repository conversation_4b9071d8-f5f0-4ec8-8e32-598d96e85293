#!/bin/bash

# Automated Testing Pipeline for Blast-Radius Security Tool
# This script runs comprehensive automated tests for CI/CD pipeline

set -e

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TEST_ENVIRONMENT=${1:-staging}
TEST_TYPE=${2:-full}
PARALLEL_JOBS=${3:-4}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test result tracking
TESTS_PASSED=0
TESTS_FAILED=0
FAILED_TESTS=()

# Help function
show_help() {
    cat << EOF
Automated Testing Pipeline for Blast-Radius Security Tool

Usage: $0 [environment] [test-type] [parallel-jobs]

Arguments:
    environment   - Test environment (staging, production, local) [default: staging]
    test-type     - Type of tests to run (unit, integration, e2e, security, performance, full) [default: full]
    parallel-jobs - Number of parallel test jobs [default: 4]

Examples:
    $0                          # Run full test suite on staging
    $0 local unit               # Run unit tests locally
    $0 staging security         # Run security tests on staging
    $0 production e2e 8         # Run E2E tests on production with 8 parallel jobs

EOF
}

# Setup test environment
setup_test_environment() {
    log_info "Setting up test environment: $TEST_ENVIRONMENT"
    
    # Create test reports directory
    mkdir -p "$PROJECT_ROOT/test-reports"
    mkdir -p "$PROJECT_ROOT/test-reports/coverage"
    mkdir -p "$PROJECT_ROOT/test-reports/security"
    mkdir -p "$PROJECT_ROOT/test-reports/performance"
    
    # Set environment variables
    export TEST_ENVIRONMENT
    export PYTHONPATH="$PROJECT_ROOT/backend:$PYTHONPATH"
    
    # Install test dependencies if needed
    if [ "$TEST_ENVIRONMENT" = "local" ]; then
        cd "$PROJECT_ROOT/backend"
        pip install -r requirements/test.txt
        cd "$PROJECT_ROOT/frontend"
        npm install
    fi
    
    log_success "Test environment setup completed"
}

# Run unit tests
run_unit_tests() {
    log_info "Running unit tests..."
    
    local exit_code=0
    
    # Backend unit tests
    cd "$PROJECT_ROOT/backend"
    python -m pytest tests/unit/ \
        --cov=app \
        --cov-report=html:../test-reports/coverage/backend-unit \
        --cov-report=xml:../test-reports/coverage/backend-unit.xml \
        --cov-report=term-missing \
        --junitxml=../test-reports/backend-unit-results.xml \
        --maxfail=5 \
        -n $PARALLEL_JOBS \
        --tb=short || exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log_success "Backend unit tests passed"
        ((TESTS_PASSED++))
    else
        log_error "Backend unit tests failed"
        ((TESTS_FAILED++))
        FAILED_TESTS+=("backend-unit")
    fi
    
    # Frontend unit tests
    cd "$PROJECT_ROOT/frontend"
    npm run test:unit -- --coverage --watchAll=false --ci || {
        log_error "Frontend unit tests failed"
        ((TESTS_FAILED++))
        FAILED_TESTS+=("frontend-unit")
        exit_code=1
    }
    
    if [ $exit_code -eq 0 ]; then
        log_success "Frontend unit tests passed"
        ((TESTS_PASSED++))
    fi
    
    return $exit_code
}

# Run integration tests
run_integration_tests() {
    log_info "Running integration tests..."
    
    local exit_code=0
    
    # Backend integration tests
    cd "$PROJECT_ROOT/backend"
    python -m pytest tests/integration/ \
        --cov=app \
        --cov-report=html:../test-reports/coverage/backend-integration \
        --cov-report=xml:../test-reports/coverage/backend-integration.xml \
        --junitxml=../test-reports/backend-integration-results.xml \
        --maxfail=3 \
        -n $PARALLEL_JOBS \
        --tb=short || exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log_success "Backend integration tests passed"
        ((TESTS_PASSED++))
    else
        log_error "Backend integration tests failed"
        ((TESTS_FAILED++))
        FAILED_TESTS+=("backend-integration")
    fi
    
    # API integration tests
    cd "$PROJECT_ROOT"
    python -m pytest tests/api/ \
        --junitxml=test-reports/api-integration-results.xml \
        --maxfail=3 \
        --tb=short || {
        log_error "API integration tests failed"
        ((TESTS_FAILED++))
        FAILED_TESTS+=("api-integration")
        exit_code=1
    }
    
    if [ $exit_code -eq 0 ]; then
        log_success "API integration tests passed"
        ((TESTS_PASSED++))
    fi
    
    return $exit_code
}

# Run end-to-end tests
run_e2e_tests() {
    log_info "Running end-to-end tests..."
    
    local exit_code=0
    
    # Determine test URL based on environment
    local test_url
    case $TEST_ENVIRONMENT in
        "local")
            test_url="http://localhost:3000"
            ;;
        "staging")
            test_url="https://staging.blast-radius.com"
            ;;
        "production")
            test_url="https://blast-radius.com"
            ;;
        *)
            log_error "Unknown test environment: $TEST_ENVIRONMENT"
            return 1
            ;;
    esac
    
    # Frontend E2E tests
    cd "$PROJECT_ROOT/frontend"
    export CYPRESS_baseUrl="$test_url"
    npm run test:e2e -- --headless --record false || {
        log_error "Frontend E2E tests failed"
        ((TESTS_FAILED++))
        FAILED_TESTS+=("frontend-e2e")
        exit_code=1
    }
    
    if [ $exit_code -eq 0 ]; then
        log_success "Frontend E2E tests passed"
        ((TESTS_PASSED++))
    fi
    
    # API E2E tests
    cd "$PROJECT_ROOT"
    python -m pytest tests/e2e/ \
        --base-url="$test_url" \
        --junitxml=test-reports/e2e-results.xml \
        --maxfail=2 \
        --tb=short || {
        log_error "API E2E tests failed"
        ((TESTS_FAILED++))
        FAILED_TESTS+=("api-e2e")
        exit_code=1
    }
    
    if [ $exit_code -eq 0 ]; then
        log_success "API E2E tests passed"
        ((TESTS_PASSED++))
    fi
    
    return $exit_code
}

# Run security tests
run_security_tests() {
    log_info "Running security tests..."
    
    local exit_code=0
    
    # Static security analysis
    cd "$PROJECT_ROOT/backend"
    bandit -r app/ -f json -o ../test-reports/security/bandit-report.json || {
        log_warning "Bandit found security issues"
    }
    
    # Dependency vulnerability scan
    safety check --json --output ../test-reports/security/safety-report.json || {
        log_warning "Safety found vulnerable dependencies"
    }
    
    # Secret scanning
    cd "$PROJECT_ROOT"
    truffleHog --regex --entropy=False . --json > test-reports/security/secrets-scan.json || {
        log_warning "TruffleHog found potential secrets"
    }
    
    # Security-focused tests
    cd "$PROJECT_ROOT/backend"
    python -m pytest tests/security/ \
        --junitxml=../test-reports/security-test-results.xml \
        --maxfail=1 \
        --tb=short || {
        log_error "Security tests failed"
        ((TESTS_FAILED++))
        FAILED_TESTS+=("security")
        exit_code=1
    }
    
    if [ $exit_code -eq 0 ]; then
        log_success "Security tests passed"
        ((TESTS_PASSED++))
    fi
    
    return $exit_code
}

# Run performance tests
run_performance_tests() {
    log_info "Running performance tests..."
    
    local exit_code=0
    
    # Determine test URL
    local test_url
    case $TEST_ENVIRONMENT in
        "local")
            test_url="http://localhost:8000"
            ;;
        "staging")
            test_url="https://api.staging.blast-radius.com"
            ;;
        "production")
            test_url="https://api.blast-radius.com"
            ;;
    esac
    
    # Load testing with Locust
    cd "$PROJECT_ROOT"
    locust -f tests/performance/locustfile.py \
        --host="$test_url" \
        --users=50 \
        --spawn-rate=5 \
        --run-time=300s \
        --headless \
        --html=test-reports/performance/load-test-report.html \
        --csv=test-reports/performance/load-test || {
        log_error "Load tests failed"
        ((TESTS_FAILED++))
        FAILED_TESTS+=("performance-load")
        exit_code=1
    }
    
    # Database performance tests
    cd "$PROJECT_ROOT/backend"
    python -m pytest tests/performance/ \
        --junitxml=../test-reports/performance-test-results.xml \
        --maxfail=2 \
        --tb=short || {
        log_error "Performance tests failed"
        ((TESTS_FAILED++))
        FAILED_TESTS+=("performance")
        exit_code=1
    }
    
    if [ $exit_code -eq 0 ]; then
        log_success "Performance tests passed"
        ((TESTS_PASSED++))
    fi
    
    return $exit_code
}

# Generate test report
generate_test_report() {
    log_info "Generating test report..."
    
    local report_file="$PROJECT_ROOT/test-reports/test-summary.html"
    local total_tests=$((TESTS_PASSED + TESTS_FAILED))
    local success_rate=0
    
    if [ $total_tests -gt 0 ]; then
        success_rate=$(( (TESTS_PASSED * 100) / total_tests ))
    fi
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Blast-Radius Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
        .success { color: green; }
        .failure { color: red; }
        .warning { color: orange; }
        .metric { margin: 10px 0; }
        .failed-tests { background-color: #ffe6e6; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Blast-Radius Test Report</h1>
        <p><strong>Environment:</strong> $TEST_ENVIRONMENT</p>
        <p><strong>Test Type:</strong> $TEST_TYPE</p>
        <p><strong>Generated:</strong> $(date)</p>
    </div>
    
    <h2>Test Summary</h2>
    <div class="metric">Total Tests: $total_tests</div>
    <div class="metric success">Passed: $TESTS_PASSED</div>
    <div class="metric failure">Failed: $TESTS_FAILED</div>
    <div class="metric">Success Rate: $success_rate%</div>
    
EOF
    
    if [ ${#FAILED_TESTS[@]} -gt 0 ]; then
        cat >> "$report_file" << EOF
    <h2>Failed Tests</h2>
    <div class="failed-tests">
        <ul>
EOF
        for test in "${FAILED_TESTS[@]}"; do
            echo "            <li class=\"failure\">$test</li>" >> "$report_file"
        done
        cat >> "$report_file" << EOF
        </ul>
    </div>
EOF
    fi
    
    cat >> "$report_file" << EOF
    
    <h2>Detailed Reports</h2>
    <ul>
        <li><a href="coverage/backend-unit/index.html">Backend Unit Test Coverage</a></li>
        <li><a href="coverage/backend-integration/index.html">Backend Integration Test Coverage</a></li>
        <li><a href="security/bandit-report.json">Security Scan Report</a></li>
        <li><a href="performance/load-test-report.html">Performance Test Report</a></li>
    </ul>
</body>
</html>
EOF
    
    log_success "Test report generated: $report_file"
}

# Main function
main() {
    log_info "Starting automated testing pipeline..."
    log_info "Environment: $TEST_ENVIRONMENT"
    log_info "Test Type: $TEST_TYPE"
    log_info "Parallel Jobs: $PARALLEL_JOBS"
    
    setup_test_environment
    
    case $TEST_TYPE in
        "unit")
            run_unit_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "e2e")
            run_e2e_tests
            ;;
        "security")
            run_security_tests
            ;;
        "performance")
            run_performance_tests
            ;;
        "full")
            run_unit_tests
            run_integration_tests
            run_security_tests
            if [ "$TEST_ENVIRONMENT" != "local" ]; then
                run_e2e_tests
                run_performance_tests
            fi
            ;;
        *)
            log_error "Unknown test type: $TEST_TYPE"
            show_help
            exit 1
            ;;
    esac
    
    generate_test_report
    
    # Summary
    log_info "Testing pipeline completed"
    log_info "Tests passed: $TESTS_PASSED"
    log_info "Tests failed: $TESTS_FAILED"
    
    if [ $TESTS_FAILED -gt 0 ]; then
        log_error "Some tests failed. Check the detailed reports."
        exit 1
    else
        log_success "All tests passed!"
        exit 0
    fi
}

# Cleanup function
cleanup() {
    # Kill any background processes
    jobs -p | xargs -r kill 2>/dev/null || true
}

trap cleanup EXIT

# Check if help is requested
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# Make script executable
chmod +x "$0"

# Run main function
main "$@"
