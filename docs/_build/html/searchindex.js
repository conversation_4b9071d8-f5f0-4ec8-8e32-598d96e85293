Search.setIndex({"alltitles": {"1. AWS Configuration": [[14, "aws-configuration"]], "1. Build and Push Images": [[14, "build-and-push-images"]], "1. Change Impact Assessment": [[51, "change-impact-assessment"]], "1. Clone Repository": [[14, "clone-repository"]], "1. Code Security Review": [[51, "code-security-review"]], "1. Database & Models \u2705 Implemented": [[25, "database-models-implemented"]], "1. Feature Engineering Pipeline": [[57, "feature-engineering-pipeline"]], "1. Final Infrastructure Review": [[14, "final-infrastructure-review"]], "1. Information Gathering": [[17, "information-gathering"]], "1. Input Validation and Sanitization": [[51, "input-validation-and-sanitization"]], "1. Machine Learning & Analytics \ud83c\udd95": [[26, "machine-learning-analytics"]], "1. Machine Learning Infrastructure": [[24, "machine-learning-infrastructure"]], "1. Performance & Scalability (docs/performance/index.rst)": [[0, "performance-scalability-docs-performance-index-rst"]], "1. TheHive API Connector": [[58, "thehive-api-connector"]], "1. Threat Modeling": [[51, "threat-modeling"]], "1. Weak Cryptographic Hash \u2705 FIXED": [[49, "weak-cryptographic-hash-fixed"]], "1. compliance_frameworks": [[56, "compliance-frameworks"]], "1.1 Configure AWS CLI": [[16, "configure-aws-cli"]], "1.1 Incident Detection": [[43, "incident-detection"]], "1.2 Initial Assessment": [[43, "initial-assessment"]], "1.2 Set Environment Variables": [[16, "set-environment-variables"]], "1.3 Team Notification": [[43, "team-notification"]], "10.1 Database Backup": [[16, "database-backup"]], "10.2 Application Data Backup": [[16, "application-data-backup"]], "11.1 Set Up Alerts": [[16, "set-up-alerts"]], "11.2 Performance Monitoring": [[16, "performance-monitoring"]], "2. Authentication and Authorization": [[51, "authentication-and-authorization"]], "2. Compliance Automation Framework": [[24, "compliance-automation-framework"]], "2. Compliance Automation \ud83c\udd95": [[26, "compliance-automation"]], "2. Deploy to Staging": [[14, "deploy-to-staging"]], "2. Documentation Requirements": [[51, "documentation-requirements"]], "2. Environment Configuration": [[14, "environment-configuration"]], "2. Incident to Case Mapping Service": [[58, "incident-to-case-mapping-service"]], "2. Infrastructure Security Review": [[51, "infrastructure-security-review"]], "2. Log Analysis": [[17, "log-analysis"]], "2. ML Model Implementation": [[57, "ml-model-implementation"]], "2. Production Image Preparation": [[14, "production-image-preparation"]], "2. Security Architecture Patterns": [[51, "security-architecture-patterns"]], "2. Service Layer \u2705 Partially Implemented": [[25, "service-layer-partially-implemented"]], "2. Terraform Deployment": [[14, "terraform-deployment"]], "2. Testing & Quality Assurance (docs/testing/index.rst)": [[0, "testing-quality-assurance-docs-testing-index-rst"]], "2. Unsafe Pickle Deserialization \u2705 FIXED": [[49, "unsafe-pickle-deserialization-fixed"]], "2. compliance_domains": [[56, "compliance-domains"]], "2.1 Deploy Core Infrastructure": [[16, "deploy-core-infrastructure"]], "2.1 Incident War Room Setup": [[43, "incident-war-room-setup"]], "2.2 Configure kubectl Access": [[16, "configure-kubectl-access"]], "2.2 Technical Investigation": [[43, "technical-investigation"]], "2.3 Install Cluster Add-ons": [[16, "install-cluster-add-ons"]], "2.3 Security Assessment (if applicable)": [[43, "security-assessment-if-applicable"]], "2.5.1 Compliance Framework Enhancement": [[25, "compliance-framework-enhancement"]], "2.5.2 ML Threat Prediction Integration": [[25, "ml-threat-prediction-integration"]], "2.5.3 Enhanced Asset Intelligence": [[25, "enhanced-asset-intelligence"]], "3. API Framework \u2705 Implemented": [[25, "api-framework-implemented"]], "3. Architecture Security Review": [[51, "architecture-security-review"]], "3. Backend Setup": [[14, "backend-setup"]], "3. Batch Processing Service": [[57, "batch-processing-service"]], "3. Blue-Green Deployment": [[14, "blue-green-deployment"]], "3. Configure Staging Data": [[14, "configure-staging-data"]], "3. Data Protection": [[51, "data-protection"]], "3. Development Environment Setup (docs/development/setup.rst)": [[0, "development-environment-setup-docs-development-setup-rst"]], "3. Integration Security": [[51, "integration-security"]], "3. Kubernetes Configuration": [[14, "kubernetes-configuration"]], "3. Network Connectivity": [[17, "network-connectivity"]], "3. SIEM Integration (TheHive)": [[24, "siem-integration-thehive"]], "3. SIEM Integration (TheHive) \ud83c\udd95": [[26, "siem-integration-thehive"]], "3. Weak Random Number Generation \u2705 FIXED": [[49, "weak-random-number-generation-fixed"]], "3. Webhook Processing": [[58, "webhook-processing"]], "3. compliance_controls": [[56, "compliance-controls"]], "3.1 Database Initialization": [[16, "database-initialization"]], "3.1 Fix Implementation": [[43, "fix-implementation"]], "3.1 Machine Learning Foundation (Weeks 1-3)": [[25, "machine-learning-foundation-weeks-1-3"]], "3.2 Database Migration": [[16, "database-migration"]], "3.2 SIEM Integration & Incident Response (Weeks 4-6)": [[25, "siem-integration-incident-response-weeks-4-6"]], "3.2 System Recovery": [[43, "system-recovery"]], "3.3 Advanced Visualization & Analytics (Weeks 7-8)": [[25, "advanced-visualization-analytics-weeks-7-8"]], "4. Asset Discovery Use Cases (docs/use-cases/asset-discovery.rst)": [[0, "asset-discovery-use-cases-docs-use-cases-asset-discovery-rst"]], "4. Compliance Review": [[51, "compliance-review"]], "4. Database Models": [[58, "database-models"]], "4. Database Models for ML Results": [[57, "database-models-for-ml-results"]], "4. Error Handling and Logging": [[51, "error-handling-and-logging"]], "4. Frontend Setup": [[14, "frontend-setup"]], "4. Incident Response Automation \ud83c\udd95": [[26, "incident-response-automation"]], "4. Machine Learning & Analytics \ud83c\udd95 New": [[25, "machine-learning-analytics-new"]], "4. compliance_assessments": [[56, "compliance-assessments"]], "4.1 Build and Push Images": [[16, "build-and-push-images"]], "4.1 Client SDK Development (Weeks 1-4)": [[25, "client-sdk-development-weeks-1-4"]], "4.1 Status Updates": [[43, "status-updates"]], "4.2 Interactive Visualization (Weeks 5-6)": [[25, "interactive-visualization-weeks-5-6"]], "4.2 Resolution Communication": [[43, "resolution-communication"]], "4.3 Automated Response Workflows (Weeks 7-8)": [[25, "automated-response-workflows-weeks-7-8"]], "5. Client Libraries \ud83c\udd95": [[26, "client-libraries"]], "5. Compliance Automation \ud83c\udd95 New": [[25, "compliance-automation-new"]], "5. Cryptography and Secrets": [[51, "cryptography-and-secrets"]], "5. Start Development Services": [[14, "start-development-services"]], "5. Troubleshooting Guide (docs/troubleshooting/common-issues.rst)": [[0, "troubleshooting-guide-docs-troubleshooting-common-issues-rst"]], "5. compliance_control_assessments": [[56, "compliance-control-assessments"]], "5.1 Create Namespace and Secrets": [[16, "create-namespace-and-secrets"]], "5.1 Performance & Scalability": [[25, "performance-scalability"]], "5.1 Post-Mortem Meeting": [[43, "post-mortem-meeting"]], "5.2 Deploy Application Components": [[16, "deploy-application-components"]], "5.2 Documentation": [[43, "documentation"]], "5.2 Enterprise Features": [[25, "enterprise-features"]], "5.3 Configure Ingress and Load Balancer": [[16, "configure-ingress-and-load-balancer"]], "6. Documentation Expansion Summary (docs/DOCUMENTATION_EXPANSION_SUMMARY.md)": [[0, "documentation-expansion-summary-docs-documentation-expansion-summary-md"]], "6. Enhanced Visualization \ud83c\udd95": [[26, "enhanced-visualization"]], "6. SIEM Integration \ud83c\udd95 New": [[25, "siem-integration-new"]], "6. Verify Development Setup": [[14, "verify-development-setup"]], "6. compliance_control_mappings": [[56, "compliance-control-mappings"]], "6.1 Request SSL Certificate": [[16, "request-ssl-certificate"]], "6.2 Configure HTTPS Redirect": [[16, "configure-https-redirect"]], "7. Visualization & Frontend \ud83c\udd95 New": [[25, "visualization-frontend-new"]], "7.1 Initialize Application Data": [[16, "initialize-application-data"]], "7.2 Configure Monitoring and Alerting": [[16, "configure-monitoring-and-alerting"]], "8. Client Libraries \ud83c\udd95 New": [[25, "client-libraries-new"]], "8.1 Network Policies": [[16, "network-policies"]], "8.2 Pod Security Policies": [[16, "pod-security-policies"]], "9.1 Health Checks": [[16, "health-checks"]], "9.2 Functional Testing": [[16, "functional-testing"]], "9.3 Security Testing": [[16, "security-testing"]], "A.10 - Cryptography": [[40, "a-10-cryptography"]], "A.12 - Operations Security": [[40, "a-12-operations-security"]], "A.13 - Communications Security": [[40, "a-13-communications-security"]], "A.14 - System Acquisition, Development and Maintenance": [[40, "a-14-system-acquisition-development-and-maintenance"]], "A.16 - Information Security Incident Management": [[40, "a-16-information-security-incident-management"]], "A.17 - Business Continuity Management": [[40, "a-17-business-continuity-management"]], "A.18 - Compliance": [[40, "a-18-compliance"]], "A.5 - Information Security Policies": [[40, "a-5-information-security-policies"]], "A.6 - Organization of Information Security": [[40, "a-6-organization-of-information-security"]], "A.8 - Asset Management": [[40, "a-8-asset-management"]], "A.9 - Access Control": [[40, "a-9-access-control"]], "AI-Driven Security Recommendations": [[80, "ai-driven-security-recommendations"]], "API Development": [[20, "api-development"], [62, "api-development"]], "API Discovery": [[68, "api-discovery"]], "API Documentation": [[27, "api-documentation"]], "API Documentation Section": [[1, "api-documentation-section"]], "API Endpoints": [[4, "api-endpoints"], [7, "api-endpoints"], [8, "api-endpoints"], [12, "api-endpoints"], [30, "api-endpoints"], [41, "api-endpoints"]], "API Integration": [[75, "api-integration"]], "API Reference": [[8, null], [27, null]], "API Reference (api/)": [[3, "api-reference-api"]], "API Standards": [[18, "api-standards"]], "ATT&CK Data Management": [[9, "att-ck-data-management"]], "ATT&CK Navigator Integration": [[9, "att-ck-navigator-integration"], [75, "att-ck-navigator-integration"]], "AWS Access Requirements": [[16, "aws-access-requirements"]], "AWS Authentication": [[30, "aws-authentication"]], "AWS Configuration": [[30, "aws-configuration"]], "AWS SDK": [[30, "aws-sdk"]], "AWS Service Coverage": [[68, "id1"]], "AWS Services": [[30, "aws-services"]], "Accelerated Development Timeline": [[36, "accelerated-development-timeline"]], "Access Analytics Dashboard": [[4, "access-analytics-dashboard"]], "Access Control Architecture": [[4, "access-control-architecture"]], "Access Control Settings": [[13, "access-control-settings"]], "Access Management Principles": [[4, "access-management-principles"]], "Access Request Management": [[4, "access-request-management"]], "Access Review and Certification": [[4, "access-review-and-certification"]], "Access Usage Monitoring": [[4, "access-usage-monitoring"]], "Adding New Documentation": [[3, "adding-new-documentation"]], "Adding New Features": [[20, "adding-new-features"]], "Additional Documentation (Future)": [[1, "additional-documentation-future"]], "Administrative Controls": [[42, "administrative-controls"], [42, "id2"]], "Administrative Workflow": [[70, "administrative-workflow"]], "Administrator Dashboard Features": [[70, "administrator-dashboard-features"]], "Administrators": [[74, "administrators"]], "Administrators Guide": [[70, null]], "Advanced Analysis Features": [[69, "advanced-analysis-features"]], "Advanced Analytics": [[25, "advanced-analytics"], [75, "advanced-analytics"]], "Advanced Analytics and Executive Communication Framework": [[78, "advanced-analytics-and-executive-communication-framework"]], "Advanced Attack Path Discovery and Exploitation Framework": [[77, "advanced-attack-path-discovery-and-exploitation-framework"]], "Advanced Attack Path Visualization and Analysis": [[79, "advanced-attack-path-visualization-and-analysis"]], "Advanced Attack Simulation and Specialized Testing Scenarios": [[77, "advanced-attack-simulation-and-specialized-testing-scenarios"]], "Advanced Build System": [[3, "advanced-build-system"]], "Advanced Compliance Platform Capabilities": [[72, "advanced-compliance-platform-capabilities"]], "Advanced Configuration": [[38, "advanced-configuration"]], "Advanced Detection Engineering and Security Control Optimization": [[76, "advanced-detection-engineering-and-security-control-optimization"]], "Advanced Discovery Features": [[68, "advanced-discovery-features"]], "Advanced Escalation Management and Communication Framework": [[79, "advanced-escalation-management-and-communication-framework"]], "Advanced Executive Security Platform Capabilities": [[73, "advanced-executive-security-platform-capabilities"]], "Advanced Features": [[80, "advanced-features"]], "Advanced Incident Investigation Methodologies": [[79, "advanced-incident-investigation-methodologies"]], "Advanced Optimizations": [[34, "advanced-optimizations"]], "Advanced Platform Capabilities for Security Architecture": [[78, "advanced-platform-capabilities-for-security-architecture"]], "Advanced Purple Team Platform Capabilities": [[76, "advanced-purple-team-platform-capabilities"]], "Advanced Real-time Monitoring and Threat Detection": [[79, "advanced-real-time-monitoring-and-threat-detection"]], "Advanced Red Team Platform Capabilities": [[77, "advanced-red-team-platform-capabilities"]], "Advanced Regulatory Framework Management": [[72, "advanced-regulatory-framework-management"]], "Advanced Reporting and Documentation Framework": [[79, "advanced-reporting-and-documentation-framework"]], "Advanced Reporting and Performance Metrics Framework": [[77, "advanced-reporting-and-performance-metrics-framework"]], "Advanced Risk Assessment and Management Framework": [[72, "advanced-risk-assessment-and-management-framework"]], "Advanced Risk Assessment and Quantitative Analysis Framework": [[78, "advanced-risk-assessment-and-quantitative-analysis-framework"]], "Advanced Security Architecture Design and Implementation": [[78, "advanced-security-architecture-design-and-implementation"]], "Advanced Support and Advisory Services": [[73, "advanced-support-and-advisory-services"]], "Advanced Support and Troubleshooting Framework": [[72, "advanced-support-and-troubleshooting-framework"]], "Advanced Troubleshooting and Technical Support": [[77, "advanced-troubleshooting-and-technical-support"]], "Advanced Troubleshooting and Technical Support Framework": [[76, "advanced-troubleshooting-and-technical-support-framework"]], "After Expansion:": [[0, "after-expansion"]], "Agent-Based Discovery": [[68, "agent-based-discovery"]], "Alembic Migration Script": [[56, "alembic-migration-script"]], "Alert Configuration": [[41, "alert-configuration"]], "Alert Escalation Matrix": [[33, "alert-escalation-matrix"]], "Alert Response Procedures": [[33, "alert-response-procedures"]], "Algorithm Optimization": [[35, "algorithm-optimization"]], "Analysis Configuration": [[71, "analysis-configuration"]], "Analysis Guidelines": [[69, "analysis-guidelines"]], "Analysis Types": [[69, "analysis-types"]], "Analytics and Monitoring": [[3, "analytics-and-monitoring"]], "Analyze Attack Paths": [[6, "analyze-attack-paths"]], "Analyze Attack Patterns": [[9, "analyze-attack-patterns"]], "Application Architecture": [[15, "application-architecture"]], "Application Data Backup": [[31, "application-data-backup"]], "Application Deployment": [[14, "application-deployment"], [16, "application-deployment"]], "Application Health Check": [[33, "application-health-check"]], "Application Monitoring": [[2, "application-monitoring"]], "Application Optimization": [[34, "application-optimization"], [35, "application-optimization"]], "Application Recovery": [[31, "application-recovery"]], "Application Security Layer": [[39, "application-security-layer"]], "Application Settings": [[13, "application-settings"]], "Application Startup Issues": [[17, "application-startup-issues"]], "Application Updates": [[32, "application-updates"]], "Architecture": [[27, null], [30, "architecture"]], "Architecture & Design": [[27, "architecture-design"]], "Architecture Documentation Requirements": [[46, "id4"]], "Architecture Principles": [[39, "architecture-principles"]], "Architecture Review Areas": [[46, "id2"]], "Architecture Review Process": [[46, "architecture-review-process"]], "Architecture Security Review": [[51, "id2"]], "Architecture Security Reviews": [[46, "architecture-security-reviews"]], "Architecture and Design": [[62, "architecture-and-design"]], "Article 15 - Right of Access \u2705": [[40, "article-15-right-of-access"]], "Article 16 - Right to Rectification \u2705": [[40, "article-16-right-to-rectification"]], "Article 17 - Right to Erasure \u2705": [[40, "article-17-right-to-erasure"]], "Article 18 - Right to Restriction \u2705": [[40, "article-18-right-to-restriction"]], "Article 20 - Right to Data Portability \u2705": [[40, "article-20-right-to-data-portability"]], "Article 21 - Right to Object \u2705": [[40, "article-21-right-to-object"]], "Article 33 - Notification to Supervisory Authority": [[40, "article-33-notification-to-supervisory-authority"]], "Article 34 - Communication to Data Subject": [[40, "article-34-communication-to-data-subject"]], "Article 5 - Principles of Processing": [[40, "article-5-principles-of-processing"]], "Assess Risk": [[10, "assess-risk"]], "Assessment Methodology": [[48, "assessment-methodology"]], "Assessment Results Summary": [[48, "assessment-results-summary"]], "Asset CRUD Operations": [[5, "asset-crud-operations"]], "Asset Data Models": [[62, "asset-data-models"]], "Asset Discovery": [[5, "asset-discovery"]], "Asset Discovery Use Cases": [[68, null]], "Asset Discovery and Management": [[74, "asset-discovery-and-management"]], "Asset Management": [[8, "asset-management"], [38, "asset-management"]], "Asset Management API": [[5, null]], "Asset Management \u2705": [[26, "asset-management"]], "Asset Relationships": [[5, "asset-relationships"]], "Asset Tags and Metadata": [[5, "asset-tags-and-metadata"]], "Asset Type Mapping": [[30, "asset-type-mapping"]], "Asset Types and Risk Assessment": [[30, "asset-types-and-risk-assessment"]], "Attack Path Analysis": [[8, "attack-path-analysis"], [67, "attack-path-analysis"], [74, "attack-path-analysis"]], "Attack Path Analysis API": [[6, null]], "Attack Path Analysis Architecture": [[59, null]], "Attack Path Analysis Flows": [[60, null]], "Attack Path Analysis Schema": [[61, "attack-path-analysis-schema"]], "Attack Path Analysis Tables": [[61, "attack-path-analysis-tables"]], "Attack Path Analysis Use Cases": [[69, null]], "Attack Path Analysis User Guide": [[71, null]], "Attack Path Discovery": [[71, "attack-path-discovery"]], "Attack Path Discovery Flow": [[60, "attack-path-discovery-flow"]], "Attack Path Intelligence Engine": [[79, "attack-path-intelligence-engine"]], "Attack Path Types": [[71, "attack-path-types"]], "Attack Pattern Analysis": [[9, "attack-pattern-analysis"], [75, "attack-pattern-analysis"]], "Attack Scenario Creation": [[71, "attack-scenario-creation"]], "Attack Scenario Creation Flow": [[60, "attack-scenario-creation-flow"]], "Attack Scenario Modeling": [[71, "attack-scenario-modeling"]], "Attack Simulation": [[80, "attack-simulation"]], "AttackPathAnalyzer": [[59, "attackpathanalyzer"]], "Attribute Threat Actor": [[9, "attribute-threat-actor"]], "Audit Event Structure": [[41, "audit-event-structure"]], "Audit Monitoring Dashboard": [[41, "audit-monitoring-dashboard"]], "Audit Service Architecture": [[41, "audit-service-architecture"]], "Audit Strategy": [[41, "audit-strategy"]], "Authentication": [[5, "authentication"], [6, "authentication"], [8, "authentication"], [9, "authentication"], [10, "authentication"]], "Authentication & Authorization Issues": [[66, "authentication-authorization-issues"]], "Authentication & User Management": [[8, "authentication-user-management"]], "Authentication API": [[7, null]], "Authentication Endpoints": [[7, "authentication-endpoints"]], "Authentication Flow": [[7, "authentication-flow"]], "Authentication Security Tests": [[53, "authentication-security-tests"]], "Authentication Settings": [[13, "authentication-settings"]], "Authentication Setup": [[30, "authentication-setup"]], "Authorization Security Tests": [[53, "authorization-security-tests"]], "Auto-fix Capabilities": [[54, "auto-fix-capabilities"]], "Automated Assessment Engine": [[26, "automated-assessment-engine"]], "Automated Attribution Engine": [[75, "automated-attribution-engine"]], "Automated Checks": [[18, "automated-checks"]], "Automated Compliance Checks": [[12, "automated-compliance-checks"]], "Automated Compliance Monitoring and Management": [[72, "automated-compliance-monitoring-and-management"]], "Automated DAST Pipeline": [[53, "automated-dast-pipeline"]], "Automated Daily Backup": [[31, "automated-daily-backup"]], "Automated Health Checks": [[33, "automated-health-checks"]], "Automated Remediation": [[54, "automated-remediation"]], "Automated Security Scanning": [[2, "automated-security-scanning"], [51, "automated-security-scanning"]], "Automated Visualization": [[75, "automated-visualization"]], "Automated Vulnerability Scanning": [[50, "automated-vulnerability-scanning"]], "Automatic Data Synchronization": [[75, "automatic-data-synchronization"]], "Automation Tools": [[21, "automation-tools"]], "Availability (A1)": [[40, "availability-a1"]], "Azure Authentication": [[30, "azure-authentication"]], "Azure Configuration": [[30, "azure-configuration"]], "Azure SDK": [[30, "azure-sdk"]], "Azure Services": [[30, "azure-services"]], "Backend Debugging": [[20, "backend-debugging"]], "Backend Tests": [[2, "backend-tests"]], "Backup Monitoring and Validation": [[31, "backup-monitoring-and-validation"]], "Backup Procedures": [[31, "backup-procedures"]], "Backup Verification": [[31, "backup-verification"]], "Backup and Disaster Recovery": [[70, "backup-and-disaster-recovery"]], "Backup and Recovery Runbooks - Blast-Radius Security Tool": [[31, null]], "Backup and Recovery Setup": [[16, "backup-and-recovery-setup"]], "Backup and Recovery Strategy": [[15, "backup-and-recovery-strategy"]], "Base Model with Compliance Extensions": [[56, "base-model-with-compliance-extensions"]], "Base URL": [[5, "base-url"], [6, "base-url"], [8, "base-url"], [9, "base-url"], [10, "base-url"]], "Basic ATT&CK Integration Workflow": [[75, "basic-att-ck-integration-workflow"]], "Basic Attack Path Analysis": [[71, "basic-attack-path-analysis"]], "Basic Commands": [[3, "basic-commands"]], "Basic Configuration": [[38, "basic-configuration"]], "Basic Test Structure": [[65, "basic-test-structure"]], "Basic Threat Modeling Workflow": [[80, "basic-threat-modeling-workflow"]], "Batch Correlate Events": [[9, "batch-correlate-events"]], "Before Expansion:": [[0, "before-expansion"]], "Before You Start": [[19, "before-you-start"]], "Behavioral Analytics": [[75, "behavioral-analytics"]], "Benchmark Results": [[34, "benchmark-results"]], "Best Practices": [[2, "best-practices"], [4, "best-practices"], [6, "best-practices"], [7, "best-practices"], [8, "best-practices"], [11, "best-practices"], [12, "best-practices"], [13, "best-practices"], [15, "best-practices"], [21, "best-practices"], [35, "best-practices"], [41, "best-practices"], [55, "best-practices"], [64, "best-practices"], [65, "best-practices"], [68, "best-practices"], [69, "best-practices"], [71, "best-practices"], [74, "best-practices"], [75, "best-practices"], [80, "best-practices"]], "Best Practices and Guidelines": [[46, "best-practices-and-guidelines"], [54, "best-practices-and-guidelines"]], "Best Practices for Platform Administration": [[70, "best-practices-for-platform-administration"]], "Blast Radius Algorithm": [[59, "blast-radius-algorithm"]], "Blast Radius Analysis": [[71, "blast-radius-analysis"]], "Blast Radius Assessment": [[69, "id4"]], "Blast Radius Calculation": [[71, "blast-radius-calculation"]], "Blast Radius Calculation Flow": [[60, "blast-radius-calculation-flow"]], "Blast-Radius Security Tool - Complete User Guide Documentation": [[1, null]], "Blast-Radius Security Tool - Enhanced PRD v2.0": [[25, null]], "Blast-Radius Security Tool Documentation": [[27, null]], "BlastRadiusAPIDown": [[33, "blastradiusapidown"]], "Blue-Green Deployment": [[15, "blue-green-deployment"]], "Branch Naming Conventions": [[21, "branch-naming-conventions"]], "Branch Types and Purposes": [[21, "id1"]], "Branching Strategy": [[21, "branching-strategy"]], "Breach Notification Procedures": [[40, "breach-notification-procedures"]], "Bug Bounty Program": [[47, "bug-bounty-program"]], "Build Commands": [[3, "build-commands"]], "Build Images": [[2, "build-images"]], "Building on Current Strengths": [[36, "building-on-current-strengths"]], "Bulk Import": [[5, "bulk-import"]], "Bulk Operations": [[5, "bulk-operations"]], "Bulk Update": [[5, "bulk-update"]], "Business Impact": [[49, "business-impact"]], "Business Impact Assessment": [[50, "business-impact-assessment"]], "Business Impact Levels": [[50, "id5"]], "Business Impact and Value": [[52, "business-impact-and-value"]], "Business KPIs": [[24, "business-kpis"]], "Business Logic Tests": [[53, "business-logic-tests"]], "Business Metrics": [[25, "business-metrics"], [63, "business-metrics"]], "Business Risks": [[63, "business-risks"]], "Business Security Metrics": [[52, "id7"]], "Business-Aligned Security Performance Management": [[73, "business-aligned-security-performance-management"]], "CI/CD Pipeline": [[64, "ci-cd-pipeline"]], "CI/CD Pipeline Configuration": [[14, "ci-cd-pipeline-configuration"]], "CI/CD Security Integration": [[48, "ci-cd-security-integration"]], "CI/CD Security Pipeline": [[54, "ci-cd-security-pipeline"]], "CR-001: Data Protection": [[63, "cr-001-data-protection"]], "CR-002: Security Frameworks": [[63, "cr-002-security-frameworks"]], "CVSS Risk Levels": [[48, "id4"]], "CVSS Scoring": [[50, "cvss-scoring"]], "Cache Optimization": [[33, "cache-optimization"]], "Caching Strategies": [[35, "caching-strategies"]], "Caching Strategy": [[59, "caching-strategy"]], "Caching Strategy Decision Flow": [[60, "caching-strategy-decision-flow"]], "Caching and Incremental Updates": [[30, "caching-and-incremental-updates"]], "Calculate Blast Radius": [[6, "calculate-blast-radius"]], "Can I customize risk scoring?": [[67, "can-i-customize-risk-scoring"]], "Can I run this in an air-gapped environment?": [[67, "can-i-run-this-in-an-air-gapped-environment"]], "Can I use the API for automation?": [[67, "can-i-use-the-api-for-automation"]], "Can\u2019t Access Web Interface": [[38, "can-t-access-web-interface"]], "Capacity Planning Review": [[32, "capacity-planning-review"]], "Certificate Renewal": [[32, "certificate-renewal"]], "Change Password": [[7, "change-password"]], "Clear Analysis Cache": [[6, "clear-analysis-cache"]], "Client Integration \ud83d\udd04": [[26, "client-integration"]], "Cloud Provider Discovery": [[68, "cloud-provider-discovery"]], "Cloud Provider Integration": [[38, "cloud-provider-integration"], [66, "cloud-provider-integration"]], "Cloud Provider Settings": [[13, "cloud-provider-settings"]], "Code Documentation": [[18, "code-documentation"]], "Code Examples": [[3, "code-examples"]], "Code Examples Added:": [[0, "code-examples-added"]], "Code Organization": [[21, "code-organization"]], "Code Quality Tools": [[20, "code-quality-tools"]], "Code Review Checklist": [[19, "code-review-checklist"]], "Code Review Process": [[21, "code-review-process"], [46, "code-review-process"]], "Code Review Security Checklist": [[46, "id1"]], "Code Security Review Process": [[51, "code-security-review-process"]], "Code Security Reviews": [[46, "code-security-reviews"]], "Code Standards": [[19, "code-standards"]], "Code Standards & Style Guide": [[18, null]], "Code Style": [[18, "code-style"], [18, "id1"]], "Code of Conduct": [[19, "code-of-conduct"]], "Code-Level Optimizations": [[35, "code-level-optimizations"]], "Collaboration": [[21, "collaboration"]], "Commit Message Standards": [[21, "commit-message-standards"]], "Commit Types": [[21, "id2"]], "Common Administrative Issues": [[70, "common-administrative-issues"]], "Common Development Tasks": [[20, "common-development-tasks"]], "Common Error Codes": [[5, "common-error-codes"]], "Common Issues": [[2, "common-issues"], [4, "common-issues"], [11, "common-issues"], [12, "common-issues"], [20, "common-issues"], [28, "common-issues"], [30, "common-issues"], [34, "common-issues"], [41, "common-issues"], [68, "common-issues"], [71, "common-issues"]], "Common Issues & Troubleshooting": [[66, null]], "Common Issues and Solutions": [[17, "common-issues-and-solutions"]], "Common Pitfalls": [[35, "common-pitfalls"]], "Common Quick Start Issues": [[38, "common-quick-start-issues"]], "Common Response Formats": [[8, "common-response-formats"]], "Common Security Review Pitfalls": [[46, "common-security-review-pitfalls"]], "Common Testing Patterns": [[65, "common-testing-patterns"]], "Common Workflows": [[74, "common-workflows"]], "Communication": [[19, "communication"], [43, "communication"]], "Communication Guidelines": [[47, "communication-guidelines"]], "Communication Procedures": [[33, "communication-procedures"], [45, "communication-procedures"]], "Communications Lead": [[43, "communications-lead"]], "Community Contribution": [[3, "community-contribution"]], "Community Guidelines": [[19, "community-guidelines"]], "Compensating Controls": [[50, "compensating-controls"]], "Complete System Recovery": [[17, "complete-system-recovery"], [31, "complete-system-recovery"]], "Compliance Automation": [[25, "compliance-automation"]], "Compliance Coverage": [[29, "compliance-coverage"]], "Compliance Documentation - Blast-Radius Security Tool": [[40, null]], "Compliance Framework": [[42, "compliance-framework"]], "Compliance Framework Coverage": [[37, "id2"]], "Compliance Framework Schema": [[22, "compliance-framework-schema"]], "Compliance Framework Schema - Technical Specification": [[56, null]], "Compliance Framework Status": [[52, "id4"]], "Compliance Impact Analysis": [[10, "compliance-impact-analysis"], [80, "compliance-impact-analysis"]], "Compliance Integration": [[41, "compliance-integration"]], "Compliance Monitoring": [[12, "compliance-monitoring"]], "Compliance Monitoring and Reporting": [[40, "compliance-monitoring-and-reporting"]], "Compliance Officers": [[74, "compliance-officers"]], "Compliance Officers Comprehensive Guide": [[72, null]], "Compliance Report Elements": [[68, "id3"]], "Compliance Reporting": [[12, "compliance-reporting"], [41, "compliance-reporting"]], "Compliance Requirements": [[63, "compliance-requirements"]], "Compliance Review Process": [[51, "compliance-review-process"]], "Compliance Review Workflow": [[51, "compliance-review-workflow"]], "Compliance and Audit": [[48, "compliance-and-audit"]], "Compliance and Regulatory Excellence": [[52, "compliance-and-regulatory-excellence"]], "Compliance and Standards": [[39, "compliance-and-standards"]], "Comprehensive Audit Management and Preparation": [[72, "comprehensive-audit-management-and-preparation"]], "Comprehensive Compliance and Governance Framework": [[78, "comprehensive-compliance-and-governance-framework"]], "Comprehensive Coverage": [[0, "comprehensive-coverage"], [1, "comprehensive-coverage"]], "Comprehensive Daily Operational Workflows": [[79, "comprehensive-daily-operational-workflows"]], "Comprehensive Dashboard Architecture": [[79, "comprehensive-dashboard-architecture"]], "Comprehensive Incident Response Framework": [[79, "comprehensive-incident-response-framework"]], "Comprehensive Purple Team Methodology and Framework": [[76, "comprehensive-purple-team-methodology-and-framework"]], "Comprehensive Purple Team Scenarios and Implementation Framework": [[76, "comprehensive-purple-team-scenarios-and-implementation-framework"]], "Comprehensive Red Team Methodology and Campaign Framework": [[77, "comprehensive-red-team-methodology-and-campaign-framework"]], "Comprehensive Scenario-Based Training and Response Procedures": [[79, "comprehensive-scenario-based-training-and-response-procedures"]], "Comprehensive Support Resources and Professional Development": [[79, "comprehensive-support-resources-and-professional-development"]], "Comprehensive User Ecosystem": [[27, "comprehensive-user-ecosystem"]], "Conclusion": [[4, "conclusion"], [11, "conclusion"], [12, "conclusion"], [15, "conclusion"], [29, "conclusion"], [41, "conclusion"], [45, "conclusion"], [46, "conclusion"], [47, "conclusion"], [48, "conclusion"], [49, "conclusion"], [50, "conclusion"], [52, "conclusion"], [53, "conclusion"], [54, "conclusion"], [55, "conclusion"], [70, "conclusion"], [71, "conclusion"]], "Conclusion: Excellence in Compliance and Governance": [[72, "conclusion-excellence-in-compliance-and-governance"]], "Conclusion: Excellence in Executive Security Leadership": [[73, "conclusion-excellence-in-executive-security-leadership"]], "Conclusion: Excellence in Purple Team Operations and Collaborative Security": [[76, "conclusion-excellence-in-purple-team-operations-and-collaborative-security"]], "Conclusion: Excellence in Red Team Operations": [[77, "conclusion-excellence-in-red-team-operations"]], "Conclusion: Excellence in SOC Operations": [[79, "conclusion-excellence-in-soc-operations"]], "Conclusion: Excellence in Security Architecture Leadership": [[78, "conclusion-excellence-in-security-architecture-leadership"]], "Concurrent Discovery": [[30, "concurrent-discovery"]], "Confidentiality (C1)": [[40, "confidentiality-c1"]], "Configuration": [[28, "configuration"], [30, "configuration"], [41, "configuration"]], "Configuration Backup": [[31, "configuration-backup"]], "Configuration Example": [[12, "configuration-example"]], "Configuration Guide": [[13, null]], "Configuration Hardening": [[50, "configuration-hardening"]], "Configuration Issues": [[66, "configuration-issues"]], "Configuration Management": [[4, "configuration-management"]], "Configuration Recovery": [[31, "configuration-recovery"]], "Configuration Templates:": [[0, "configuration-templates"]], "Configuration Validation": [[13, "configuration-validation"]], "Configuration and Usage": [[67, "configuration-and-usage"]], "Configuring Integrations": [[28, "configuring-integrations"]], "Congratulations!": [[38, "congratulations"]], "Consent Management System": [[12, "consent-management-system"]], "Contact Information": [[47, "contact-information"]], "Container Debugging": [[2, "container-debugging"]], "Container Discovery": [[68, "container-discovery"]], "Container Issues": [[2, "container-issues"]], "Container Optimization": [[35, "container-optimization"]], "Content Coverage": [[37, "content-coverage"]], "Content Quality": [[1, "content-quality"]], "Content Volume": [[1, "content-volume"]], "Continuous Evolution": [[3, "continuous-evolution"]], "Continuous Improvement": [[39, "continuous-improvement"], [42, "continuous-improvement"], [48, "continuous-improvement"], [50, "continuous-improvement"], [53, "continuous-improvement"]], "Continuous Integration": [[3, "continuous-integration"], [21, "continuous-integration"], [64, "continuous-integration"], [65, "continuous-integration"]], "Continuous Monitoring": [[10, "continuous-monitoring"], [40, "continuous-monitoring"], [42, "continuous-monitoring"]], "Continuous Risk Monitoring": [[80, "continuous-risk-monitoring"]], "Continuous Security": [[49, "continuous-security"]], "Continuous Security Monitoring": [[54, "continuous-security-monitoring"]], "Contributing": [[3, "contributing"]], "Contributing Guide": [[19, null]], "Contributing Guidelines": [[62, "contributing-guidelines"]], "Contributing and Development": [[62, "contributing-and-development"]], "Contribution Workflow": [[19, "contribution-workflow"]], "Control Implementation Matrix": [[40, "control-implementation-matrix"]], "Core Components": [[4, "core-components"], [11, "core-components"], [12, "core-components"], [15, "core-components"], [25, "core-components"], [41, "core-components"], [57, "core-components"], [58, "core-components"], [59, "core-components"]], "Core Concepts": [[71, "core-concepts"]], "Core Configuration": [[13, "core-configuration"]], "Core Entity Relationship Diagram": [[61, "core-entity-relationship-diagram"]], "Core Infrastructure \u2705": [[26, "core-infrastructure"]], "Core Platform Capabilities for SOC Operations": [[79, "core-platform-capabilities-for-soc-operations"]], "Core Principles": [[18, "core-principles"]], "Core Response Team": [[45, "core-response-team"]], "Core System Settings": [[70, "core-system-settings"]], "Core Tables": [[56, "core-tables"], [61, "core-tables"]], "Core Value Proposition": [[63, "core-value-proposition"]], "Correlate Security Event": [[9, "correlate-security-event"]], "Correlation Accuracy": [[75, "correlation-accuracy"]], "Coverage Completeness": [[1, "coverage-completeness"]], "Coverage Reports": [[64, "coverage-reports"]], "Coverage Requirements": [[65, "coverage-requirements"]], "Coverage Requirements by Component": [[65, "id1"]], "Coverage and Quality": [[65, "coverage-and-quality"]], "Create Asset": [[5, "create-asset"]], "Create Attack Scenario": [[6, "create-attack-scenario"]], "Create Relationship": [[5, "create-relationship"]], "Creating Admin User": [[28, "creating-admin-user"]], "Credential Management": [[30, "credential-management"]], "Crisis Management and Business Continuity": [[73, "crisis-management-and-business-continuity"]], "Critical (CVSS 9.0-10.0)": [[47, "critical-cvss-9-0-10-0"]], "Critical (P0)": [[43, "critical-p0"]], "Critical Alerts (P0)": [[33, "critical-alerts-p0"]], "Critical Security Issues Resolved": [[49, "critical-security-issues-resolved"]], "Critical Vulnerabilities (CVSS 9.0+)": [[43, "critical-vulnerabilities-cvss-9-0"]], "Cross-Functional Collaboration and Integration": [[72, "cross-functional-collaboration-and-integration"]], "Cross-References": [[3, "cross-references"]], "Current Security Posture": [[48, "current-security-posture"]], "Current State \u2705": [[24, "current-state"]], "Current Status Integration": [[25, "current-status-integration"]], "Current Vulnerability Status": [[48, "current-vulnerability-status"]], "Custom CSS (_static/custom.css)": [[3, "custom-css-static-custom-css"]], "Custom Integrations": [[62, "custom-integrations"]], "Custom Security Rules": [[55, "custom-security-rules"]], "Custom Sphinx Roles": [[3, "custom-sphinx-roles"]], "D3.js Components": [[26, "d3-js-components"]], "DAST Automation and CI/CD Integration": [[53, "dast-automation-and-ci-cd-integration"]], "DAST Benefits": [[53, "dast-benefits"]], "DAST Best Practices": [[53, "dast-best-practices"]], "DAST Results Analysis": [[53, "dast-results-analysis"]], "DAST Test Categories": [[53, "dast-test-categories"]], "DAST Tools and Coverage": [[48, "id2"], [50, "id2"]], "DAST Tools and Implementation": [[53, "dast-tools-and-implementation"]], "Daily Development Cycle": [[2, "daily-development-cycle"]], "Daily Maintenance (Automated)": [[32, "daily-maintenance-automated"]], "Daily Performance Checks": [[33, "daily-performance-checks"]], "Dashboard Overview": [[38, "dashboard-overview"], [70, "dashboard-overview"]], "Data Architecture": [[15, "data-architecture"]], "Data Breach Management": [[12, "data-breach-management"]], "Data Breach Response": [[43, "data-breach-response"]], "Data Classification": [[11, "data-classification"]], "Data Issues": [[66, "data-issues"]], "Data Models and Schemas": [[62, "data-models-and-schemas"]], "Data Processing Records": [[12, "data-processing-records"]], "Data Processing Workflows": [[12, "data-processing-workflows"]], "Data Protection": [[11, "data-protection"], [30, "data-protection"], [49, "data-protection"]], "Data Protection Principles": [[40, "data-protection-principles"]], "Data Retention and Archival": [[61, "data-retention-and-archival"]], "Data Security Layer": [[39, "data-security-layer"]], "Data Subject Rights Implementation": [[40, "data-subject-rights-implementation"]], "Data Subject Rights Management": [[12, "data-subject-rights-management"]], "Database Architecture": [[61, "database-architecture"]], "Database Backup Procedures": [[31, "database-backup-procedures"]], "Database Configuration": [[13, "database-configuration"]], "Database Connection Issues": [[16, "database-connection-issues"], [66, "database-connection-issues"]], "Database Design": [[62, "database-design"]], "Database Design and Schema": [[61, null]], "Database Issues": [[2, "database-issues"], [17, "database-issues"]], "Database Maintenance": [[32, "database-maintenance"], [32, "id1"], [61, "database-maintenance"]], "Database Management": [[20, "database-management"]], "Database Optimization": [[33, "database-optimization"], [34, "database-optimization"], [35, "database-optimization"]], "Database Recovery": [[31, "database-recovery"]], "Database Rollback": [[16, "database-rollback"]], "Database Schema": [[12, "database-schema"], [41, "database-schema"]], "Database Standards": [[18, "database-standards"]], "DatabaseConnectionsHigh": [[33, "databaseconnectionshigh"]], "Debug Mode": [[2, "debug-mode"], [30, "debug-mode"]], "Debugging": [[20, "debugging"]], "Debugging Tests": [[65, "debugging-tests"]], "Definition of Done": [[21, "definition-of-done"]], "Delete Asset": [[5, "delete-asset"]], "Dependency Scanning Tools": [[50, "id3"]], "Dependency Security Scanning": [[54, "dependency-security-scanning"]], "Deployment": [[62, "deployment"]], "Deployment & Operations": [[27, "deployment-operations"], [27, null]], "Deployment Architecture": [[29, "deployment-architecture"]], "Deployment Checklist": [[11, "deployment-checklist"]], "Deployment Gates Configuration": [[54, "deployment-gates-configuration"]], "Deployment Script": [[14, "deployment-script"]], "Deployment Strategy": [[15, "deployment-strategy"]], "Design Review Process": [[51, "design-review-process"]], "Detect (DE)": [[40, "detect-de"]], "Detection Effectiveness Analysis": [[69, "id2"]], "Developer Guidelines": [[54, "developer-guidelines"]], "Developer Responsibilities": [[51, "developer-responsibilities"]], "Developer Workstation Security": [[54, "developer-workstation-security"]], "Development": [[27, "development"], [27, null]], "Development Commands": [[3, "development-commands"]], "Development Configuration": [[14, "development-configuration"]], "Development Environment": [[13, "development-environment"], [14, "development-environment"], [62, "development-environment"]], "Development Environment Setup": [[14, "development-environment-setup"], [20, null]], "Development Process": [[21, "development-process"]], "Development Tools": [[2, "development-tools"], [16, "development-tools"], [18, "development-tools"], [21, "development-tools"]], "Development Workflow": [[3, "development-workflow"], [20, "development-workflow"], [21, null]], "Development and Integration": [[62, "development-and-integration"]], "Digital Transformation and Innovation Enablement": [[73, "digital-transformation-and-innovation-enablement"]], "Disaster Recovery": [[15, "disaster-recovery"]], "Disaster Recovery Procedures": [[31, "disaster-recovery-procedures"]], "Discovery Automation": [[68, "discovery-automation"]], "Discovery Engine Architecture": [[30, "discovery-engine-architecture"]], "Discovery Job Monitoring": [[30, "discovery-job-monitoring"]], "Discovery Methods": [[68, "discovery-methods"]], "Discovery Results": [[5, "discovery-results"]], "Discovery Results Example": [[68, "id2"]], "Discovery Status": [[5, "discovery-status"]], "Discovery Strategy": [[68, "discovery-strategy"]], "Discovery Types": [[5, "id7"]], "Docker & Traefik Integration": [[24, "docker-traefik-integration"], [25, "docker-traefik-integration"]], "Docker Commands": [[3, "docker-commands"]], "Docker Deployment": [[3, "docker-deployment"]], "Docker Development": [[3, "docker-development"]], "Docker Installation Problems": [[66, "docker-installation-problems"]], "Documentation": [[2, "documentation"], [43, "id1"]], "Documentation & Operations - 100% Complete \u2705": [[29, "documentation-operations-100-complete"]], "Documentation Achievements Summary": [[22, null]], "Documentation Build Metrics": [[37, "id1"]], "Documentation Build and Deployment": [[1, "documentation-build-and-deployment"]], "Documentation Completeness": [[37, "documentation-completeness"], [37, "id3"]], "Documentation Completion Summary": [[1, "documentation-completion-summary"]], "Documentation Expansion Summary": [[0, null]], "Documentation Maintenance": [[1, "documentation-maintenance"]], "Documentation Metrics": [[3, "documentation-metrics"]], "Documentation Overview and Achievements": [[23, null]], "Documentation Quality:": [[0, "documentation-quality"]], "Documentation Standards": [[18, "documentation-standards"], [19, "documentation-standards"]], "Documentation Standards:": [[0, "documentation-standards"]], "Documentation Structure": [[22, "documentation-structure"]], "Documentation Types": [[19, "documentation-types"]], "Documentation Updates": [[43, "documentation-updates"]], "Documentation and Resources": [[51, "documentation-and-resources"]], "Dynamic Application Security Testing (DAST)": [[48, "dynamic-application-security-testing-dast"], [53, null], [54, "dynamic-application-security-testing-dast"]], "Emergency Contacts": [[43, "emergency-contacts"]], "Emergency Procedures": [[17, "emergency-procedures"]], "Emergency Rollback": [[16, "emergency-rollback"]], "Enable Debug Logging": [[2, "enable-debug-logging"]], "Enable MFA": [[7, "enable-mfa"]], "Encryption Implementation": [[11, "encryption-implementation"]], "Encryption Settings": [[13, "encryption-settings"]], "End-to-End Testing": [[64, "end-to-end-testing"]], "End-to-End Tests": [[2, "end-to-end-tests"]], "Endpoints": [[6, "endpoints"]], "Enforcement": [[18, "enforcement"]], "Enhanced Audit Logging": [[22, "enhanced-audit-logging"], [37, "enhanced-audit-logging"]], "Enhanced Audit Logging - 100% Complete \u2705": [[29, "enhanced-audit-logging-100-complete"]], "Enhanced Audit Logging System": [[41, null]], "Enhanced Capabilities \ud83c\udd95": [[24, "enhanced-capabilities"]], "Enhanced Configuration": [[3, "enhanced-configuration"]], "Enhanced Features Summary - Blast-Radius Security Tool": [[24, null]], "Enhanced Implementation Details": [[36, "enhanced-implementation-details"], [36, "id1"]], "Enhanced KPIs Building on Phase 2": [[36, "enhanced-kpis-building-on-phase-2"]], "Enhanced Security Configuration": [[49, "enhanced-security-configuration"]], "Enrich IOCs": [[9, "enrich-iocs"]], "Enterprise Risk Assessment": [[52, "enterprise-risk-assessment"]], "Enterprise Security Architecture Best Practices and Excellence": [[78, "enterprise-security-architecture-best-practices-and-excellence"]], "Enterprise Security Architecture Methodology": [[78, "enterprise-security-architecture-methodology"]], "Enterprise User Guides (6,000+ Lines)": [[22, "enterprise-user-guides-6-000-lines"]], "Enumeration Types": [[61, "enumeration-types"]], "Environment Cleanup Script": [[14, "environment-cleanup-script"]], "Environment Configurations": [[2, "environment-configurations"]], "Environment Management Scripts": [[14, "environment-management-scripts"]], "Environment Setup Documentation - Blast-Radius Security Tool": [[14, null]], "Environment Types": [[14, "environment-types"]], "Environment Variable Problems": [[66, "environment-variable-problems"]], "Environment Variables": [[2, "environment-variables"], [28, "environment-variables"]], "Environment-Specific Configuration": [[13, "environment-specific-configuration"]], "Environment-Specific Configurations": [[14, "environment-specific-configurations"]], "Error Codes": [[5, "id9"]], "Error Handling": [[5, "error-handling"], [9, "error-handling"], [10, "error-handling"], [30, "error-handling"]], "Error Handling and Recovery Flow": [[60, "error-handling-and-recovery-flow"]], "Error Response": [[8, "error-response"]], "Error Response Format": [[5, "error-response-format"]], "Error Responses": [[6, "error-responses"]], "Escalation Procedures": [[17, "escalation-procedures"], [33, "escalation-procedures"]], "Essential Features Tour": [[38, "essential-features-tour"]], "Event Enrichment Pipeline": [[41, "event-enrichment-pipeline"]], "Evidence Handling": [[45, "evidence-handling"]], "Example Review Process": [[51, "example-review-process"]], "Example Security Policies": [[51, "example-security-policies"]], "Examples": [[6, "examples"], [8, "examples"]], "Exception Testing": [[65, "exception-testing"]], "Executive Leadership": [[74, "executive-leadership"]], "Executive Leadership Comprehensive Guide": [[73, null]], "Executive Overview": [[52, "executive-overview"]], "Executive Reporting": [[50, "executive-reporting"]], "Executive Summary": [[25, "executive-summary"], [27, "executive-summary"], [49, "executive-summary"], [63, "executive-summary"], [79, "executive-summary"]], "Executive Summary for Compliance Excellence": [[72, "executive-summary-for-compliance-excellence"]], "Executive Summary for Purple Team Excellence": [[76, "executive-summary-for-purple-team-excellence"]], "Executive Summary for Red Team Operations": [[77, "executive-summary-for-red-team-operations"]], "Executive Summary for Security Architects": [[78, "executive-summary-for-security-architects"]], "Executive Summary for Strategic Security Leadership": [[73, "executive-summary-for-strategic-security-leadership"]], "Export Analysis Results": [[6, "export-analysis-results"]], "Export Navigator Layer": [[9, "export-navigator-layer"]], "Extended Response Team": [[45, "extended-response-team"]], "External Communications": [[45, "external-communications"]], "External Contacts": [[43, "external-contacts"]], "External Dependencies": [[16, "external-dependencies"]], "External Support": [[17, "external-support"]], "FR-001: Attack Path Discovery": [[63, "fr-001-attack-path-discovery"]], "FR-002: Blast Radius Calculation": [[63, "fr-002-blast-radius-calculation"]], "FR-003: MITRE ATT&CK Integration": [[63, "fr-003-mitre-att-ck-integration"]], "FR-004: Attack Scenario Modeling": [[63, "fr-004-attack-scenario-modeling"]], "FR-005: Asset Discovery and Management": [[63, "fr-005-asset-discovery-and-management"]], "False Positive Management": [[55, "false-positive-management"]], "Feature Coverage": [[1, "feature-coverage"]], "Feature Development": [[2, "feature-development"]], "Feature Development Workflow": [[21, "feature-development-workflow"]], "Feature-Specific Guides": [[74, "feature-specific-guides"]], "Features by Use Case": [[27, "features-by-use-case"]], "Feedback and Contributions": [[74, "feedback-and-contributions"]], "Field Selection": [[8, "field-selection"]], "File System Backup": [[31, "file-system-backup"]], "Filtering and Sorting": [[8, "filtering-and-sorting"]], "Financial Impact Assessment": [[10, "financial-impact-assessment"]], "First Login and Setup": [[38, "first-login-and-setup"]], "Fixtures and Factories": [[64, "fixtures-and-factories"]], "Fixtures and Test Data": [[65, "fixtures-and-test-data"]], "For Administrators:": [[0, "for-administrators"]], "For Purple Team Members:": [[0, "for-purple-team-members"]], "For Red Team Members:": [[0, "for-red-team-members"]], "For SOC Operators:": [[0, "for-soc-operators"]], "For Security Architects:": [[0, "for-security-architects"]], "Framework Implementation": [[40, "framework-implementation"]], "Framework Mapping": [[41, "framework-mapping"], [59, "framework-mapping"]], "Frequently Asked Questions (FAQ)": [[67, null]], "Frontend Debugging": [[20, "frontend-debugging"]], "Frontend Issues": [[66, "frontend-issues"]], "Frontend Tests": [[2, "frontend-tests"]], "Full Application Recovery": [[31, "full-application-recovery"]], "Full Pipeline (Recommended)": [[2, "full-pipeline-recommended"]], "Functional Requirements": [[63, "functional-requirements"]], "Future Enhancements": [[30, "future-enhancements"], [48, "future-enhancements"]], "Future Roadmap": [[63, "future-roadmap"]], "Future Security Roadmap": [[52, "future-security-roadmap"]], "GCP Authentication": [[30, "gcp-authentication"]], "GCP Configuration": [[30, "gcp-configuration"]], "GCP SDK": [[30, "gcp-sdk"]], "GCP Services": [[30, "gcp-services"]], "GDPR Compliance": [[40, "gdpr-compliance"], [42, "gdpr-compliance"]], "GDPR Compliance Checklist": [[51, "gdpr-compliance-checklist"]], "GDPR Compliance Framework": [[12, null], [22, "gdpr-compliance-framework"], [37, "gdpr-compliance-framework"]], "GDPR Compliance Framework - 100% Complete \u2705": [[29, "gdpr-compliance-framework-100-complete"]], "GDPR Impact Assessment": [[10, "gdpr-impact-assessment"]], "GDPR Service Architecture": [[12, "gdpr-service-architecture"]], "General Guidelines": [[74, "general-guidelines"]], "General Principles": [[18, "general-principles"]], "General Questions": [[67, "general-questions"]], "General Troubleshooting Approach": [[17, "general-troubleshooting-approach"]], "Generate Mitigations": [[10, "generate-mitigations"]], "Generate Navigator Layer": [[9, "generate-navigator-layer"]], "Get Asset": [[5, "get-asset"]], "Get Attack Scenario": [[6, "get-attack-scenario"]], "Get Data Status": [[9, "get-data-status"]], "Get Event Context": [[9, "get-event-context"]], "Get Simulation Results": [[10, "get-simulation-results"]], "Get Technique Information": [[9, "get-technique-information"]], "Get Threat Actor Profile": [[9, "get-threat-actor-profile"]], "Getting Additional Help": [[66, "getting-additional-help"]], "Getting Help": [[3, "getting-help"], [17, "getting-help"], [19, "getting-help"], [20, "getting-help"], [28, "getting-help"], [38, "getting-help"], [71, "getting-help"], [74, "getting-help"]], "Getting Help and Support": [[70, "getting-help-and-support"]], "Getting Started": [[19, "getting-started"], [70, "getting-started"], [71, "getting-started"], [75, "getting-started"], [80, "getting-started"]], "Getting Started Section": [[1, "getting-started-section"]], "Getting Started:": [[0, "getting-started"]], "Getting Started: SOC Operator Onboarding": [[79, "getting-started-soc-operator-onboarding"]], "Git Flow Model": [[21, "git-flow-model"]], "GitHub Actions Integration": [[55, "github-actions-integration"]], "GitHub Actions Workflow": [[14, "github-actions-workflow"]], "Grafana Dashboard Management": [[33, "grafana-dashboard-management"]], "Graph Algorithms": [[59, "graph-algorithms"]], "Graph Analysis Engine": [[62, "graph-analysis-engine"]], "Graph Data Structures": [[62, "graph-data-structures"]], "Graph Statistics": [[6, "graph-statistics"]], "GraphEngine": [[59, "graphengine"]], "HIPAA Impact Assessment": [[10, "hipaa-impact-assessment"]], "HTTP Status Code Usage": [[18, "id3"]], "HTTP Status Codes": [[8, "http-status-codes"]], "Health Check Commands": [[66, "health-check-commands"]], "Health Checks": [[2, "health-checks"], [28, "health-checks"]], "High (CVSS 7.0-8.9)": [[47, "high-cvss-7-0-8-9"]], "High (P1)": [[43, "high-p1"]], "High Availability Design": [[15, "high-availability-design"]], "High Priority Alerts (P1)": [[33, "high-priority-alerts-p1"]], "High Priority:": [[0, "high-priority"]], "High Vulnerabilities (CVSS 7.0-8.9)": [[43, "high-vulnerabilities-cvss-7-0-8-9"]], "HighMemoryUsage": [[33, "highmemoryusage"]], "Horizontal Scaling": [[34, "horizontal-scaling"]], "How can I contribute?": [[67, "how-can-i-contribute"]], "How do I add new users?": [[67, "how-do-i-add-new-users"]], "How do I backup my data?": [[67, "how-do-i-backup-my-data"]], "How do I configure audit logging?": [[67, "how-do-i-configure-audit-logging"]], "How do I configure cloud provider integrations?": [[67, "how-do-i-configure-cloud-provider-integrations"]], "How do I enable Multi-Factor Authentication (MFA)?": [[67, "how-do-i-enable-multi-factor-authentication-mfa"]], "How do I get API credentials?": [[67, "how-do-i-get-api-credentials"]], "How do I get help?": [[67, "how-do-i-get-help"]], "How do I install the Blast-Radius Security Tool?": [[67, "how-do-i-install-the-blast-radius-security-tool"]], "How do I integrate with my SIEM?": [[67, "how-do-i-integrate-with-my-siem"]], "How do I reset the admin password?": [[67, "how-do-i-reset-the-admin-password"]], "How do I update to a new version?": [[67, "how-do-i-update-to-a-new-version"]], "How does attack path analysis work?": [[67, "how-does-attack-path-analysis-work"]], "How long does attack path analysis take?": [[67, "how-long-does-attack-path-analysis-take"]], "How to Report": [[47, "how-to-report"]], "IOC Enhancement": [[75, "ioc-enhancement"]], "IR-001: SIEM Integration": [[63, "ir-001-siem-integration"]], "IR-002: SOAR Integration": [[63, "ir-002-soar-integration"]], "IR-003: Cloud Platform Integration": [[63, "ir-003-cloud-platform-integration"]], "ISO 27001 Compliance": [[40, "iso-27001-compliance"], [42, "iso-27001-compliance"]], "Identify (ID)": [[40, "identify-id"]], "Identity Verification Service": [[11, "identity-verification-service"]], "Immediate Actions": [[29, "immediate-actions"], [37, "immediate-actions"], [38, "immediate-actions"]], "Immediate Actions (0-1 hour)": [[43, "immediate-actions-0-1-hour"]], "Immediate Actions (Phase 2.5 - Next 4-6 weeks)": [[36, "immediate-actions-phase-2-5-next-4-6-weeks"]], "Implementation Achievements": [[29, "implementation-achievements"]], "Implementation Details": [[4, "implementation-details"], [12, "implementation-details"], [41, "implementation-details"]], "Implementation Gap Analysis - Blast-Radius Security Tool": [[26, null]], "Implementation Guidelines": [[11, "implementation-guidelines"]], "Implementation Path:": [[0, "implementation-path"]], "Implementation Tips": [[11, "implementation-tips"]], "In Scope": [[47, "in-scope"]], "Incident Classification": [[43, "incident-classification"], [45, "incident-classification"]], "Incident Commander (IC)": [[43, "incident-commander-ic"]], "Incident Response": [[25, "incident-response"], [42, "incident-response"], [71, "incident-response"]], "Incident Response Metrics": [[45, "incident-response-metrics"]], "Incident Response Objectives": [[45, "incident-response-objectives"]], "Incident Response Procedures - Blast-Radius Security Tool": [[43, null]], "Incident Response Process": [[43, "incident-response-process"], [45, "incident-response-process"]], "Incident Response Team": [[43, "incident-response-team"], [45, "incident-response-team"]], "Incident Response Training": [[45, "incident-response-training"]], "Incident Response Workflow": [[74, "incident-response-workflow"]], "Indexes and Performance Optimization": [[61, "indexes-and-performance-optimization"]], "Indices and Tables": [[27, "indices-and-tables"]], "Industry Standards Compliance": [[52, "industry-standards-compliance"]], "Information Security Management System (ISMS)": [[40, "information-security-management-system-isms"]], "Infrastructure Deployment": [[16, "infrastructure-deployment"]], "Infrastructure Enhancements": [[24, "infrastructure-enhancements"], [26, "infrastructure-enhancements"]], "Infrastructure Optimization": [[35, "infrastructure-optimization"]], "Infrastructure Provisioning": [[14, "infrastructure-provisioning"]], "Infrastructure Requirements": [[16, "infrastructure-requirements"], [37, "infrastructure-requirements"]], "Infrastructure Security Layer": [[39, "infrastructure-security-layer"]], "Infrastructure Security Review": [[51, "id1"]], "Infrastructure as Code": [[15, "infrastructure-as-code"]], "Infrastructure as Code (IaC) - 100% Complete \u2705": [[29, "infrastructure-as-code-iac-100-complete"]], "Infrastructure as Code (IaC) Review": [[51, "infrastructure-as-code-iac-review"]], "Initial Login": [[38, "initial-login"]], "Initial Platform Setup": [[70, "initial-platform-setup"]], "Initial Platform Setup and Configuration": [[79, "initial-platform-setup-and-configuration"]], "Initial Setup": [[28, "initial-setup"]], "Input Validation Tests": [[53, "input-validation-tests"]], "Installation Guide": [[20, "installation-guide"], [28, null]], "Installation Issues": [[66, "installation-issues"]], "Installation Methods": [[28, "installation-methods"]], "Installation and Setup": [[30, "installation-and-setup"], [67, "installation-and-setup"]], "Integration Configuration": [[13, "integration-configuration"]], "Integration Examples": [[7, "integration-examples"]], "Integration Issues": [[66, "integration-issues"]], "Integration Management": [[70, "integration-management"]], "Integration Requirements": [[63, "integration-requirements"]], "Integration Setup": [[38, "integration-setup"]], "Integration Testing": [[64, "integration-testing"]], "Integration and API": [[67, "integration-and-api"]], "Integration and Automation": [[69, "integration-and-automation"]], "Integration with Existing Docs:": [[0, "integration-with-existing-docs"]], "Integration with External Tools": [[68, "integration-with-external-tools"]], "Integration with SDLC": [[50, "integration-with-sdlc"]], "Integrations": [[8, "integrations"]], "Interactive Debugging": [[2, "interactive-debugging"]], "Interactive Elements": [[3, "interactive-elements"]], "Internal Communications": [[45, "internal-communications"]], "Internal Resources": [[17, "internal-resources"]], "Internal Team": [[43, "internal-team"]], "Interpretation Guidelines": [[71, "interpretation-guidelines"]], "Introduction": [[48, "introduction"]], "Is my data secure?": [[67, "is-my-data-secure"]], "Is there a community or forum?": [[67, "is-there-a-community-or-forum"]], "Issue: Database Connection Timeout": [[17, "issue-database-connection-timeout"]], "Issue: Database Migration Failures": [[17, "issue-database-migration-failures"]], "Issue: High Response Times": [[17, "issue-high-response-times"]], "Issue: Memory Leaks": [[17, "issue-memory-leaks"]], "Issue: Missing Metrics": [[17, "issue-missing-metrics"]], "Issue: Persistent Volume Problems": [[17, "issue-persistent-volume-problems"]], "Issue: Pod Stuck in CrashLoopBackOff": [[17, "issue-pod-stuck-in-crashloopbackoff"]], "Issue: Pod Stuck in Pending State": [[17, "issue-pod-stuck-in-pending-state"]], "Issue: SSL/TLS Certificate Problems": [[17, "issue-ssl-tls-certificate-problems"]], "Issue: Service Not Accessible": [[17, "issue-service-not-accessible"]], "JSONB Structure for assessment_criteria": [[56, "jsonb-structure-for-assessment-criteria"]], "JWT Token Structure": [[7, "jwt-token-structure"]], "JavaScript SDK": [[5, "javascript-sdk"]], "JavaScript/TypeScript": [[7, "javascript-typescript"]], "JavaScript/TypeScript Standards": [[18, "javascript-typescript-standards"]], "JavaScript/TypeScript Style": [[19, "javascript-typescript-style"]], "June 2025 Security Review Results": [[52, "june-2025-security-review-results"]], "Just-in-Time Access Provisioning": [[4, "just-in-time-access-provisioning"]], "Key Capabilities": [[69, "key-capabilities"]], "Key Metrics": [[55, "key-metrics"]], "Key Performance Indicators": [[34, "key-performance-indicators"], [40, "key-performance-indicators"], [50, "key-performance-indicators"]], "Key Permissions": [[70, "key-permissions"]], "Key Security Fixes Implemented": [[52, "key-security-fixes-implemented"]], "Key Security Metrics": [[42, "id6"]], "Kubernetes Cluster Updates": [[32, "kubernetes-cluster-updates"]], "Kubernetes Health Check": [[33, "kubernetes-health-check"]], "Kubernetes Infrastructure": [[15, "kubernetes-infrastructure"]], "Kubernetes Network Policies": [[11, "kubernetes-network-policies"]], "Kubernetes Security Review": [[51, "kubernetes-security-review"]], "Latest Implementations Summary - Production Ready Release": [[29, null]], "Layered Security Model": [[42, "layered-security-model"]], "Learning Resources": [[38, "learning-resources"]], "Least Privilege Access Control": [[22, "least-privilege-access-control"], [37, "least-privilege-access-control"]], "Least Privilege Access Control - 100% Complete \u2705": [[29, "least-privilege-access-control-100-complete"]], "Least Privilege Access Control Framework": [[4, null]], "Legal Considerations": [[47, "legal-considerations"]], "Legal and Regulatory Considerations": [[45, "legal-and-regulatory-considerations"]], "Leveraging Existing Infrastructure": [[36, "leveraging-existing-infrastructure"]], "License": [[27, "license"]], "List Assets": [[5, "list-assets"]], "List Relationships": [[5, "list-relationships"]], "List Threat Actors": [[10, "list-threat-actors"]], "Load Balancer Issues": [[16, "load-balancer-issues"]], "Load Balancing": [[35, "load-balancing"]], "Load Testing": [[34, "load-testing"]], "Local Container Management": [[2, "local-container-management"]], "Local Deployment": [[3, "local-deployment"]], "Local Development": [[3, "local-development"], [64, "local-development"]], "Local Development & CI/CD Guide": [[2, null]], "Local Development Setup": [[14, "local-development-setup"]], "Local Performance Testing": [[2, "local-performance-testing"]], "Log Analysis": [[33, "log-analysis"]], "Log Collection": [[66, "log-collection"]], "Log Management Procedures": [[33, "log-management-procedures"]], "Log Retention Management": [[33, "log-retention-management"]], "Log Rotation and Cleanup (2:00 AM UTC)": [[32, "log-rotation-and-cleanup-2-00-am-utc"]], "Logging": [[30, "logging"]], "Logging Configuration": [[13, "logging-configuration"]], "Logical Backup Recovery": [[31, "logical-backup-recovery"]], "Login": [[7, "login"]], "Login Issues": [[38, "login-issues"]], "Login Problems": [[66, "login-problems"]], "Logout": [[7, "logout"]], "Logs and Metrics": [[2, "logs-and-metrics"]], "Long-Term Strategic Goals (2026)": [[52, "long-term-strategic-goals-2026"]], "Long-term Roadmap": [[29, "long-term-roadmap"], [37, "long-term-roadmap"]], "Low (CVSS 0.1-3.9)": [[47, "low-cvss-0-1-3-9"]], "Low (P3)": [[43, "low-p3"]], "Low Priority:": [[0, "low-priority"]], "MITRE ATT&CK Data Management": [[75, "mitre-att-ck-data-management"]], "MITRE ATT&CK Framework": [[42, "mitre-att-ck-framework"]], "MITRE ATT&CK Integration": [[8, "mitre-att-ck-integration"], [59, "mitre-att-ck-integration"], [69, "mitre-att-ck-integration"], [74, "mitre-att-ck-integration"]], "MITRE ATT&CK Integration API Reference": [[9, null]], "MITRE ATT&CK Integration User Guide": [[75, null]], "MITRE ATT&CK Mapping": [[6, "mitre-att-ck-mapping"]], "MITRE ATT&CK Mapping Decision Tree": [[60, "mitre-att-ck-mapping-decision-tree"]], "ML Threat Prediction": [[22, "ml-threat-prediction"], [37, "ml-threat-prediction"]], "Machine Learning Capabilities": [[25, "machine-learning-capabilities"]], "Machine Learning Threat Prediction - Technical Specification": [[57, null]], "Maintenance": [[64, "maintenance"]], "Maintenance Procedures - Blast-Radius Security Tool": [[32, null]], "Maintenance Windows": [[32, "maintenance-windows"]], "Maintenance:": [[0, "maintenance"]], "Makefile Integration": [[53, "makefile-integration"], [55, "makefile-integration"]], "Manage Tags": [[5, "manage-tags"]], "Manifest Security Checklist": [[51, "manifest-security-checklist"]], "Manual Database Backup": [[31, "manual-database-backup"]], "Medium (CVSS 4.0-6.9)": [[47, "medium-cvss-4-0-6-9"]], "Medium (P2)": [[43, "medium-p2"]], "Medium Priority:": [[0, "medium-priority"]], "Medium-term Objectives (Phase 4 - Q4 2025)": [[36, "medium-term-objectives-phase-4-q4-2025"]], "Memory Issues": [[66, "memory-issues"]], "Mermaid Diagrams": [[3, "mermaid-diagrams"], [3, "id6"]], "Method 1: Quick Start with Docker (Recommended)": [[28, "method-1-quick-start-with-docker-recommended"]], "Method 2: Development Installation": [[28, "method-2-development-installation"]], "Method 3: Production Installation": [[28, "method-3-production-installation"]], "Metrics and Reporting": [[45, "metrics-and-reporting"], [50, "metrics-and-reporting"], [54, "metrics-and-reporting"]], "Minimum System Requirements": [[16, "minimum-system-requirements"]], "Missing or Incorrect Data": [[66, "missing-or-incorrect-data"]], "Mission Statement": [[63, "mission-statement"]], "Mitigation Strategies": [[10, "mitigation-strategies"]], "Mitigation Strategy Generation": [[80, "mitigation-strategy-generation"]], "Mocking and Patching": [[65, "mocking-and-patching"]], "Monitoring": [[62, "monitoring"]], "Monitoring & Dashboards": [[8, "monitoring-dashboards"]], "Monitoring Architecture": [[15, "monitoring-architecture"]], "Monitoring Dashboard Procedures": [[33, "monitoring-dashboard-procedures"]], "Monitoring Runbooks - Blast-Radius Security Tool": [[33, null]], "Monitoring Settings": [[13, "monitoring-settings"]], "Monitoring Setup": [[34, "monitoring-setup"]], "Monitoring and Alerting": [[11, "monitoring-and-alerting"], [30, "monitoring-and-alerting"], [41, "monitoring-and-alerting"], [43, "monitoring-and-alerting"]], "Monitoring and Alerting Issues": [[17, "monitoring-and-alerting-issues"]], "Monitoring and Analytics": [[4, "monitoring-and-analytics"]], "Monitoring and Logging": [[13, "monitoring-and-logging"]], "Monitoring and Logging Layer": [[39, "monitoring-and-logging-layer"]], "Monitoring and Maintenance": [[16, "monitoring-and-maintenance"], [70, "monitoring-and-maintenance"]], "Monitoring and Profiling": [[35, "monitoring-and-profiling"]], "Monitoring \ud83d\udd04": [[26, "monitoring"]], "Monthly Maintenance (First Sunday 4:00 AM UTC)": [[32, "monthly-maintenance-first-sunday-4-00-am-utc"]], "Morning Health Check (9:00 AM)": [[33, "morning-health-check-9-00-am"]], "Multi-Cloud Integration Guide": [[30, null]], "Multi-Environment Support": [[2, "multi-environment-support"]], "Multi-Factor Authentication": [[7, "multi-factor-authentication"]], "Multi-Factor Authentication (Recommended)": [[38, "multi-factor-authentication-recommended"]], "Multi-Factor Risk Assessment": [[59, "multi-factor-risk-assessment"]], "Multi-Framework Compliance": [[37, "multi-framework-compliance"]], "Multi-Framework Schema": [[26, "multi-framework-schema"]], "Multi-Layered Security Approach": [[52, "multi-layered-security-approach"]], "Multi-Level Caching": [[35, "multi-level-caching"]], "NFR-001: Performance": [[63, "nfr-001-performance"]], "NFR-002: Scalability": [[63, "nfr-002-scalability"]], "NFR-003: Security": [[63, "nfr-003-security"]], "NFR-004: Reliability": [[63, "nfr-004-reliability"]], "NFR-005: Usability": [[63, "nfr-005-usability"]], "NIST CSF Framework Seed Data": [[56, "nist-csf-framework-seed-data"]], "NIST Cybersecurity Framework": [[40, "nist-cybersecurity-framework"]], "Navigation": [[3, "navigation"]], "Neo4j Optimization": [[35, "neo4j-optimization"]], "Network Architecture": [[15, "network-architecture"]], "Network Connectivity Issues": [[66, "network-connectivity-issues"]], "Network Requirements": [[16, "network-requirements"]], "Network Security": [[11, "network-security"], [30, "network-security"]], "Network and Connectivity Issues": [[17, "network-and-connectivity-issues"]], "Network-Based Discovery": [[68, "network-based-discovery"]], "New Dependencies Required": [[24, "new-dependencies-required"], [26, "new-dependencies-required"]], "New Feature Workflow": [[2, "new-feature-workflow"]], "Next Steps": [[16, "next-steps"], [20, "next-steps"], [21, "next-steps"], [28, "next-steps"], [34, "next-steps"], [35, "next-steps"], [38, "next-steps"], [42, "next-steps"], [64, "next-steps"], [65, "next-steps"], [68, "next-steps"], [69, "next-steps"]], "Next Steps and Recommendations": [[29, "next-steps-and-recommendations"]], "Non-Functional Requirements": [[63, "non-functional-requirements"]], "Notification Requirements": [[43, "notification-requirements"]], "One-Command Setup": [[2, "one-command-setup"]], "Operational Benefits": [[49, "operational-benefits"]], "Operational Excellence": [[15, "operational-excellence"], [70, "operational-excellence"]], "Operational Guidelines": [[4, "operational-guidelines"], [12, "operational-guidelines"], [41, "operational-guidelines"]], "Operational Readiness": [[14, "operational-readiness"]], "Optimization Guidelines": [[35, "optimization-guidelines"]], "Optimization Recommendations": [[34, "optimization-recommendations"]], "Optimization Strategy": [[35, "optimization-strategy"]], "Original Phase 3 Goals": [[36, "original-phase-3-goals"]], "Original Phase 4 Goals": [[36, "original-phase-4-goals"]], "Out of Scope": [[47, "out-of-scope"]], "Overview": [[0, "overview"], [4, "overview"], [5, "overview"], [6, "overview"], [7, "overview"], [8, "overview"], [9, "overview"], [10, "overview"], [11, "overview"], [12, "overview"], [13, "overview"], [14, "overview"], [15, "overview"], [16, "overview"], [17, "overview"], [18, "overview"], [21, "overview"], [26, "overview"], [29, "overview"], [30, "overview"], [31, "overview"], [32, "overview"], [33, "overview"], [34, "overview"], [35, "overview"], [38, "overview"], [40, "overview"], [41, "overview"], [42, "overview"], [43, "overview"], [44, "overview"], [45, "overview"], [46, "overview"], [47, "overview"], [50, "overview"], [51, "overview"], [53, "overview"], [54, "overview"], [55, "overview"], [56, "overview"], [57, "overview"], [58, "overview"], [61, "overview"], [62, "overview"], [64, "overview"], [65, "overview"], [68, "overview"], [69, "overview"], [70, "overview"], [71, "overview"], [74, "overview"], [75, "overview"], [80, "overview"]], "PCI DSS Compliance": [[42, "pci-dss-compliance"]], "PCI DSS Compliance Assessment": [[69, "id3"]], "PCI DSS Implementation": [[42, "id5"]], "Pagination": [[8, "pagination"]], "Parallel Processing": [[59, "parallel-processing"]], "Parametrized Testing": [[65, "parametrized-testing"]], "Password Change": [[38, "password-change"]], "Password Management": [[7, "password-management"]], "Patching and Updates": [[50, "patching-and-updates"]], "Path Discovery Algorithm": [[59, "path-discovery-algorithm"]], "Path Parameters": [[5, "id2"], [5, "id4"], [5, "id5"], [5, "id8"]], "Pattern Recognition Engine": [[75, "pattern-recognition-engine"]], "Performance & Scalability": [[27, "performance-scalability"], [34, null]], "Performance Architecture": [[34, "performance-architecture"]], "Performance Benchmarks": [[34, "id3"]], "Performance Best Practices": [[13, "performance-best-practices"]], "Performance Characteristics": [[29, "performance-characteristics"]], "Performance Considerations": [[6, "performance-considerations"]], "Performance Impact Assessment": [[53, "performance-impact-assessment"]], "Performance Issues": [[17, "performance-issues"], [38, "performance-issues"], [66, "performance-issues"]], "Performance Methodology": [[35, "performance-methodology"]], "Performance Metrics": [[34, "id1"]], "Performance Monitoring": [[2, "performance-monitoring"], [34, "performance-monitoring"], [35, "performance-monitoring"], [61, "performance-monitoring"]], "Performance Monitoring Procedures": [[33, "performance-monitoring-procedures"]], "Performance Optimization": [[4, "performance-optimization"], [11, "performance-optimization"], [12, "performance-optimization"], [15, "performance-optimization"], [30, "performance-optimization"], [32, "performance-optimization"], [34, "performance-optimization"], [35, null], [68, "performance-optimization"], [69, "performance-optimization"], [70, "performance-optimization"], [71, "performance-optimization"], [75, "performance-optimization"], [80, "performance-optimization"]], "Performance Optimization Flow": [[60, "performance-optimization-flow"]], "Performance Optimization Procedures": [[33, "performance-optimization-procedures"]], "Performance Optimizations": [[59, "performance-optimizations"]], "Performance Profiling": [[20, "performance-profiling"]], "Performance Review": [[14, "performance-review"]], "Performance Specifications": [[15, "performance-specifications"]], "Performance Targets by Operation": [[35, "id1"]], "Performance Testing": [[28, "performance-testing"], [34, "performance-testing"], [64, "performance-testing"], [65, "performance-testing"]], "Performance Troubleshooting": [[34, "performance-troubleshooting"]], "Performance Tuning": [[41, "performance-tuning"], [62, "performance-tuning"]], "Performance and Operations": [[62, "performance-and-operations"]], "Performance and Troubleshooting": [[67, "performance-and-troubleshooting"]], "Periodic Assessment Coverage": [[46, "id3"]], "Periodic Security Assessments": [[46, "periodic-security-assessments"]], "Permission Errors": [[66, "permission-errors"]], "Permission Issues": [[2, "permission-issues"]], "Permission System": [[7, "permission-system"]], "Phase 1 Deliverables": [[25, "phase-1-deliverables"]], "Phase 1 Success Metrics": [[25, "phase-1-success-metrics"]], "Phase 1: Detection and Analysis": [[45, "phase-1-detection-and-analysis"]], "Phase 1: Detection and Initial Response (0-15 minutes)": [[43, "phase-1-detection-and-initial-response-0-15-minutes"]], "Phase 1: Infrastructure Setup (Weeks 1-2)": [[24, "phase-1-infrastructure-setup-weeks-1-2"]], "Phase 2 Deliverables": [[25, "phase-2-deliverables"]], "Phase 2 Status (96% Complete)": [[36, "phase-2-status-96-complete"]], "Phase 2 Success Metrics": [[25, "phase-2-success-metrics"]], "Phase 2.5 Integration Steps": [[36, "phase-2-5-integration-steps"]], "Phase 2.5: Enhanced Security Features (Current + 4-6 weeks)": [[25, "phase-2-5-enhanced-security-features-current-4-6-weeks"]], "Phase 2.5: Enhanced Security Features (Immediate - 4-6 weeks)": [[36, "phase-2-5-enhanced-security-features-immediate-4-6-weeks"]], "Phase 2.5: Enhanced Security Features (Immediate - 4-6 weeks) - HIGH PRIORITY": [[24, "phase-2-5-enhanced-security-features-immediate-4-6-weeks-high-priority"]], "Phase 2.5: Enhanced Security Features Integration": [[25, "phase-2-5-enhanced-security-features-integration"]], "Phase 2.5: Immediate Integration (Next 4-6 weeks) - HIGH PRIORITY": [[26, "phase-2-5-immediate-integration-next-4-6-weeks-high-priority"]], "Phase 2: Containment": [[45, "phase-2-containment"]], "Phase 2: Feature Deployment (Weeks 3-18)": [[24, "phase-2-feature-deployment-weeks-3-18"]], "Phase 2: Investigation and Containment (15 minutes - 2 hours)": [[43, "phase-2-investigation-and-containment-15-minutes-2-hours"]], "Phase 2: Threat Intelligence Integration (Q2 2025)": [[63, "phase-2-threat-intelligence-integration-q2-2025"]], "Phase 3 Deliverables": [[25, "phase-3-deliverables"]], "Phase 3 Success Metrics": [[25, "phase-3-success-metrics"]], "Phase 3: Advanced Analytics & Intelligence (Q3 2025 - 6-8 weeks)": [[25, "phase-3-advanced-analytics-intelligence-q3-2025-6-8-weeks"]], "Phase 3: Advanced Analytics & Intelligence (Q3 2025)": [[25, "phase-3-advanced-analytics-intelligence-q3-2025"], [36, "phase-3-advanced-analytics-intelligence-q3-2025"]], "Phase 3: Advanced Analytics (Q3 2025 - 6-8 weeks) - HIGH PRIORITY": [[24, "phase-3-advanced-analytics-q3-2025-6-8-weeks-high-priority"]], "Phase 3: Advanced Analytics (Q3 2025)": [[63, "phase-3-advanced-analytics-q3-2025"]], "Phase 3: Advanced Analytics (Q3 2025) - HIGH PRIORITY": [[26, "phase-3-advanced-analytics-q3-2025-high-priority"]], "Phase 3: Eradication": [[45, "phase-3-eradication"]], "Phase 3: Production Optimization (Weeks 19-24)": [[24, "phase-3-production-optimization-weeks-19-24"]], "Phase 3: Resolution and Recovery (2-8 hours)": [[43, "phase-3-resolution-and-recovery-2-8-hours"]], "Phase 4 Deliverables": [[25, "phase-4-deliverables"]], "Phase 4 Success Metrics": [[25, "phase-4-success-metrics"]], "Phase 4: Automation & Orchestration (Q4 2025)": [[36, "phase-4-automation-orchestration-q4-2025"]], "Phase 4: Automation and Orchestration (Q4 2025)": [[63, "phase-4-automation-and-orchestration-q4-2025"]], "Phase 4: Client Libraries & Automation (Q4 2025 - 6-8 weeks)": [[25, "phase-4-client-libraries-automation-q4-2025-6-8-weeks"]], "Phase 4: Client Libraries & Automation (Q4 2025 - 6-8 weeks) - MEDIUM PRIORITY": [[24, "phase-4-client-libraries-automation-q4-2025-6-8-weeks-medium-priority"]], "Phase 4: Client Libraries & Automation (Q4 2025)": [[25, "phase-4-client-libraries-automation-q4-2025"]], "Phase 4: Client Libraries & Automation (Q4 2025) - MEDIUM PRIORITY": [[26, "phase-4-client-libraries-automation-q4-2025-medium-priority"]], "Phase 4: Communication and Documentation (Ongoing)": [[43, "phase-4-communication-and-documentation-ongoing"]], "Phase 4: Recovery": [[45, "phase-4-recovery"]], "Phase 5: Enterprise Scaling (Q1 2026) - LOWER PRIORITY": [[26, "phase-5-enterprise-scaling-q1-2026-lower-priority"]], "Phase 5: Post-Incident Analysis": [[45, "phase-5-post-incident-analysis"]], "Phase 5: Post-Incident Review (24-72 hours after resolution)": [[43, "phase-5-post-incident-review-24-72-hours-after-resolution"]], "Phase 5: Production Optimization & Scaling (Q1 2026 - 4-6 weeks)": [[25, "phase-5-production-optimization-scaling-q1-2026-4-6-weeks"]], "Phase 5: Production Optimization & Scaling (Q1 2026)": [[25, "phase-5-production-optimization-scaling-q1-2026"]], "Phase 5: Production Optimization (Q1 2026 - 4-6 weeks) - MEDIUM PRIORITY": [[24, "phase-5-production-optimization-q1-2026-4-6-weeks-medium-priority"]], "Phase Integration Plan - Enhanced Features with Existing Roadmap": [[36, null]], "Physical Controls": [[42, "physical-controls"]], "Physical Security Controls": [[42, "id3"]], "Pipeline Commands": [[2, "pipeline-commands"]], "Pipeline Configuration": [[54, "pipeline-configuration"]], "Pipeline Options": [[2, "pipeline-options"]], "Pipeline Overview": [[2, "pipeline-overview"]], "Planned Features": [[30, "planned-features"]], "Planned Improvements": [[3, "planned-improvements"]], "Platform Capabilities and Features": [[27, "platform-capabilities-and-features"]], "Platform Updates and Maintenance": [[70, "platform-updates-and-maintenance"]], "Platform Won\u2019t Start": [[38, "platform-won-t-start"]], "Pod Startup Issues": [[16, "pod-startup-issues"]], "Point-in-Time Recovery": [[31, "point-in-time-recovery"]], "Policy Configuration": [[4, "policy-configuration"]], "Port Conflicts": [[2, "port-conflicts"]], "Post-Deployment Configuration": [[16, "post-deployment-configuration"]], "PostgreSQL Tuning": [[35, "postgresql-tuning"]], "PowerShell Module": [[26, "powershell-module"]], "Practical Focus": [[0, "practical-focus"], [1, "practical-focus"]], "Practical Scenarios:": [[0, "practical-scenarios"]], "Pre-Deployment Backup": [[31, "pre-deployment-backup"]], "Pre-Production Checklist": [[14, "pre-production-checklist"]], "Pre-Review Checklist": [[51, "pre-review-checklist"]], "Pre-commit Security Checks": [[54, "id1"]], "Pre-commit Security Hooks": [[54, "pre-commit-security-hooks"]], "Pre-loaded Threat Actors": [[80, "pre-loaded-threat-actors"]], "Preparedness Activities": [[45, "preparedness-activities"]], "Prerequisites": [[2, "prerequisites"], [3, "prerequisites"], [3, "id2"], [14, "prerequisites"], [16, "prerequisites"], [20, "prerequisites"], [28, "prerequisites"], [30, "prerequisites"], [38, "prerequisites"], [71, "prerequisites"], [75, "prerequisites"], [80, "prerequisites"]], "Preventive Maintenance": [[32, "preventive-maintenance"]], "Primary DAST Tools": [[53, "primary-dast-tools"]], "Primary Goals": [[25, "primary-goals"]], "Primary Responsibilities": [[79, "primary-responsibilities"]], "Primary SAST Tools": [[55, "primary-sast-tools"]], "Privacy (P1)": [[40, "privacy-p1"]], "Privacy Impact Assessment": [[12, "privacy-impact-assessment"]], "Privacy by Design Implementation": [[12, "privacy-by-design-implementation"]], "Privilege Escalation Management": [[4, "privilege-escalation-management"]], "Processing Integrity (PI1)": [[40, "processing-integrity-pi1"]], "Product Overview": [[63, "product-overview"]], "Product Requirements Document": [[62, "product-requirements-document"]], "Product Requirements Document (PRD)": [[63, null]], "Production Architecture": [[22, "production-architecture"], [37, "production-architecture"]], "Production Architecture Overview": [[15, null]], "Production Configuration": [[14, "production-configuration"]], "Production Deployment": [[3, "production-deployment"]], "Production Deployment Guide - Blast-Radius Security Tool": [[16, null]], "Production Deployment Infrastructure - 100% Complete \u2705": [[29, "production-deployment-infrastructure-100-complete"]], "Production Deployment Process": [[14, "production-deployment-process"]], "Production Environment": [[13, "production-environment"], [14, "production-environment"], [29, "production-environment"]], "Production Environment Setup": [[14, "production-environment-setup"]], "Production Infrastructure \u2705": [[26, "production-infrastructure"]], "Production Readiness Indicators": [[22, "production-readiness-indicators"]], "Production Readiness Status": [[37, null]], "Professional Communication": [[47, "professional-communication"]], "Professional Development and Excellence Framework": [[72, "professional-development-and-excellence-framework"]], "Professional Development and Executive Excellence": [[73, "professional-development-and-executive-excellence"]], "Professional Development and Support Resources": [[78, "professional-development-and-support-resources"]], "Professional Quality": [[0, "professional-quality"]], "Profiling Tools": [[35, "profiling-tools"]], "Program Enhancement": [[50, "program-enhancement"]], "Prometheus Configuration Management": [[33, "prometheus-configuration-management"]], "Protect (PR)": [[40, "protect-pr"]], "Public Disclosure": [[47, "public-disclosure"]], "Purple Team Collaboration": [[71, "purple-team-collaboration"]], "Purple Team Members": [[74, "purple-team-members"]], "Purple Team Members Comprehensive Guide": [[76, null]], "Python": [[7, "python"]], "Python Code Style": [[19, "python-code-style"]], "Python SDK": [[5, "python-sdk"], [26, "python-sdk"]], "Python SDK Usage": [[30, "python-sdk-usage"]], "Python Standards": [[18, "python-standards"]], "Qualitative Benefits": [[24, "qualitative-benefits"]], "Quality Assurance": [[3, "quality-assurance"], [3, "id8"], [18, "quality-assurance"], [62, "quality-assurance"]], "Quality Assurance and Improvement": [[46, "quality-assurance-and-improvement"]], "Quality Assurance:": [[0, "quality-assurance"]], "Quality Gates": [[21, "quality-gates"], [64, "quality-gates"]], "Quality Metrics": [[64, "quality-metrics"]], "Quantitative Benefits (Enhanced Timeline)": [[24, "quantitative-benefits-enhanced-timeline"]], "Quantitative Risk Assessment": [[80, "quantitative-risk-assessment"]], "Query Parameters": [[5, "id1"], [5, "id3"], [5, "id6"]], "Quick Diagnostics": [[66, "quick-diagnostics"]], "Quick Installation": [[38, "quick-installation"]], "Quick Reference": [[44, null]], "Quick Security Reference": [[44, "quick-security-reference"]], "Quick Start Example": [[71, "quick-start-example"]], "Quick Start Guide": [[38, null]], "Quick Wins": [[34, "quick-wins"]], "RBAC Implementation": [[11, "rbac-implementation"]], "REST API Design": [[18, "rest-api-design"]], "RESTful API": [[75, "restful-api"]], "Rate Limiting": [[5, "rate-limiting"], [6, "rate-limiting"], [8, "rate-limiting"], [9, "rate-limiting"], [10, "rate-limiting"]], "Real-Time Monitoring": [[38, "real-time-monitoring"], [41, "real-time-monitoring"]], "Real-Time Security Event Correlation": [[11, "real-time-security-event-correlation"]], "Real-time Event Correlation": [[75, "real-time-event-correlation"]], "Recent Security Achievements": [[52, "recent-security-achievements"]], "Recognition": [[19, "recognition"]], "Recognition and Rewards": [[47, "recognition-and-rewards"]], "Recognition and Standards": [[3, "recognition-and-standards"]], "Recommended Resources by Environment Size": [[34, "id2"]], "Recover (RC)": [[40, "recover-rc"]], "Recovery Procedures": [[31, "recovery-procedures"]], "Recovery Testing": [[31, "recovery-testing"]], "Red Team Analysis Results": [[69, "id1"]], "Red Team Best Practices and Operational Excellence": [[77, "red-team-best-practices-and-operational-excellence"]], "Red Team Exercise Workflow": [[74, "red-team-exercise-workflow"]], "Red Team Exercises": [[71, "red-team-exercises"]], "Red Team Members": [[74, "red-team-members"]], "Red Team Members Comprehensive Guide": [[77, null]], "Redis Optimization": [[35, "redis-optimization"]], "Reduced Risk": [[36, "reduced-risk"]], "Refresh Graph Data": [[6, "refresh-graph-data"]], "Regular Training": [[43, "regular-training"]], "Regulatory Compliance": [[45, "regulatory-compliance"], [48, "regulatory-compliance"]], "Regulatory Compliance Assessment": [[80, "regulatory-compliance-assessment"]], "Regulatory Compliance Status": [[52, "regulatory-compliance-status"]], "Regulatory Compliance and Governance Excellence": [[73, "regulatory-compliance-and-governance-excellence"]], "Regulatory Requirements": [[51, "regulatory-requirements"]], "Release Management": [[21, "release-management"]], "Release Notes": [[27, "release-notes"]], "Release Notes Section": [[1, "release-notes-section"]], "Release Process": [[21, "release-process"]], "Remediation Strategies": [[50, "remediation-strategies"]], "Remediation Tracking": [[50, "remediation-tracking"]], "Reporting Dashboard": [[55, "reporting-dashboard"]], "Reporting Schedule": [[40, "reporting-schedule"]], "Reporting and Analysis": [[45, "reporting-and-analysis"]], "Required Software": [[14, "required-software"], [20, "required-software"]], "Required Tools and Access": [[16, "required-tools-and-access"]], "Resource Limits": [[30, "resource-limits"]], "Resource Monitoring": [[2, "resource-monitoring"]], "Resource Requirements": [[15, "resource-requirements"]], "Respond (RS)": [[40, "respond-rs"]], "Response Engine": [[26, "response-engine"]], "Response Process": [[47, "response-process"]], "Response Time Performance": [[52, "id5"]], "Responsible Disclosure Principles": [[47, "responsible-disclosure-principles"]], "Responsive Design": [[3, "responsive-design"]], "Results Processing": [[55, "results-processing"]], "Review Criteria": [[51, "review-criteria"]], "Review Documentation Template": [[51, "review-documentation-template"]], "Review Documentation and Tracking": [[46, "review-documentation-and-tracking"]], "Review Guidelines": [[19, "review-guidelines"]], "Review Process": [[19, "review-process"], [21, "review-process"]], "Review Process Workflow": [[51, "review-process-workflow"]], "Review Quality Metrics": [[46, "review-quality-metrics"]], "Review Requirements": [[21, "review-requirements"]], "Review Tracking and Metrics": [[46, "review-tracking-and-metrics"]], "Review Types": [[51, "review-types"]], "Review Types and Triggers": [[46, "review-types-and-triggers"]], "Reviewer Quality Metrics": [[46, "id7"]], "Risk Assessment": [[10, "risk-assessment"], [63, "risk-assessment"]], "Risk Assessment Engine": [[4, "risk-assessment-engine"]], "Risk Assessment Framework": [[48, "risk-assessment-framework"]], "Risk Assessment Matrix": [[50, "risk-assessment-matrix"]], "Risk Assessment Summary": [[52, "id6"]], "Risk Calculation": [[48, "risk-calculation"]], "Risk Calculation Methodology": [[80, "risk-calculation-methodology"]], "Risk Forecasting": [[26, "risk-forecasting"]], "Risk Levels": [[30, "risk-levels"]], "Risk Management Excellence": [[52, "risk-management-excellence"]], "Risk Prioritization Framework": [[69, "id6"]], "Risk Reduction": [[49, "risk-reduction"]], "Risk Score Calculation": [[30, "risk-score-calculation"]], "Risk Scoring Decision Flow": [[60, "risk-scoring-decision-flow"]], "Risk Scoring Methodology": [[59, "risk-scoring-methodology"], [71, "risk-scoring-methodology"]], "Risk Scoring and Prioritization": [[69, "risk-scoring-and-prioritization"]], "Risk-Based Prioritization": [[50, "risk-based-prioritization"]], "Role Definition and Offensive Security Responsibilities": [[77, "role-definition-and-offensive-security-responsibilities"]], "Role Definition and Responsibilities": [[79, "role-definition-and-responsibilities"]], "Role Definition and Strategic Responsibilities": [[72, "role-definition-and-strategic-responsibilities"], [73, "role-definition-and-strategic-responsibilities"], [76, "role-definition-and-strategic-responsibilities"], [78, "role-definition-and-strategic-responsibilities"]], "Role Template Configuration": [[4, "role-template-configuration"]], "Role-Based Access Control (RBAC)": [[70, "role-based-access-control-rbac"]], "Role-Based Access Control and Permissions": [[79, "role-based-access-control-and-permissions"]], "Role-Based Access Templates": [[4, "role-based-access-templates"]], "Role-Based Guides": [[74, "role-based-guides"]], "Role-Specific Tips": [[74, "role-specific-tips"]], "Roles and Responsibilities": [[43, "roles-and-responsibilities"]], "Rollback Procedure": [[17, "rollback-procedure"]], "Rollback Procedures": [[16, "rollback-procedures"]], "Rollback Strategy": [[36, "rollback-strategy"]], "Routine Maintenance Schedule": [[32, "routine-maintenance-schedule"]], "Run Containers": [[2, "run-containers"]], "Running Attack Simulations": [[80, "running-attack-simulations"]], "Running Tests": [[2, "running-tests"], [64, "running-tests"], [65, "running-tests"]], "Runtime Security Monitoring": [[54, "runtime-security-monitoring"]], "SAST Benefits": [[55, "sast-benefits"]], "SAST Implementation Best Practices": [[55, "sast-implementation-best-practices"]], "SAST Integration in CI/CD": [[55, "sast-integration-in-ci-cd"]], "SAST Metrics and Reporting": [[55, "sast-metrics-and-reporting"]], "SAST Results Analysis": [[55, "sast-results-analysis"]], "SAST Rule Configuration": [[55, "sast-rule-configuration"]], "SAST Tools and Coverage": [[48, "id1"], [50, "id1"]], "SAST Tools and Implementation": [[55, "sast-tools-and-implementation"]], "SDK Examples": [[5, "sdk-examples"]], "SDK and Libraries": [[8, "sdk-and-libraries"]], "SIEM Integration": [[69, "siem-integration"]], "SOAR Integration": [[69, "soar-integration"]], "SOC 2 Compliance": [[40, "soc-2-compliance"]], "SOC 2 Compliance Checklist": [[51, "soc-2-compliance-checklist"]], "SOC 2 Implementation": [[42, "id4"]], "SOC 2 Type II Compliance": [[42, "soc-2-type-ii-compliance"]], "SOC Operations Best Practices and Excellence Framework": [[79, "soc-operations-best-practices-and-excellence-framework"]], "SOC Operators": [[74, "soc-operators"]], "SOC Operators Comprehensive Guide": [[79, null]], "SOLID Principles": [[18, "solid-principles"]], "SOLID Principles Application": [[18, "id2"]], "SSL/TLS Certificate Issues": [[66, "ssl-tls-certificate-issues"]], "STRIDE Threat Analysis": [[46, "id5"]], "Safe Harbor": [[47, "safe-harbor"]], "Scalability Considerations": [[25, "scalability-considerations"]], "Scalability Metrics": [[15, "scalability-metrics"]], "Scalability Patterns": [[34, "scalability-patterns"]], "Scenario 1: Multi-Cloud Asset Inventory": [[68, "scenario-1-multi-cloud-asset-inventory"]], "Scenario 1: Red Team Attack Simulation": [[69, "scenario-1-red-team-attack-simulation"]], "Scenario 2: Continuous Asset Monitoring": [[68, "scenario-2-continuous-asset-monitoring"]], "Scenario 2: Purple Team Validation Exercise": [[69, "scenario-2-purple-team-validation-exercise"]], "Scenario 3: Compliance Asset Reporting": [[68, "scenario-3-compliance-asset-reporting"]], "Scenario 3: Compliance Risk Assessment": [[69, "scenario-3-compliance-risk-assessment"]], "Scenario 4: Incident Response Planning": [[69, "scenario-4-incident-response-planning"]], "Scenario 5: Zero Trust Architecture Planning": [[69, "scenario-5-zero-trust-architecture-planning"]], "Scheduled Discovery": [[68, "scheduled-discovery"]], "Scheduled Maintenance Window": [[32, "scheduled-maintenance-window"]], "Schema Design": [[18, "schema-design"]], "Scope": [[47, "scope"]], "Search Features": [[3, "search-features"]], "Security": [[21, "security"]], "Security & Compliance": [[27, "security-compliance"], [27, null]], "Security & Compliance Section": [[1, "security-compliance-section"]], "Security (CC6)": [[40, "security-cc6"]], "Security Administration": [[70, "security-administration"]], "Security Analysis \u2705": [[26, "security-analysis"]], "Security Architects": [[74, "security-architects"]], "Security Architects Comprehensive Guide": [[78, null]], "Security Architecture": [[15, "security-architecture"], [42, "security-architecture"], [44, "security-architecture"]], "Security Architecture Excellence": [[52, "security-architecture-excellence"]], "Security Architecture Layers": [[39, "security-architecture-layers"]], "Security Architecture Overview": [[39, null]], "Security Assessment": [[71, "security-assessment"]], "Security Assessment Overview": [[48, null]], "Security Assessment Workflow": [[74, "security-assessment-workflow"]], "Security Automation Architecture": [[54, "security-automation-architecture"]], "Security Automation Best Practices": [[54, "security-automation-best-practices"]], "Security Automation KPIs": [[54, "id2"]], "Security Automation Metrics": [[54, "security-automation-metrics"]], "Security Automation Performance": [[52, "id3"]], "Security Best Practices": [[13, "security-best-practices"], [15, "security-best-practices"]], "Security Certifications and Compliance": [[44, "security-certifications-and-compliance"]], "Security Commands": [[2, "security-commands"]], "Security Configuration": [[13, "security-configuration"]], "Security Considerations": [[30, "security-considerations"], [68, "security-considerations"]], "Security Contact Information": [[44, "security-contact-information"]], "Security Controls Framework": [[42, "security-controls-framework"]], "Security Controls Implementation": [[39, "security-controls-implementation"]], "Security Documentation": [[44, null]], "Security Documentation Sections": [[44, "security-documentation-sections"]], "Security Documentation Structure": [[52, "security-documentation-structure"]], "Security Documentation Summary": [[52, null]], "Security Framework": [[42, null], [44, "security-framework"]], "Security Gates": [[48, "security-gates"]], "Security Improvement Metrics": [[52, "id1"]], "Security Improvements Implemented": [[49, "security-improvements-implemented"]], "Security Incident Categories": [[45, "security-incident-categories"]], "Security Incident Response": [[45, null]], "Security Investment ROI": [[52, "security-investment-roi"]], "Security Layer Assessment": [[52, "id2"]], "Security Layer Implementation": [[42, "id1"]], "Security Lead": [[43, "security-lead"]], "Security Maturity Assessment": [[42, "security-maturity-assessment"]], "Security Maturity Levels": [[42, "id8"]], "Security Metrics": [[48, "id3"]], "Security Metrics Comparison": [[49, "id1"]], "Security Metrics and KPIs": [[48, "security-metrics-and-kpis"], [51, "security-metrics-and-kpis"]], "Security Model": [[62, "security-model"]], "Security Monitoring": [[11, "security-monitoring"], [42, "security-monitoring"]], "Security Monitoring and Incident Response": [[52, "security-monitoring-and-incident-response"]], "Security Operations Excellence": [[52, "security-operations-excellence"]], "Security Patches": [[32, "security-patches"]], "Security Principles": [[11, "security-principles"], [44, "security-principles"]], "Security Quality Gates": [[54, "security-quality-gates"]], "Security Recommendations": [[7, "security-recommendations"]], "Security Researcher Recognition": [[47, "security-researcher-recognition"]], "Security Review": [[14, "security-review"]], "Security Review - June 14, 2025": [[49, null]], "Security Review Best Practices": [[46, "security-review-best-practices"]], "Security Review Framework": [[46, "security-review-framework"], [51, "security-review-framework"]], "Security Review Metrics": [[46, "id6"]], "Security Review Procedures": [[46, "security-review-procedures"]], "Security Review Process": [[46, null]], "Security Review Processes - Blast-Radius Security Tool": [[51, null]], "Security Review Templates": [[46, "security-review-templates"]], "Security Review Tools and Automation": [[51, "security-review-tools-and-automation"]], "Security Review Training": [[51, "security-review-training"]], "Security Scan Results": [[49, "security-scan-results"]], "Security Specifications": [[29, "security-specifications"]], "Security Standards": [[2, "security-standards"], [18, "security-standards"]], "Security Test Coverage": [[49, "security-test-coverage"]], "Security Testing": [[64, "security-testing"]], "Security Testing Automation": [[48, "security-testing-automation"], [54, null]], "Security Testing Excellence": [[52, "security-testing-excellence"]], "Security Testing Tools Integration": [[54, "security-testing-tools-integration"]], "Security Testing and Validation": [[39, "security-testing-and-validation"]], "Security Training and Awareness": [[42, "security-training-and-awareness"]], "Security Updates Check": [[32, "security-updates-check"]], "Security Validation": [[11, "security-validation"]], "Security and Authorization": [[7, "security-and-authorization"]], "Security and Compliance": [[62, "security-and-compliance"], [67, "security-and-compliance"], [70, "security-and-compliance"], [70, "id1"]], "Security-Specific Procedures": [[43, "security-specific-procedures"]], "SecurityThreatSpike": [[33, "securitythreatspike"]], "Service Account Management": [[11, "service-account-management"]], "Service Discovery": [[25, "service-discovery"]], "Service Discovery Benefits": [[24, "service-discovery-benefits"]], "ServiceNow Integration": [[13, "servicenow-integration"], [38, "servicenow-integration"]], "Setup Monitoring": [[10, "setup-monitoring"]], "Severity Classification": [[47, "severity-classification"]], "Severity Levels": [[43, "severity-levels"], [45, "severity-levels"]], "Short-Term Objectives (Q3-Q4 2025)": [[52, "short-term-objectives-q3-q4-2025"]], "Short-term Goals (Phase 3 - Q3 2025)": [[36, "short-term-goals-phase-3-q3-2025"]], "Simulate Attack": [[10, "simulate-attack"]], "Slow Response Times": [[66, "slow-response-times"]], "Snapshot Recovery": [[31, "snapshot-recovery"]], "Software Dependencies": [[28, "software-dependencies"]], "Specific Stages": [[2, "specific-stages"]], "Sphinx Build Quality": [[37, "sphinx-build-quality"]], "Staging Configuration": [[14, "staging-configuration"]], "Staging Environment": [[13, "staging-environment"], [14, "staging-environment"]], "Staging Environment Setup": [[14, "staging-environment-setup"]], "Standard Authentication Flow": [[7, "standard-authentication-flow"]], "Start AWS Discovery": [[30, "start-aws-discovery"]], "Start Azure Discovery": [[30, "start-azure-discovery"]], "Start Discovery": [[5, "start-discovery"]], "Start GCP Discovery": [[30, "start-gcp-discovery"]], "Starting Development Services": [[20, "starting-development-services"]], "Statement of Applicability (SoA)": [[40, "statement-of-applicability-soa"]], "Static Application Security Testing (SAST)": [[48, "static-application-security-testing-sast"], [54, "static-application-security-testing-sast"], [55, null]], "Status Tracking": [[50, "status-tracking"]], "Step 10: Backup Configuration": [[16, "step-10-backup-configuration"]], "Step 11: Ongoing Monitoring": [[16, "step-11-ongoing-monitoring"]], "Step 1: Add Sample Assets": [[38, "step-1-add-sample-assets"]], "Step 1: Automated Security Checks": [[51, "step-1-automated-security-checks"]], "Step 1: Clone Repository": [[20, "step-1-clone-repository"]], "Step 1: Environment Configuration": [[16, "step-1-environment-configuration"]], "Step 1: Find or Create an Issue": [[19, "step-1-find-or-create-an-issue"]], "Step 1: Get the Code": [[38, "step-1-get-the-code"]], "Step 2: Backend Setup": [[20, "step-2-backend-setup"]], "Step 2: Fork and Clone": [[19, "step-2-fork-and-clone"]], "Step 2: Infrastructure Provisioning": [[16, "step-2-infrastructure-provisioning"]], "Step 2: Manual Security Review": [[51, "step-2-manual-security-review"]], "Step 2: Run Attack Path Analysis": [[38, "step-2-run-attack-path-analysis"]], "Step 2: Start the Platform": [[38, "step-2-start-the-platform"]], "Step 3: Access the Platform": [[38, "step-3-access-the-platform"]], "Step 3: Create a Branch": [[19, "step-3-create-a-branch"]], "Step 3: Database Setup": [[16, "step-3-database-setup"], [20, "step-3-database-setup"]], "Step 3: Security Testing": [[51, "step-3-security-testing"]], "Step 3: View Results": [[38, "step-3-view-results"]], "Step 4: Container Image Preparation": [[16, "step-4-container-image-preparation"]], "Step 4: Frontend Setup": [[20, "step-4-frontend-setup"]], "Step 4: Make Changes": [[19, "step-4-make-changes"]], "Step 5: Development Tools Setup": [[20, "step-5-development-tools-setup"]], "Step 5: Kubernetes Deployment": [[16, "step-5-kubernetes-deployment"]], "Step 5: Test Your Changes": [[19, "step-5-test-your-changes"]], "Step 6: SSL/TLS Configuration": [[16, "step-6-ssl-tls-configuration"]], "Step 6: Submit Pull Request": [[19, "step-6-submit-pull-request"]], "Step 7: Application Configuration": [[16, "step-7-application-configuration"]], "Step 8: Security Hardening": [[16, "step-8-security-hardening"]], "Step 9: Deployment Verification": [[16, "step-9-deployment-verification"]], "Storage Issues": [[17, "storage-issues"]], "Strategic Security Communication and Stakeholder Management": [[73, "strategic-security-communication-and-stakeholder-management"]], "Strategic Security Investment and Resource Management": [[73, "strategic-security-investment-and-resource-management"]], "Strategic Security Risk Management Framework": [[73, "strategic-security-risk-management-framework"]], "Structure and Organization": [[1, "structure-and-organization"]], "Style Guide": [[3, "style-guide"]], "Success Criteria for Month 1": [[24, "success-criteria-for-month-1"]], "Success Metrics and KPIs": [[63, "success-metrics-and-kpis"]], "Success Response": [[8, "success-response"]], "Support": [[27, "support"]], "Support Channels": [[66, "support-channels"]], "Support Resources": [[74, "support-resources"]], "Support and Community": [[67, "support-and-community"]], "Support and Resources": [[8, "support-and-resources"], [62, "support-and-resources"]], "Supported Cloud Services": [[30, "supported-cloud-services"]], "Sync ATT&CK Data": [[9, "sync-att-ck-data"]], "System Architecture": [[62, "system-architecture"]], "System Configuration and Management": [[70, "system-configuration-and-management"]], "System Health Check (6:00 AM UTC)": [[32, "system-health-check-6-00-am-utc"]], "System Health Monitoring": [[33, "system-health-monitoring"]], "System Monitoring": [[70, "system-monitoring"]], "System Overview": [[59, "system-overview"]], "System Requirements": [[14, "system-requirements"], [20, "system-requirements"], [28, "system-requirements"]], "System Updates and Patches": [[32, "system-updates-and-patches"]], "TAR-001: Graph Processing Engine": [[63, "tar-001-graph-processing-engine"]], "TAR-002: Database Architecture": [[63, "tar-002-database-architecture"]], "TAR-003: API Architecture": [[63, "tar-003-api-architecture"]], "TAR-004: Security Architecture": [[63, "tar-004-security-architecture"]], "TLS 1.3+ Enforcement": [[11, "tls-1-3-enforcement"]], "Table Definitions": [[61, "table-definitions"]], "Table of Contents": [[5, "table-of-contents"], [18, "table-of-contents"], [19, "table-of-contents"], [20, "table-of-contents"], [21, "table-of-contents"], [23, "table-of-contents"], [34, "table-of-contents"], [35, "table-of-contents"], [42, "table-of-contents"], [64, "table-of-contents"], [65, "table-of-contents"], [66, "table-of-contents"], [68, "table-of-contents"], [69, "table-of-contents"], [72, "table-of-contents"], [73, "table-of-contents"], [76, "table-of-contents"], [77, "table-of-contents"], [78, "table-of-contents"], [79, "table-of-contents"]], "Tamper-Proof Audit Chain": [[41, "tamper-proof-audit-chain"]], "Target Users": [[25, "target-users"], [63, "target-users"]], "Technical Architecture Requirements": [[63, "technical-architecture-requirements"]], "Technical Controls": [[42, "technical-controls"]], "Technical Coverage": [[1, "technical-coverage"]], "Technical Documentation": [[27, "technical-documentation"], [62, null]], "Technical Documentation (technical/)": [[3, "id5"]], "Technical Documentation Section": [[1, "technical-documentation-section"]], "Technical Implementation": [[12, "technical-implementation"]], "Technical KPIs": [[24, "technical-kpis"]], "Technical Lead": [[43, "technical-lead"]], "Technical Metrics": [[25, "technical-metrics"], [63, "technical-metrics"]], "Technical Performance Targets": [[36, "technical-performance-targets"]], "Technical Risks": [[63, "technical-risks"]], "Technical Specifications": [[29, "technical-specifications"]], "Technical Support": [[62, "technical-support"]], "Technique Correlation": [[9, "technique-correlation"]], "Technique Correlation Engine": [[75, "technique-correlation-engine"]], "Terms and Conditions": [[47, "terms-and-conditions"]], "Terraform Security Checklist": [[51, "terraform-security-checklist"]], "Test Automation": [[64, "test-automation"]], "Test Categories": [[2, "test-categories"], [64, "test-categories"]], "Test Data Management": [[53, "test-data-management"], [64, "test-data-management"]], "Test Design Principles": [[65, "test-design-principles"]], "Test Environment Management": [[53, "test-environment-management"]], "Test Environment Setup": [[64, "test-environment-setup"]], "Test Execution": [[65, "test-execution"]], "Test Organization": [[18, "test-organization"]], "Test Quality Metrics": [[65, "test-quality-metrics"]], "Test Reporting": [[64, "test-reporting"]], "Test Results": [[49, "test-results"]], "Test Structure": [[19, "test-structure"], [65, "test-structure"]], "Test Writing Guidelines": [[64, "test-writing-guidelines"]], "Test-Driven Development": [[21, "test-driven-development"]], "Testing & Quality Assurance": [[27, "testing-quality-assurance"], [64, null]], "Testing Async Code": [[65, "testing-async-code"]], "Testing Framework": [[62, "testing-framework"], [65, "testing-framework"]], "Testing Guidelines": [[19, "testing-guidelines"]], "Testing Philosophy": [[64, "testing-philosophy"]], "Testing Standards": [[18, "testing-standards"]], "Testing Strategy": [[21, "testing-strategy"]], "Testing and Development": [[8, "testing-and-development"]], "Testing and Quality Assurance": [[62, "testing-and-quality-assurance"]], "Testing and Validation": [[49, "testing-and-validation"]], "Testing in Development": [[20, "testing-in-development"]], "TheHive Connector": [[26, "thehive-connector"]], "TheHive Integration": [[22, "thehive-integration"], [37, "thehive-integration"]], "TheHive Integration - Technical Specification": [[58, null]], "Threat Actor Attribution": [[9, "threat-actor-attribution"], [75, "threat-actor-attribution"]], "Threat Actor Profiles": [[80, "threat-actor-profiles"]], "Threat Actor Simulation": [[10, "threat-actor-simulation"]], "Threat Intelligence": [[8, "threat-intelligence"], [38, "threat-intelligence"]], "Threat Intelligence Enrichment": [[9, "threat-intelligence-enrichment"], [75, "threat-intelligence-enrichment"]], "Threat Intelligence Integration": [[42, "threat-intelligence-integration"], [74, "threat-intelligence-integration"], [80, "threat-intelligence-integration"]], "Threat Intelligence Settings": [[13, "threat-intelligence-settings"]], "Threat Modeling": [[48, "threat-modeling"], [74, "threat-modeling"]], "Threat Modeling & Risk Assessment": [[8, "threat-modeling-risk-assessment"]], "Threat Modeling API Reference": [[10, null]], "Threat Modeling User Guide": [[80, null]], "Threat Modeling Workflow": [[80, "threat-modeling-workflow"]], "Threat Prediction Models": [[26, "threat-prediction-models"]], "Token Refresh": [[7, "token-refresh"]], "Tools and Configuration": [[18, "tools-and-configuration"]], "Tools and Resources": [[43, "tools-and-resources"]], "Track Campaigns": [[9, "track-campaigns"]], "Training Program": [[42, "training-program"]], "Training Program Components": [[42, "id7"]], "Training and Awareness": [[51, "training-and-awareness"]], "Training and Certification": [[74, "training-and-certification"]], "Training and Drills": [[43, "training-and-drills"]], "Training and Preparedness": [[45, "training-and-preparedness"]], "Troubleshooting": [[4, "troubleshooting"], [11, "troubleshooting"], [12, "troubleshooting"], [13, "troubleshooting"], [20, "troubleshooting"], [27, "troubleshooting"], [28, "troubleshooting"], [30, "troubleshooting"], [41, "troubleshooting"], [68, "troubleshooting"], [71, "troubleshooting"]], "Troubleshooting Common Issues": [[16, "troubleshooting-common-issues"]], "Troubleshooting Guide - Blast-Radius Security Tool": [[17, null]], "Troubleshooting Section": [[1, "troubleshooting-section"]], "Troubleshooting and Support": [[70, "troubleshooting-and-support"]], "Trust Boundary Analysis": [[69, "id5"]], "Trust Service Criteria": [[40, "trust-service-criteria"]], "UI Not Loading": [[66, "ui-not-loading"]], "Understanding the Visualization": [[38, "understanding-the-visualization"]], "Unit Testing": [[64, "unit-testing"]], "Unit Testing Guide": [[65, null]], "Update Asset": [[5, "update-asset"]], "Update Management": [[70, "update-management"]], "Update Metadata": [[5, "update-metadata"]], "Updates and Changes": [[47, "updates-and-changes"]], "Usability Features": [[1, "usability-features"]], "Usage": [[30, "usage"]], "Use Case Scenarios": [[68, "use-case-scenarios"], [69, "use-case-scenarios"]], "Use Cases": [[27, null]], "Use Cases Section": [[1, "use-cases-section"]], "Use Cases and Workflows": [[71, "use-cases-and-workflows"]], "User Account Management": [[70, "user-account-management"]], "User Adoption Metrics": [[63, "user-adoption-metrics"]], "User Experience:": [[0, "user-experience"]], "User Guides": [[74, null]], "User Guides (user-guides/)": [[3, "id4"]], "User Guides Section": [[1, "user-guides-section"]], "User Profile Setup": [[38, "user-profile-setup"]], "User Role Coverage": [[1, "user-role-coverage"]], "User and Access Management": [[70, "user-and-access-management"]], "User and Role Management": [[38, "user-and-role-management"]], "User-Centric Organization": [[0, "user-centric-organization"]], "User-Friendly Format": [[1, "user-friendly-format"]], "Using the API": [[71, "using-the-api"]], "Using the Web Interface": [[71, "using-the-web-interface"]], "Verification": [[28, "verification"]], "Verification and Testing": [[16, "verification-and-testing"]], "Versioning Strategy": [[21, "versioning-strategy"]], "Vertical Scaling": [[34, "vertical-scaling"]], "Vision Statement": [[63, "vision-statement"]], "Visualization \ud83d\udd04": [[26, "visualization"]], "Vulnerability Assessment": [[50, "vulnerability-assessment"], [53, "vulnerability-assessment"]], "Vulnerability Classification": [[48, "vulnerability-classification"], [55, "vulnerability-classification"]], "Vulnerability Database": [[50, "vulnerability-database"]], "Vulnerability Disclosure Policy": [[47, null]], "Vulnerability Discovery": [[50, "vulnerability-discovery"]], "Vulnerability KPIs": [[50, "id8"]], "Vulnerability Management": [[48, "vulnerability-management"], [50, null]], "Vulnerability Management Lifecycle": [[50, "vulnerability-management-lifecycle"]], "Vulnerability Management Program": [[52, "vulnerability-management-program"]], "Vulnerability Prioritization": [[50, "vulnerability-prioritization"]], "Vulnerability Reporting Process": [[47, "vulnerability-reporting-process"]], "Vulnerability Response": [[43, "vulnerability-response"]], "Vulnerability Risk Assessment": [[50, "id4"]], "Vulnerability Status Definitions": [[50, "id7"]], "Vulnerability Tracking Fields": [[50, "id6"]], "Ways to Contribute": [[19, "ways-to-contribute"]], "Webhooks": [[8, "webhooks"]], "Week 1 Actions": [[36, "week-1-actions"]], "Week 1-2 Priorities": [[24, "week-1-2-priorities"]], "Week 2 Actions": [[36, "week-2-actions"]], "Weekly Maintenance (Sunday 3:00 AM UTC)": [[32, "weekly-maintenance-sunday-3-00-am-utc"]], "Weekly Performance Review (Monday 10:00 AM)": [[33, "weekly-performance-review-monday-10-00-am"]], "What are the default login credentials?": [[67, "what-are-the-default-login-credentials"]], "What are the system requirements?": [[67, "what-are-the-system-requirements"]], "What is the Blast-Radius Security Tool?": [[67, "what-is-the-blast-radius-security-tool"]], "What\u2019s the roadmap for future features?": [[67, "what-s-the-roadmap-for-future-features"]], "Who should use this tool?": [[67, "who-should-use-this-tool"]], "Why am I not seeing any attack paths?": [[67, "why-am-i-not-seeing-any-attack-paths"]], "Why can\u2019t I access the web interface?": [[67, "why-can-t-i-access-the-web-interface"]], "Why is the platform running slowly?": [[67, "why-is-the-platform-running-slowly"]], "Writing Guidelines": [[19, "writing-guidelines"]], "Writing Unit Tests": [[65, "writing-unit-tests"]], "Your First Attack Path Analysis": [[38, "your-first-attack-path-analysis"]], "Zero Sphinx Warnings Achievement": [[22, "zero-sphinx-warnings-achievement"]], "Zero Trust Implementation": [[42, "zero-trust-implementation"]], "Zero-Trust Architecture": [[22, "zero-trust-architecture"], [37, "zero-trust-architecture"]], "Zero-Trust Architecture - 100% Complete \u2705": [[29, "zero-trust-architecture-100-complete"]], "Zero-Trust Architecture Implementation": [[11, null]], "Zero-Trust Security Implementation": [[15, "zero-trust-security-implementation"]], "pytest Configuration": [[65, "pytest-configuration"]], "\u2696\ufe0f Governance and Leadership": [[3, "governance-and-leadership"]], "\u2705 Completed Documentation Files": [[1, "completed-documentation-files"]], "\ud83c\udf1f Industry Leadership": [[3, "industry-leadership"]], "\ud83c\udf1f Industry Leadership and Recognition": [[23, "industry-leadership-and-recognition"]], "\ud83c\udf89 Conclusion": [[0, "conclusion"], [1, "conclusion"], [37, "conclusion"]], "\ud83c\udf89 Major Achievements": [[22, "major-achievements"]], "\ud83c\udf89 NEW: Complete User Guide Ecosystem": [[27, null]], "\ud83c\udf89 Production Ready Platform": [[27, "production-ready-platform"]], "\ud83c\udf89 Production Status": [[27, null]], "\ud83c\udfa8 Features": [[3, "features"]], "\ud83c\udfa8 Styling and Customization": [[3, "styling-and-customization"]], "\ud83c\udfaf Core Documentation": [[3, "core-documentation"]], "\ud83c\udfaf Current State Analysis": [[36, "current-state-analysis"]], "\ud83c\udfaf Enhanced Vision & Objectives": [[25, "enhanced-vision-objectives"]], "\ud83c\udfaf Enhancement Overview": [[24, "enhancement-overview"]], "\ud83c\udfaf Executive Summary": [[37, "executive-summary"]], "\ud83c\udfaf Framework Data Population": [[56, "framework-data-population"]], "\ud83c\udfaf Key Deliverables by Phase": [[25, "key-deliverables-by-phase"]], "\ud83c\udfaf Key Documentation Features": [[1, "key-documentation-features"], [23, "key-documentation-features"]], "\ud83c\udfaf Key Features": [[3, "key-features"]], "\ud83c\udfaf Key Improvements": [[0, "key-improvements"]], "\ud83c\udfaf Next Immediate Actions": [[24, "next-immediate-actions"]], "\ud83c\udfaf Next Immediate Steps": [[36, "next-immediate-steps"]], "\ud83c\udfc5 Professional Excellence": [[3, "professional-excellence"]], "\ud83c\udfc6 Documentation Excellence": [[3, "documentation-excellence"]], "\ud83c\udfc6 Documentation Excellence Achievements": [[23, "documentation-excellence-achievements"]], "\ud83c\udfd7\ufe0f Architecture & Security Status": [[37, "architecture-security-status"]], "\ud83c\udfd7\ufe0f Development Workflow": [[2, "development-workflow"]], "\ud83c\udfd7\ufe0f Enhanced Technical Architecture": [[25, "enhanced-technical-architecture"]], "\ud83c\udfd7\ufe0f Technical Architecture Documentation": [[22, "technical-architecture-documentation"]], "\ud83c\udfd7\ufe0f Technical Architecture Enhancements": [[24, "technical-architecture-enhancements"]], "\ud83d\udc0d SQLAlchemy Models": [[56, "sqlalchemy-models"]], "\ud83d\udc33 Container Development": [[2, "container-development"]], "\ud83d\udc65 User Impact": [[0, "user-impact"]], "\ud83d\udcc1 New Documentation Structure": [[0, "new-documentation-structure"]], "\ud83d\udcc8 Content Metrics": [[0, "content-metrics"]], "\ud83d\udcc8 Expected Business Impact - Accelerated Through Integration": [[24, "expected-business-impact-accelerated-through-integration"]], "\ud83d\udcc8 Measurable Impact and Value": [[23, "measurable-impact-and-value"]], "\ud83d\udcc8 Next Steps for Production Deployment": [[37, "next-steps-for-production-deployment"]], "\ud83d\udcc8 Performance and SEO": [[3, "performance-and-seo"]], "\ud83d\udcc8 Success Criteria": [[25, "success-criteria"]], "\ud83d\udcc8 Success Metrics Integration": [[36, "success-metrics-integration"]], "\ud83d\udcca API Endpoints": [[57, "api-endpoints"]], "\ud83d\udcca Analytics and Monitoring": [[3, "id7"]], "\ud83d\udcca Assessment Criteria Schema": [[56, "assessment-criteria-schema"]], "\ud83d\udcca Documentation Excellence": [[27, null]], "\ud83d\udcca Documentation Metrics": [[1, "documentation-metrics"]], "\ud83d\udcca Documentation Overview": [[27, "documentation-overview"]], "\ud83d\udcca Documentation Quality Metrics": [[22, "documentation-quality-metrics"], [37, "documentation-quality-metrics"]], "\ud83d\udcca Effort Estimation Summary - Integrated Timeline": [[26, "effort-estimation-summary-integrated-timeline"]], "\ud83d\udcca Expansion Statistics": [[0, "expansion-statistics"]], "\ud83d\udcca Implementation Priority Matrix - Integrated with Existing Phases": [[24, "implementation-priority-matrix-integrated-with-existing-phases"]], "\ud83d\udcca Integration Benefits": [[36, "integration-benefits"]], "\ud83d\udcca Key Performance Indicators": [[25, "key-performance-indicators"]], "\ud83d\udcca Key Statistics": [[3, "key-statistics"]], "\ud83d\udcca Measurable Impact": [[3, "measurable-impact"]], "\ud83d\udcca Monitoring & Debugging": [[2, "monitoring-debugging"]], "\ud83d\udcca Success Metrics": [[0, "success-metrics"]], "\ud83d\udccb Documentation Coverage": [[1, "documentation-coverage"]], "\ud83d\udccb Enterprise-Grade Content": [[3, "enterprise-grade-content"]], "\ud83d\udccb Executive Summary": [[24, "executive-summary"], [36, "executive-summary"]], "\ud83d\udccb Future Expansion Opportunities": [[0, "future-expansion-opportunities"]], "\ud83d\udccb Implementation Priority Matrix - Integrated with Existing Phases": [[26, "implementation-priority-matrix-integrated-with-existing-phases"]], "\ud83d\udccb Success Metrics": [[24, "success-metrics"]], "\ud83d\udcd6 Documentation Quality": [[1, "documentation-quality"]], "\ud83d\udcd6 Documentation Sections": [[3, "documentation-sections"]], "\ud83d\udcd6 User Guides (user-guides/)": [[3, "user-guides-user-guides"]], "\ud83d\udcda Additional Resources": [[2, "additional-resources"]], "\ud83d\udcda Blast-Radius Security Tool Documentation": [[3, null]], "\ud83d\udcda Comprehensive Documentation Ecosystem": [[22, "comprehensive-documentation-ecosystem"]], "\ud83d\udcda Comprehensive User Guide Ecosystem": [[23, "comprehensive-user-guide-ecosystem"]], "\ud83d\udcda Comprehensive User Guides": [[27, "comprehensive-user-guides"]], "\ud83d\udcda Documentation Structure": [[1, "documentation-structure"], [3, "documentation-structure"], [3, "id1"]], "\ud83d\udcda Enterprise User Guides": [[27, null]], "\ud83d\udcde Support": [[3, "support"]], "\ud83d\udd04 API Endpoints": [[58, "api-endpoints"]], "\ud83d\udd04 Celery Tasks for Batch Processing": [[57, "celery-tasks-for-batch-processing"]], "\ud83d\udd04 Implementation Roadmap Details - Integrated Timeline": [[25, "implementation-roadmap-details-integrated-timeline"]], "\ud83d\udd04 Migration Strategy": [[36, "migration-strategy"], [56, "migration-strategy"]], "\ud83d\udd04 Migration and Rollout Plan": [[24, "migration-and-rollout-plan"]], "\ud83d\udd04 Next Steps": [[25, "next-steps"]], "\ud83d\udd0d Search and Navigation": [[3, "search-and-navigation"]], "\ud83d\udd10 Security & Compliance Documentation": [[22, "security-compliance-documentation"]], "\ud83d\udd10 Security & Compliance Frameworks": [[37, "security-compliance-frameworks"]], "\ud83d\udd12 Security-First Development": [[2, "security-first-development"]], "\ud83d\udd17 Integration Architecture": [[58, "integration-architecture"]], "\ud83d\udd17 Integration Strategy": [[36, "integration-strategy"]], "\ud83d\udd27 Additional Build Features": [[3, "additional-build-features"]], "\ud83d\udd27 Build Commands": [[3, "id3"]], "\ud83d\udd27 Implementation Roadmap - Integrated with Existing Phases": [[25, "implementation-roadmap-integrated-with-existing-phases"]], "\ud83d\udd27 Technical Dependencies": [[24, "technical-dependencies"], [26, "technical-dependencies"]], "\ud83d\udd27 Technical Documentation (technical/)": [[3, "technical-documentation-technical"]], "\ud83d\udd27 Technical Implementation": [[0, "technical-implementation"]], "\ud83d\udd27 Technical Operations": [[3, "technical-operations"]], "\ud83d\udd27 Troubleshooting": [[2, "troubleshooting"]], "\ud83d\udd2e Future Enhancements": [[3, "future-enhancements"]], "\ud83d\udd34 New Features to Implement": [[26, "new-features-to-implement"]], "\ud83d\udd34 Offensive Security Operations": [[3, "offensive-security-operations"]], "\ud83d\udd35 Defensive Security Operations": [[3, "defensive-security-operations"]], "\ud83d\uddc4\ufe0f Database Schema Design": [[56, "database-schema-design"]], "\ud83d\ude80 Building the Documentation": [[3, "building-the-documentation"]], "\ud83d\ude80 Deployment": [[3, "deployment"]], "\ud83d\ude80 Deployment Architecture": [[25, "deployment-architecture"]], "\ud83d\ude80 Deployment Readiness": [[37, "deployment-readiness"]], "\ud83d\ude80 Deployment Strategy": [[24, "deployment-strategy"]], "\ud83d\ude80 Future Enhancements and Roadmap": [[23, "future-enhancements-and-roadmap"]], "\ud83d\ude80 Getting Started": [[27, null]], "\ud83d\ude80 Implementation Priorities": [[36, "implementation-priorities"]], "\ud83d\ude80 Next Steps": [[1, "next-steps"]], "\ud83d\ude80 Production Deployment Ready": [[22, "production-deployment-ready"]], "\ud83d\ude80 Quick Start": [[2, "quick-start"], [3, "quick-start"], [27, "quick-start"]], "\ud83d\ude80 Usage Recommendations": [[0, "usage-recommendations"]], "\ud83d\udee0\ufe0f Development": [[3, "development"]], "\ud83d\udee0\ufe0f Local CI/CD Pipeline": [[2, "local-ci-cd-pipeline"]], "\ud83d\udee1\ufe0f Enhanced Security Features": [[25, "enhanced-security-features"]], "\ud83d\udee1\ufe0f Security Documentation (security/)": [[3, "security-documentation-security"]], "\ud83d\udfe1 Partially Implemented Features": [[26, "partially-implemented-features"]], "\ud83d\udfe2 Already Implemented Features": [[26, "already-implemented-features"]], "\ud83e\udde0 Advanced Technical Capabilities": [[37, "advanced-technical-capabilities"]], "\ud83e\udde0 Advanced Technical Specifications": [[22, "advanced-technical-specifications"]], "\ud83e\udde0 ML Architecture Design": [[57, "ml-architecture-design"]], "\ud83e\uddea Testing Strategy": [[2, "testing-strategy"]]}, "docnames": ["DOCUMENTATION_EXPANSION_SUMMARY", "DOCUMENTATION_SUMMARY", "LOCAL-DEVELOPMENT", "README", "access-control/least-privilege-framework", "api/asset-management", "api/attack-path-analysis", "api/authentication", "api/index", "api/mitre-attack-integration", "api/threat-modeling", "architecture/zero-trust-architecture", "compliance/gdpr-compliance-framework", "configuration", "deployment/environment-setup", "deployment/production-architecture", "deployment/production-deployment-guide", "deployment/troubleshooting-guide", "development/code-standards", "development/contributing", "development/setup", "development/workflow", "documentation-achievements-summary", "documentation-overview", "enhanced-features-summary", "enhanced-prd-v2", "implementation-gap-analysis", "index", "installation", "latest-implementations-summary", "multi-cloud-integration", "operations/runbooks/backup-recovery-runbooks", "operations/runbooks/maintenance-procedures", "operations/runbooks/monitoring-runbooks", "performance/index", "performance/optimization", "phase-integration-plan", "production-readiness-status", "quick-start-guide", "security/architecture/overview", "security/compliance-documentation", "security/enhanced-audit-logging", "security/framework", "security/incident-response-procedures", "security/index", "security/operations/incident-response", "security/procedures/security-review-process", "security/procedures/vulnerability-disclosure", "security/reviews/security-assessment", "security/reviews/security-review-2025-06-14", "security/reviews/vulnerability-management", "security/security-review-processes", "security/security-summary", "security/testing/dynamic-testing", "security/testing/security-automation", "security/testing/static-analysis", "technical-specifications/compliance-framework-schema", "technical-specifications/ml-threat-prediction", "technical-specifications/thehive-integration", "technical/attack-path-architecture", "technical/attack-path-flows", "technical/database-design", "technical/index", "technical/product-requirements", "testing/index", "testing/unit-tests", "troubleshooting/common-issues", "troubleshooting/faq", "use-cases/asset-discovery", "use-cases/attack-path-analysis", "user-guides/administrators", "user-guides/attack-path-analysis", "user-guides/compliance-officers", "user-guides/executive-leadership", "user-guides/index", "user-guides/mitre-attack-integration", "user-guides/purple-team-members", "user-guides/red-team-members", "user-guides/security-architects", "user-guides/soc-operators", "user-guides/threat-modeling"], "envversion": {"sphinx": 62, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.intersphinx": 1, "sphinx.ext.todo": 2, "sphinx.ext.viewcode": 1}, "filenames": ["DOCUMENTATION_EXPANSION_SUMMARY.md", "DOCUMENTATION_SUMMARY.md", "LOCAL-DEVELOPMENT.md", "README.md", "access-control/least-privilege-framework.rst", "api/asset-management.rst", "api/attack-path-analysis.rst", "api/authentication.rst", "api/index.rst", "api/mitre-attack-integration.rst", "api/threat-modeling.rst", "architecture/zero-trust-architecture.rst", "compliance/gdpr-compliance-framework.rst", "configuration.rst", "deployment/environment-setup.md", "deployment/production-architecture.rst", "deployment/production-deployment-guide.md", "deployment/troubleshooting-guide.md", "development/code-standards.rst", "development/contributing.rst", "development/setup.rst", "development/workflow.rst", "documentation-achievements-summary.rst", "documentation-overview.rst", "enhanced-features-summary.md", "enhanced-prd-v2.md", "implementation-gap-analysis.md", "index.rst", "installation.rst", "latest-implementations-summary.rst", "multi-cloud-integration.md", "operations/runbooks/backup-recovery-runbooks.md", "operations/runbooks/maintenance-procedures.md", "operations/runbooks/monitoring-runbooks.md", "performance/index.rst", "performance/optimization.rst", "phase-integration-plan.md", "production-readiness-status.rst", "quick-start-guide.rst", "security/architecture/overview.rst", "security/compliance-documentation.md", "security/enhanced-audit-logging.rst", "security/framework.rst", "security/incident-response-procedures.md", "security/index.rst", "security/operations/incident-response.rst", "security/procedures/security-review-process.rst", "security/procedures/vulnerability-disclosure.rst", "security/reviews/security-assessment.rst", "security/reviews/security-review-2025-06-14.rst", "security/reviews/vulnerability-management.rst", "security/security-review-processes.md", "security/security-summary.rst", "security/testing/dynamic-testing.rst", "security/testing/security-automation.rst", "security/testing/static-analysis.rst", "technical-specifications/compliance-framework-schema.md", "technical-specifications/ml-threat-prediction.md", "technical-specifications/thehive-integration.md", "technical/attack-path-architecture.rst", "technical/attack-path-flows.rst", "technical/database-design.rst", "technical/index.rst", "technical/product-requirements.rst", "testing/index.rst", "testing/unit-tests.rst", "troubleshooting/common-issues.rst", "troubleshooting/faq.rst", "use-cases/asset-discovery.rst", "use-cases/attack-path-analysis.rst", "user-guides/administrators.rst", "user-guides/attack-path-analysis.rst", "user-guides/compliance-officers.rst", "user-guides/executive-leadership.rst", "user-guides/index.rst", "user-guides/mitre-attack-integration.rst", "user-guides/purple-team-members.rst", "user-guides/red-team-members.rst", "user-guides/security-architects.rst", "user-guides/soc-operators.rst", "user-guides/threat-modeling.rst"], "indexentries": {}, "objects": {}, "objnames": {}, "objtypes": {}, "terms": {"": [0, 7, 14, 16, 18, 19, 21, 26, 27, 28, 31, 32, 33, 37, 38, 40, 44, 47, 48, 50, 53, 54, 55, 65, 66, 70, 71, 76, 77, 78, 79], "0": [4, 5, 6, 8, 9, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 24, 26, 27, 28, 30, 31, 32, 33, 34, 35, 37, 41, 42, 46, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 59, 61, 64, 65, 66, 67, 68, 69, 71, 74, 75, 79, 80], "00": [5, 16, 18, 31, 56], "000": [3, 15, 23, 27, 33, 37, 41, 47, 67], "000000": [18, 56], "000_initi": 18, "001": [5, 9, 69, 75], "001_attack_path": 18, "002": 69, "0044": 6, "00z": [5, 6, 7, 8, 18, 75], "01": [4, 5, 7, 18, 31, 34, 41, 56, 59, 65, 75], "01t00": 5, "02": 5, "03": [16, 41], "04": [5, 16, 19, 20, 28], "041": [3, 22, 23, 27, 37], "05": [16, 80], "06": 47, "07": 16, "0_linux_amd64": 16, "0f": [71, 80], "0m": 54, "1": [2, 3, 4, 5, 6, 8, 9, 12, 13, 15, 18, 21, 22, 23, 27, 29, 30, 31, 32, 33, 34, 35, 37, 39, 40, 41, 42, 44, 46, 48, 50, 52, 53, 54, 55, 59, 61, 62, 63, 64, 65, 66, 67, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80], "10": [0, 5, 6, 8, 12, 13, 17, 18, 19, 20, 21, 23, 27, 28, 30, 31, 32, 34, 35, 38, 41, 42, 44, 46, 48, 50, 52, 53, 54, 56, 57, 59, 61, 63, 64, 65, 66, 67, 68, 69, 77, 78, 79, 80], "100": [0, 1, 3, 5, 8, 10, 12, 13, 15, 16, 17, 18, 23, 30, 32, 33, 34, 35, 40, 41, 43, 46, 48, 49, 52, 56, 57, 59, 61, 62, 63, 64, 68, 71, 75], "1000": [5, 6, 8, 9, 10, 13, 15, 16, 17, 27, 29, 32, 34, 35, 41, 57, 59, 62, 63, 75, 77, 78], "10000": [9, 10, 15, 29, 32, 34, 35, 41, 80], "100000": 29, "10000000": 29, "100000000": [29, 80], "1000m": [17, 34], "10021": 53, "100gb": [20, 28, 67], "100k": [15, 24, 25, 27, 34], "100m": [29, 34, 61, 62, 63, 80], "100mb": 13, "101": 5, "1024": 35, "10485760": 13, "10k": 34, "10m": [15, 24, 25, 27, 29, 34, 35, 36, 63, 71], "10mb": 13, "10tb": 29, "10x": 63, "11": [2, 3, 14, 18, 20, 28, 32, 35, 42, 53, 54, 55, 64, 65, 67], "11223344": 7, "11t22": [6, 8], "12": [6, 8, 12, 13, 16, 18, 26, 31, 38, 42, 47, 48, 49, 52, 59, 63, 64, 68, 69, 76, 77, 78], "120": [6, 47], "123": [4, 18, 21, 41, 68], "1234": [5, 21, 30], "12345": 30, "123456": 7, "12345678": [7, 30], "123456789012": [30, 66], "1234567890abcdef0": [5, 65], "123e4567": 7, "1247": 5, "1248": 5, "1249": 5, "1250": [6, 8], "125000": 6, "127": [5, 14, 20], "128": [34, 41], "128k": [34, 35], "12d3": 7, "12gb": [34, 35], "13": [24, 25, 26, 47, 63], "133": 5, "14": [26, 36, 44, 47, 48, 50, 52, 67, 74, 75, 78, 79], "145": 6, "15": [0, 1, 5, 6, 12, 16, 18, 20, 22, 23, 27, 28, 30, 32, 33, 34, 37, 41, 42, 45, 47, 52, 53, 56, 64, 67, 68, 69, 72, 74, 78, 79], "150": 12, "15000": 6, "150m": 34, "156": [5, 68], "15t10": [5, 7, 18, 75], "16": [5, 12, 16, 25, 28, 34, 56, 65, 68, 79], "1639234567": [6, 8], "1642248000": [5, 10], "168": [4, 5, 18, 35, 53, 64, 65, 66, 68, 75, 80], "16gb": [14, 20, 28, 35, 37, 67], "17": [12, 30], "1705312200": 7, "1705314000": 7, "172": [65, 68], "18": [2, 6, 12, 14, 20, 26, 28, 32, 36, 42, 52, 67, 78], "180": 27, "1800": [7, 13, 35], "183": [3, 23, 27, 37], "192": [5, 18, 35, 53, 64, 65, 66, 68, 75], "1_hour_crit": 42, "1f": [71, 80], "1gb": [34, 35, 66], "1gi": 17, "1h": 33, "1k": 34, "1x": 78, "1y": 35, "2": [2, 3, 4, 5, 6, 8, 9, 15, 18, 21, 22, 27, 30, 31, 33, 34, 35, 37, 39, 41, 44, 46, 47, 48, 50, 52, 54, 59, 61, 64, 65, 67, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80], "20": [0, 5, 6, 8, 12, 13, 14, 15, 17, 19, 20, 21, 22, 28, 30, 32, 33, 34, 35, 37, 41, 50, 57, 58, 59, 61, 63, 65, 80], "200": [7, 8, 18, 34, 35, 38, 53, 54, 64, 65], "2000": [9, 15, 34], "200k": 52, "200m": [24, 25, 34, 35, 36, 62, 63], "201": [8, 18, 53], "2012": 30, "2018": 56, "2021": 16, "2023": 5, "2024": [5, 6, 7, 8, 18, 21, 31, 41, 56, 75], "2025": [37, 44, 45, 46, 47, 48, 50], "2026": 36, "204": 18, "20m": 34, "21": [12, 16], "22": [5, 28, 68], "23": [5, 6, 18, 54, 64], "24": [5, 6, 8, 12, 13, 22, 23, 26, 27, 33, 34, 36, 37, 41, 42, 45, 47, 48, 50, 51, 52, 64, 66, 68, 69, 72, 73, 74, 76, 77, 78, 79], "245": 8, "247": 68, "24h": [31, 32, 75], "24x7": 42, "25": [1, 6, 24, 26, 30, 35, 52, 69], "250": 47, "255": [18, 56, 58, 61, 65], "256": [11, 13, 28, 29, 39, 41, 42, 44, 49, 52, 54, 62, 63, 65, 67], "256k": [34, 35], "256mb": [34, 35, 66], "27001": [22, 23, 24, 25, 26, 27, 37, 39, 41, 44, 46, 48, 52, 56, 63, 67, 69, 72, 78], "27002": 78, "27005": 78, "276": [3, 23, 27, 37], "28": [16, 49], "285": 6, "295": 68, "2e": 53, "2f": [53, 58], "2fetc": 53, "2fpasswd": 53, "2g": [35, 66], "2gb": 63, "2gi": 17, "2h": 32, "2x": 34, "2xlarg": 15, "3": [2, 3, 4, 5, 6, 8, 13, 15, 18, 21, 27, 29, 30, 31, 33, 34, 35, 37, 39, 40, 41, 42, 44, 46, 48, 50, 52, 53, 54, 55, 59, 61, 62, 64, 65, 66, 67, 71, 72, 73, 74, 75, 76, 77, 78, 79], "30": [4, 5, 6, 7, 8, 12, 13, 15, 16, 18, 20, 21, 24, 25, 28, 29, 30, 31, 32, 33, 34, 35, 38, 40, 41, 42, 43, 45, 47, 48, 50, 52, 54, 56, 58, 59, 61, 63, 64, 65, 68, 75, 78, 79], "300": [0, 1, 5, 6, 8, 13, 31, 32, 34, 35, 63, 66, 68, 71, 79], "3000": [13, 14, 20, 28, 33, 38, 64, 66, 67], "3001": 28, "30d": 75, "31": [24, 26, 41], "312": [3, 23, 27, 37], "32": [16, 34, 35, 49, 52, 66], "327": [49, 52], "32gb": [20, 28], "3306": 5, "3389": 68, "34": 63, "3420": 6, "35": [5, 30, 78], "36": 78, "3600": [13, 34, 35, 59, 68], "365": [12, 41, 42, 66], "370": [3, 23, 27, 37], "374": [3, 23], "39": [30, 71], "3d": 77, "3f": 57, "4": [2, 4, 5, 6, 8, 13, 15, 17, 21, 22, 28, 29, 31, 33, 34, 35, 37, 40, 42, 46, 48, 50, 52, 54, 59, 61, 64, 65, 67, 68, 71, 72, 73, 74, 76, 77, 78, 79], "40": [0, 3, 5, 23, 24, 25, 30, 34, 35, 36, 46, 50, 59, 61, 63, 71, 79], "400": [6, 8, 18, 34, 35, 51, 53, 58], "400m": 34, "401": [8, 18, 53], "403": [8, 18, 53], "404": [6, 8, 18, 53, 57, 58, 66], "409": [8, 18], "4096": 66, "42": 57, "422": [3, 8, 18, 22, 23, 27, 37, 53], "423": [6, 8, 53], "426614174000": 7, "429": [8, 53], "443": [5, 16, 17, 32, 66, 68], "446": [3, 22, 23, 27, 37], "45": [6, 30, 42, 46, 52, 68, 78], "450": 69, "456": 4, "470": [3, 22, 23, 27, 37], "48": [47, 50, 51, 52, 69], "480": [13, 66], "488": [23, 27], "4g": [34, 35, 66], "4gb": [34, 35], "4gi": [17, 33], "4h": 69, "4m": 45, "4xlarg": 15, "5": [1, 2, 5, 6, 8, 13, 17, 18, 21, 23, 28, 30, 31, 32, 33, 34, 35, 37, 38, 41, 42, 46, 47, 48, 49, 50, 52, 53, 54, 57, 59, 61, 63, 64, 65, 66, 67, 68, 71, 72, 73, 76, 77, 78, 79, 80], "50": [0, 1, 3, 5, 12, 13, 23, 30, 33, 34, 35, 38, 47, 54, 56, 57, 58, 59, 61, 63, 79], "500": [6, 8, 9, 16, 18, 34, 56, 58, 64, 66, 77], "5000": [34, 80], "50000": 80, "500000": 80, "500gb": 28, "500k": 80, "500m": [17, 34, 35], "502": [49, 52], "503": 8, "5050": 20, "50gb": [14, 28, 67], "50m": 29, "51": 5, "512": 35, "514": [23, 27], "519": [3, 22, 23, 27, 37], "522": [3, 22, 23, 27, 37], "53": 16, "5432": [2, 13, 14, 16, 17, 20, 28, 33, 66], "551": [3, 22, 23, 27, 37], "569": [23, 27], "57": 49, "58": [63, 68], "59": [30, 71], "5g": 78, "5k": 34, "5m": [34, 35, 54], "6": [3, 6, 13, 15, 18, 21, 23, 27, 31, 33, 37, 42, 48, 50, 52, 54, 63, 64, 65, 69, 71, 74, 76, 78, 79, 80], "60": [4, 5, 6, 8, 13, 21, 23, 24, 25, 30, 34, 35, 36, 47, 54, 61, 62, 63, 64, 66, 67, 71, 73, 77, 78, 79], "600": [5, 34], "61": 5, "62": [48, 49, 52], "63": 5, "6379": [13, 14, 16, 20, 28, 66], "64": [34, 35, 41], "64mb": [34, 35], "65": [5, 35, 76], "65535": 61, "69": 56, "6m": 75, "7": [3, 4, 5, 13, 19, 20, 22, 23, 26, 27, 28, 31, 32, 33, 36, 37, 40, 41, 42, 46, 48, 50, 52, 54, 57, 61, 64, 65, 67, 68, 69, 72, 73, 74, 75, 76, 77, 78, 79, 80], "70": [21, 23, 34, 56, 73], "72": [12, 40, 45, 47, 48, 50, 52, 68, 79], "720h": 32, "75": [3, 6, 13, 23, 35, 52, 72, 73, 76, 77, 80], "7687": [13, 20, 28, 66], "79": [30, 71], "7d": 75, "8": [4, 5, 6, 13, 18, 19, 20, 22, 26, 28, 32, 34, 35, 36, 37, 42, 48, 50, 54, 59, 66, 67, 68, 69, 75, 76, 78, 80], "80": [3, 23, 24, 25, 30, 32, 34, 35, 36, 51, 52, 54, 56, 61, 63, 68, 71, 72, 75, 76, 78], "800": [0, 74, 75, 76, 77, 78, 79], "8000": [2, 3, 13, 14, 16, 17, 20, 24, 28, 30, 31, 32, 34, 35, 38, 53, 55, 66, 67], "802": 78, "8080": [3, 11], "8088": 68, "80m": 34, "8125": 54, "85": [3, 6, 18, 23, 24, 25, 33, 36, 46, 52, 72, 73, 77, 79, 80], "8601": 31, "86400": [13, 32], "87": 6, "87654321": 7, "877": [22, 37], "88": [6, 18, 67, 79], "892": 68, "8g": [34, 35], "8gb": [14, 28, 34, 35, 38, 67], "9": [2, 3, 15, 20, 24, 28, 29, 31, 34, 35, 36, 42, 46, 48, 50, 52, 56, 57, 59, 61, 63, 66, 69, 80], "90": [1, 4, 12, 13, 18, 21, 23, 24, 34, 46, 47, 48, 50, 52, 56, 57, 61, 62, 63, 64, 65, 67, 72, 73, 76, 77, 78, 79, 80], "900": [34, 35], "9000": 3, "9090": [3, 13, 17, 33], "90d": 75, "91": 19, "9121": 34, "9187": 34, "92": [6, 46, 48, 52, 54], "94": [48, 52, 56], "95": [1, 5, 6, 8, 18, 24, 25, 26, 34, 36, 40, 42, 46, 48, 50, 51, 52, 54, 56, 62, 63, 64, 65, 72, 76, 77, 78, 79], "96": [24, 25, 26, 48, 52], "97": [42, 52], "98": [36, 46, 48, 50, 52, 54], "99": [15, 24, 29, 36, 42, 52, 63], "999": 10, "A": [3, 8, 14, 17, 19, 24, 30, 33, 45, 48, 61, 78], "AND": [18, 32, 35, 61, 68], "AS": [32, 33, 61], "AT": 40, "As": [43, 69, 70, 72, 73, 76, 77, 78, 79], "BE": 40, "BY": [17, 20, 32, 33, 34, 35, 61, 66], "Be": [19, 21, 47], "By": [54, 64, 71], "FOR": [35, 61], "For": [2, 3, 5, 7, 9, 10, 13, 16, 18, 19, 20, 21, 26, 27, 28, 34, 35, 38, 39, 42, 44, 45, 47, 53, 55, 59, 64, 65, 67, 69, 74, 75, 80], "IF": 61, "IN": [35, 61], "INTO": 53, "IT": [5, 42, 72, 78, 79], "If": [6, 18, 19, 28, 33, 38, 44, 53, 66], "In": [3, 25, 27, 35, 36, 38, 42, 50, 59], "It": [26, 52, 55, 67], "NOT": [12, 18, 32, 41, 47, 56, 61, 66], "No": [3, 5, 17, 18, 19, 21, 24, 26, 30, 40, 42, 45, 47, 51, 53, 57, 58, 64, 66, 67, 69, 71], "Not": [6, 8, 18, 35], "ON": [16, 17, 18, 35, 41, 56, 61, 66], "OR": [20, 53, 54, 61, 64], "On": [7, 17, 33, 43, 62, 68, 79], "One": [10, 19, 64, 79], "Or": [2, 19, 20, 67], "THEN": 61, "TO": 16, "The": [0, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 18, 21, 22, 23, 24, 25, 27, 29, 30, 33, 34, 36, 37, 38, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 52, 54, 56, 57, 58, 59, 60, 61, 62, 63, 64, 67, 68, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80], "Then": 19, "There": 19, "These": [18, 46, 60, 65], "To": [19, 38, 47, 63], "WITH": [16, 18, 41, 56, 61], "Will": [20, 65], "With": [52, 66, 79], "_": [53, 61, 65], "__aenter__": [35, 58], "__aexit__": [35, 58], "__class__": 58, "__init__": [7, 19, 26, 35, 42, 46, 53, 54, 55, 57, 58, 59], "__main__": 65, "__name__": [18, 19, 35, 57, 58, 65], "__post_init__": 58, "__pycache__": 20, "__repr__": 65, "__table_args__": 56, "__tablename__": [26, 56, 57, 58], "_access_ord": 35, "_add_incident_observ": 58, "_add_incident_task": 58, "_analyze_single_target": 59, "_build": 3, "_build_case_descript": 58, "_cach": 35, "_calculate_behavioral_featur": 57, "_calculate_edge_likelihood": 59, "_calculate_impact_metr": 59, "_calculate_network_featur": 57, "_calculate_path_risk": 35, "_calculate_propagation_prob": 59, "_calculate_vulnerability_featur": 57, "_categorize_risk": 57, "_count_by_sever": 53, "_count_by_tool": 53, "_encode_categor": 57, "_evaluate_model_perform": 57, "_fetch_mitre_techniqu": 35, "_generate_case_tag": 58, "_generate_recommend": [53, 55], "_generate_threat_alert": 57, "_get_active_asset_id": 57, "_get_asset_data": 57, "_get_files_with_critical_issu": 55, "_get_thehive_config": 58, "_get_user_token": 53, "_id": 58, "_internal_method": 18, "_is_false_posit": 53, "_load_mitre_map": 59, "_load_model": 57, "_map_bandit_sever": 55, "_map_indicator_to_observable_typ": 58, "_map_semgrep_sever": 55, "_perform_attack_analysi": 35, "_precompute_shortest_path": 35, "_prepare_training_data": 57, "_process_case_update_webhook": 58, "_rank_path": 59, "_save_model": 57, "_search": 58, "_store_case_map": 58, "_store_l1": 35, "_store_prediction_result": 57, "_templat": 3, "_update_incident_from_case_data": 58, "_update_incident_from_case_statu": 58, "_validate_asset": 19, "_validate_input": 18, "_validate_webhook_signatur": 58, "a1b2c3d4e5f6": 75, "a456": 7, "aaa": [19, 64, 65], "abac": 78, "abil": 79, "abort": 14, "about": [5, 7, 9, 18, 19, 39, 44, 47, 53, 55, 67, 79], "abov": [33, 43], "abstract": 18, "abus": [6, 62, 76, 77, 79], "ac": [40, 50], "academ": [76, 77, 78, 79], "acceler": [23, 26, 73], "accept": [14, 19, 21, 30, 40, 47, 50, 53, 62, 63, 65, 70, 78], "access": [0, 3, 6, 7, 8, 11, 12, 14, 15, 19, 20, 26, 27, 28, 30, 33, 35, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 59, 61, 62, 63, 66, 68, 69, 71, 72, 73, 74, 75, 76, 77, 78, 80], "access_certif": 42, "access_control": [29, 42, 80], "access_control_polici": 42, "access_key_id": [30, 68], "access_log": 35, "access_method": 41, "access_pattern_anomali": 57, "access_performance_metr": 79, "access_raw_data": 79, "access_request": 4, "access_review": 4, "access_reviews_quarterli": 42, "access_token": [7, 8, 64], "access_typ": 4, "accomplish": [0, 49], "accord": 70, "accordingli": [11, 68], "account": [6, 7, 8, 12, 13, 16, 28, 29, 30, 38, 40, 41, 42, 45, 46, 47, 50, 53, 58, 66, 67, 68, 69, 71, 72, 77, 78, 79, 80], "account_id": 14, "account_lockout_mechan": 42, "accumul": 55, "accur": [1, 12, 63, 69, 71, 77, 79], "accuraci": [0, 1, 12, 24, 25, 33, 34, 36, 40, 42, 46, 55, 57, 63, 76, 77, 78, 79], "achiev": [3, 25, 27, 37, 49, 63, 72, 73, 76, 77, 78, 79], "acid": 63, "acknowledg": [47, 79], "acknowledge_alert": 79, "aclos": 58, "acm": [16, 17, 32], "acquir": 78, "acquisit": [73, 77, 78], "across": [0, 1, 3, 5, 18, 22, 23, 24, 27, 29, 30, 34, 37, 38, 42, 46, 49, 50, 52, 56, 63, 64, 68, 69, 71, 72, 74, 75, 76, 77, 78, 79], "act": [19, 47, 64, 65, 72, 78, 80], "action": [0, 1, 3, 11, 18, 19, 21, 23, 25, 30, 33, 41, 42, 46, 47, 48, 51, 53, 56, 58, 62, 63, 64, 65, 66, 67, 68, 71, 74, 76, 77, 78, 79], "activ": [0, 2, 4, 5, 7, 8, 11, 12, 14, 20, 25, 28, 29, 30, 32, 34, 38, 40, 41, 42, 47, 50, 56, 58, 61, 62, 63, 65, 68, 69, 70, 72, 74, 76, 77, 78, 79], "active_connect": 32, "active_job": 30, "actor": [6, 8, 23, 27, 38, 39, 41, 42, 59, 63, 67, 69, 70, 71, 74, 76, 77, 78, 79], "actor_id": 80, "actor_prob": 75, "actor_scor": 69, "actor_techniqu": 69, "actual": [0, 21, 52, 53, 66, 67, 71, 74, 79], "acumen": [72, 76, 77, 78, 79], "ad": [1, 19, 21, 24, 25, 26, 30, 36, 42, 46, 58, 64, 66, 69, 70], "adapt": [39, 63, 76, 77, 78], "add": [2, 3, 5, 17, 18, 19, 20, 21, 24, 25, 30, 32, 33, 36, 56, 58, 59, 64, 65, 68, 79], "add_argu": 64, "add_asset": 59, "add_compliance_framework": 56, "add_find": 46, "add_head": 35, "add_network_rang": 68, "add_observ": 58, "add_provid": 68, "add_relationship": 59, "add_task": 58, "addit": [16, 19, 28, 29, 30, 35, 38, 44, 45, 47, 51, 53, 55, 59, 67, 68, 69, 71, 77, 79], "additional_context": 75, "additional_depend": [18, 54], "addon": 32, "addopt": [64, 65], "address": [1, 3, 14, 18, 19, 21, 31, 38, 47, 51, 52, 54, 63, 65, 71, 78, 80], "adequ": [40, 42, 47, 79], "adequaci": [78, 79], "adher": [47, 52, 72, 77, 79], "adjac": 50, "adjust": [13, 17, 33, 34, 35, 41, 67, 70, 73, 75, 77, 78, 79], "admin": [4, 6, 13, 14, 16, 20, 33, 38, 47, 53, 64, 65, 66, 71], "admin123": 14, "admin_endpoint": 53, "admin_us": 65, "admin_workst": 6, "administ": 72, "administr": [1, 3, 4, 7, 13, 16, 22, 23, 27, 28, 37, 38, 41, 45, 47, 50, 62, 67, 77, 78, 79], "admiss": 45, "admonit": 3, "adopt": [0, 1, 23, 24, 26, 39, 50, 72, 73, 76, 77, 78, 79], "advanc": [0, 1, 6, 7, 8, 11, 23, 27, 29, 30, 48, 50, 52, 55, 65, 67, 71, 74], "advanced_attack_path_analysi": 77, "advanced_compliance_manag": 72, "advanced_evasion_techniqu": 77, "advanced_exercise_coordin": 76, "advantag": [3, 23, 52, 72, 73, 76, 77, 78], "adversari": [6, 76], "advisor": 45, "advisori": [32, 44, 47, 50, 72, 74, 76, 77, 78, 79], "ae": [11, 13, 29, 39, 40, 42, 44, 49, 52, 62, 63, 67], "aes_decrypt": 42, "aes_encrypt": 42, "aes_encrypt_hsm": 42, "affect": [24, 31, 43, 45, 47, 50, 51, 58, 63, 67, 71, 75, 79], "affected_asset": [6, 42, 56, 58, 59, 71, 75, 80], "affected_assets_count": 58, "affected_data_subject": 12, "affected_servic": 45, "affected_system": 51, "after": [14, 16, 20, 21, 22, 28, 31, 35, 37, 38, 40, 47, 49, 52, 53, 67, 68, 69, 71, 79], "afternoon": 78, "ag": [14, 28], "against": [0, 10, 11, 40, 42, 47, 48, 52, 75, 76, 77, 78, 79], "agenc": 45, "agenda": 43, "agent": [0, 5, 79, 80], "aggreg": [14, 15, 59, 73, 78, 79], "agil": 78, "agnost": [24, 25], "ago": [31, 33], "agre": 71, "agreement": [40, 47, 51, 72, 78], "ahead": [35, 76, 77, 78], "ai": [9, 10, 23, 27, 48, 52, 67, 72, 74, 75, 76, 77, 78, 79], "aicpa": 56, "aiohttp": 35, "air": [62, 77], "ak": 30, "akia": 30, "akto": 68, "alb": [16, 17, 32, 33], "alemb": [14, 16, 17, 18, 20, 26], "alert": [3, 4, 7, 8, 12, 14, 15, 22, 23, 24, 25, 27, 29, 32, 37, 38, 39, 40, 44, 45, 46, 50, 52, 53, 54, 57, 62, 63, 64, 68, 69, 70, 71, 72, 74, 76, 77, 78, 79], "alert_manag": 42, "alert_nam": 33, "alert_threshold": 80, "alert_triag": 42, "alertmanag": [16, 43], "alertmanagerservic": 42, "alertnam": 16, "algorithm": [3, 6, 18, 22, 25, 34, 37, 51, 55, 57, 60, 62, 63, 67, 76, 77, 79], "alias": 75, "alibaba": 30, "align": [3, 23, 24, 25, 26, 27, 36, 39, 41, 42, 44, 45, 50, 52, 63, 67, 72, 74, 76, 77, 78], "all": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 18, 19, 20, 21, 22, 23, 25, 27, 28, 29, 31, 35, 37, 38, 40, 41, 42, 43, 44, 45, 46, 48, 49, 50, 51, 52, 55, 56, 57, 59, 60, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80], "all_actor": 42, "all_cloud": 68, "all_endpoint": 42, "all_network": 68, "all_simple_path": 35, "all_techniqu": 42, "allkei": [33, 34, 35], "alloc": [0, 15, 17, 23, 29, 33, 34, 45, 67, 70, 72, 73, 74, 76, 77, 78, 79], "allocated_storag": 15, "allow": [2, 11, 13, 17, 30, 55, 67, 71], "allow_failur": 54, "allowed_valu": [5, 18], "allowprivilegeescal": 51, "alpin": 32, "alreadi": [1, 5, 10, 18, 20, 23, 24, 58], "alter": [18, 20, 66], "altern": [16, 17, 19, 32, 45, 52, 70, 77, 79], "alwai": [7, 8, 11, 15, 19, 35, 39, 42, 44, 61], "am": [5, 26, 31, 40, 56, 57, 68], "amazon": 16, "amazonaw": [14, 16], "amber": 58, "amd64": 16, "amtool": 16, "an": [3, 5, 6, 10, 18, 22, 27, 34, 35, 40, 48, 50, 52, 53, 57, 58, 63, 65, 68, 69, 70, 71, 73, 79], "analys": [35, 38, 63, 79], "analysi": [0, 1, 2, 3, 4, 11, 12, 13, 14, 15, 18, 19, 20, 21, 22, 23, 24, 25, 27, 28, 29, 30, 32, 34, 35, 37, 39, 40, 41, 42, 43, 48, 50, 51, 57, 58, 63, 64, 65, 66, 68, 70, 72, 73, 76, 77], "analysis_metadata": 18, "analysis_result": 69, "analysis_tim": 65, "analysiserror": [18, 19], "analysisopt": 18, "analysisresult": 18, "analyst": [4, 27, 45, 61, 63, 72, 73, 74, 75, 76, 79], "analyt": [15, 23, 27, 29, 30, 52, 67, 69, 72, 73, 74, 76, 77, 79], "analyz": [3, 8, 17, 18, 19, 20, 26, 32, 33, 35, 40, 42, 53, 55, 58, 59, 61, 63, 66, 68, 69, 70, 71, 74, 75, 77, 79, 80], "analyze_api_secur": 68, "analyze_asset_blast_radiu": 58, "analyze_attack_path": [18, 21, 59, 69], "analyze_attack_path_async": 65, "analyze_attack_pattern": 75, "analyze_behavior": 75, "analyze_blast_radiu": [26, 69], "analyze_boundary_cross": 69, "analyze_bulk_path": 65, "analyze_compliance_risk": 69, "analyze_container_secur": 68, "analyze_dast_result": 53, "analyze_detection_coverag": 69, "analyze_incid": 42, "analyze_multiple_target": 59, "analyze_nuclei_result": 53, "analyze_path": [18, 19, 65], "analyze_technique_trend": 75, "analyze_threat_actor": 42, "analyze_threat_actor_fit": 69, "analyze_zap_result": 53, "analyzeattackpath": [18, 19], "android": 77, "angular": 14, "ani": [7, 18, 19, 27, 35, 47, 53, 54, 55, 57, 58, 79], "annot": [16, 17, 18, 19, 32, 52, 54], "announc": 47, "annual": [40, 42, 43, 45, 46, 50, 51, 52, 72, 73, 78, 79], "annual_revenu": 80, "anomal": [57, 79], "anomali": [4, 11, 25, 26, 30, 36, 39, 40, 41, 57, 59, 63, 75, 76, 77, 78, 79], "anomalies_detect": 57, "anomaly_detect": [4, 41, 57], "anomaly_scor": [57, 75], "anonym": [30, 39, 49, 78], "answer": [19, 67], "anti": [72, 77, 78, 79], "anticip": 79, "antiviru": [38, 42, 68], "apach": 3, "api": [0, 2, 11, 13, 14, 15, 16, 17, 19, 21, 22, 24, 26, 28, 29, 32, 33, 34, 35, 36, 37, 38, 39, 42, 43, 46, 47, 48, 50, 51, 53, 54, 61, 64, 65, 66, 70, 73, 74, 76, 77, 78, 79, 80], "api_bas": [8, 71], "api_base_url": 18, "api_discoveri": 68, "api_kei": [26, 54, 58], "api_key_encrypt": 58, "api_rate_limit": 13, "api_requests_per_second": 29, "api_response_time_p95": 29, "api_secur": 42, "api_security_architectur": 78, "api_token": [3, 5, 8, 26, 75, 80], "api_url": [26, 58], "api_v1_str": [13, 64], "api_workflow": 18, "apidiscoveryservic": 68, "apierror": 18, "apirout": [20, 58], "apitoken": [5, 8], "apivers": [14, 31, 34, 51, 68], "apm": 54, "app": [2, 13, 14, 16, 17, 18, 19, 20, 28, 30, 31, 32, 33, 34, 35, 38, 46, 48, 51, 53, 54, 55, 56, 57, 58, 64, 65, 66, 67, 68, 69, 79], "app_replica": 16, "app_server_001": 6, "appar": 53, "appear": [66, 79], "append": [35, 42, 46, 51, 53, 54, 55, 57, 58, 59, 64, 69], "appetit": [72, 73, 78], "appli": [0, 14, 16, 17, 20, 31, 33, 34, 35, 43, 47, 50, 51, 59, 61, 67, 79], "applianc": 79, "applic": [0, 5, 6, 7, 9, 10, 11, 19, 20, 23, 27, 28, 29, 30, 38, 41, 42, 44, 45, 46, 47, 49, 50, 51, 52, 58, 61, 63, 64, 66, 67, 69, 70, 72, 73, 75, 76, 77, 78, 79, 80], "application_awar": 42, "application_compliance_assess": 78, "application_layer_secur": 42, "application_log": 42, "application_memori": 34, "application_secur": 42, "application_security_design": 78, "application_security_group_id": 17, "application_threat_model": 78, "application_url": 54, "appreci": 47, "approach": [2, 21, 24, 27, 33, 36, 45, 46, 48, 50, 54, 63, 64, 74, 78, 79], "appropri": [1, 3, 4, 8, 12, 13, 19, 21, 30, 38, 41, 45, 47, 51, 54, 55, 56, 69, 70, 71, 75, 77, 79, 80], "approv": [4, 14, 19, 21, 22, 29, 37, 40, 43, 46, 47, 48, 51, 54, 70, 72, 73, 77, 78, 79], "approval_author": 42, "approval_record": 56, "approve_response_act": 79, "apt": [6, 8, 14, 22, 23, 27, 28, 32, 71, 76, 77, 79, 80], "apt simul": 77, "apt1": 77, "apt28": [10, 74, 75, 76, 80], "apt29": [6, 8, 10, 71, 74, 75, 77, 80], "apt_simulation_author": 77, "aquasec": [14, 32, 54], "ar": [5, 6, 8, 9, 10, 15, 17, 18, 19, 21, 30, 33, 35, 38, 40, 42, 45, 47, 48, 50, 51, 52, 56, 65, 70, 71, 72, 73, 76, 77, 78, 79, 80], "architect": [1, 3, 22, 23, 25, 27, 37, 38, 46, 51, 63, 66, 67, 70, 73, 79], "architectur": [0, 1, 2, 3, 17, 19, 20, 21, 23, 26, 35, 36, 38, 43, 49, 67, 70, 71, 74, 76, 77], "architecture review": 46, "architecture_doc": 46, "archiv": [15, 33, 39, 41, 62, 66, 67, 70], "archive_retention_year": 41, "arcsight": 79, "area": [8, 42, 69, 78, 79], "area_scor": 42, "arg": [18, 19, 20, 35, 54, 64], "arn": [16, 17, 30, 32, 33, 66], "around": 69, "arrai": [6, 8, 56], "arrang": [19, 64, 65, 79], "arrivalr": 34, "arrow": 38, "articl": [12, 29], "artifact": [3, 48, 51, 53, 54, 55, 58, 79], "artifici": [73, 76, 77, 78], "artilleri": 34, "as_uuid": [56, 57, 58], "asc": [5, 8], "asdict": 57, "ask": [19, 20], "aspect": [0, 42, 46, 62, 64, 70, 78, 79], "asreproast": 77, "assembli": 79, "assert": [19, 21, 53, 64, 65], "assert_called_onc": 65, "assert_called_once_with": 19, "assert_us": [54, 55], "assertionerror": 65, "assess": [1, 3, 11, 15, 18, 19, 21, 22, 23, 24, 25, 27, 29, 36, 37, 38, 39, 40, 41, 44, 45, 47, 49, 54, 58, 62, 67, 70, 73, 75, 76, 77, 79], "assess_area_matur": 42, "assess_compliance_impact": 51, "assess_control_effect": 46, "assess_financial_impact": 80, "assess_framework": 26, "assess_gdpr_impact": 80, "assess_hipaa_impact": 80, "assess_implement": 46, "assess_monitor": 46, "assess_risk": 7, "assess_security_matur": 42, "assess_sox_impact": 80, "assess_test": 46, "assess_threat_coverag": 42, "assessment_area": 42, "assessment_criteria": 26, "assessment_d": 56, "assessment_id": [53, 56], "assessment_method": 56, "assessment_nam": 56, "assessment_typ": 56, "assessmentresult": 26, "assessor": [4, 56], "assessor_not": 56, "asset": [1, 3, 4, 6, 10, 13, 14, 15, 16, 17, 18, 19, 21, 24, 27, 32, 33, 34, 35, 36, 41, 42, 50, 51, 53, 54, 56, 57, 58, 59, 60, 61, 64, 65, 66, 67, 69, 71, 73, 75, 77, 78, 79, 80], "asset_ag": 68, "asset_configuration_chang": 68, "asset_count": 66, "asset_crit": 69, "asset_data": [34, 51, 59], "asset_discoveri": [18, 68], "asset_discovery_servic": 65, "asset_filt": 68, "asset_id": [5, 18, 26, 34, 35, 57, 58, 59], "asset_id_index": 35, "asset_inventory_complet": 56, "asset_manag": 42, "asset_metadata": 59, "asset_nam": 41, "asset_not_found": [5, 10], "asset_relationship": 61, "asset_relationships_confidence_rang": 61, "asset_relationships_no_self_refer": 61, "asset_relationships_port_rang": 61, "asset_remov": 68, "asset_risk": 59, "asset_status_enum": 61, "asset_typ": [5, 8, 17, 18, 35, 51, 56, 57, 58, 61, 65, 69], "asset_type_encod": 57, "asset_type_enum": 61, "asset_type_index": 35, "assetcr": 51, "assetcreaterequest": 18, "assetdiscoveryservic": 65, "assetfactori": [64, 65], "assetfeatur": 57, "assetid": 26, "assetmonitoringservic": 68, "assets_discov": [5, 68], "assets_found": 18, "assets_process": 57, "assets_risk_score_rang": 61, "assets_soft_delet": 61, "assetservic": 65, "assettyp": [5, 18], "assign": [21, 24, 38, 40, 43, 47, 50, 51, 65, 66, 67, 70, 71, 77, 79], "assigne": 58, "assist": [28, 62, 72, 76, 77, 79], "associ": [72, 73, 75, 78, 79], "assum": [11, 42, 65, 77], "assume_role_arn": 30, "assumerol": 30, "assur": [72, 73, 77, 78, 79], "ast": 54, "astext_typ": 56, "astyp": 35, "async": [7, 18, 19, 25, 26, 34, 35, 51, 56, 57, 58, 59, 69], "async_analysis_servic": 65, "asyncanalysisservic": 65, "asyncassetdiscoveri": 35, "asynccli": 58, "asynchron": [15, 35, 41, 65], "asyncio": [35, 57, 59, 65], "atm": 77, "atom": 35, "att": [3, 18, 23, 25, 26, 27, 34, 36, 44, 52, 62, 67, 71, 76, 77, 78, 79, 80], "attack": [0, 1, 3, 5, 7, 11, 13, 18, 19, 21, 22, 23, 24, 25, 26, 27, 33, 34, 35, 36, 37, 42, 43, 45, 46, 47, 48, 49, 50, 51, 53, 54, 62, 64, 65, 66, 68, 70, 76, 78], "attack path analysi": 27, "attack simul": 77, "attack_object": 69, "attack_path": [3, 6, 8, 18, 34, 35, 59, 61, 69, 71], "attack_path_result": 18, "attack_path_servic": [19, 64, 65, 69], "attack_path_timeout": 66, "attack_paths_detection_difficulty_rang": 61, "attack_paths_impact_score_rang": 61, "attack_paths_likelihood_rang": 61, "attack_paths_path_length_posit": 61, "attack_paths_per_minut": 34, "attack_paths_risk_score_rang": 61, "attack_scenario": [61, 80], "attack_scenarios_detection_probability_rang": 61, "attack_scenarios_impact_score_rang": 61, "attack_scenarios_likelihood_rang": 61, "attack_scenarios_risk_score_rang": 61, "attack_servic": 69, "attack_sophist": 75, "attack_techniqu": [6, 59, 61, 71], "attack_typ": 69, "attack_vector": 69, "attackpath": [8, 18, 59], "attackpathanalyz": [18, 19], "attackpathanalyzerprop": 18, "attackpatherror": 18, "attackpathprop": 19, "attackpathresult": [18, 19, 21], "attackpathservic": [18, 19, 64, 65, 69], "attackpathvisu": 18, "attackscenario": 59, "attackscenariorequest": 8, "attacktechniqu": 59, "attempt": [13, 43, 45, 53, 64, 77, 79], "attend": [71, 76, 77, 79], "attende": 43, "attent": [30, 38, 71, 79], "attract": 79, "attribut": [5, 8, 39, 47, 62, 63, 70, 74, 76, 77, 78, 79], "attribute_threat_actor": 75, "attributed_group": 75, "attribution_fail": 9, "attribution_threshold": 75, "auc": 57, "aud": 7, "audienc": [42, 78, 79], "audit": [2, 4, 7, 12, 13, 15, 23, 24, 25, 26, 27, 30, 39, 40, 42, 44, 45, 46, 49, 50, 51, 52, 61, 62, 63, 68, 69, 70, 73, 74, 76, 77, 78, 79, 80], "audit_alert": 41, "audit_all_access": 4, "audit_block": 41, "audit_configuration_chang": 13, "audit_coordination_access": 78, "audit_data_access": 13, "audit_databas": 80, "audit_en": 13, "audit_ev": 41, "audit_impl": 80, "audit_log": [29, 41, 51, 61, 68], "audit_log_all_request": 13, "audit_log_path": 13, "audit_log_sensitive_data": 13, "audit_logg": 42, "audit_login_ev": 13, "audit_permission_chang": 13, "audit_planning_and_execut": 72, "audit_support_act": 72, "auditloggingservic": 42, "auditor": [4, 69, 72, 78], "auth": [7, 8, 18, 19, 21, 24, 33, 38, 43, 53, 64, 68], "auth_head": 64, "auth_rate_limit": 13, "auth_respons": 8, "auth_token": 54, "authclient": 7, "authent": [0, 1, 3, 11, 15, 16, 18, 19, 21, 24, 25, 26, 27, 29, 39, 40, 41, 42, 43, 44, 46, 47, 49, 54, 55, 59, 61, 62, 63, 70, 71, 74, 75, 77, 78, 79], "authenticate_us": 42, "authentication_bypass": 54, "authi": 38, "author": [0, 1, 5, 6, 8, 9, 10, 11, 12, 17, 18, 19, 20, 21, 24, 27, 30, 32, 33, 39, 41, 42, 43, 44, 45, 46, 47, 54, 56, 58, 61, 62, 63, 64, 67, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80], "authority_contact": 12, "authservic": 7, "auto": [0, 14, 15, 18, 20, 22, 27, 29, 31, 32, 34, 37, 46, 48, 51, 55, 62, 64, 65, 70], "auto_approve_threshold": 4, "auto_create_cas": 58, "auto_map": 41, "auto_notif": 12, "auto_remedi": 54, "auto_revert": 4, "autobuild": 3, "autocomplet": 3, "autodiscoveri": 16, "autodoc": 3, "autogener": 20, "autom": [0, 3, 4, 5, 8, 9, 11, 15, 16, 22, 23, 27, 29, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 55, 56, 58, 61, 62, 70, 73, 74, 76, 77, 78, 79], "automat": [3, 4, 11, 12, 13, 15, 21, 24, 25, 29, 41, 51, 54, 63, 65, 71, 74, 79, 80], "automated test": 54, "automated_act": 58, "automated_block": 42, "automated_check": 56, "automated_decision_mak": 12, "automated_initi": 42, "automated_process": 12, "automatic_failov": 15, "automation_level": 56, "autonom": [24, 25, 36, 63, 77], "autonomous_attack_simul": 77, "autonomous_threat_hunt": 76, "autosc": [15, 34], "autoscal": 16, "aux": [33, 34], "auxiliari": 21, "av": [3, 50], "avail": [3, 5, 6, 7, 8, 10, 14, 17, 21, 22, 23, 24, 27, 28, 29, 31, 35, 36, 37, 38, 42, 44, 45, 47, 48, 50, 54, 62, 63, 66, 67, 69, 70, 71, 74, 78, 79], "available_actor": 10, "averag": [3, 23, 24, 25, 32, 46, 57, 61, 71, 76, 77, 79], "average_clust": 6, "average_review_tim": 46, "averageutil": 34, "avg": 52, "avg_review_tim": 46, "avg_time_per_cal": 35, "avg_vuln_age_dai": 57, "aviat": 77, "avoid": [19, 30, 46, 47, 59, 65, 68, 73, 76, 77], "aw": [0, 1, 5, 13, 17, 19, 23, 27, 28, 29, 31, 32, 33, 38, 42, 47, 63, 65, 66, 67, 70, 74, 76, 78, 79], "await": [5, 7, 8, 18, 19, 30, 35, 50, 51, 57, 58, 59, 65, 75], "awar": [4, 18, 27, 40, 49, 72, 73, 74, 76, 77, 78, 79], "award": 73, "awk": [31, 32, 33], "aws_access_key_id": [13, 28, 66], "aws_account_id": 16, "aws_config": 30, "aws_default_region": [13, 28], "aws_discoveri": 68, "aws_ec2": 5, "aws_enable_ec2": 13, "aws_enable_iam": 13, "aws_enable_rd": 13, "aws_enable_s3": 13, "aws_enable_vpc": 13, "aws_engin": 30, "aws_instance_id": 5, "aws_max_retri": 13, "aws_poll_interv": 13, "aws_profil": 14, "aws_region": [5, 14, 16, 31], "aws_result": 30, "aws_retry_delai": 13, "aws_secret_access_kei": [13, 28, 66], "aws_session_token": 13, "awsdiscoveryservic": 68, "awsregion": 16, "az": [14, 15, 29, 30, 66], "azur": [0, 1, 5, 13, 21, 23, 27, 28, 38, 42, 47, 63, 66, 67, 68, 70, 74, 76, 78, 79], "azure_client_id": [13, 28, 66], "azure_client_secret": [13, 28, 66], "azure_config": 30, "azure_enable_comput": 13, "azure_enable_network": 13, "azure_enable_secur": 13, "azure_enable_storag": 13, "azure_engin": 30, "azure_max_retri": 13, "azure_poll_interv": 13, "azure_result": 30, "azure_subscription_id": 13, "azure_tenant_id": [13, 28, 66], "b": [2, 3, 14, 19, 20, 21, 30, 45, 55], "b101": [54, 55], "b102": [54, 55], "b103": [54, 55], "b104": [54, 55], "b105": [54, 55], "b106": [54, 55], "b107": [54, 55], "b108": 54, "b110": 54, "b112": 54, "b999": 55, "baa": [72, 78], "back": [21, 32, 66], "back_popul": [26, 56, 57, 58], "backdoor": [45, 76, 77, 79], "backend": [0, 13, 16, 17, 25, 28, 31, 32, 33, 34, 35, 38, 40, 43, 51, 54, 66, 67], "backend_serv": [34, 35], "background": [6, 12, 19, 24, 25, 26, 35, 63, 69, 79], "backlog": 41, "backoff": 8, "backs_up": 61, "backup": [14, 17, 20, 25, 27, 28, 29, 30, 32, 33, 38, 39, 40, 42, 43, 45, 51, 53, 63, 69, 71, 74, 77, 78, 79], "backup_bucket": 31, "backup_cod": 7, "backup_d": 31, "backup_fil": 31, "backup_nam": 31, "backup_retent": 15, "backup_s": 31, "backup_schedul": 5, "backup_server_001": [6, 71], "backup_system": 61, "backup_typ": 31, "backward": [21, 62, 63], "bad": [6, 8, 18, 21, 41, 51, 65], "badg": 42, "balanc": [17, 24, 25, 26, 29, 30, 32, 33, 34, 37, 41, 47, 55, 57, 68, 70, 71, 76, 77, 78, 79], "bandit": [0, 2, 18, 20, 21, 46, 48, 50, 51, 54, 55, 64], "bandit_result": 54, "banditen": 20, "bandwidth": [28, 34, 67], "bank": 77, "base": [0, 1, 2, 3, 7, 11, 12, 13, 15, 18, 22, 23, 24, 25, 26, 27, 29, 30, 32, 35, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 48, 49, 51, 55, 57, 58, 59, 62, 63, 64, 66, 67, 69, 71, 72, 73, 75, 76, 77, 78, 80], "base64": [7, 16, 17, 31, 33], "base64_payload": 75, "base_class": 56, "base_risk": 35, "base_url": [5, 7, 8, 26, 54, 58, 75, 80], "basel": [72, 78], "baselin": [34, 35, 40, 45, 50, 51, 53, 54, 64, 70, 71, 75, 77, 78, 79], "baseline_comparison": 68, "baseline_devi": 68, "baseline_scan": 53, "baseline_scor": 75, "basemodel": 18, "baseurl": [5, 7, 8], "bash": [0, 1, 2, 14, 28, 31, 32, 33, 68], "basi": [4, 12, 22, 37, 40, 42, 51], "basic": [0, 13, 14, 26, 27, 28, 33, 34, 37, 42, 47, 53, 57, 72, 74, 76, 77, 79], "basic_attack_path_discoveri": 77, "basic_campaign_particip": 77, "basic_compliance_monitor": 72, "basic_exercise_particip": 76, "basic_metrics_access": 76, "basic_reporting_access": [72, 77], "basicconfig": 65, "batch": [5, 6, 16, 17, 22, 24, 25, 30, 31, 36, 37, 41, 62, 75, 80], "batch_siz": [30, 41, 68], "battl": [24, 36], "bcrypt": 13, "bear": [6, 76, 77, 80], "bearer": [5, 6, 7, 8, 9, 10, 20, 30, 53, 54, 58, 64, 67, 71, 75, 80], "beat": 14, "beat_schedul": 57, "becom": [38, 40], "been": [19, 29, 49, 79], "befor": [2, 7, 8, 12, 14, 21, 22, 27, 35, 37, 38, 49, 51, 52, 53, 55, 61, 63, 65, 66, 67, 71, 74, 75, 77, 80], "before_script": 54, "begin": [0, 6, 25, 36, 39, 43, 44, 47, 61, 64, 68, 71], "behavior": [4, 6, 11, 19, 24, 25, 26, 27, 36, 39, 41, 48, 52, 57, 59, 63, 64, 66, 67, 69, 76, 77, 78, 79], "behavior_analysi": 75, "behavioral_analysi": 42, "behavioral_featur": 57, "behavioral_scor": 75, "behind": 19, "being": [33, 66], "benchmark": [0, 21, 35, 46, 52, 62, 64, 70, 72, 73, 76, 77, 78, 79], "benefit": [6, 26, 72, 73, 76, 77, 78, 79, 80], "best": [0, 1, 3, 19, 22, 23, 27, 28, 37, 38, 39, 44, 45, 47, 48, 50, 51, 62, 72, 73, 76], "better": [6, 18, 19, 20, 35, 48, 49, 80], "between": [0, 1, 3, 18, 19, 24, 27, 38, 58, 64, 69, 71, 76, 78, 79], "bf": 59, "bgwriter_delai": 35, "bgwriter_lru_maxpag": 35, "bgwriter_lru_multipli": 35, "bi": [38, 42, 63, 67, 70], "bidirect": [24, 35, 58, 79], "biggest": 35, "bigint": 18, "biginteg": 18, "bigseri": 18, "billing_system": 80, "bin": [2, 14, 16, 18, 20, 28, 31, 32, 33], "bind": [17, 30, 35, 55, 64, 66], "bind_development_serv": 55, "biometr": [11, 15, 29, 42], "biometric_support": 29, "bit": [13, 28, 79], "black": [18, 20, 21, 54, 64, 79], "blank": 66, "blast": [0, 2, 4, 5, 7, 8, 9, 10, 11, 12, 13, 15, 18, 19, 20, 21, 22, 23, 28, 29, 30, 34, 35, 36, 37, 38, 39, 41, 42, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 61, 62, 64, 65, 66, 68, 70, 72, 73, 74, 75, 76, 77, 78, 79, 80], "blast-radiu": 49, "blast_dev": 20, "blast_neo4j": [28, 66], "blast_neo4j_password": 13, "blast_pass": [13, 28], "blast_radiu": [3, 6, 8, 13, 14, 16, 28, 32, 33, 43, 58, 61, 66, 67, 69, 75, 80], "blast_radius_analysi": 69, "blast_radius_app": 16, "blast_radius_cach": 59, "blast_radius_dev": [14, 20], "blast_radius_incident_id": 58, "blast_radius_restor": 31, "blast_radius_scor": 58, "blast_radius_sdk": [5, 26], "blast_radius_servic": 58, "blast_radius_sess": 13, "blast_respons": 8, "blast_result": 71, "blast_us": [13, 28, 66, 67], "blastradiu": [7, 8, 26, 28, 38, 66, 67], "blastradius2024": [28, 38, 67], "blastradiuscli": [3, 5, 8, 26, 75, 80], "blastradiusdast": 54, "blastradiusresult": [26, 59], "blastradiusrol": 30, "blastradiusservic": 58, "blastradiusus": 64, "blind": [74, 76], "bloat": 32, "blob": 30, "block": [3, 11, 19, 33, 34, 41, 43, 47, 48, 54], "block_hash": 41, "block_id": 41, "block_siz": 41, "block_threat_ind": 42, "blockchain": [22, 29, 37, 41, 73, 77, 78], "blue": [0, 27, 29, 70, 71, 74, 76, 77], "blue team": 27, "blue_team_detect": 69, "board": [23, 27, 45, 72, 73, 74, 77, 78, 79], "board govern": 73, "board_level_report": [72, 73], "board_reporting_access": 78, "bodi": [5, 6, 7, 19, 21, 40, 72, 73, 78, 79], "bolt": [13, 20, 28, 34, 35], "bool": [18, 26, 42, 58], "boolean": [5, 12, 56, 57, 58, 61], "boot": 77, "bootcamp": 79, "border": [39, 72, 78], "both": [35, 55], "boto3": [30, 65], "botocor": 30, "bottleneck": [34, 35, 64, 70, 79], "bound": 40, "boundari": [18, 22, 37, 46, 51, 53, 64, 77, 78, 79, 80], "boundary_analysi": 69, "bounti": 44, "box": 3, "bradlc": 20, "branch": [2, 3, 14, 20, 46, 53, 55, 62, 64], "branch_label": 56, "brand": [3, 70, 73, 77, 78, 79], "breach": [11, 22, 29, 37, 42, 45, 47, 48, 50, 51, 69, 72, 73, 77, 78, 79], "breach_manag": 12, "breach_notif": 29, "breach_scenario": 80, "breach_typ": 80, "breadcrumb": 3, "breadth": [4, 59], "break": [4, 19, 21, 50, 59], "breaker": 15, "breakout": 77, "breakpoint": 20, "bridg": [43, 61, 76, 79], "bridgecrew": 54, "brief": [19, 43, 45, 51, 73, 77, 78, 79], "broader": [47, 73, 77, 79], "broken": [3, 22, 49], "broker": 78, "brows": 38, "browser": [0, 19, 20, 38, 64, 66, 67, 77, 79], "brute": [53, 70], "bruteforc": 68, "bside": 79, "bucket": [30, 68], "budget": [73, 76, 78, 79, 80], "budget_constraint": 80, "budget_planning_access": 78, "buffer": 35, "buffer_s": 41, "bug": [19, 20, 21, 27, 44, 62, 65, 67, 74], "bugfix": [19, 21], "build": [19, 20, 21, 22, 24, 25, 26, 32, 35, 48, 50, 52, 54, 58, 66, 72, 73, 76, 77, 78, 79], "build_imag": 54, "builder": [3, 35], "building_system": 61, "built": [3, 27, 39, 44, 53, 59, 75, 79], "bulk": [4, 8, 25, 35, 36, 62, 65, 70], "bulk_operation_fail": 5, "bulk_risk_calcul": 35, "bundl": [0, 66], "burp": [48, 50], "busi": [3, 4, 10, 23, 27, 42, 43, 45, 46, 47, 48, 51, 59, 62, 65, 67, 68, 69, 70, 71, 72, 74, 76, 77, 78, 79, 80], "business enabl": 73, "business_context": 80, "business_continu": 42, "business_crit": [59, 80], "business_disrupt": 80, "business_impact": 46, "business_impact_scor": 46, "business_justif": 4, "business_multipli": 59, "business_object": 42, "button": 64, "by_sever": [53, 55], "by_tool": 53, "byod": 77, "bypass": [21, 47, 53, 54, 55, 76, 77, 78], "bypass_attempt": 53, "byte": 35, "c": [2, 3, 14, 17, 20, 22, 23, 27, 28, 30, 31, 32, 33, 43, 66, 69, 73, 74, 77, 78, 79], "c-suit": 73, "ca": [13, 66], "cach": [0, 2, 3, 4, 11, 12, 13, 14, 15, 16, 20, 24, 25, 26, 27, 32, 34, 36, 41, 43, 49, 61, 62, 63, 66, 67, 68, 69, 70, 71, 75, 79, 80], "cache_attack_path": 59, "cache_hit": 6, "cache_kei": [35, 49, 59], "cache_level": 34, "cache_miss": 6, "cache_pattern": 34, "cache_result": 59, "cache_ttl": [13, 34], "cached_data": 35, "cached_result": 35, "cached_valu": 35, "cachemanag": 35, "cafil": 66, "calcul": [8, 10, 12, 25, 26, 27, 35, 46, 50, 57, 58, 59, 62, 67, 69, 74, 75, 77, 78, 79], "calculate_average_review_tim": 46, "calculate_blast_radiu": 59, "calculate_effectiveness_scor": 42, "calculate_metr": 46, "calculate_path_risk": 69, "calculate_path_risk_scor": 59, "calculate_resolution_tim": 42, "calculate_security_risk": 46, "calculateblastradiu": 8, "calendar": 72, "calibr": 76, "california": [63, 72], "call": [13, 17, 20, 30, 32, 33, 34, 35, 41, 43, 55, 61, 66, 74, 79], "call_function_name_qu": 55, "caller": [16, 66], "cam": 72, "camelcas": 18, "campaign": [8, 23, 71, 74, 75, 76, 78, 79, 80], "campaign_correl": 75, "campaign_id": 75, "campaign_planning_author": 77, "can": [3, 13, 30, 35, 47, 63, 65, 66, 71, 77], "canari": 15, "cannot": [17, 18, 65, 66], "cap": 69, "capabl": [0, 3, 4, 6, 7, 9, 10, 11, 15, 19, 20, 22, 23, 26, 28, 29, 34, 36, 38, 41, 42, 52, 58, 59, 62, 63, 67, 68, 70, 71, 74, 75, 80], "capac": [15, 24, 25, 33, 36, 40, 63, 70, 74, 76, 78, 79], "capit": [73, 78], "captur": [73, 76, 78, 79], "car": 77, "carbanak": 80, "card": [41, 42, 63, 72, 77, 78], "cardhold": [42, 68, 72, 78], "cardholder_data": 69, "cardholder_data_asset": 69, "cardholder_data_environ": 68, "care": [32, 63, 79], "career": [3, 23, 27, 72, 76, 77, 78, 79], "carefulli": [11, 16], "carlo": [23, 78], "carrier": 79, "casb": 78, "cascad": [27, 56, 61, 78], "case": [19, 21, 22, 23, 24, 25, 26, 31, 32, 36, 37, 38, 43, 49, 53, 54, 61, 64, 73, 74, 77, 78], "case_data": 58, "case_id": [26, 58], "case_map": 58, "case_numb": 58, "caseid": 58, "cass": 77, "cat": [14, 16, 31, 32, 33, 53, 66], "catalog": 80, "catalogu": 40, "catalyst": 76, "catch": [18, 19, 64, 65], "categor": [40, 57, 65, 79], "categori": [19, 20, 26, 41, 47, 52, 55, 63, 68, 69, 78, 79], "caught": 18, "caus": [17, 19, 35, 43, 45, 67, 73, 79], "causal": 76, "cc6": 56, "cc6_1_logical_access": 42, "cc6_2_authent": 42, "cc7_1_threat_protect": 42, "cca": 78, "ccep": 72, "ccpa": [63, 72, 79, 80], "ccpto": 76, "ccrto": 77, "ccsa": 78, "ccsp": 76, "cctv": 42, "cd": [0, 1, 3, 13, 15, 16, 18, 19, 20, 21, 27, 28, 31, 36, 38, 49, 62, 66, 67, 77, 78], "cde": [72, 76, 78], "cdn": [13, 14, 34, 42, 47, 68, 77], "cdpo": 72, "ced": 76, "cef": 67, "ceh": 77, "celeri": [14, 24, 25, 26, 36], "celery_app": [14, 24, 57], "center": [17, 42, 52, 70, 72, 73, 74, 76, 77, 78, 79], "cento": 28, "central": [15, 55, 62, 70, 77, 78, 79], "central1": [13, 30, 68], "centric": [22, 23, 52, 78], "ceo": [73, 78, 79, 80], "cert": [11, 13, 66], "cert_arn": 16, "cert_expiri": 32, "cert_non": 13, "cert_opt": 13, "cert_requir": 13, "certain": 66, "certbot": 28, "certif": [3, 11, 13, 14, 23, 24, 27, 28, 29, 37, 38, 40, 46, 51, 62, 68, 70, 72, 73, 76, 77, 78, 79, 80], "certifi": [72, 74, 76, 77, 78, 79], "certificate_arn": 17, "certificate_author": 61, "certificatearn": [16, 32], "certification_impact": 80, "certonli": 28, "cesa": 78, "cessat": 40, "cfo": [78, 80], "cgrc": 72, "chacha20": 11, "chain": [11, 29, 40, 45, 63, 66, 71, 72, 73, 75, 76, 77, 78, 79, 80], "challeng": [7, 76, 77, 78], "chang": [0, 1, 2, 3, 4, 8, 13, 14, 16, 17, 18, 20, 21, 24, 26, 28, 30, 31, 35, 36, 40, 41, 42, 43, 46, 49, 50, 53, 54, 58, 62, 63, 65, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80], "change_descript": 51, "change_detect": 68, "changelog": [1, 3, 8, 19, 21, 27, 67], "channel": [19, 38, 43, 62, 67, 68, 72, 76, 77, 78, 79], "charact": [18, 38], "characterist": [15, 34, 41, 42, 47], "chart": [16, 40, 79], "chat": 67, "chc": 72, "chd": 68, "check": [0, 3, 4, 9, 11, 13, 15, 17, 19, 20, 21, 24, 25, 26, 30, 31, 34, 35, 36, 38, 40, 41, 42, 43, 46, 48, 50, 55, 58, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 74, 75, 79], "check_custom_crypto": 55, "check_id": 55, "check_permiss": [18, 42], "check_request": 12, "check_risk_threshold": 54, "check_typ": 56, "checker": 18, "checklist": [21, 54, 79], "checkout": [2, 3, 14, 19, 20, 21, 53, 55, 64, 65], "checkov": [51, 54], "checkpoint": [22, 37], "checkpoint_completion_target": [34, 35], "checksum": 41, "chief": [72, 73, 78, 79], "children": 18, "chmod": [2, 14, 20], "chokepoint": [69, 79], "choos": [19, 55, 71, 80], "chore": 21, "chown": [2, 17, 28], "chrome": [19, 20, 38, 64, 67, 79], "chromeopt": 64, "chronolog": [41, 79], "chronologi": 79, "chunk": 35, "chunk_siz": 35, "churn": 78, "ci": [0, 3, 15, 18, 21, 27, 36, 44, 49, 52, 62, 63, 77, 78], "ci/cd secur": 54, "ci_commit_branch": 54, "ci_commit_sha": 54, "ci_registry_imag": 54, "cia": 72, "cicd": 2, "cicst": 77, "cio": [78, 79], "cipher": 11, "cipp": 72, "cipt": 76, "circuit": 15, "cism": [72, 79], "ciso": [42, 45, 73, 79], "cissp": 79, "ck": [3, 18, 23, 25, 26, 27, 34, 36, 44, 52, 62, 67, 71, 76, 77, 78, 79, 80], "cl": 18, "claim": [7, 17, 31, 45, 78, 79], "clariti": [18, 65], "class": [7, 17, 18, 19, 21, 26, 31, 35, 42, 46, 53, 54, 55, 56, 57, 58, 59, 64, 65], "class_weight": 57, "classif": [4, 25, 27, 36, 39, 40, 41, 42, 44, 46, 57, 62, 63, 70, 71, 72, 74, 76, 77, 78, 79], "classification_report": 57, "classification_schem": 42, "classnam": [18, 19, 65], "clean": [2, 3, 4, 14, 20, 22, 32, 33, 37, 45, 53, 54, 66, 79], "cleanup": [29, 31, 61, 67, 70], "cleanup_attack_path": 61, "cleanup_audit_log": 61, "clear": [0, 3, 12, 17, 18, 19, 20, 30, 33, 35, 40, 45, 47, 63, 65, 66, 67, 68, 70, 71, 77, 78, 79], "clearli": [4, 79], "clever": 18, "cli": [13, 14, 19, 20, 24, 25, 26, 28, 32, 33, 36, 38, 43, 64, 65, 66, 67, 68], "click": [19, 24, 25, 26, 36, 38, 64, 67, 71], "click_rat": 42, "client": [3, 5, 7, 8, 13, 28, 30, 31, 36, 53, 62, 64, 65, 66, 67, 68, 71, 75, 77, 80], "client_addr": 33, "client_body_timeout": 35, "client_header_timeout": 35, "client_id": [30, 68], "client_max_body_s": 35, "client_secret": [30, 68], "clientsess": 35, "climat": 42, "clo": 79, "clock": 78, "clone": [2, 28, 38, 67], "close": [4, 18, 19, 30, 35, 50, 57, 64], "closur": [50, 70, 77, 78, 79], "cloud": [0, 1, 2, 5, 8, 21, 23, 27, 28, 37, 43, 47, 59, 62, 70, 71, 73, 74, 76, 77, 78, 79], "cloud_automation_design": 78, "cloud_compliance_manag": 78, "cloud_cost_optim": 78, "cloud_discoveri": 30, "cloud_governance_framework": 78, "cloud_inst": 30, "cloud_secur": 61, "cloud_security_assess": 78, "cloud_servic": 61, "cloud_vendor_manag": 78, "clouddiscoveryengin": 30, "cloudflar": 42, "cloudfront": 68, "cloudtrail": [70, 79], "cloudwatch": [16, 17], "cluster": [14, 15, 17, 25, 29, 30, 31, 33, 37, 68, 76, 77, 79], "cluster_s": 15, "clusternam": 16, "cm": 40, "cmd": [35, 53, 54, 55, 64], "cmdb": [0, 13, 38, 40, 47, 67, 68], "cmdb_sync": 68, "cni": 32, "co": [40, 65], "coach": [73, 78, 79], "cobalt": 80, "code": [1, 2, 6, 9, 10, 17, 25, 27, 36, 40, 44, 47, 48, 49, 50, 52, 53, 54, 55, 62, 64, 67, 74, 77, 78, 79], "code review": 46, "code_coverag": 18, "code_security_standard": 78, "codeactionsonsav": [18, 20], "codebas": [20, 49], "codecov": [64, 65], "codeql": [48, 50, 55], "col": 57, "cold_retention_year": 41, "collabor": [0, 1, 3, 19, 22, 23, 27, 37, 38, 46, 47, 63, 67, 69, 70, 73, 74, 77, 78, 79], "collaboration_result": 69, "collaborative secur": 76, "collect": [0, 3, 8, 12, 13, 15, 22, 25, 26, 27, 34, 35, 37, 40, 41, 45, 55, 62, 69, 72, 73, 74, 76, 77, 78, 79], "collis": 49, "color": [3, 79], "color_schem": 75, "column": [18, 26, 32, 56, 57, 58, 61], "com": [2, 5, 6, 7, 8, 9, 10, 12, 13, 14, 16, 17, 18, 19, 20, 24, 25, 28, 30, 32, 33, 35, 38, 41, 43, 44, 47, 51, 53, 54, 64, 65, 66, 67, 68, 69, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80], "combin": [0, 25, 48, 52, 59, 69, 76, 77, 78, 79], "come": [8, 27, 38, 44, 62, 74], "comma": 5, "command": [21, 24, 25, 28, 39, 45, 46, 53, 54, 55, 64, 65, 67, 70, 72, 73, 74, 75, 76, 77, 78, 79, 80], "command_lin": 75, "command_payload": 53, "comment": [4, 19, 20, 21], "commerc": 78, "commerci": 55, "commercial_fe": 80, "commit": [0, 2, 3, 14, 18, 19, 20, 46, 47, 48, 49, 50, 52, 64, 76, 77, 78, 79], "committe": 78, "common": [1, 3, 9, 10, 13, 19, 27, 29, 47, 48, 50, 54, 55, 61, 62, 64, 67, 75, 76, 77], "common_techniqu": 75, "commonli": 67, "commun": [0, 8, 11, 20, 21, 23, 27, 38, 42, 46, 50, 53, 62, 63, 66, 69, 70, 71, 72, 74, 76, 77], "communicates_with": 61, "communication_plan": 42, "communication_protocol": 42, "communicationservic": 42, "compani": [5, 24, 25, 41, 68, 69, 76, 77, 78], "compar": [71, 74, 75, 78, 79], "compare_threat_actor": 75, "comparison": [46, 72, 75, 77, 78, 79], "compat": [0, 21, 62, 63, 76, 78, 79], "compens": [72, 77], "compet": [42, 76, 77, 78, 79], "competit": [3, 23, 52, 63, 72, 73, 76, 77, 78], "compil": 77, "complement": 53, "complementari": [0, 55], "complet": [0, 3, 4, 5, 8, 9, 12, 14, 15, 16, 18, 19, 20, 21, 22, 23, 24, 25, 26, 32, 33, 38, 40, 41, 43, 45, 46, 47, 49, 50, 51, 52, 53, 55, 56, 57, 59, 60, 61, 62, 63, 64, 65, 66, 67, 69, 70, 71, 74, 75, 77, 78, 79, 80], "completed_at": 12, "completed_review": 46, "completion_r": [42, 46], "complex": [0, 1, 3, 6, 18, 19, 21, 22, 26, 34, 35, 47, 50, 51, 53, 55, 59, 62, 63, 67, 69, 70, 71, 74, 75, 76, 77, 78, 79, 80], "complexity_factor": 59, "complexity_scor": 69, "compli": 77, "complianc": [0, 3, 4, 8, 11, 15, 23, 36, 43, 46, 47, 49, 50, 53, 59, 61, 71, 76, 77, 79], "compliance_analysi": 69, "compliance_control": 26, "compliance_documentation_access": 72, "compliance_framework": [26, 29, 41, 69], "compliance_framework_manag": 78, "compliance_impact": 6, "compliance_level": 56, "compliance_manag": 42, "compliance_monitor": 4, "compliance_program_oversight": 72, "compliance_risk_assess": 72, "compliance_risk_chang": 80, "compliance_servic": 69, "compliance_statu": 5, "compliance_track": 29, "compliance_training_access": 72, "complianceassess": 56, "complianceassessmentservic": 26, "compliancecontrol": [26, 56], "compliancecontrolassess": 56, "compliancedomain": 56, "complianceframework": [26, 56, 69], "compliancereportingservic": 68, "complianceservic": 69, "compliant": [3, 5, 37, 46, 47, 51, 52, 56, 67, 69], "compon": [0, 1, 2, 3, 13, 18, 19, 20, 22, 24, 27, 29, 34, 35, 36, 37, 46, 47, 50, 51, 55, 62, 64, 66, 69, 70], "componentdidcatch": 18, "componentstatus": 33, "compos": [0, 2, 3, 14, 20, 24, 28, 35, 38, 62, 66, 67], "composit": [35, 61, 76, 77], "comprehens": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 21, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 48, 49, 50, 51, 52, 53, 54, 55, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 74, 75], "comprehensive_risk_report": 73, "comprehensive_security_manag": 73, "compress": [13, 31, 34, 35], "compromis": [6, 9, 11, 25, 26, 39, 43, 45, 47, 49, 50, 58, 59, 63, 69, 71, 74, 75, 76, 77, 78, 79, 80], "compromised_serv": [6, 8, 71], "compromised_us": 45, "comput": [30, 67, 68, 73, 76, 78], "con": 79, "concept": [47, 74, 77, 78], "concern": [45, 46], "concis": [19, 65, 79], "conclus": [27, 74], "concret": 18, "concurr": [13, 17, 34, 35, 53, 61, 62, 63, 65, 68, 70, 80], "concurrent_request": 53, "concurrent_us": 29, "condit": [31, 35, 46, 51, 53, 64, 65, 68, 76, 78], "conduct": [16, 40, 42, 49, 70, 72, 74, 76, 77, 78, 79], "conf": [3, 33, 35, 57], "confer": [23, 43, 47, 51, 73, 76, 77, 78, 79], "confid": [5, 9, 21, 22, 23, 29, 37, 52, 53, 55, 61, 65, 69, 72, 73, 74, 75, 76, 77, 78, 79], "confidence_bas": 75, "confidence_scor": [57, 69, 75], "confidenti": [42, 44, 47, 48, 50, 78], "confidential_internal_publ": 42, "config": [2, 8, 13, 14, 17, 18, 20, 28, 30, 31, 32, 33, 34, 46, 48, 51, 53, 54, 55, 58, 64, 66, 68, 70], "config_backup": 31, "configmap": [14, 17, 31, 32, 33, 51, 68], "configur": [1, 7, 8, 10, 11, 17, 20, 21, 22, 23, 24, 25, 27, 29, 34, 35, 37, 39, 42, 44, 45, 46, 47, 51, 52, 53, 57, 58, 61, 62, 63, 64, 68, 69, 72, 74, 75, 76, 77, 78, 80], "configuration_chang": 68, "configuration_change_frequ": 57, "configure_detection_rul": 79, "configure_integr": 79, "configure_system": [7, 70], "confirm": [20, 38, 40, 45, 70, 71, 79], "confirm_password": 7, "conflict": [4, 8, 18, 28, 40, 76], "conftest": [64, 65], "congratul": 27, "connect": [0, 5, 11, 13, 14, 20, 21, 28, 30, 32, 33, 34, 35, 38, 41, 43, 58, 64, 65, 67, 68, 69, 70, 74, 75, 77, 78, 79], "connection_count": [35, 57], "connectionerror": 65, "connector": [24, 25, 34, 35, 36, 62], "connects_to": 5, "consent": [29, 39, 40, 41, 42], "consent_id": 12, "consent_manag": [12, 29, 42], "consent_text": 12, "consentmanagementservic": 42, "consequ": [48, 78, 79], "conserv": 71, "consid": [1, 6, 18, 19, 21, 28, 38, 50, 55, 67, 68, 71, 74, 79, 80], "consider": [8, 21, 41, 43, 44, 46, 51, 54, 59, 71, 77, 78, 80], "consider_supply_chain": 80, "consist": [0, 1, 3, 4, 8, 18, 21, 40, 41, 43, 45, 46, 53, 63, 65, 70, 77, 79], "consol": [18, 20, 66, 70], "consolid": 78, "const": [5, 7, 8, 18, 19, 26], "constant": [18, 19], "constantli": 79, "constraint": [16, 18, 35, 47, 61, 62, 66, 68, 78], "construct": [19, 74], "constructor": 18, "consult": [17, 28, 70, 72, 73, 76, 77, 78, 79], "consum": 72, "contact": [0, 19, 27, 38, 40, 45, 66, 70, 71, 72, 73, 74, 76, 77, 78, 79], "contact_info": 12, "contain": [0, 3, 8, 14, 17, 18, 19, 20, 25, 30, 32, 33, 34, 36, 38, 39, 42, 44, 46, 48, 50, 52, 54, 61, 66, 67, 69, 71, 74, 76, 77, 78, 79], "container": [0, 3, 20, 26, 62], "container_discoveri": 68, "container_nam": 17, "container_scan": 54, "container_secur": 54, "containerdiscoveryservic": 68, "containerservic": 30, "containerstatus": 33, "contamin": [45, 57], "content": [6, 7, 9, 10, 27, 30, 33, 47, 53, 54, 58, 75, 80], "context": [4, 16, 17, 18, 19, 31, 41, 46, 47, 49, 51, 53, 55, 58, 63, 65, 68, 69, 71, 74, 75, 78, 79], "context_of_organ": 42, "context_weight": 4, "contextu": [9, 48, 75, 78, 79], "conting": [70, 77, 78], "continu": [0, 1, 4, 8, 11, 12, 15, 17, 18, 22, 23, 27, 29, 34, 35, 37, 41, 43, 44, 45, 46, 52, 55, 56, 59, 62, 63, 66, 69, 70, 71, 72, 74, 76, 77, 78, 79], "continual_improv": 42, "continuous_authent": 42, "continuous_monitor": 29, "contract": [72, 73, 78], "contractu": 79, "contribut": [0, 20, 21, 23, 27, 52, 55, 57, 72, 73, 76, 77, 78, 79], "contributor": [1, 19, 27], "control": [1, 2, 3, 5, 6, 7, 8, 10, 11, 12, 15, 16, 17, 20, 24, 25, 26, 27, 28, 32, 35, 36, 38, 41, 44, 45, 46, 47, 48, 49, 51, 52, 53, 55, 56, 59, 62, 63, 66, 67, 68, 69, 71, 72, 73, 74, 75, 77, 78, 80], "control_assess": 56, "control_autom": 29, "control_effect": 69, "control_id": [26, 46, 56], "control_map": [29, 41], "control_nam": 80, "control_typ": 56, "convent": [18, 19, 62], "convert": [24, 57], "cooki": [7, 53, 66, 79], "cool": 34, "cooper": [45, 47, 78], "coordin": [24, 40, 43, 44, 45, 47, 70, 72, 73, 74, 76, 77, 78, 79], "copi": [0, 3, 14, 20, 28, 35, 59, 67, 79], "cor": [13, 28, 39, 66, 70], "core": [1, 14, 17, 20, 24, 27, 28, 34, 37, 39, 42, 44, 54, 55, 62, 64, 65, 66, 67, 69, 74, 77], "coredn": [32, 33], "corner": 38, "corpor": [47, 73, 74, 77, 78], "correct": [12, 19, 21, 22, 38, 40, 46, 51, 56, 63, 78], "correctli": [51, 79], "correl": [8, 12, 25, 27, 29, 34, 35, 37, 38, 39, 40, 58, 62, 63, 67, 69, 70, 72, 73, 74, 76, 77, 78, 79], "correlate_ev": 75, "correlation_fail": 9, "correlation_window": 75, "correspond": 58, "corrupt": [43, 47, 66, 77], "cors_allow_credenti": 13, "cors_allow_head": 13, "cors_allow_method": 13, "cors_max_ag": 13, "cors_origin": [13, 14, 20, 28, 66], "cosmet": 50, "cosmo": 30, "cost": [10, 35, 45, 52, 55, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80], "cost_cent": 5, "could": [49, 79], "council": 78, "counsel": [43, 45, 47], "count": [30, 31, 32, 33, 35, 54, 66, 75, 76, 79], "count_critical_issu": 46, "count_issues_by_sever": 54, "counter": [35, 53, 55, 77], "countermeasur": 77, "coupl": 62, "coursera": 79, "court": 45, "cov": [2, 18, 20, 51, 64, 65], "cover": [0, 1, 3, 7, 13, 19, 21, 22, 27, 30, 35, 38, 40, 42, 46, 47, 62, 64, 65, 66, 67, 68, 69, 71, 74, 75, 76, 78, 79, 80], "coverag": [2, 3, 8, 9, 18, 19, 20, 21, 22, 23, 24, 25, 27, 36, 39, 41, 42, 51, 52, 54, 55, 62, 63, 72, 74, 75, 76, 77, 78, 79, 80], "coverage_analysi": 42, "coverage_percentag": 42, "coveragerc": 65, "covert": [77, 79], "cozi": [6, 77, 80], "cp": [14, 20, 28, 31, 66, 67], "cpra": 72, "cprofil": [20, 35], "cpst": 77, "cptl": 76, "cptma": 76, "cptp": [76, 77], "cpu": [14, 16, 17, 20, 28, 32, 34, 35, 37, 38, 62, 65, 66, 67, 70], "cpu_cor": 5, "cpu_impact": 53, "cpu_perc": 53, "cpu_usage_avg": 34, "cpu_usage_max": 34, "cpuperc": 34, "crash": 17, "crcm": 72, "creat": [0, 1, 2, 3, 4, 7, 8, 11, 12, 14, 17, 18, 20, 21, 23, 24, 25, 26, 30, 31, 33, 35, 36, 38, 41, 42, 43, 45, 49, 53, 55, 56, 58, 59, 61, 62, 63, 64, 65, 66, 67, 68, 70, 71, 73, 74, 75, 77, 79, 80], "create_al": 64, "create_alert_rul": 68, "create_asset": 51, "create_attack_scenario": 59, "create_cas": [26, 58], "create_case_from_incid": 58, "create_engin": [35, 64], "create_improvement_roadmap": 42, "create_index": [18, 56], "create_new": 68, "create_new_featur": 20, "create_response_playbook": 69, "create_review": 46, "create_t": [18, 56], "create_thehive_case_from_incid": 58, "createasset": 18, "created_at": [5, 6, 18, 35, 41, 56, 58, 61, 66], "created_bi": [31, 56, 61], "created_cas": 58, "created_d": [31, 46], "createscenario": 8, "creation": [0, 8, 16, 21, 22, 24, 25, 36, 37, 45, 58, 59, 62, 63, 70, 73, 74, 76, 77, 78, 79], "credenti": [7, 11, 13, 14, 16, 17, 23, 27, 28, 29, 31, 38, 40, 42, 43, 46, 50, 51, 53, 55, 66, 68, 70, 77, 79], "credential_harvesting_tool": 6, "credential_monitor": 42, "credentials_path": 30, "credibl": [76, 77], "credit": [47, 77], "creep": 77, "crime": 72, "crimin": [77, 79], "crise": [73, 78], "crisi": [23, 45, 74, 76, 77, 78, 79], "crisis_management_access": 78, "crisis_management_author": 73, "crisis_oversight_author": 73, "criteria": [21, 41, 42, 46, 47, 50, 62, 63, 71, 76, 77, 78, 79], "critic": [2, 4, 5, 6, 8, 13, 16, 21, 23, 27, 30, 32, 35, 40, 41, 45, 46, 48, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 61, 63, 64, 65, 67, 68, 69, 70, 71, 73, 74, 76, 77, 78, 79, 80], "critical_asset": 80, "critical_assets_affect": [6, 71], "critical_databas": 80, "critical_fil": 55, "critical_infrastructure_test": 77, "critical_issu": [46, 54], "critical_vuln": 32, "critical_vulns_count": 57, "criticality_level": [6, 8, 57, 58, 61, 71], "criticality_level_encod": 57, "criticality_level_enum": 61, "criticality_multipli": 35, "criticality_scor": [6, 61], "criticalvulnerabilitydetect": 54, "crl": 11, "crm": 77, "crma": 78, "crmp": 72, "cro": [73, 79], "cron": [67, 68], "cronjob": [16, 41], "crontab": 57, "cross": [0, 1, 13, 15, 19, 23, 24, 25, 26, 27, 29, 31, 39, 45, 47, 55, 56, 69, 73, 74, 75, 76, 77, 78, 79], "cross_domain_attack_plan": 77, "cross_functional_commun": 72, "cross_functional_coordin": 76, "cross_team_commun": 76, "cross_team_integration_manag": 76, "cross_val_scor": 57, "crowdstrik": [42, 79], "crown": [69, 76, 77, 78], "crown_jewel": [67, 69], "crtl": 77, "crto": 77, "crtp": 77, "crud": [8, 67], "crypto": 54, "cryptocurr": 77, "cryptograph": [22, 37, 40, 41, 51, 52, 54, 55, 79], "cryptographi": [11, 52, 73, 77, 78], "csa": 78, "csap": 76, "csep": 77, "csf": [24, 25, 26, 36, 37, 41, 52, 72, 78], "csi": 31, "cspm": 78, "csrf": [44, 47], "csrf_trusted_origin": 14, "css": 35, "csv": [5, 70, 79], "cth": 76, "ctrl": 66, "cultiv": [76, 77, 78], "cultur": [23, 42, 52, 73, 76, 78], "cumul": 35, "curl": [2, 4, 5, 6, 9, 10, 11, 12, 14, 16, 17, 20, 28, 30, 31, 32, 33, 35, 41, 43, 66, 67, 68, 75, 80], "currenc": 63, "current": [0, 1, 6, 7, 9, 17, 19, 20, 21, 26, 32, 33, 35, 37, 42, 43, 44, 45, 46, 50, 52, 53, 54, 55, 59, 63, 64, 67, 69, 70, 71, 74, 75, 76, 77, 78, 79], "current_boundari": 69, "current_level": 59, "current_password": 7, "current_risk_scor": 57, "current_scor": 75, "current_st": 69, "current_target": 5, "current_timestamp": 61, "current_us": [18, 20], "cursor": [8, 54], "custodi": [45, 72, 77, 78, 79], "custom": [0, 1, 2, 5, 18, 25, 28, 31, 32, 34, 38, 41, 42, 43, 45, 47, 48, 50, 52, 53, 54, 63, 65, 68, 69, 70, 72, 73, 74, 75, 76, 77, 78, 79, 80], "custom_apt": 80, "custom_bandit_rul": 55, "custom_threat_actor": 80, "customer_count": 80, "customer_databas": 80, "customfield": 58, "customiz": [4, 79], "cut": [3, 23, 27, 32, 76, 77], "cutoff": 35, "cve": [5, 21, 50, 53], "cve_id": 5, "cvss": [67, 77, 79], "cvss_base_scor": 46, "cvss_score": 46, "cvss_score_avg": 57, "cwd": 20, "cwe": [49, 52], "cwpp": 78, "cyber": [40, 45, 73, 77, 78, 79], "cyberark": 42, "cybercrimin": [76, 77], "cybersecur": [19, 22, 39, 41, 44, 56, 63, 72, 73, 76, 77, 78, 79], "cybrari": 79, "cycl": [59, 69, 78], "cypher": [28, 35, 66], "czf": [31, 33, 43, 66], "d": [2, 3, 5, 6, 14, 16, 17, 19, 20, 21, 28, 30, 31, 32, 33, 38, 40, 43, 48, 51, 54, 66, 67, 75, 80], "d3": [24, 25, 36], "d_": 31, "daemon": 66, "dai": [4, 12, 23, 31, 32, 33, 34, 35, 40, 42, 43, 46, 47, 48, 50, 51, 52, 54, 61, 63, 66, 69, 75, 77, 79, 80], "daili": [5, 24, 25, 40, 48, 50, 57, 63, 68, 70, 78, 80], "daily_full_discoveri": 68, "damag": [72, 79], "danger": 52, "dark": [3, 76, 77], "dashboard": [0, 1, 11, 12, 15, 16, 17, 19, 21, 22, 24, 25, 26, 27, 28, 31, 34, 36, 37, 40, 43, 45, 46, 48, 50, 51, 52, 62, 66, 67, 68, 72, 73, 74, 76, 77, 78], "dashboard_refresh_second": 41, "dashboard_uid": 33, "dast": [0, 26, 39, 42, 44, 51, 52, 55, 64], "dast_autom": 54, "dast_requests_tot": 53, "dast_scan": 54, "dast_scan_dur": 53, "dast_scan_duration_second": 53, "dastperformancemonitor": 53, "dastresultsanalyz": 53, "data": [0, 1, 3, 4, 5, 7, 8, 10, 13, 17, 18, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29, 32, 33, 34, 35, 36, 37, 38, 41, 42, 44, 45, 46, 47, 48, 50, 52, 55, 57, 58, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80], "data_access": 41, "data_assets_affect": 6, "data_at_rest": 29, "data_breach": 80, "data_categories_affect": 12, "data_classif": 42, "data_encrypt": 69, "data_exfiltr": [61, 69], "data_in_transit": 29, "data_integr": 80, "data_loss_prevent": [42, 61], "data_processing_system": 68, "data_processor": 42, "data_protect": 42, "data_protection_polici": 42, "data_sensit": 80, "data_sourc": [59, 69, 75], "data_subject": 42, "data_subject_impact": 80, "data_subject_request": 12, "data_subject_right": 29, "data_subjects_affect": 80, "data_sync_in_progress": 9, "data_typ": 80, "databas": [0, 3, 4, 5, 6, 8, 11, 14, 15, 19, 21, 24, 26, 28, 30, 36, 37, 38, 39, 43, 47, 51, 53, 59, 64, 65, 67, 68, 69, 70, 71, 76, 77, 79], "database_001": [6, 8, 71, 80], "database_cach": 34, "database_command_timeout": 13, "database_connect_timeout": 13, "database_echo": 13, "database_endpoint": 16, "database_host": [17, 31, 32, 33], "database_identifi": 31, "database_max_overflow": [13, 28, 34], "database_nam": [17, 31, 32, 33], "database_pool_recycl": 13, "database_pool_s": [13, 28, 34], "database_pool_timeout": 13, "database_query_time_p95": 29, "database_security_group_id": [17, 31], "database_ssl_mod": 13, "database_url": [13, 14, 20, 28, 35], "database_us": [17, 31, 32, 33], "database_usernam": 16, "database_vers": 31, "databaseerror": [18, 65], "dataclass": [57, 58], "datadog": 54, "datafram": [35, 57], "dataprocessingservic": 42, "dataset": [8, 62, 67, 75, 79], "datatyp": 58, "date": [2, 12, 16, 18, 21, 31, 32, 33, 43, 46, 47, 50, 56, 66, 70, 80], "datetim": [18, 42, 46, 54, 56, 57, 58], "datid": 66, "datnam": 66, "day_of_week": 57, "days_since_last_scan": 57, "days_since_last_upd": 57, "days_to_expiri": 32, "db": [2, 4, 14, 15, 16, 17, 18, 28, 30, 31, 33, 56, 57, 58, 59, 64, 65, 66, 69], "db_allocated_storag": 16, "db_endpoint": 16, "db_host": 43, "db_instance_class": 16, "db_max_allocated_storag": 16, "db_password": 16, "db_security_group_id": 16, "db_session": [57, 58, 64], "db_user": 43, "db_usernam": 16, "dbeaver": 2, "dbinstanc": [17, 31], "dbinstancestatu": 17, "dbm": [34, 35], "dbname": 28, "dbsnapshot": 31, "dbsnapshotidentifi": 31, "dcsync": 77, "dd": [43, 45, 46], "ddo": [42, 70], "de": [4, 22, 37, 55, 56], "deactiv": 70, "dead_percentag": 32, "deb": 14, "debt": 55, "debug": [0, 13, 14, 17, 19, 28, 62, 70], "debug_mod": 65, "debugg": [2, 65], "decentr": 77, "deception_technologi": 61, "decim": [18, 56, 57, 61], "decis": [3, 4, 12, 19, 21, 36, 43, 45, 55, 62, 70, 71, 73, 74, 76, 77, 78, 79], "decision_funct": 57, "decod": 49, "decomposit": 62, "decor": [18, 35], "decrypt": [42, 79], "decrypt_sensitive_data": 42, "dedic": [15, 16, 28, 43, 53, 67, 69, 76, 77, 78, 79], "dedupl": 79, "deep": [30, 48, 50, 55, 62, 67, 76, 78, 79], "def": [7, 18, 19, 20, 21, 26, 35, 42, 46, 51, 53, 54, 55, 56, 57, 58, 59, 64, 65, 69, 79], "default": [3, 5, 6, 8, 11, 12, 13, 16, 18, 28, 35, 38, 41, 42, 46, 50, 51, 53, 56, 57, 58, 59, 61, 66, 68, 70, 71, 72, 78, 79], "default_duration_hour": 4, "default_expiry_dai": 12, "default_pap": 58, "default_timeout_second": 18, "default_tlp": 58, "default_ttl": 35, "defaultinterpreterpath": [18, 20], "defend": [42, 79], "defens": [4, 11, 15, 23, 27, 29, 39, 42, 44, 51, 62, 69, 71, 74, 76, 77, 78, 79], "defense valid": 76, "defici": [25, 41, 78], "defin": [4, 15, 21, 40, 42, 51, 56, 57, 58, 64, 67, 69, 70, 71, 73, 74, 77, 78, 79, 80], "definit": [3, 4, 23, 24, 25, 27, 36, 37, 43, 62, 70, 71, 74], "degrad": [43, 45, 47, 50, 63], "degre": [6, 23, 26, 27, 35, 59, 63, 67, 71, 77, 79], "del": [32, 33, 35], "delai": [4, 12, 35, 40, 49, 79], "deleg": [61, 70, 79], "delet": [4, 6, 12, 13, 14, 17, 18, 20, 21, 25, 27, 32, 33, 35, 40, 41, 43, 51, 56, 58, 61, 62, 63, 64, 65, 70, 79], "deleted_at": [56, 61], "deliv": [3, 23, 24, 25, 76], "deliver": [46, 69], "deliveri": [16, 40, 47, 52, 73, 77, 78, 79], "demand": [15, 29, 41], "demo": 0, "demonstr": [0, 3, 12, 23, 27, 47, 52, 63, 69, 72, 73, 76, 77, 78, 79], "deni": [11, 66], "denial": [45, 46, 47, 48, 51], "densiti": [6, 48, 50], "deny_all_default": 42, "dep": [18, 20], "depart": 41, "depend": [2, 3, 5, 14, 18, 19, 20, 21, 38, 40, 46, 47, 49, 51, 52, 53, 55, 57, 58, 62, 64, 65, 67, 69, 70, 71, 74, 76, 77, 78, 79], "dependabot": 54, "dependency_scan": 51, "depends_on": [5, 56, 61], "deploi": [1, 2, 3, 11, 21, 24, 25, 28, 29, 31, 32, 35, 42, 43, 52, 54, 68, 69], "deploy": [0, 2, 4, 12, 13, 17, 21, 26, 28, 32, 33, 34, 36, 38, 41, 43, 45, 46, 47, 48, 51, 52, 53, 55, 61, 63, 68, 70, 72, 73, 74, 76, 77, 78, 79, 80], "deployment_d": 57, "deployment_vers": 31, "deploymenttest": 16, "deprec": [22, 56, 78], "deprovis": 70, "depth": [3, 4, 11, 15, 18, 19, 22, 27, 29, 34, 39, 42, 44, 48, 51, 62, 66, 67, 69, 71, 78, 79], "deriv": 18, "desc": [5, 8, 17, 18, 20, 32, 33, 34, 35, 53, 57, 61, 66], "describ": [11, 16, 17, 18, 19, 30, 31, 32, 33, 43, 65, 66], "describe_inst": 65, "describeinst": 66, "descript": [0, 3, 4, 5, 6, 12, 13, 18, 19, 20, 21, 22, 26, 33, 35, 40, 41, 42, 43, 45, 46, 47, 50, 51, 53, 54, 56, 58, 61, 63, 64, 65, 71, 74, 75, 77, 79], "description_part": 58, "deseri": [52, 55], "design": [1, 11, 19, 21, 22, 24, 25, 29, 34, 36, 38, 39, 40, 44, 45, 46, 48, 50, 55, 59, 63, 67, 70, 72, 73, 74, 76, 77, 79], "design_security_control": 7, "desired_capac": 15, "desk": 79, "desktop": 2, "destin": 42, "destroi": 14, "destruct": [47, 70, 77], "detail": [0, 1, 2, 3, 5, 6, 8, 9, 10, 11, 13, 14, 16, 17, 18, 19, 22, 27, 28, 29, 30, 33, 34, 37, 38, 39, 40, 43, 44, 45, 47, 51, 55, 57, 58, 60, 62, 64, 66, 67, 68, 69, 70, 71, 74, 75, 76, 77, 78, 79, 80], "detect": [1, 3, 4, 8, 11, 12, 15, 22, 23, 24, 25, 26, 27, 29, 30, 33, 36, 38, 39, 41, 42, 46, 48, 50, 52, 53, 54, 55, 56, 57, 58, 59, 62, 63, 68, 71, 72, 74, 75, 77, 78, 80], "detect_anomali": [26, 57], "detection_analysi": 69, "detection_cap": 42, "detection_difficulti": [6, 61], "detection_en": 12, "detection_engineering_author": 76, "detection_engineering_expertis": 76, "detection_enhanc": 80, "detection_method": [6, 59], "detection_prob": [6, 61, 80], "detection_queri": 69, "detection_rul": 69, "detection_rule_test": 76, "detection_tim": 58, "determin": [40, 43, 45, 50, 59, 71, 79], "determinist": 65, "dev": [2, 3, 14, 20, 28, 32, 45, 55], "develop": [1, 17, 19, 23, 24, 26, 29, 30, 37, 39, 42, 43, 44, 46, 47, 48, 50, 52, 55, 56, 63, 65, 66, 67, 70, 71, 74, 76, 77], "developer_security_train": 78, "deviat": [72, 76, 78, 79], "devic": [3, 11, 40, 42, 47, 56, 66, 76, 77, 78], "device_certif": 42, "device_compli": 42, "device_health_check": 42, "device_id": 42, "device_verif": 42, "devop": [43, 51, 77], "devpassword123": 20, "devsecop": [50, 54, 73, 78], "devtool": 20, "df": [32, 35, 38, 41, 66], "dfir": 78, "dgvzddp0zxn0": 53, "diagnos": [17, 66], "diagnosi": 17, "diagnost": [0, 43, 77], "diagram": [0, 1, 17, 22, 37, 43, 46, 51, 60, 62], "dialect": 56, "dict": [7, 18, 26, 35, 42, 51, 53, 54, 55, 57, 58, 59, 66], "differ": [0, 1, 3, 12, 14, 15, 19, 28, 34, 38, 56, 65, 67, 70, 74, 78, 79], "differenti": [26, 52, 73, 76, 77, 78], "difficulti": [59, 71], "digest": 79, "digit": [23, 41, 45, 46, 72, 74, 76, 77, 78, 79], "digraph": [35, 59], "dilig": 78, "dimens": 76, "dimension": [72, 76, 77, 78, 79], "diplomat": 77, "dir": [35, 54], "direct": [3, 4, 35, 38, 40, 51, 61, 63, 67, 70, 71, 73, 78, 79, 80], "direct_cost": 80, "direction_enum": 61, "directli": [65, 71], "director": [72, 73, 77, 78, 79], "directori": [3, 14, 17, 20, 28, 54, 65, 70, 77, 79], "disabl": [13, 32, 35, 45, 49, 50, 52, 58, 64, 65, 67, 70, 79], "disast": [22, 25, 27, 37, 38, 40, 42, 43, 51, 62, 63, 73, 74, 77, 78], "disc_1234567890": 5, "disciplinari": 79, "disclos": 44, "disclosur": [44, 45, 46, 48, 51, 55, 73, 77], "disconnect": 45, "discord": 67, "discov": [5, 6, 8, 16, 18, 19, 30, 35, 44, 50, 63, 66, 67, 68, 71, 74, 77, 79, 80], "discover_api": 68, "discover_api_endpoint": 65, "discover_asset": [18, 35], "discover_assets_batch": 35, "discover_aws_asset": 65, "discover_docker_contain": 68, "discover_ec2_inst": 68, "discover_rds_inst": 68, "discover_s3_bucket": 68, "discovered_api": 68, "discoveri": [3, 6, 8, 12, 16, 18, 19, 21, 23, 26, 27, 33, 34, 35, 36, 38, 47, 62, 64, 65, 66, 67, 69, 72, 78, 79, 80], "discovery_config": 30, "discovery_fail": 5, "discovery_job": [66, 68], "discovery_opt": 5, "discovery_orchestr": 30, "discovery_respons": 8, "discovery_result": 68, "discovery_sourc": 5, "discovery_typ": [5, 68], "discoveryorchestr": 30, "discoveryschedul": 68, "discret": 79, "discretionari": 47, "discuss": [3, 8, 18, 19, 20, 21, 27, 46, 47, 62, 67, 72, 74, 80], "disgruntl": 77, "disk": [14, 30, 32, 34, 66], "disk_gb": 5, "disk_read_lat": 34, "disk_write_lat": 34, "diskpressur": 33, "dispar": 78, "displai": [13, 30, 71, 79], "dispos": [39, 40, 42, 44, 70, 77], "disposal_procedur": 42, "disput": 47, "disrupt": [45, 50, 52, 63, 73, 76, 77, 78, 79, 80], "dist": 20, "distinct": 61, "distinguish": 79, "distribut": [15, 24, 29, 34, 59, 73, 77, 78, 79], "distributedcach": 59, "div": [18, 19], "dive": [62, 78, 79], "divers": [61, 62, 78], "diversif": 73, "dkr": [14, 16], "dl": 16, "dlp": [11, 42, 77, 78], "dmz": [38, 42], "dn": [16, 17, 31, 32, 47, 77, 78, 79], "do": [32, 44, 47], "doc": [1, 3, 5, 8, 13, 17, 19, 20, 21, 25, 28, 38, 40, 43, 66, 67, 74], "docker": [0, 1, 2, 14, 16, 17, 19, 20, 21, 26, 32, 34, 35, 38, 43, 53, 62, 67, 68], "docker_contain": 61, "dockerfil": [14, 16, 32], "docs_url": [13, 20], "docstr": [18, 19, 20], "document": [4, 5, 7, 8, 9, 10, 11, 12, 15, 16, 17, 20, 21, 24, 25, 26, 28, 31, 32, 33, 35, 36, 38, 39, 41, 42, 45, 47, 48, 49, 53, 54, 55, 56, 57, 58, 59, 60, 61, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80], "document_incid": 42, "documentation_coverag": 18, "documented_inform": 42, "doe": [5, 7, 9, 18, 75], "doesn": 42, "dogstatsdcli": 54, "domain": [5, 9, 13, 16, 17, 23, 28, 32, 38, 47, 48, 56, 58, 65, 66, 69, 71, 74, 75, 76, 77, 78, 79], "domain_control": 61, "domain_id": 56, "domain_nam": 16, "don": [19, 21, 35, 51, 66], "done": [32, 43], "down": [2, 14, 17, 19, 32, 33, 34, 35, 38, 66, 67], "down_revis": 56, "downgrad": [17, 18, 20, 56], "download": [8, 14, 16, 28, 30, 31, 38, 67, 79], "downstream": 77, "downtim": [15, 24, 29, 73], "downwardapi": 51, "dpi": 78, "dpia": [72, 78], "dpo": 12, "dr": [31, 40], "draft": 56, "drift": [38, 68, 78, 79], "drill": [27, 40, 45], "drive": [76, 78, 79], "driven": [10, 15, 23, 27, 42, 46, 54, 73, 74, 76, 77, 78, 79], "driver": [64, 77, 78], "drop": [45, 53, 54, 56, 64, 65], "drop_al": 64, "drop_tabl": [18, 56], "dry": [19, 21, 31], "dss": [0, 23, 27, 36, 41, 51, 63, 68, 72, 74, 78], "du": 33, "due": [18, 43, 78], "dump": [31, 35, 59, 67, 79], "duplic": [8, 19, 58, 79], "duplicate_asset": 5, "durabl": [34, 35], "durat": [4, 18, 32, 33, 34, 35, 43, 53, 55, 65, 68, 75, 77], "duration_dai": 75, "duration_hour": [4, 69], "duration_m": 18, "dure": [3, 17, 18, 28, 40, 43, 50, 53, 55, 63, 65, 66, 71, 73, 74, 76, 77, 78, 79], "duti": [4, 40], "dwell": 79, "dynam": [4, 11, 15, 25, 29, 36, 39, 41, 44, 50, 51, 52, 55, 62, 64, 68, 76, 77, 78, 79], "dynamodb": [30, 68], "e": [8, 9, 14, 18, 19, 20, 26, 30, 31, 32, 33, 48, 56, 57, 58, 59, 66, 71, 72, 78], "e2": [0, 2, 18, 21, 64], "e203": 18, "e89b": 7, "each": [1, 3, 13, 18, 23, 27, 43, 45, 48, 49, 59, 61, 64, 65, 70, 74, 79], "earli": [21, 54, 55, 64, 72, 76], "early_stop": 57, "eas": [48, 50, 65], "easi": [0, 1, 8, 12, 15, 18, 26], "east": [5, 13, 28, 30, 31, 65, 68, 78], "east1": [30, 68], "eastern_europ": 75, "eastu": 30, "eb": [30, 31, 68], "ec": [30, 64, 68], "ec2": [5, 16, 17, 30, 66, 67, 68], "ec2_inst": 68, "echo": [14, 21, 31, 32, 33, 35, 53, 55, 66], "economi": [73, 76, 77, 78], "ecosystem": [3, 37, 50, 54, 72, 73], "ecr": [14, 16], "ecr_repo": 32, "edg": [3, 6, 19, 21, 23, 26, 27, 38, 53, 59, 62, 64, 73, 76, 77, 78, 79], "edge_data": [35, 59], "edge_likelihood": 59, "edit": [2, 3, 14, 20, 28, 66], "editor": [18, 20], "edr": [42, 68, 75, 77, 78, 79], "educ": [18, 23, 55, 69, 72, 73, 76, 77, 78, 79], "ef": 68, "effect": [3, 4, 13, 18, 19, 20, 21, 23, 27, 30, 34, 40, 41, 42, 43, 45, 46, 47, 48, 51, 53, 55, 59, 67, 68, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80], "effective_cache_s": [34, 35], "effective_d": 56, "effective_io_concurr": [34, 35], "effectiveness_scor": 42, "effici": [0, 3, 4, 5, 18, 21, 23, 24, 25, 30, 35, 36, 41, 45, 46, 59, 62, 70, 72, 73, 76, 77, 78, 79, 80], "efficientdataprocessor": 35, "effort": [21, 24, 47, 72, 73, 79], "ehr": 77, "ek": [14, 16, 29, 30, 32, 68], "eks_cluster_vers": 16, "eks_desired_capac": 16, "eks_max_capac": 16, "eks_node_instance_typ": 16, "el": 78, "elaps": 54, "elasticach": [14, 16, 29, 68], "elb": 68, "elbv2": [16, 17, 33], "electr": 77, "electron": 77, "element": [0, 1, 25], "element_to_be_click": 64, "elev": [46, 48, 51, 76, 79], "elif": [35, 42, 46, 49, 57], "elimin": [49, 52, 79], "els": [32, 35, 42, 46, 53, 57, 58, 61, 65, 69], "elsif": 61, "email": [7, 11, 12, 14, 16, 19, 20, 28, 38, 43, 44, 47, 53, 58, 61, 64, 65, 66, 67, 68, 69, 77, 79], "email_secur": 61, "email_serv": 71, "embed": [42, 77], "embezzl": 77, "embrac": [73, 79], "emerg": [3, 4, 23, 27, 33, 44, 45, 48, 50, 73, 76, 77, 78, 79], "emergency_approval_bypass": 4, "emergency_duration_hour": 4, "empir": 80, "employe": [42, 47, 70, 71, 72, 77, 78, 79], "employee_id": 42, "empow": [3, 23, 63, 72, 73, 76, 77, 78, 79], "empti": [18, 65, 66], "emptydir": 51, "emul": [23, 27, 76, 77], "enabl": [3, 9, 10, 11, 13, 16, 17, 18, 20, 23, 24, 25, 27, 30, 32, 34, 35, 38, 42, 50, 51, 53, 55, 58, 63, 65, 68, 70, 72, 74, 76, 77, 78, 79], "enable_external_api": 14, "enable_profil": 14, "enc": 75, "encod": [17, 39, 44, 46, 49, 55, 57], "encompass": [44, 78], "encount": [5, 17, 28, 38], "encourag": 79, "encrypt": [15, 28, 29, 30, 39, 40, 41, 42, 44, 46, 47, 49, 51, 52, 59, 61, 62, 63, 67, 68, 70, 71, 77, 78, 79], "encrypt_personal_data": 42, "encrypt_sensitive_data": 42, "encrypted_commun": 42, "encrypted_data": 42, "encryption_algorithm": 13, "encryption_at_rest": 42, "encryption_in_transit": 42, "encryption_system": 61, "encryptionservic": 42, "end": [0, 15, 18, 19, 21, 30, 32, 35, 39, 47, 49, 53, 61, 62, 63, 72, 76, 77, 78], "end_dat": 41, "end_monitor": 53, "end_tim": [57, 65], "enddat": 32, "endpoint": [1, 3, 5, 9, 10, 16, 17, 18, 19, 20, 21, 22, 24, 25, 26, 27, 31, 33, 34, 35, 42, 47, 53, 54, 62, 65, 66, 67, 68, 69, 71, 75, 77, 78, 79], "endpoint_act": 42, "endpoint_detect": 75, "endpoint_protect": 61, "energi": 77, "enforc": [29, 45, 47, 51, 62, 70, 76, 77, 78, 79], "engag": [3, 23, 27, 63, 73, 76, 77, 78, 79], "engin": [6, 17, 22, 23, 24, 25, 27, 33, 35, 36, 37, 43, 47, 51, 59, 60, 64, 67, 71, 74, 77, 78, 80], "english": 35, "enhanc": [0, 9, 19, 21, 27, 45, 46, 52, 56, 63, 67, 71, 72, 73, 74, 76, 77, 78, 79], "enough": [19, 42], "enrich": [8, 24, 25, 26, 36, 74, 79], "enrich_ioc": 75, "enriched_ioc": 75, "ensembl": [24, 57], "ensemble_proba": 57, "ensemble_scor": 57, "ensur": [1, 4, 9, 10, 11, 12, 18, 19, 20, 21, 27, 33, 35, 36, 38, 40, 41, 44, 45, 46, 48, 50, 51, 53, 54, 63, 65, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80], "enter": [6, 38, 67], "enterpris": [0, 6, 8, 9, 10, 15, 23, 24, 28, 29, 34, 35, 37, 38, 44, 59, 61, 62, 63, 64, 66, 67, 68, 73, 74, 75, 76, 77, 79, 80], "enterprise secur": [23, 78], "enterprise_architecture_design": 78, "enterprise_count": 75, "enterprise_risk_integr": 73, "entir": [18, 27, 49, 50], "entiti": [3, 8, 52, 62, 76, 77, 78], "entity_id": 75, "entity_typ": 75, "entri": [35, 58, 59, 71, 74, 79], "entropi": 51, "entry_point": [6, 61, 63, 71], "entrypoint": 8, "enum": [54, 61], "enumer": [57, 62, 74, 77, 79], "env": [2, 13, 14, 16, 17, 18, 20, 28, 35, 43, 53, 64, 65, 66, 67], "environ": [1, 5, 7, 8, 17, 19, 21, 22, 23, 24, 25, 26, 27, 30, 31, 35, 36, 37, 38, 40, 42, 43, 45, 46, 47, 54, 56, 57, 61, 63, 68, 70, 71, 72, 75, 76, 77, 78, 79], "environment": [42, 50, 73, 75, 77], "environment_encod": 57, "eof": [14, 16, 31, 32], "eq": 32, "equip": [76, 77, 78, 79], "equival": 56, "erad": [42, 79], "erasur": 12, "erm": 78, "erp": 77, "err": [8, 18, 19], "error": [0, 3, 7, 13, 17, 18, 19, 20, 21, 22, 25, 26, 28, 32, 33, 34, 38, 40, 42, 43, 46, 49, 52, 53, 54, 55, 57, 58, 59, 62, 63, 64, 65, 67, 68, 70], "error_count": 32, "error_messag": 65, "error_r": [32, 34], "error_typ": 18, "errorboundari": 18, "errorfallback": 18, "errorinfo": 18, "errormessag": 18, "esac": [31, 32], "esbenp": 20, "escal": [0, 11, 15, 25, 26, 29, 41, 45, 46, 47, 51, 53, 55, 63, 69, 70, 71, 72, 74, 76, 77, 78], "escalate_incid": 26, "escalation_id": 4, "escalation_level": 26, "escalation_procedur": 42, "escap": [76, 77], "esg": 73, "eslint": 18, "especi": 7, "espionag": [77, 80], "essenti": [27, 32, 42, 79], "establish": [1, 18, 23, 24, 34, 35, 36, 40, 42, 45, 70, 71, 73, 76, 77, 78, 79], "estim": [21, 33, 36, 45, 63, 69, 71, 74, 77, 79, 80], "estimated_complet": 5, "estimated_dur": [5, 6, 61], "estimated_tim": [6, 61], "estimated_time_to_compromis": 80, "eta": 33, "etc": [3, 21, 28, 32, 33, 53, 66, 72, 74, 76, 80], "eth0": 68, "ethic": [22, 27, 72, 76, 77], "eu": [30, 63, 68], "europ": 30, "european": 44, "evalu": [4, 24, 25, 28, 40, 43, 45, 46, 48, 50, 53, 54, 55, 57, 71, 73, 74, 76, 77, 78, 79], "evaluate_security_postur": 54, "evaluation_interv": 34, "evas": [63, 74, 76, 77], "event": [7, 8, 13, 15, 16, 17, 22, 24, 25, 26, 27, 29, 33, 34, 36, 37, 38, 39, 40, 43, 45, 54, 56, 62, 63, 67, 68, 73, 74, 76, 77, 78, 79], "event_count": [41, 75], "event_data": [26, 75], "event_id": [41, 75], "event_process": 41, "event_processing_time_p95": 29, "event_typ": [41, 54, 75], "events_per_second": 29, "ever": [73, 76, 77, 78, 79], "everi": [2, 3, 11, 21, 23, 27, 35, 43, 48, 50, 52, 54, 62, 68, 79], "everyon": [19, 44, 77], "everyth": [1, 2, 3, 8, 20, 74], "everywher": 15, "evict": 63, "evid": [22, 25, 35, 37, 38, 39, 40, 41, 43, 51, 56, 69, 72, 74, 75, 76, 77, 78, 79], "evidence_collect": 29, "evidence_requir": 56, "evolut": [25, 26, 62, 63, 73, 76, 77, 78, 79], "evolv": [3, 11, 12, 18, 21, 23, 25, 39, 46, 48, 50, 52, 54, 73, 76, 77, 78, 79], "evt_12345": 75, "evt_67890": 75, "ex": 75, "examin": [71, 72, 78, 79], "exampl": [1, 9, 10, 14, 18, 19, 20, 21, 27, 28, 29, 35, 38, 43, 47, 48, 50, 53, 55, 64, 65, 66, 67, 73, 74, 75, 80], "exc_info": 65, "exc_tb": [35, 58], "exc_typ": [35, 58], "exc_val": [35, 58], "exce": [18, 39, 44, 52, 54], "exceed": [8, 59], "excel": [0, 25, 36, 41, 46, 48, 50, 63, 68, 74], "except": [4, 18, 19, 27, 33, 35, 43, 49, 52, 57, 58, 59, 70, 78], "excerpt": 51, "exchang": 76, "exclud": [20, 47], "exclude_dir": [54, 55], "exclude_lin": 65, "exclus": 68, "exec": [2, 11, 12, 14, 16, 17, 31, 32, 33, 38, 41, 43, 66, 67], "exec_us": [54, 55], "execut": [2, 3, 4, 5, 15, 16, 18, 22, 23, 26, 35, 40, 41, 42, 45, 46, 47, 53, 54, 55, 58, 59, 61, 62, 64, 69, 70, 71, 80], "execute_automated_respons": 26, "execute_valid": 69, "executive leadership": 73, "executive overview": 52, "executive_reporting_access": [72, 76, 77, 78], "executive_security_report": 73, "executive_summari": 56, "executor": 59, "exercis": [23, 39, 42, 43, 45, 51, 63, 76, 77, 78, 79], "exercise_design_author": 76, "exercise_result": 69, "exfiltr": [6, 45, 63, 71, 76, 77, 79], "exhaust": [18, 21, 30], "exist": [1, 5, 9, 18, 19, 22, 28, 37, 38, 58, 64, 65, 66, 67, 71, 74, 76, 78, 79], "existing_map": 58, "exit": [14, 17, 31, 32, 35, 38, 58, 66], "exp": [7, 57], "expand": [0, 3, 27, 29, 38, 52, 68], "expans": [3, 23, 25, 27, 29, 37, 50, 77], "expect": [19, 65, 66, 67, 68, 69, 78, 79], "expected_condit": 64, "expected_path": 19, "expected_result": 65, "expens": 78, "expensive_attack_analysi": 35, "expensive_attack_path_calcul": 35, "experi": [3, 19, 20, 24, 66, 73, 78, 79], "experienc": 0, "expert": [45, 48, 72, 76, 77, 78, 79], "expertis": [21, 70, 72, 74, 76, 77, 78, 79], "expir": [4, 7, 12, 21, 29, 30, 32, 33, 35, 66, 67], "expire_in": 54, "expires_at": 12, "expires_in": 7, "expiri": [21, 32], "explain": [19, 35, 64], "explan": [19, 47], "explicit": [18, 40], "explicitli": [42, 47], "exploit": [43, 46, 47, 48, 50, 63, 67, 69, 71, 74, 76, 78, 79, 80], "exploit_development_access": 77, "exploitability_scor": 46, "explor": [0, 18, 19, 20, 38, 59, 77, 79], "exponenti": 8, "export": [2, 4, 7, 12, 14, 18, 25, 26, 33, 34, 38, 40, 59, 62, 68, 70, 71, 74, 75, 79], "export_navigator_lay": 75, "expos": [8, 19, 53, 64, 79], "exposur": [19, 30, 43, 45, 47, 50, 52, 77, 78, 79, 80], "exposure_level": 69, "exposure_risk": 35, "expr": 54, "extend": [12, 18, 24, 25, 26, 36, 55, 58, 59, 61, 67, 69, 73, 78], "extens": [0, 2, 3, 4, 18, 20, 36, 59, 61, 62, 77, 78, 79], "extern": [0, 1, 7, 11, 13, 14, 18, 19, 38, 39, 40, 41, 46, 52, 62, 64, 65, 67, 69, 70, 71, 72, 74, 75, 76, 77, 78, 79, 80], "external_asset": 69, "external_exposur": 35, "external_factor": 42, "extort": 77, "extra": [30, 54, 55], "extract": [31, 57], "extract_asset_featur": 57, "extract_batch_featur": 57, "extract_ioc": 42, "eyjhbgcioijiuzi1niisinr5cci6ikpxvcj9": 7, "eyjhbgcioijiuzi1nij9": 53, "eyjpzci6mtizndu2nzg5mh0": 8, "f": [2, 5, 6, 7, 8, 14, 16, 17, 18, 19, 20, 28, 30, 31, 32, 33, 34, 35, 42, 43, 46, 48, 51, 53, 54, 55, 57, 58, 59, 64, 65, 66, 68, 69, 71, 75, 80], "f1_score": 57, "f2": 32, "f5": 66, "faa": 77, "face": [45, 47, 71, 74, 78, 80], "facet": 3, "facial": 11, "facil": [42, 47, 76, 77, 78], "facilit": [76, 78], "factor": [3, 4, 6, 11, 13, 15, 29, 30, 39, 40, 42, 44, 46, 48, 49, 50, 51, 62, 63, 69, 70, 71, 74, 76, 77, 78, 79, 80], "factor_scor": 69, "factori": [0, 65], "fail": [2, 5, 6, 7, 9, 10, 17, 18, 19, 21, 33, 37, 38, 39, 43, 49, 51, 53, 54, 55, 57, 58, 59, 64, 65, 66, 79], "fail_on_crit": 54, "fail_timeout": [34, 35], "failed_login_spik": 41, "failov": [15, 24, 29, 40, 62, 70], "failur": [11, 15, 21, 30, 35, 38, 41, 43, 45, 65, 66, 78], "fair": [9, 10, 40, 46], "faith": 47, "faker": [64, 65], "fallback": [49, 52], "fals": [3, 4, 5, 7, 12, 13, 14, 16, 18, 19, 20, 23, 24, 26, 28, 32, 33, 35, 42, 45, 46, 48, 49, 50, 51, 52, 53, 54, 56, 57, 58, 59, 61, 65, 68, 69, 75, 76, 77, 79], "false_posit": [53, 55], "false_positive_r": [42, 69], "false_positives_tot": 55, "fame": 47, "famili": 79, "familiar": [38, 44], "fanci": [76, 77, 80], "faq": 1, "far": 78, "fast": [4, 21, 53, 54, 55, 62, 64, 65], "fastapi": [20, 24, 25, 26, 53, 58, 64], "faster": [0, 2, 24, 25, 35, 49, 78], "fastest": 28, "fault": [15, 27, 76, 78], "fc": [18, 19, 26], "fda": 72, "feasibl": 80, "feat": [2, 19, 21], "featur": [0, 4, 10, 11, 12, 14, 15, 19, 22, 29, 37, 41, 43, 46, 50, 51, 59, 62, 63, 64, 65, 66, 71, 75, 77, 79], "feature_column": 57, "feature_count": 57, "feature_dict": 57, "feature_engin": 57, "feature_import": 57, "feature_scal": 57, "feature_vector": 57, "featureengin": 57, "features_df": 57, "features_list": 57, "feder": [61, 77, 78, 79], "feed": [11, 38, 39, 45, 47, 51, 62, 63, 67, 70, 74, 75, 76, 79, 80], "feedback": [0, 1, 3, 19, 21, 23, 39, 45, 53, 55, 64, 65, 76, 78, 79], "fetch": [7, 21, 35, 65], "fetch_func": 35, "fetchal": 18, "few": 21, "fi": [14, 31, 32], "field": [5, 11, 13, 18, 25, 27, 32, 38, 79], "field_encryption_kei": 13, "file": [0, 2, 3, 5, 13, 14, 15, 16, 17, 19, 20, 21, 27, 28, 32, 35, 43, 51, 53, 54, 55, 64, 65, 66, 67, 68, 69, 70, 71, 77, 79], "file_hash": 58, "file_path": 54, "fileless": 77, "filenam": [55, 58], "filesystem": 31, "fill": 67, "filter": [3, 5, 18, 27, 30, 32, 33, 38, 57, 58, 67, 68, 69, 79], "filter_bi": 64, "fin7": [10, 74, 80], "final": [18, 19, 21, 24, 25, 35, 36, 53, 57, 73, 76, 77, 78, 79], "financ": [78, 80], "financi": [6, 23, 27, 59, 63, 71, 72, 73, 74, 76, 77, 78, 79, 80], "financial_data_affect": 80, "financial_impact": [6, 71, 80], "financial_reporting_system": 80, "financially_motiv": 69, "find": [18, 20, 32, 33, 35, 40, 45, 46, 55, 56, 58, 59, 66, 67, 69, 70, 71, 72, 74, 76, 77, 78, 79], "find_attack_path": [59, 64], "find_attack_paths_optim": 35, "find_el": 64, "find_path": [19, 65], "fine": [7, 67, 68, 70, 78, 79, 80], "fingerprint": 11, "fire": [42, 77], "firefox": [38, 67, 79], "firestor": 30, "firewal": [15, 38, 39, 42, 44, 46, 50, 61, 66, 67, 68, 69, 70, 78, 79], "firmwar": [61, 77], "first": [15, 18, 19, 20, 21, 27, 28, 35, 44, 47, 50, 52, 57, 58, 59, 64, 65, 66, 67, 68, 70, 71, 73, 75, 79], "first_nam": [7, 61, 64, 65], "first_seen": 75, "fit": 57, "fit_transform": 57, "fix": [2, 17, 19, 21, 22, 46, 47, 50, 55], "fix_hardcoded_secret": 54, "fix_weak_crypto": 54, "fixat": 53, "fixer": 64, "fixtur": [16, 19], "fk_attack_paths_source_asset": 18, "flag": [20, 36, 65, 76, 77], "flake8": [18, 20, 21, 51, 54, 64], "flake8en": [18, 20], "flaw": [47, 77], "flexibl": [56, 61, 62, 63], "float": [26, 35, 57, 59], "flow": [1, 3, 8, 12, 15, 22, 29, 34, 37, 39, 40, 46, 51, 62, 68, 74, 78, 79], "flushal": [66, 67], "focu": [19, 35, 38, 46, 51, 61, 68, 69, 71, 72, 74, 76, 77], "focus": [0, 1, 19, 21, 23, 38, 41, 46, 49, 57, 65, 69, 73, 77], "follow": [0, 1, 3, 4, 8, 13, 16, 18, 19, 21, 38, 39, 44, 45, 46, 47, 48, 50, 51, 54, 60, 64, 66, 70, 71, 72, 74, 77, 79], "font": 3, "footer": 21, "foothold": [6, 77], "footprint": [77, 78], "forbidden": [8, 18], "forc": [20, 35, 53, 70, 75], "force_upd": 75, "forecast": [25, 72, 73, 76, 78, 79], "forecast_risk_trend": 26, "forefront": [77, 79], "foreign": 18, "foreignkei": [26, 56, 57, 58], "foreignkeyconstraint": 18, "forens": [22, 25, 29, 37, 40, 41, 42, 45, 69, 71, 74, 76, 77, 78, 79], "forensicsservic": 42, "forest": [24, 25, 36, 57, 77], "forget": 35, "fork": [3, 62], "forkrul": [2, 19, 20, 28, 38, 66, 67], "form": [53, 65], "formal": [3, 40, 42, 50, 70, 78, 79], "format": [0, 3, 6, 9, 12, 16, 18, 19, 20, 21, 27, 31, 32, 34, 41, 42, 48, 54, 55, 59, 62, 63, 65, 67, 68, 69, 70, 75, 79], "formatonsav": [18, 20], "formatt": [18, 20], "formula": 48, "forum": [8, 19, 38, 62, 66, 70, 74, 76, 77, 78, 79], "forward": [17, 33, 34, 35, 67, 70, 79], "foster": [73, 76, 77, 78], "found": [6, 8, 10, 14, 18, 19, 32, 46, 54, 55, 57, 58, 64, 66, 68, 69, 71], "foundat": [1, 24, 26, 29, 36, 37, 52, 56, 57, 59, 61, 63, 65, 68, 72, 76, 77, 78], "fp_rate": 69, "framework": [0, 3, 6, 8, 9, 23, 27, 36, 39, 45, 49, 50, 54, 64, 67, 68, 71, 74, 75, 80], "framework_funct": 29, "framework_id": [26, 56], "fraud": [76, 77], "fraudul": 77, "free": [6, 20, 28, 66, 71], "frequenc": [4, 40, 42, 45, 46, 48, 50, 76, 77, 78, 79], "frequent": [4, 11, 12, 19, 35, 41, 65, 66, 69, 75, 77], "fresh": 35, "fresh_valu": 35, "fridai": 78, "friendli": [3, 74], "from": [0, 1, 3, 5, 6, 7, 8, 9, 10, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 27, 30, 31, 32, 33, 34, 35, 37, 38, 39, 40, 41, 43, 44, 45, 48, 51, 53, 54, 55, 56, 57, 58, 60, 61, 62, 63, 64, 65, 66, 68, 69, 70, 71, 72, 74, 75, 79, 80], "front": 77, "frontend": [0, 5, 16, 19, 24, 26, 28, 36, 38, 67], "frontlin": 79, "fsgroup": [17, 51], "fssl": 14, "full": [3, 4, 11, 12, 14, 19, 25, 26, 29, 36, 37, 38, 43, 44, 45, 46, 52, 54, 56, 64, 67, 68, 70, 73, 77, 79], "full_nam": 53, "full_network": 42, "fulli": [15, 42, 48, 50, 52], "func": [18, 35, 54, 56, 57, 58], "function": [2, 3, 4, 8, 17, 18, 19, 21, 23, 24, 25, 26, 30, 35, 36, 40, 41, 43, 45, 47, 50, 54, 55, 61, 62, 65, 67, 68, 70, 73, 74, 76, 77, 78, 79, 80], "functool": [18, 35], "fundament": [55, 72, 73, 74, 76, 77, 78, 79], "further": [40, 79], "fusion": [76, 79], "futur": [25, 27, 56, 71, 73, 76, 77, 78, 79], "fuzz": 77, "g": [8, 9, 14, 18, 19, 26, 30, 56, 71], "g0016": 75, "g0028": 75, "ga": 77, "gain": [6, 71, 77, 79], "game": 77, "gang": 76, "gap": [0, 24, 25, 41, 42, 45, 46, 50, 62, 71, 72, 74, 76, 77, 78, 79], "gapanalysi": 26, "garbag": [34, 35, 41], "gate": [0, 18, 52], "gate_statu": 54, "gatewai": [29, 42, 47, 78], "gather": [25, 35, 47, 65, 74, 76, 77, 78, 79], "gaug": [35, 54, 55], "gb": [16, 34], "gc": 35, "gcfa": 79, "gcih": 79, "gcloud": 30, "gcm": [11, 13, 29, 42], "gcp": [0, 1, 5, 13, 23, 27, 28, 38, 47, 63, 67, 68, 74, 76, 78, 79], "gcp_config": 30, "gcp_default_region": 13, "gcp_enable_comput": 13, "gcp_enable_iam": 13, "gcp_enable_network": 13, "gcp_enable_storag": 13, "gcp_engin": 30, "gcp_poll_interv": 13, "gcp_project_id": 13, "gcp_result": 30, "gdpr": [0, 6, 23, 27, 39, 41, 43, 44, 45, 46, 48, 52, 63, 67, 68, 71, 72, 78, 79, 80], "gdpr_complianc": 12, "gdpr_consent_record": 12, "gdpr_data_subject_request": 12, "gdpr_impact": 80, "gdpr_report": 68, "gdprcomplianceservic": 42, "gen_random_uuid": [56, 61], "gener": [0, 1, 3, 7, 8, 12, 13, 16, 19, 21, 23, 24, 25, 27, 29, 32, 33, 41, 42, 43, 44, 48, 51, 52, 53, 54, 55, 57, 58, 61, 62, 63, 64, 66, 68, 69, 70, 71, 72, 75, 76, 77, 78, 79], "generate_asset_report": 68, "generate_basic_report": 79, "generate_collaboration_report": 69, "generate_compliance_report": 69, "generate_detection_rul": 69, "generate_executive_report": 79, "generate_executive_summari": 54, "generate_gap_analysi": 26, "generate_improvement_recommend": 42, "generate_mitig": 80, "generate_navigator_lay": 75, "generate_playbook": 69, "generate_recommend": [46, 54], "generate_red_team_report": 69, "generate_report": [7, 53], "generate_review_id": 46, "generate_summari": 55, "generate_visu": 75, "generate_zero_trust_roadmap": 69, "genuin": 79, "geograph": [4, 15, 42, 70, 78, 79, 80], "geographic_region": 75, "geoloc": 41, "geopolit": [77, 78], "geospati": 79, "get": [4, 8, 11, 12, 13, 14, 16, 18, 26, 30, 31, 32, 33, 34, 35, 41, 43, 53, 54, 55, 57, 58, 59, 63, 64, 65], "get_active_job": 30, "get_alert": 69, "get_all_techniqu": 42, "get_asset": [18, 65], "get_asset_threat_predict": 57, "get_assets_by_classif": 69, "get_assets_by_typ": 18, "get_assets_by_type_orm": 18, "get_attack_analysi": 35, "get_attack_path": [35, 59], "get_by_id": [18, 65], "get_by_nam": 69, "get_cached_result": 59, "get_cas": 58, "get_compliance_scop": 69, "get_correlation_rul": 75, "get_critical_issu": 54, "get_current_us": [18, 20], "get_data_statu": 75, "get_database_password": 55, "get_db": [57, 58], "get_discovery_statist": 30, "get_edge_data": [35, 59], "get_event_context": 75, "get_event_loop": 59, "get_factor_scor": 69, "get_high_issu": 54, "get_incident_involv": 42, "get_incident_playbook": 42, "get_job_statu": 30, "get_kei": 42, "get_legal_basi": 42, "get_logg": 18, "get_mitre_attack_map": 59, "get_mitre_techniqu": 35, "get_or_set": 35, "get_phishing_test_result": 42, "get_remediation_r": 54, "get_response_team": 42, "get_review": 46, "get_role_permiss": 42, "get_security_postur": 54, "get_statu": 68, "get_techniqu": 65, "get_techniques_for_step": 69, "get_threat_actor": 42, "get_threat_actor_profil": 75, "get_total_scan": 54, "get_training_histori": 42, "get_training_recommend": 42, "get_us": 42, "get_user_rol": 42, "get_vulnerability_trend": 54, "getasset": 18, "getbucketloc": 30, "getderivedstatefromerror": 18, "getenv": 54, "getlogg": [19, 30, 54, 57, 58], "gh": [3, 19, 21], "giac": 79, "gin": [35, 61], "git": [2, 14, 15, 19, 20, 28, 31, 38, 46, 54, 62, 67], "githook": 20, "github": [0, 1, 2, 3, 8, 16, 18, 19, 20, 21, 27, 28, 38, 47, 51, 53, 54, 62, 64, 65, 66, 67], "github_token": 3, "gitlab": 54, "give": [12, 19], "given_at": 12, "gke": 30, "glass": 4, "global": [15, 29, 34, 73, 76, 77, 78, 79], "global_architecture_author": 78, "gnfa": 79, "go": [8, 19, 38, 62, 63, 65, 67, 71, 79], "goal": [42, 76, 77, 79], "golden": 77, "good": [17, 18, 19, 21, 38, 46, 47, 51, 65], "googl": [5, 13, 30, 38, 68, 70], "google_application_credenti": [13, 28, 68], "govern": [22, 23, 27, 40, 42, 70, 74, 75, 76, 77, 79, 80], "governance_and_risk_manag": 42, "grace": [63, 67], "gracefulli": [8, 21, 65], "grade": [0, 1, 9, 10, 15, 22, 23, 24, 25, 26, 27, 29, 37, 44, 59, 62, 63, 64, 75, 77, 79, 80], "gradual": [11, 15, 36, 38, 43, 45, 68, 71, 79], "graduat": 79, "grafana": [0, 16, 17, 24, 25, 26, 31, 34, 36, 37, 40, 43], "grafana_en": 14, "grafana_password": 33, "grain": [7, 67, 70], "grant": [4, 11, 16], "granular": [12, 22, 29, 37, 62, 63, 70, 75], "graph": [3, 8, 13, 15, 18, 19, 20, 21, 24, 25, 26, 27, 28, 30, 34, 35, 36, 38, 65, 66, 67, 69, 71, 74, 76, 77, 79], "graph_data": 26, "graph_engin": 59, "graph_servic": 19, "graph_statist": 6, "graphql": [30, 62, 68], "graphservic": 19, "grc": [27, 72], "green": [29, 38, 58, 70], "grep": [4, 12, 17, 28, 32, 33, 38, 41, 43, 66, 67], "grid": 77, "griffon": 80, "group": [6, 9, 11, 14, 15, 16, 17, 29, 30, 31, 32, 33, 35, 41, 51, 54, 68, 70, 71, 75, 76, 77, 79, 80], "group1_uniqu": 75, "group2_uniqu": 75, "group_id": 75, "group_not_found": 9, "grow": [19, 35, 79], "growth": [32, 33, 34, 52, 63, 70, 73, 74, 75, 78, 79], "growth_rat": 75, "gserviceaccount": 30, "gt": 32, "guarante": 4, "guardian": [72, 73], "guest_network": 42, "gui": 2, "guid": [7, 9, 10, 21, 24, 25, 26, 29, 35, 37, 44, 51, 62, 63, 64, 66, 67, 68, 69], "guidanc": [0, 1, 3, 14, 22, 23, 27, 32, 34, 43, 45, 46, 63, 64, 72, 74, 76, 77, 78, 79], "guided_attack_simul": 77, "guidelin": [0, 2, 3, 18, 21, 22, 27, 34, 37, 51, 55, 67, 77, 78], "gunicorn": 35, "gv": 40, "gz": [16, 31, 33, 43, 66], "gzip": 35, "gzip_min_length": 35, "gzip_typ": 35, "gzip_vari": 35, "h": [5, 6, 9, 10, 16, 17, 20, 28, 30, 31, 32, 33, 41, 43, 66, 67, 75, 80], "ha": [1, 15, 18, 19, 24, 29, 37, 52, 54, 68, 71, 72, 73, 76, 77, 78, 79], "hack": [22, 27, 77], "hacker": [53, 77], "hall": 47, "hand": [0, 3, 42, 51, 78], "handl": [0, 3, 4, 8, 12, 13, 18, 19, 21, 22, 24, 25, 26, 34, 35, 39, 40, 42, 43, 44, 46, 53, 58, 62, 63, 65, 67, 70, 77, 79], "handle_security_incid": 42, "handle_thehive_case_upd": 58, "handleanalysi": 18, "handler": [4, 42, 79], "handov": [23, 79], "handshak": 66, "happen": 19, "happi": 2, "harass": 47, "hard": 66, "hard_delet": 5, "hardcod": [13, 48, 50, 51, 52, 54, 55], "hardcoded_bind_all_interfac": [54, 55], "hardcoded_password_default": [54, 55], "hardcoded_password_funcarg": [54, 55], "hardcoded_password_str": [54, 55], "hardcoded_tmp_directori": 54, "harden": [22, 23, 24, 25, 36, 37, 44, 45, 46, 52, 70, 76, 78, 79], "hardwar": [11, 20, 68, 70, 76, 77], "harmon": 78, "harvest": 77, "has_edg": 35, "has_lawful_basi": 42, "has_mor": 8, "has_next": 5, "has_prev": 5, "haserror": 18, "hash": [22, 35, 37, 41, 52, 54, 58, 75, 77, 79], "hash_algorithm": 41, "hashicorp": 16, "hashlib": [49, 54], "hat": 79, "have": [0, 4, 21, 27, 29, 38, 49, 70, 71, 75, 79, 80], "head": [1, 14, 17, 20, 31, 32, 33, 34], "header": [3, 5, 6, 7, 8, 10, 11, 21, 32, 47, 53, 54, 58, 64, 66, 67, 70, 71], "headless": 64, "heal": [25, 36, 63], "health": [0, 11, 13, 14, 15, 17, 24, 25, 26, 31, 35, 36, 38, 43, 53, 62, 63, 64, 70, 72, 74, 77, 78, 79, 80], "health_check_databas": 13, "health_check_en": 13, "health_check_external_api": 13, "health_check_interv": 13, "health_check_neo4j": 13, "health_check_redi": 13, "health_check_timeout": 13, "healthcar": [10, 23, 41, 63, 72, 77, 78, 80], "healthcheck": 35, "heap": [34, 35], "heat": [9, 74, 75, 78, 79], "heavi": 41, "heavili": 61, "hec": 68, "helm": 16, "help": [21, 27, 47, 55, 69, 76, 77, 79], "helper": 65, "here": [28, 38, 67, 71], "hesit": 66, "hexdigest": 49, "hhmmss": 43, "hidden": 77, "hidden_layer_s": 57, "hierarch": [3, 4, 13, 78], "hierarchi": [0, 1, 3, 70], "high": [4, 5, 6, 8, 12, 19, 21, 22, 25, 27, 28, 29, 30, 32, 34, 35, 37, 38, 39, 40, 41, 42, 45, 46, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 66, 68, 69, 70, 71, 74, 75, 77, 78, 79, 80], "high_degree_nod": 35, "high_issu": 54, "high_risk_asset": 57, "high_risk_trigg": 12, "high_vuln": 32, "high_vulns_count": 57, "higher": [18, 28, 57, 59, 71, 79], "highest": [13, 47, 69, 73, 76, 77], "highli": 77, "highlight": [0, 3, 19, 37, 62], "hijack": [47, 77], "hint": 19, "hipaa": [23, 27, 41, 63, 71, 72, 78, 79, 80], "hipaa_impact": 80, "histogram": [35, 53, 55], "histor": [4, 25, 41, 62, 78, 79, 80], "histori": [1, 12, 17, 47, 62, 77, 79], "historian": 77, "historical_weight": 4, "hit": [32, 33, 34, 59, 62], "hit_perc": 61, "hit_rat": 33, "hmac": 41, "hmi": 77, "hoc": [42, 46], "hold": [19, 45, 72, 79], "hole": 77, "homepag": 3, "hook": [0, 14, 18, 20, 21, 49, 64], "hookspath": 20, "hop": [6, 18, 38, 63, 67, 71, 74, 77, 79, 80], "horizont": [0, 15, 17, 25, 33, 41, 62, 63, 79], "horizontalpodautoscal": 34, "hospit": [77, 80], "host": [1, 2, 13, 14, 16, 17, 20, 24, 25, 28, 31, 34, 35, 53, 54, 55, 61, 66, 79], "host_id": 42, "hosted_zone_id": [16, 17, 31], "hostnam": [5, 16], "hot_retention_dai": 41, "hotfix": [21, 50], "hotlin": [76, 77, 78, 79], "hotspot": [48, 50, 55], "hour": [9, 10, 12, 13, 15, 29, 31, 33, 34, 35, 40, 42, 45, 47, 48, 50, 51, 52, 54, 57, 59, 68, 69, 71, 74, 78, 79, 80], "hour_of_dai": 57, "hourli": [43, 68, 80], "hourly_increment": 68, "housekeep": 70, "how": [36, 51, 53, 65, 71, 74, 79], "hpa": [32, 34], "hr": [70, 79], "hs256": [13, 28], "hsm": [11, 42, 49], "hst": 11, "html": [1, 3, 20, 41, 51, 53, 55, 64, 65], "htmlcov": [64, 65], "htop": 2, "http": [2, 3, 5, 6, 7, 9, 10, 11, 13, 14, 17, 19, 20, 24, 25, 28, 30, 31, 32, 33, 34, 35, 38, 43, 51, 53, 54, 64, 65, 66, 67, 68, 71, 74, 75, 79, 80], "http_request_duration_second": 35, "http_requests_tot": [33, 35], "httperror": 58, "httpexcept": [51, 57, 58], "httpie": 20, "httponli": 7, "httpuser": 64, "httpx": [24, 26, 58], "hub": [72, 76, 77], "human": [73, 76, 77, 78, 79], "humint": [76, 77], "hunt": [11, 23, 25, 27, 36, 39, 42, 52, 63, 76, 77, 78, 79], "hunter": [76, 79], "hvt": 77, "hybrid": [56, 62, 68, 77, 78], "hybrid_architecture_design": 78, "hyperparamet": 57, "hypervisor": 77, "hypothes": 79, "hypothesi": [27, 76, 79], "i": [1, 2, 5, 6, 8, 10, 12, 13, 15, 16, 17, 18, 19, 20, 21, 22, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 37, 38, 39, 40, 42, 43, 44, 45, 46, 48, 49, 50, 52, 53, 54, 55, 57, 58, 59, 61, 63, 64, 65, 66, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80], "iac": [15, 77, 78], "iam": [16, 30, 66, 67, 68, 70, 76, 77, 78, 79], "iast": [39, 48, 52], "iat": 7, "ibm": 30, "ic": [9, 74, 75, 77], "icon": 38, "ics_count": 75, "id": [0, 2, 5, 6, 7, 8, 9, 12, 13, 16, 17, 18, 19, 20, 26, 28, 30, 31, 35, 42, 45, 46, 50, 53, 54, 55, 56, 57, 58, 59, 61, 62, 64, 65, 66, 67, 68, 69, 77, 78, 79], "idea": [20, 74], "ident": [12, 13, 15, 16, 23, 28, 29, 30, 40, 42, 46, 48, 52, 66, 69, 76, 77, 78, 79], "identif": [4, 9, 10, 11, 25, 26, 39, 40, 41, 42, 43, 45, 50, 51, 55, 62, 63, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80], "identifi": [3, 5, 8, 9, 16, 17, 19, 26, 31, 33, 34, 35, 36, 43, 45, 47, 49, 50, 51, 53, 55, 56, 63, 64, 67, 69, 70, 71, 74, 75, 76, 77, 78, 79, 80], "identify_affected_asset": 42, "identify_risk_pattern": 26, "identify_trust_boundari": 69, "identity_provid": 61, "identity_verif": 42, "ideologi": 80, "ids_ip": 61, "idx": 57, "idx_asset_relationships_act": 61, "idx_asset_relationships_sourc": 61, "idx_asset_relationships_target": 61, "idx_asset_relationships_typ": 61, "idx_assets_config_gin": 61, "idx_assets_created_at": [35, 61], "idx_assets_discovery_composit": 35, "idx_assets_environment_statu": 61, "idx_assets_ip_address": 35, "idx_assets_properties_gin": 61, "idx_assets_risk_scor": 61, "idx_assets_soft_delet": 61, "idx_assets_typ": 17, "idx_assets_type_act": 35, "idx_assets_type_env_statu": 61, "idx_assets_type_provid": 61, "idx_attack_paths_created_at": 61, "idx_attack_paths_crit": 61, "idx_attack_paths_nodes_gin": 61, "idx_attack_paths_path_typ": 61, "idx_attack_paths_risk_scor": [18, 35, 61], "idx_attack_paths_source_asset": 18, "idx_attack_paths_source_risk_crit": 61, "idx_attack_paths_source_target": [35, 61], "idx_attack_paths_techniques_gin": 61, "idx_attack_scenarios_entry_points_gin": 61, "idx_attack_scenarios_objectives_gin": 61, "idx_audit_events_event_typ": 41, "idx_audit_events_resourc": 41, "idx_audit_events_timestamp": 41, "idx_audit_events_user_id": 41, "idx_compliance_assessments_assessment_d": 56, "idx_compliance_assessments_deleted_at": 56, "idx_compliance_assessments_framework_id": 56, "idx_compliance_assessments_statu": 56, "idx_compliance_control_assessments_assessment_id": 56, "idx_compliance_control_assessments_control_id": 56, "idx_compliance_control_assessments_deleted_at": 56, "idx_compliance_control_assessments_risk_r": 56, "idx_compliance_control_assessments_statu": 56, "idx_compliance_control_mappings_deleted_at": 56, "idx_compliance_control_mappings_mapping_typ": 56, "idx_compliance_control_mappings_source_control_id": 56, "idx_compliance_control_mappings_target_control_id": 56, "idx_compliance_controls_automation_level": 56, "idx_compliance_controls_control_id": 56, "idx_compliance_controls_deleted_at": 56, "idx_compliance_controls_domain_id": 56, "idx_compliance_controls_framework_id": 56, "idx_compliance_domains_deleted_at": 56, "idx_compliance_domains_framework_id": 56, "idx_compliance_frameworks_deleted_at": 56, "idx_compliance_frameworks_nam": 56, "idx_compliance_frameworks_statu": 56, "idx_mitre_techniques_search": 35, "idx_mitre_techniques_tact": 35, "idx_relationships_source_type_act": 61, "idx_scan": [32, 35], "idx_tup_fetch": [32, 35], "idx_tup_read": [32, 35], "iga": 78, "ignor": [14, 18, 35, 46, 53, 54, 58, 66], "ii": [22, 37, 39, 44, 48, 52, 63, 67, 78], "iii": [72, 78], "illustr": 60, "im": 40, "imag": [3, 7, 21, 22, 24, 30, 32, 35, 38, 39, 45, 48, 50, 51, 53, 54, 64, 66, 67, 77, 78, 79], "image_vulner": 68, "img": [53, 64], "immedi": [0, 1, 12, 25, 28, 30, 33, 40, 45, 46, 48, 50, 66, 67, 69, 71, 79], "immut": [22, 35, 37, 41], "impact": [6, 8, 11, 19, 21, 22, 26, 27, 29, 33, 35, 37, 40, 43, 45, 47, 48, 58, 59, 62, 63, 67, 69, 71, 72, 73, 74, 76, 77, 78, 79], "impact_area": 51, "impact_by_degre": [6, 59], "impact_scor": [6, 61], "impair": 43, "imperson": [46, 77], "implant": [76, 77], "implement": [1, 2, 3, 5, 7, 8, 13, 14, 18, 19, 20, 21, 22, 23, 27, 30, 33, 34, 35, 37, 38, 44, 45, 46, 48, 50, 51, 54, 56, 59, 61, 62, 63, 64, 68, 69, 70, 71, 72, 73, 74, 77, 79, 80], "implementation_dai": 80, "implementation_guid": 56, "implementation_phas": 69, "implementation_scor": 46, "implementation_timelin": 80, "implic": [18, 19, 21, 45, 55, 71, 73, 74, 77, 78, 79, 80], "implicit": 11, "import": [2, 3, 6, 7, 8, 14, 16, 17, 18, 19, 20, 21, 30, 32, 33, 35, 38, 46, 47, 53, 54, 55, 56, 57, 58, 64, 65, 66, 67, 68, 69, 70, 71, 75, 79, 80], "improv": [1, 8, 18, 19, 21, 22, 23, 24, 25, 27, 30, 34, 35, 36, 37, 40, 41, 43, 45, 54, 56, 59, 62, 63, 64, 67, 69, 71, 72, 73, 74, 76, 77, 78, 79, 80], "improvement_program_manag": 76, "improvement_roadmap": 42, "in_progress": [46, 56], "inaccess": 30, "inact": 65, "inadequ": 46, "inbound_connect": 57, "inc": [35, 45, 53], "incent": 73, "incid": [1, 3, 4, 7, 8, 10, 11, 12, 13, 14, 15, 22, 23, 24, 27, 29, 32, 33, 36, 37, 38, 39, 41, 44, 46, 47, 50, 51, 56, 62, 63, 67, 70, 72, 73, 76, 77, 78], "incident respons": 79, "incident_classif": 42, "incident_data": 26, "incident_detail": 42, "incident_frequ": 42, "incident_id": [26, 32, 33, 42, 58], "incident_involv": 42, "incident_nam": 33, "incident_respons": [4, 42], "incident_response_polici": 42, "incident_response_servic": 69, "incident_scenario": 69, "incidentcasemapp": 58, "incidentrespons": 42, "incidentresponseengin": 26, "incidentresponseservic": [42, 69], "incidentscenario": 69, "includ": [0, 2, 3, 5, 6, 7, 8, 10, 13, 14, 16, 18, 19, 20, 21, 22, 23, 27, 31, 32, 33, 38, 41, 43, 47, 54, 55, 59, 61, 62, 63, 64, 66, 67, 71, 72, 73, 75, 77, 79, 80], "include_access_control": 69, "include_application_boundari": 69, "include_attack_path": [5, 8], "include_baselin": 75, "include_blast_radiu": 8, "include_business_impact": 69, "include_campaign": 75, "include_communication_plan": 69, "include_containment_step": 69, "include_control": 68, "include_data_flow": 68, "include_evid": 69, "include_financial_impact": 69, "include_group": 75, "include_histor": 75, "include_identity_boundari": 69, "include_insider_threat": 80, "include_lateral_mov": 69, "include_legend": 75, "include_mitr": [18, 69], "include_monitor": 69, "include_network_seg": 69, "include_network_segment": [68, 69], "include_ongo": 75, "include_predict": 75, "include_privilege_escal": 69, "include_recommend": 69, "include_recovery_procedur": 69, "include_recovery_tim": 69, "include_relationship": 5, "include_stop": 5, "include_sub_techniqu": 75, "include_techniqu": [69, 75], "include_technology_recommend": 69, "include_tool": 69, "include_vulner": 5, "includemitr": 18, "inclus": 19, "incom": [26, 79], "incompat": 40, "incomplet": [37, 65], "inconsist": [17, 42, 46, 66], "incorpor": [0, 3, 40, 46, 54, 67, 78], "incorrect": [13, 46], "increas": [4, 6, 17, 18, 23, 29, 30, 33, 38, 50, 66, 67, 68, 71, 76, 77, 79, 80], "increment": [21, 36, 54, 68, 75, 80], "independ": [47, 62, 65, 76, 77, 78, 79], "index": [1, 3, 4, 12, 13, 15, 17, 18, 25, 27, 32, 33, 34, 35, 41, 56, 62, 63, 64, 65, 66, 67, 70], "indexnam": [32, 35], "indic": [3, 4, 9, 17, 39, 41, 43, 58, 64, 65, 69, 72, 73, 74, 75, 76, 77, 78, 79], "indicator_typ": 58, "indirect": [78, 79, 80], "indirect_cost": 80, "individu": [2, 38, 41, 42, 59, 67, 79], "industri": [1, 27, 39, 41, 42, 44, 45, 46, 47, 48, 50, 51, 63, 70, 72, 73, 75, 76, 77, 78, 79], "industrial_control": 61, "industry_collaboration_access": [72, 76, 78], "industry_represent": 73, "ineffect": 46, "ineffici": 35, "inet": [41, 61], "infect": 45, "infer": 25, "influenc": 78, "info": [3, 13, 14, 16, 17, 18, 24, 28, 30, 32, 33, 35, 43, 53, 57, 58, 66], "infograph": 78, "inform": [0, 5, 7, 8, 10, 12, 19, 27, 28, 30, 38, 39, 41, 42, 43, 45, 46, 48, 51, 53, 55, 56, 58, 62, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79], "information_schema": 31, "information_security_control": 29, "information_security_polici": 42, "infrastructur": [3, 6, 23, 25, 27, 28, 30, 31, 38, 40, 41, 44, 45, 46, 47, 48, 50, 52, 54, 56, 57, 62, 63, 68, 69, 70, 71, 74, 76, 77, 78, 79, 80], "infrastructure_secur": 54, "ingest": [25, 70, 75, 79], "ingress": [17, 31, 32, 33], "inherit": [4, 61, 70], "ini": [51, 64, 65], "init": [14, 16, 55], "initi": [0, 1, 2, 6, 12, 14, 20, 23, 25, 27, 33, 36, 42, 45, 47, 53, 55, 58, 59, 60, 68, 69, 71, 72, 73, 74, 75, 76, 77, 78], "initial_access": [6, 59], "initial_compromis": 69, "initial_cpu": 53, "initial_memori": 53, "initial_s": [34, 35], "initial_techniqu": 69, "inject": [18, 19, 21, 39, 44, 46, 47, 51, 53, 54, 55, 64, 77, 80], "inlin": 19, "innov": [23, 52, 63, 74, 76, 77, 78, 79], "innovation_program_access": 78, "input": [5, 8, 18, 19, 21, 39, 40, 44, 45, 46, 52, 55, 64, 65], "inquiri": [44, 52], "insecur": [48, 55], "insert": [51, 53, 76, 77], "insid": [45, 63, 71, 76, 77, 78, 79, 80], "insider_threat": [10, 61], "insight": [4, 24, 25, 36, 41, 77], "inspect": 78, "inspir": [22, 29, 37, 41, 73, 77], "instal": [0, 1, 2, 3, 8, 14, 18, 19, 21, 27, 35, 45, 53, 54, 55, 62, 64, 65, 68, 79], "instanc": [8, 13, 14, 16, 17, 18, 19, 24, 28, 30, 31, 33, 38, 53, 65, 66, 68, 71, 75, 80], "instance_class": 15, "instance_typ": 15, "instanceid": 65, "instanceof": [18, 19], "instancetyp": 65, "instant": [4, 15, 79], "instead": [0, 8, 13], "institut": [76, 77, 78], "instruct": [1, 2, 3, 14, 16, 19, 67], "instrument": 77, "insuffici": [5, 8, 17, 18, 66, 67], "insufficient_permiss": 10, "insur": [45, 72, 73, 77, 78, 79, 80], "int": [18, 19, 26, 35, 53, 57, 58, 59], "integ": [5, 6, 8, 41, 56, 57, 58, 61], "integr": [1, 2, 6, 11, 15, 16, 18, 19, 20, 23, 27, 29, 31, 39, 43, 44, 45, 46, 47, 49, 52, 61, 71, 73, 76, 77, 78, 79], "integrate_threat_intellig": 80, "integratedtermin": 20, "integrity_viol": 41, "intel": 50, "intellectu": [40, 73, 76, 77, 78, 79], "intellig": [1, 4, 6, 11, 15, 23, 24, 26, 27, 29, 35, 37, 39, 40, 41, 45, 47, 48, 50, 51, 52, 54, 59, 60, 62, 67, 69, 70, 71, 72, 73, 76, 77, 78], "intelligentcach": 59, "intend": [19, 21], "intens": [33, 34, 71, 79], "intent": [65, 78], "inter": 11, "interact": [0, 5, 8, 19, 22, 23, 24, 26, 36, 37, 38, 39, 48, 50, 52, 53, 62, 65, 67, 77, 78, 79], "interconnect": 78, "interdepend": [72, 73, 78], "interest": 79, "interfac": [13, 18, 19, 26, 29, 40, 47, 62, 68, 70, 72, 73, 74, 76, 77, 78, 79], "intern": [0, 1, 3, 6, 8, 18, 38, 44, 46, 47, 50, 70, 71, 72, 76, 77, 78, 79, 80], "internal_audit": 42, "internal_controls_compromis": 80, "internal_extern": 42, "internal_factor": 42, "internal_firewal": 42, "internal_network": 42, "internet": [20, 28, 38, 62, 71, 73, 75, 78], "internet_gatewai": [6, 8, 71], "interoper": [76, 78], "interpret": [69, 72, 75, 80], "intersect": 78, "interv": [22, 33, 35, 37, 53, 54, 61, 64, 68, 74, 77], "interview": [73, 79], "introduc": [19, 21, 71], "introduct": 47, "intrus": [39, 50, 77, 78], "intrusion_detect": 42, "intset": 35, "invalid": [5, 6, 7, 8, 9, 13, 18, 19, 30, 35, 38, 50, 51, 53, 65, 66], "invalid_asset": 19, "invalid_email": 65, "invalid_id": 65, "invalid_paramet": 10, "invalid_sourc": 65, "invalid_threat_actor": 10, "invalid_timefram": 9, "invalid_typ": 65, "invalidate_pattern": 35, "invalidtransitionerror": 65, "inventori": [0, 5, 8, 38, 40, 56, 61, 63, 66, 67, 69, 74, 76, 77, 78, 79], "invers": 18, "invert": 57, "invest": [23, 63, 65, 72, 74, 76, 77, 78, 79, 80], "investig": [4, 7, 11, 24, 29, 32, 33, 40, 41, 45, 47, 58, 72, 74, 76, 78], "investigate_incid": [7, 79], "investor": [73, 74], "invit": 67, "invok": 26, "involv": [46, 74, 79, 80], "io": [16, 17, 31, 32, 33, 77], "ioc": [8, 13, 24, 38, 39, 58, 63, 70, 74, 76, 79], "ioc_auto_correl": 13, "ioc_confidence_threshold": 13, "ioc_correl": 80, "ioc_retention_dai": 13, "iot": [73, 77, 78], "iot_devic": 61, "ip": [5, 13, 18, 30, 33, 34, 35, 40, 41, 42, 43, 50, 58, 65, 75, 77, 78, 79], "ip_address": [5, 18, 35, 41, 58, 61, 64, 65], "ipaddress": [5, 18], "ipdb": 20, "iptabl": [45, 66], "ipv4": [64, 65], "ir_playbook": 69, "ir_servic": 69, "is_act": [5, 18, 35, 57, 58, 61, 64, 65, 66], "is_complet": 68, "is_compromis": 57, "is_connect": 6, "is_data_accur": 42, "is_delet": 61, "is_new": 5, "is_purpose_compat": 42, "is_retention_period_exceed": 42, "is_superus": 65, "is_train": 57, "is_valid": 65, "is_valid_asset_typ": 51, "is_verifi": 61, "isaca": 79, "isanalyz": [18, 19], "isc": 79, "ism": [42, 72, 78], "iso": [22, 23, 24, 25, 26, 27, 31, 37, 39, 41, 44, 46, 48, 52, 56, 63, 67, 69, 72, 78], "iso27001": [25, 29, 36, 39, 41, 74], "iso27001_": 42, "iso_proba": 57, "iso_scor": 57, "isoformat": [54, 57, 58], "isol": [15, 16, 19, 24, 25, 26, 36, 39, 42, 43, 45, 53, 57, 64, 65, 66, 69, 71, 74, 76, 77, 78, 79], "isolate_affected_system": 42, "isolated_asset": 66, "isolation_forest": [26, 57], "isolation_forest_scor": 57, "isolationforest": [26, 57], "isort": [18, 20, 21, 64], "iss": 7, "issu": [1, 3, 8, 13, 14, 18, 21, 22, 27, 29, 32, 33, 35, 40, 43, 44, 45, 46, 47, 48, 50, 51, 52, 53, 54, 55, 62, 64, 67, 72, 74, 76, 77, 78, 79], "issue_confid": 55, "issue_sever": 55, "issue_text": 55, "istio": 11, "item": [4, 5, 8, 16, 35, 38, 43, 51, 54, 55, 57, 66, 69], "iter": [2, 34, 35, 64, 65, 74], "itgc": 72, "its": [29, 53], "ivborw0kggoaaaansuheugaa": 7, "j": [2, 3, 14, 20, 24, 25, 28, 30, 36, 45, 50, 51, 53, 54, 67], "javascript": [1, 8, 20, 35, 48, 50, 53, 62, 63, 64, 66], "jbswy3dpehpk3pxp": 7, "jewel": [69, 76, 77, 78], "jinja2": 54, "jira": 33, "jira_token": 33, "jit": 4, "jitter": 35, "job": [3, 4, 5, 14, 16, 17, 25, 26, 53, 55, 64, 65, 66, 67, 68, 70, 77, 79], "job_id": [5, 30, 64], "job_nam": 34, "job_statu": 30, "joblib": [24, 57], "john": [7, 21, 67, 75], "join": [3, 6, 8, 19, 27, 49, 53, 58, 66, 67, 71, 75], "joint": [63, 71, 73, 74, 77], "jop": 77, "journalctl": [17, 43], "journei": 52, "jq": [16, 33, 51], "json": [5, 6, 7, 8, 9, 10, 13, 16, 17, 18, 20, 24, 26, 28, 30, 31, 32, 33, 34, 35, 41, 46, 48, 49, 51, 52, 53, 54, 55, 58, 59, 63, 64, 65, 67, 68, 71, 75, 79, 80], "jsonb": [12, 26, 41, 57, 61, 63], "jsondecodeerror": 49, "jsonpath": [16, 17], "jsx": [18, 19], "juli": 49, "june": [44, 48, 50], "junior": [21, 72, 76, 77, 78, 79], "jurisdict": 78, "just": [21, 29, 42, 78], "just_in_tim": 29, "justif": [4, 40, 55, 70, 73], "justifi": 4, "jwt": [3, 5, 6, 8, 13, 14, 16, 19, 20, 21, 28, 55, 63, 64, 66, 71], "jwt_algorithm": [13, 28], "jwt_audienc": 13, "jwt_expire_minut": [13, 28, 66], "jwt_issuer": 13, "jwt_refresh_expire_dai": 13, "jwt_secret_kei": [13, 14, 20, 28, 66], "k": [11, 30, 35, 64, 65], "k8": [14, 16, 17, 31, 32, 33, 54], "keep": [0, 1, 2, 4, 12, 13, 19, 20, 21, 32, 47, 53, 54, 64, 69, 71, 74, 79], "keepal": [34, 35], "keepalive_timeout": [13, 35], "kei": [11, 12, 13, 14, 16, 18, 19, 20, 24, 27, 28, 29, 30, 31, 33, 35, 37, 38, 39, 41, 43, 44, 46, 47, 49, 51, 56, 57, 59, 61, 62, 64, 65, 66, 67, 68, 72, 74, 75, 76, 77, 78, 79], "kerbero": 77, "kerberoast": 77, "kernel": 77, "key_manag": [29, 42, 61], "key_part": 49, "keymanagementservic": 42, "keyout": 66, "keyspace_hit": [32, 33], "keyspace_miss": [32, 33], "keyword": [1, 75], "kill": [2, 20, 28, 33, 66, 75], "kind": [14, 19, 31, 34, 51, 68], "kit": 77, "kiterunn": 68, "kloc": [48, 50], "km": [29, 68], "know": [4, 40, 42], "knowledg": [3, 21, 23, 46, 67, 70, 73, 76, 77, 78, 79], "known": [10, 17, 41, 50, 68, 75, 79, 80], "kpi": [23, 25, 27, 62, 72, 73, 76, 78, 79], "kube": [16, 17, 33], "kubeconfig": [14, 16], "kubectl": [4, 11, 12, 14, 17, 31, 32, 33, 41, 43], "kubelet": 17, "kubernet": [0, 17, 22, 24, 25, 27, 29, 30, 31, 34, 37, 54, 62, 68, 76, 77], "kubernetes_clust": 15, "kubernetes_pod": 61, "kwarg": [18, 35], "l": [14, 16, 30, 31, 32, 33, 45, 53, 55, 65, 66], "l0": 35, "l1": [34, 35], "l1_cach": 35, "l1_max_siz": 35, "l1_ttl": 35, "l2": [34, 35], "l3": 34, "la": 53, "label": [3, 17, 21, 24, 25, 35, 53, 54, 62, 78], "labelencod": 57, "laboratori": [76, 77], "lack": [10, 46], "lambda": [30, 35, 42, 57, 64, 65, 68, 69], "lambda_funct": 61, "land": [77, 79], "landscap": [39, 44, 46, 48, 73, 74, 75, 76, 77, 78, 79], "languag": [0, 1, 3, 8, 12, 19, 47, 48, 50, 54, 55, 61, 64, 76, 78], "language_vers": [18, 54], "larg": [5, 6, 8, 12, 15, 16, 22, 28, 34, 37, 38, 47, 62, 64, 67, 68, 71, 75, 76, 77, 78, 79], "large_scale_process": 12, "larger": 38, "last": [17, 20, 28, 32, 33, 47, 66, 67, 73, 75], "last_act": 42, "last_analysis_tim": 6, "last_assess": 56, "last_login": [7, 61], "last_nam": [7, 61, 64, 65], "last_patch": 5, "last_seen": [5, 35], "last_sync": 58, "last_test_d": 58, "last_test_statu": 58, "last_upd": 75, "lasttimestamp": [16, 17, 33], "latenc": [15, 25, 29, 41, 66, 67, 70, 78], "later": [6, 59, 63, 69, 71, 76, 77, 79], "lateral_mov": [6, 61, 69], "latest": [2, 3, 11, 14, 16, 24, 27, 28, 31, 32, 38, 44, 51, 52, 53, 54, 55, 57, 64, 65, 79], "latest_backup": 31, "latest_snapshot": 31, "latexpdf": 3, "launch": 20, "launder": 72, "law": [40, 42, 43, 45, 47, 51, 76, 77, 79], "lax": 13, "layer": [0, 3, 4, 11, 15, 18, 22, 29, 44, 48, 54, 59, 62, 64, 75, 77, 78, 79], "layer_id": 75, "layer_json": 75, "layer_nam": 75, "layout": 3, "lazaru": 77, "ldap": 70, "lead": [3, 17, 18, 23, 27, 33, 45, 49, 51, 52, 69, 73, 76, 77, 78, 79], "leader": [52, 73, 77, 78, 79], "leadership": [22, 27, 37, 45, 52, 69, 72, 76, 77, 79], "leadership_commit": 42, "leak": [21, 33, 34, 40], "leakag": [40, 47, 78, 79], "learn": [3, 11, 19, 21, 22, 23, 30, 36, 39, 40, 42, 43, 45, 46, 48, 52, 55, 63, 69, 71, 72, 73, 74, 75, 76, 77, 78, 79], "least": [11, 13, 15, 21, 27, 30, 35, 39, 42, 44, 46, 50, 51, 68, 70], "least_conn": [34, 35], "least_privileg": [29, 42], "least_privilege_config": 4, "ledger": [73, 78], "leef": 67, "left": [50, 54, 64, 66], "legaci": 78, "legal": [12, 22, 37, 40, 43, 70, 72, 73, 76, 77, 78, 79], "legal_basi": [12, 42], "legisl": 40, "legitim": [33, 40, 45, 47, 77, 79, 80], "legitimate_interest": 12, "len": [18, 19, 32, 35, 42, 46, 53, 55, 57, 58, 59, 64, 65, 68, 69, 71, 75], "length": [6, 18, 22, 35, 56, 59, 61, 71, 77], "less": 57, "lesson": [39, 40, 42, 43, 45, 46, 69, 71, 73, 74, 76, 77, 78, 79], "lessons_learn": 42, "let": 28, "letsencrypt": 28, "level": [3, 4, 11, 12, 13, 15, 17, 19, 23, 27, 34, 38, 41, 46, 51, 52, 53, 54, 55, 57, 59, 62, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80], "leverag": [6, 8, 24, 25, 26, 46, 59, 71, 72, 73, 75, 76, 77, 78, 79, 80], "liabil": 45, "liaison": 78, "lib": [16, 33], "librari": [12, 27, 36, 46, 51, 62, 78, 79], "licens": [46, 74], "life": 77, "lifecycl": [4, 5, 11, 12, 27, 39, 54, 62, 70, 73, 74, 76, 77, 78, 79], "lifetim": 66, "light": 3, "lightweight": 68, "like": [2, 19, 29, 35, 53, 71, 74, 79], "likelihood": [6, 10, 38, 40, 48, 59, 61, 63, 71, 74, 75, 77, 78, 79, 80], "likely_consequ": 12, "limit": [0, 3, 4, 12, 13, 17, 18, 20, 21, 25, 26, 27, 32, 33, 34, 35, 39, 40, 41, 42, 45, 46, 47, 50, 51, 53, 54, 62, 63, 65, 66, 67, 68, 69, 70, 71, 77, 78, 79], "line": [0, 3, 18, 23, 25, 27, 37, 38, 53, 55, 67, 77, 79], "line_length": 18, "line_numb": 55, "linear": [15, 34, 62], "lineno": 55, "link": [0, 3, 19, 75, 77, 79], "linkag": 41, "linkcheck": 3, "linkedin": 47, "lint": [2, 18, 19, 20, 21, 51, 62], "linter": [18, 21, 55, 64], "linux": [0, 14, 16, 20, 28, 59], "liskov": 18, "list": [8, 17, 18, 19, 26, 30, 31, 32, 35, 42, 47, 50, 51, 53, 54, 55, 57, 58, 59, 66, 77, 79], "listallmybucket": 30, "listen": [34, 35], "liter": 16, "litig": [45, 72], "live": [3, 4, 11, 14, 17, 18, 19, 21, 27, 28, 29, 38, 41, 48, 65, 76, 77, 79], "livenessprob": 17, "ll": [38, 64, 70], "lo": 16, "load": [0, 2, 13, 14, 15, 17, 20, 24, 25, 26, 28, 29, 30, 32, 33, 36, 37, 38, 40, 47, 49, 53, 55, 59, 62, 63, 64, 67, 68, 70, 71, 74, 76], "load_bal": 61, "load_balanc": 61, "load_bandit_result": 54, "load_safety_result": 54, "load_security_control": 46, "loadbalanc": [16, 24], "local": [1, 16, 19, 20, 27, 28, 31, 35, 36, 38, 47, 50, 58, 62, 65, 67, 70, 77, 79, 80], "localhost": [2, 3, 13, 14, 17, 20, 28, 30, 31, 32, 33, 34, 35, 38, 53, 54, 64, 66, 67], "localstorag": 7, "locat": [4, 34, 35, 38, 40, 41, 46, 67, 79], "lock": [6, 8, 15, 20, 53], "lockdown": 79, "lockout": [46, 53, 70], "locust": [0, 2, 64], "locustfil": [2, 64], "log": [0, 3, 4, 7, 11, 12, 14, 15, 16, 18, 19, 20, 21, 24, 25, 26, 27, 28, 31, 35, 36, 38, 40, 42, 43, 44, 46, 48, 49, 50, 52, 53, 54, 57, 58, 59, 61, 62, 63, 65, 68, 69, 70, 75, 76, 77, 78, 79], "log_backup_count": 13, "log_file_path": 13, "log_format": 13, "log_include_level": 13, "log_include_logger_nam": 13, "log_include_thread_id": 13, "log_include_timestamp": 13, "log_level": [2, 13, 14, 20, 28], "log_max_s": 13, "log_processing_act": 42, "log_sourc": 75, "log_stat": 20, "logger": [18, 19, 30, 49, 54, 57, 58, 59], "logging_system": 61, "logic": [1, 3, 8, 18, 19, 21, 25, 30, 40, 42, 47, 51, 65, 77], "login": [8, 13, 14, 16, 27, 28, 33, 41, 53, 64, 70, 79], "login_respons": 53, "login_url": 53, "logist": 77, "loglevel": [14, 24], "logout": 41, "logrot": [32, 33], "long": [6, 17, 18, 25, 32, 33, 41, 45, 46, 63, 65, 66, 70, 73, 75, 76, 77, 78, 79], "long_term": 69, "longer": 12, "look": [19, 79], "lookback_dai": 57, "lookup": 12, "loop": [55, 59, 64, 79], "loos": 62, "loss": [10, 70, 73, 74, 76, 77, 78, 79, 80], "low": [30, 33, 35, 38, 45, 46, 48, 49, 50, 51, 52, 54, 55, 56, 57, 58, 59, 61, 69, 71, 79], "lower": [53, 54, 64, 65, 71], "lowest": 13, "lru": [33, 34, 35, 59, 63], "lru_cach": 35, "lru_kei": 35, "lrucach": 59, "lsass": 69, "lsof": [2, 20, 66], "lt": 32, "m": [2, 13, 14, 16, 20, 21, 28, 31, 32, 33, 38, 65, 66, 67, 78], "m5": 15, "machin": [11, 22, 23, 30, 36, 48, 52, 63, 73, 74, 75, 76, 77, 78, 79], "machine_learn": 42, "maco": [0, 14, 20, 28, 59], "made": 79, "magicmock": 65, "mai": [13, 18, 38, 47, 53, 67, 79], "mail": 58, "mailbox": 77, "main": [2, 3, 14, 20, 21, 35, 38, 46, 53, 55, 64, 65, 67, 71, 79], "maintain": [0, 4, 11, 12, 15, 18, 19, 21, 22, 33, 36, 39, 40, 41, 42, 45, 46, 47, 48, 50, 52, 53, 54, 55, 56, 63, 64, 65, 68, 69, 70, 72, 73, 74, 76, 77, 78, 79], "mainten": [17, 23, 27, 29, 35, 41, 50, 53, 62, 63, 72, 74, 76, 77, 78], "maintenance_mod": 32, "maintenance_reindex": 61, "maintenance_typ": 32, "maintenance_vacuum_analyz": 61, "maintenance_window": 5, "maintenance_work_mem": [34, 35], "major": [0, 1, 21, 27, 29, 43, 45, 47, 60, 63, 72, 73, 75, 80], "make": [0, 1, 2, 3, 12, 20, 21, 27, 28, 36, 43, 45, 64, 71, 73, 76, 77, 78, 79], "makefil": 20, "maker": 78, "malform": 21, "malici": [40, 49, 53, 75, 76, 77, 79], "malicious_payload": 64, "malwar": [45, 77, 78, 79], "manag": [0, 1, 3, 6, 13, 15, 16, 22, 23, 24, 25, 27, 29, 32, 35, 36, 37, 39, 41, 42, 43, 44, 45, 46, 47, 49, 51, 56, 58, 61, 62, 65, 66, 67, 68, 69, 71, 76, 77, 78], "manage_backup": 70, "manage_incident_respons": 79, "manage_integr": [7, 70], "manage_rol": 70, "manage_soc_oper": 79, "manage_us": [7, 70], "management_network": 42, "management_review": 42, "mani": [0, 8, 10, 18, 19, 21, 35], "manifest": [31, 43, 53], "manipul": 77, "manner": 47, "manual": [2, 5, 16, 17, 18, 19, 21, 24, 25, 36, 43, 46, 47, 48, 50, 54, 56, 64, 66, 67, 75, 77, 79], "manual_check": 56, "manufactur": [76, 77], "map": [3, 5, 7, 9, 12, 18, 22, 23, 24, 25, 26, 27, 29, 35, 37, 38, 40, 51, 56, 62, 63, 67, 69, 70, 71, 72, 74, 75, 76, 77, 78, 79], "map_attack_path": 59, "map_attack_path_to_mitr": 69, "mapper": 58, "mapping_strength": 56, "mapping_typ": 56, "maritim": 77, "mark": [17, 64, 65], "marker": [64, 65], "market": [23, 40, 63, 73, 77, 78], "markup": 1, "mass": [77, 79], "massiv": [3, 22, 23, 27], "master": [2, 53, 54, 55, 76, 77, 78, 79], "masteri": [76, 77, 78, 79], "match": [19, 35, 39, 42, 53, 64, 65, 66, 74, 75, 79], "match_field": 5, "matching_techniqu": 75, "materi": [0, 25, 44, 72, 78, 80], "material_weak": 80, "mathemat": [10, 71, 74, 78, 80], "matplotlib": 26, "matric": [22, 37], "matrix": [46, 55, 69, 78, 79], "matter": [19, 72, 78], "matur": [50, 72, 73, 76, 77, 78, 79], "maturity_assess": 29, "maturity_scor": 42, "max": [5, 8, 18, 35, 38, 56, 66, 71], "max_access_duration_hour": 4, "max_allocated_storag": 15, "max_attack_dur": 80, "max_attack_path_depth": [18, 66], "max_attempt": 35, "max_concurrent_request": [13, 30], "max_concurrent_sess": 13, "max_connect": [34, 35], "max_critical_age_dai": 56, "max_degre": [6, 8, 59, 63, 71], "max_depth": [18, 19, 57, 58, 64, 69], "max_duration_hour": 4, "max_escalation_duration_hour": 4, "max_fail": [34, 35], "max_fin": 80, "max_high_age_dai": 56, "max_hop": 67, "max_it": 57, "max_length": 59, "max_nb_char": [64, 65], "max_nod": 29, "max_overflow": 35, "max_path": 35, "max_path_length": [6, 8, 63, 71], "max_paths_per_target": [6, 63, 71], "max_relationship": 29, "max_request": [34, 35], "max_request_s": 13, "max_requests_jitt": 34, "max_retri": [30, 41], "max_retry_attempt": 18, "max_siz": [15, 34, 35, 59], "max_wal_s": [34, 35], "max_work": 59, "maxdegre": 8, "maxdepth": [18, 26], "maxfail": 64, "maxim": [76, 79], "maximum": [6, 13, 18, 19, 35, 71, 74, 76, 77], "maxmemori": [33, 34, 35], "maxreplica": 34, "maxsiz": 35, "md": [19, 21, 40], "md5": [49, 52, 54, 55], "mdm": 78, "me": 53, "mean": [3, 23, 24, 25, 42, 45, 48, 50, 51, 52, 54, 76, 79], "mean_tim": [17, 20, 32, 33, 34, 35, 61, 66], "mean_time_to_detect": 42, "mean_time_to_respond": 42, "meaning": [2, 19, 21], "measur": [12, 21, 27, 34, 35, 40, 42, 45, 51, 62, 67, 70, 72, 73, 74, 76, 77, 78, 79], "measures_taken": 12, "mechan": [12, 15, 22, 26, 37, 39, 40, 46, 51, 52, 53, 62, 77, 78, 79], "media": [45, 47, 73, 77, 78, 79, 80], "mediat": 47, "medic": 77, "medical_devic": 61, "medium": [21, 25, 30, 33, 34, 35, 45, 46, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 61, 67, 69, 71, 79, 80], "medium_vulns_count": 57, "meet": [19, 21, 38, 39, 44, 51, 64, 78, 79], "mem": [33, 34], "mem_limit": 66, "member": [1, 3, 17, 21, 22, 23, 25, 27, 30, 33, 37, 38, 43, 46, 51, 63, 67, 70, 73, 78, 79], "memori": [6, 13, 14, 16, 21, 28, 30, 32, 33, 34, 35, 43, 59, 62, 63, 67, 70, 71, 77, 79], "memory_gb": 5, "memory_impact": 53, "memory_info": 35, "memory_profil": 17, "memory_usag": [34, 35], "memory_usage_byt": 35, "memoryefficientcach": 35, "memorypressur": 33, "memusag": 34, "mention": 19, "mentor": [21, 23, 73, 76, 77, 78, 79], "mentor_junior_analyst": 79, "mentorship": [78, 79], "menu": [38, 64, 71], "merg": [2, 18, 21, 46, 62], "merge_request": 54, "merger": [77, 78], "merger_acquisition_assess": 78, "merkl": 41, "merkle_root": 41, "mermaid": [0, 1, 22, 37], "mesh": [11, 15, 78], "messag": [2, 5, 6, 7, 8, 9, 10, 13, 15, 18, 19, 38, 45, 46, 51, 54, 55, 58, 63, 66, 67, 73, 78, 79], "met": [21, 51, 63], "meta": [3, 5, 18, 64, 65], "metadata": [8, 12, 14, 16, 17, 27, 31, 32, 34, 41, 51, 54, 56, 57, 58, 62, 63, 64, 68, 71, 74], "metamorph": 77, "metavers": 73, "method": [1, 7, 8, 16, 17, 18, 19, 27, 32, 35, 47, 49, 53, 54, 56, 63, 65, 66, 77, 79], "methodologi": [3, 23, 27, 62, 72, 74], "methodology_develop": [76, 77], "metric": [4, 6, 8, 13, 14, 16, 23, 26, 27, 32, 33, 35, 38, 40, 41, 53, 57, 59, 62, 69, 70, 72, 73, 74, 76, 78, 79], "metrics_analytics_expertis": 76, "metrics_en": 13, "metrics_endpoint": 13, "metrics_include_appl": 13, "metrics_include_system": 13, "metrics_path": 34, "metrics_report": 42, "mfa": [11, 13, 26, 38, 39, 40, 42, 44, 52, 78, 79], "mfa_backup_codes_count": 13, "mfa_en": [7, 13], "mfa_method": 42, "mfa_require_for_admin": 13, "mfa_require_for_al": 13, "mfa_system": 61, "mfa_token": [7, 42], "mfa_totp_digit": 13, "mfa_totp_issu": 13, "mfa_totp_period": 13, "mfaservic": 42, "mfatoken": 7, "mgmt": 30, "micro": [39, 52, 65, 78], "micro_segment": 42, "microsegment": [11, 15, 29], "microservic": [15, 27, 61, 62, 73, 78], "microsoft": [30, 38, 42, 79], "middai": 78, "middlewar": [25, 50, 61], "migrat": [0, 14, 18, 20, 25, 26, 28, 62, 65, 67, 78], "mileston": [21, 76, 77, 78, 79], "militari": 80, "millisecond": 41, "mimicri": 77, "min": [34, 35, 56, 59, 69], "min_confid": 75, "min_duration_dai": 75, "min_fin": 80, "min_siz": 15, "min_wal_s": 35, "mine": 76, "minim": [11, 12, 21, 35, 42, 43, 44, 45, 47, 50, 51, 52, 65, 68, 71, 72, 73], "minimis": 40, "minimize_data": 42, "minimized_data": 42, "minimum": [4, 11, 13, 14, 15, 20, 28, 37, 38, 64, 65, 67], "minimum_complet": 56, "minor": [19, 21, 43, 45, 47, 55, 79], "minreplica": 34, "minut": [5, 6, 8, 13, 24, 25, 33, 34, 35, 38, 42, 45, 57, 62, 63, 64, 66, 67, 68, 78, 79], "mirror": 18, "misconfigur": [47, 53, 68, 76, 77, 78, 79], "misp": 80, "miss": [0, 13, 18, 22, 26, 30, 32, 33, 46, 53, 59, 64, 65, 69, 79], "missing_var": 17, "mission": [40, 49, 62], "mistak": 46, "mit": 27, "mitig": [6, 8, 12, 15, 38, 43, 46, 50, 51, 52, 59, 60, 63, 69, 71, 72, 73, 74, 76, 77, 78, 79], "mitigation_cost": [6, 61], "mitigation_strategi": [6, 61, 71], "mitr": [3, 18, 21, 23, 25, 26, 27, 34, 35, 36, 44, 52, 62, 65, 67, 71, 76, 77, 78, 79, 80], "mitre att&ck": 27, "mitre_attack": 75, "mitre_correlations_per_second": 34, "mitre_coverag": 42, "mitre_data": [34, 59], "mitre_map": 59, "mitre_servic": [42, 65, 69], "mitre_techniqu": [18, 35, 69, 80], "mitreattackmap": 59, "mitreattackservic": 42, "mitredataerror": 65, "mitreservic": 65, "mkdir": [28, 31, 53, 54, 55, 66], "ml": [23, 24, 26, 29, 36, 41, 79], "ml_model": 57, "ml_model_metadata": 57, "mlmodelmetadata": 57, "mlpclassifi": [26, 57], "mm": [43, 46], "mobil": [3, 9, 29, 40, 47, 74, 75, 76, 77, 78, 79], "mobile_count": 75, "mobile_devic": 61, "mock": [8, 18, 19, 64], "mock_boto_cli": 65, "mock_ec2": 65, "mock_get": 65, "mock_graph_servic": [19, 65], "mock_sess": 65, "mockup": 19, "mode": [20, 32], "model": [3, 6, 7, 18, 19, 20, 22, 24, 27, 36, 37, 39, 41, 46, 52, 59, 60, 64, 65, 69, 72, 73, 76, 77, 78, 79], "model_nam": 57, "model_scor": 57, "model_select": 57, "model_typ": 57, "model_vers": 57, "moder": [13, 30, 38, 45, 50, 71, 79], "modern": [27, 38, 67, 77, 78], "modif": [18, 19, 46, 51, 67, 70, 76, 77, 78, 79], "modifi": [3, 4, 7, 16, 18, 21, 30, 47, 59, 67, 70, 79], "modify_incident_statu": 79, "modul": [15, 24, 25, 27, 29, 36, 41, 42, 49, 64, 75], "modular": [0, 15, 62], "module_nam": 65, "mondai": 78, "monei": 72, "monetari": [10, 74, 80], "monitor": [0, 1, 6, 7, 9, 14, 19, 20, 21, 22, 23, 24, 25, 27, 28, 29, 32, 36, 37, 44, 45, 46, 48, 49, 50, 51, 53, 59, 63, 64, 66, 67, 69, 71, 73, 74, 75, 76, 77, 78], "monitor_detect": 69, "monitor_for_reoccurr": 42, "monitor_perform": 35, "monitoring_coverag": 42, "monitoring_frequ": 80, "monitoring_measur": 42, "monitoring_scor": 46, "monitoring_system": 61, "mont": [23, 78], "month": [25, 26, 31, 46, 52, 63, 69, 78], "monthli": [31, 40, 41, 42, 43, 45, 46, 48, 50, 51, 78, 79], "more": [0, 4, 7, 10, 18, 19, 35, 38, 39, 56, 61, 73, 76, 77, 78, 79], "morn": [78, 79], "most": [3, 23, 27, 35, 44, 52, 62, 63, 66, 67, 69, 78, 79], "motiv": [19, 77, 78, 80], "mount": 17, "move": [35, 77], "movement": [6, 63, 69, 71, 76, 77, 79], "msp": 77, "mtime": [32, 33], "mtl": 11, "mtta": [50, 79], "mttc": [45, 79], "mttd": [3, 23, 45, 48, 50, 52, 76, 79], "mttr": [3, 23, 45, 48, 50, 52, 76, 79], "mttrec": 79, "much": 35, "multi": [0, 3, 4, 6, 11, 13, 14, 15, 21, 22, 23, 24, 25, 27, 29, 34, 39, 40, 42, 44, 46, 48, 49, 50, 51, 54, 55, 56, 61, 62, 63, 64, 69, 70, 71, 72, 74, 75, 76, 77, 78, 79], "multi_az": 15, "multi_cloud_architecture_design": 78, "multi_factor": 29, "multi_factor_authent": 42, "multi_line_output": 18, "multiclouddiscoveryorchestr": 68, "multilevelcach": 35, "multin": [77, 78], "multipl": [0, 1, 3, 4, 5, 9, 11, 12, 15, 22, 24, 29, 30, 35, 37, 39, 41, 44, 50, 53, 55, 57, 59, 64, 65, 68, 69, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80], "multivari": 76, "must": [2, 8, 18, 21, 51, 67], "mustrunasnonroot": 51, "mutual": [47, 77], "mv": 16, "my": [12, 30], "mypi": [18, 21, 51], "myst": 3, "n": [4, 11, 12, 14, 16, 17, 31, 32, 33, 35, 41, 43, 57, 58, 64, 65, 66], "n_dead_tup": 32, "n_estim": 57, "n_live_tup": 32, "n_tup_del": 33, "n_tup_in": 33, "n_tup_upd": 33, "nac": 78, "name": [3, 5, 6, 8, 11, 14, 16, 17, 18, 19, 20, 26, 28, 30, 31, 32, 33, 34, 35, 38, 42, 46, 47, 51, 53, 54, 55, 56, 58, 59, 61, 64, 65, 66, 67, 68, 69, 70, 71, 75, 80], "namespac": [14, 31, 68], "nano": [20, 28], "narr": 78, "nat": 29, "nation": [76, 77, 79], "nativ": [25, 26, 27, 62, 63, 76, 78, 79, 80], "natur": [76, 79], "navig": [0, 1, 8, 14, 20, 33, 38, 64, 67, 71, 74, 79], "nc": [11, 16, 17], "ndarrai": 57, "necessari": [4, 12, 16, 33, 35, 40, 45, 79], "need": [0, 1, 3, 4, 6, 8, 12, 14, 16, 17, 19, 20, 21, 28, 32, 33, 42, 43, 45, 47, 68, 69, 70, 71, 74, 76, 77, 78, 79], "neg": [45, 76, 77, 79], "negoti": [76, 78], "neighbor": 59, "neo4j": [0, 13, 15, 20, 28, 34, 37, 38, 66, 67], "neo4j_connection_acquisition_timeout": 13, "neo4j_databas": 13, "neo4j_max_connection_lifetim": 13, "neo4j_max_connection_pool_s": 13, "neo4j_password": [13, 20, 28], "neo4j_uri": [13, 20, 28], "neo4j_us": [13, 20, 28], "nessu": [48, 50], "nest": 18, "net": 24, "netstat": [28, 38, 66, 67], "network": [0, 3, 4, 5, 6, 13, 14, 18, 20, 23, 24, 25, 27, 28, 29, 34, 35, 36, 37, 38, 39, 40, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 53, 57, 59, 65, 67, 69, 70, 71, 74, 75, 76, 77, 78, 79], "network_access": 6, "network_access_control": 42, "network_centr": 57, "network_devic": [5, 61, 65], "network_discoveri": 68, "network_discovery_retri": 66, "network_discovery_timeout": 66, "network_featur": 57, "network_id": 42, "network_lat": 34, "network_secur": 42, "network_segment": [42, 61], "network_traff": 42, "networkdiscoveryservic": 68, "networkgraph": 26, "networkgraphprop": 26, "networkpolici": [11, 31], "networkx": [24, 26, 30, 35, 59, 62, 63], "networkxnopath": 35, "neural": [24, 25, 36, 57], "neural_network": [26, 57], "neural_network_scor": 57, "never": [8, 11, 15, 39, 42, 44], "new": [5, 7, 8, 11, 14, 16, 17, 19, 21, 23, 28, 29, 31, 32, 33, 36, 37, 38, 43, 46, 50, 51, 53, 54, 55, 58, 61, 64, 65, 66, 68, 70, 71, 73, 74, 78, 79, 80], "new_assess": 53, "new_asset": 5, "new_asset_discov": 68, "new_attack_path": 80, "new_cert_arn": 32, "new_certificate_arn": 17, "new_endpoint": 31, "new_featur": 20, "new_instance_id": 31, "new_likelihood": 59, "new_password": 7, "new_statu": 58, "new_valu": 41, "new_vers": 32, "newasset": 5, "newfeaturecr": 20, "newfeaturerespons": 20, "newkei": 66, "newli": 71, "newpassword123": 66, "newpassword456": 7, "newsecurepassword123": 67, "newserv": 5, "next": [8, 27, 33, 43, 45, 47, 49, 52, 59, 75, 76, 77, 78, 79], "next_assessment_du": 56, "next_cursor": 8, "next_gener": 42, "next_level": 59, "nf": 33, "nfv": 78, "ng": 32, "ngfw": 78, "nginx": [3, 35], "nist": [22, 23, 24, 25, 26, 27, 36, 37, 39, 41, 44, 52, 63, 72, 76, 78, 79], "nist_csf": [29, 41, 56], "nitpick": 19, "nmap": [0, 5, 66], "nmap_opt": 68, "nn_proba": 57, "no_map": 58, "no_modif": 4, "node": [2, 6, 14, 15, 16, 17, 20, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 43, 50, 55, 59, 62, 63, 66, 67, 71, 79], "node_group": [15, 32], "node_modul": 20, "node_typ": 15, "nodegroup": 32, "nodeid": 26, "nodej": 14, "nodeselector": 17, "nodesourc": 14, "non": [14, 18, 19, 21, 32, 34, 43, 46, 50, 53, 62, 65, 69, 79], "non_compli": 56, "nonconformity_corrective_act": 42, "none": [7, 13, 18, 19, 35, 49, 50, 53, 56, 57, 58, 59, 64, 65, 69], "noout": [11, 32, 66], "normal": [25, 45, 57, 77, 79], "normal_data": 57, "northeurop": 30, "nosec": 55, "not_applic": 56, "notaft": 32, "note": [3, 7, 8, 18, 19, 21, 22, 37, 44, 62, 65, 67, 70, 74, 79], "notic": [38, 40, 47, 67, 72], "notif": [4, 8, 12, 17, 22, 24, 29, 30, 33, 37, 38, 44, 45, 48, 51, 62, 67, 68, 70, 72, 73, 76, 77, 78, 79, 80], "notifi": [12, 17, 33, 79], "notification_timelin": 80, "notimplementederror": 65, "now": [1, 13, 18, 21, 22, 27, 28, 29, 32, 33, 35, 38, 41, 42, 46, 54, 56, 57, 58, 68], "np": 57, "npm": [2, 8, 14, 18, 20, 28, 50], "nr": [32, 33], "nslookup": [17, 43], "nta": 78, "nuclear": 77, "nuclei": 53, "null": [6, 12, 13, 17, 18, 19, 32, 41, 56, 61], "nullabl": [18, 26, 56, 57, 58], "nullif": [32, 61], "num_cache_nod": 15, "number": [5, 8, 13, 18, 21, 35, 46, 51, 67, 71, 76, 77, 79, 80], "number_of_compon": 6, "numer": [18, 65], "numpi": [24, 26, 57], "nx": [35, 59], "o": [14, 16, 17, 19, 20, 30, 31, 32, 33, 34, 35, 46, 48, 51, 53, 54, 55, 64, 66, 68, 80], "oauth": [32, 33], "obfusc": 77, "object": [6, 8, 12, 15, 50, 56, 58, 59, 61, 63, 70, 71, 73, 74, 76, 77, 78], "objectview": 30, "oblig": [78, 79], "observ": [15, 24, 26, 29, 35, 43, 45, 47, 53, 58, 62, 79], "observable_data": 58, "obsolet": [64, 71], "obtain": [7, 8], "obviou": 21, "occur": 40, "occurr": 79, "oci": 30, "ocr": 80, "ocr_reporting_requir": 80, "ocsp": 11, "octav": 78, "octet": 65, "off": [35, 71, 77, 78, 79], "offens": [23, 27, 74, 76], "offensive secur": 77, "offer": 55, "offic": [3, 22, 23, 27, 37, 45, 51, 73, 78, 79], "offici": [8, 9, 62, 63, 75], "offlin": 67, "often": [35, 64], "oid": 66, "oidc": [7, 47, 79], "oil": 77, "ok": [7, 8], "okta": 42, "old": [32, 33, 61], "old_valu": 41, "oldest": 35, "oldest_kei": 35, "oldpassword123": 7, "omit": 65, "on_start": 64, "onanalysiscomplet": [18, 19], "onboard": [0, 3, 18, 37, 51, 74], "one": [18, 21, 28, 53, 64, 69], "onerror": [53, 64], "ongo": [1, 10, 12, 18, 35, 42, 45, 52, 68, 70, 72, 74, 75, 76, 77, 78, 79], "onli": [2, 3, 4, 11, 12, 13, 20, 21, 27, 30, 35, 40, 46, 47, 53, 54, 55, 57, 64, 66, 67, 77, 79, 80], "onlin": [42, 78, 79], "onload": 53, "onnodeclick": 26, "ons": 32, "oomkil": 17, "op": [18, 24, 56], "open": [3, 5, 18, 19, 23, 32, 38, 46, 47, 52, 53, 54, 55, 58, 64, 65, 66, 67, 76, 77, 79], "openapi": [0, 13, 15, 19, 26, 27, 37, 62, 63, 67, 68], "openapi_url": 13, "openssl": [11, 16, 17, 32, 66], "oper": [1, 6, 8, 16, 17, 18, 20, 22, 23, 24, 25, 28, 30, 31, 33, 34, 36, 37, 38, 39, 42, 43, 44, 45, 50, 51, 58, 59, 63, 66, 67, 71, 72, 73, 78], "operating_system": [5, 61], "operation": 78, "operational_plan": 42, "opportun": [3, 19, 23, 27, 47, 69, 70, 73, 76, 77, 78, 79], "opsec": 77, "opsgeni": 79, "opt": [28, 40, 68], "optim": [0, 1, 3, 8, 14, 16, 17, 18, 22, 23, 26, 27, 28, 29, 36, 37, 41, 42, 45, 50, 62, 63, 64, 66, 67, 72, 73, 74, 77, 78, 79], "optimizedattackpathfind": 35, "option": [1, 3, 5, 6, 7, 13, 14, 18, 20, 21, 22, 35, 53, 55, 57, 58, 59, 62, 63, 64, 65, 66, 75, 80], "oracl": 30, "orchestr": [0, 22, 23, 25, 30, 37, 39, 46, 50, 52, 62, 68, 72, 76, 77, 78], "order": [5, 13, 17, 20, 32, 33, 34, 35, 40, 41, 56, 61, 66], "order_bi": 57, "org": [14, 40], "organ": [3, 4, 8, 12, 22, 23, 27, 35, 42, 45, 52, 56, 58, 65, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79], "organiz": [3, 23, 25, 40, 56, 70, 72, 73, 76, 77, 78, 79], "organization_ev": 75, "organization_profil": 42, "organization_wid": 42, "organizational_transformation_leadership": 76, "organizeimport": [18, 20], "orient": [0, 19], "origin": [2, 13, 19, 21, 24, 25, 26, 40, 66, 67, 79, 80], "original_purpos": 42, "orm": [18, 56, 58, 64], "orphan": 56, "os_detect": [5, 68], "osint": [76, 77], "ot": [76, 77, 78], "other": [3, 19, 21, 29, 40, 47, 53, 56, 58, 65, 67, 69, 72, 75, 76, 79], "our": [2, 3, 19, 21, 23, 27, 28, 39, 44, 45, 46, 47, 48, 50, 52, 53, 54, 55, 65, 67, 74], "out": [2, 7, 14, 16, 19, 24, 31, 40, 51, 65, 66, 67], "outag": [43, 45], "outbound": 61, "outbound_connect": 57, "outbreak": 79, "outcom": [77, 78], "outdat": [3, 30, 32], "outlier": 79, "outlin": [36, 43, 44, 45, 47, 49, 63], "outlook": 78, "outofdisk": 33, "output": [16, 31, 32, 39, 41, 44, 45, 46, 48, 51, 53, 54, 55, 64, 65, 66, 79], "outsid": 79, "outsourc": [73, 78], "outstand": 19, "outward": 11, "over": [5, 9, 17, 18, 35, 52, 55, 71, 72, 73, 75, 76, 77, 78, 79, 80], "overal": [43, 45, 46, 48, 49, 50, 52, 54, 55, 64, 65, 66, 72, 73, 76, 77, 78, 79, 80], "overall_matur": 42, "overall_scor": 56, "overall_statu": 56, "overdu": 4, "overdue_notification_dai": 4, "overflow": 67, "overhead": 34, "overlap": [69, 76], "overnight": [78, 79], "overse": [72, 73, 78], "oversight": [22, 23, 27, 72, 73, 74, 77, 78, 79], "overview": [3, 19, 37, 49, 73, 78, 79], "owasp": [16, 39, 42, 44, 47, 48, 50, 52, 53, 54], "own": 19, "owner": [5, 21, 43, 56], "ownership": [40, 70], "oxlei": [72, 80], "p": [2, 14, 16, 17, 20, 28, 31, 32, 33, 34, 35, 38, 53, 54, 55, 66, 67], "p0": 63, "p1": [45, 63, 79], "p2": [33, 45, 79], "p3": [33, 45, 79], "p4": [45, 79], "p95": 34, "pace": 77, "packag": [20, 21, 24, 26, 31, 32, 50, 54, 69], "packet": 78, "packet_loss": 34, "page": [1, 3, 5, 8, 19, 23, 27, 32, 33, 38, 43, 66], "page_id": [32, 33], "pagecach": [34, 35], "pagerduti": [17, 43, 79], "pagin": [5, 27, 30], "pair": 79, "pam": [42, 77, 78], "pan": 38, "panda": [24, 26, 35, 57], "panel": 34, "pap": 58, "paragraph": 79, "parallel": [34, 62, 63, 65, 68, 69, 75, 80], "parallel_work": 68, "parallelanalyz": 59, "param": [18, 26, 54, 64], "paramet": [6, 8, 51, 56, 67, 70, 71, 80], "parameter": 18, "parent_control": 56, "parent_control_id": 56, "parent_process": 75, "pars": [13, 31, 70, 74], "parse_invalid_mitre_data": 65, "parser": 3, "parti": [8, 40, 42, 43, 45, 46, 47, 51, 52, 62, 71, 72, 76, 77, 78, 79], "partial": [46, 56, 69], "partially_compli": 56, "particip": [19, 23, 45, 52, 62, 70, 73, 74, 76, 77, 78, 79], "partner": [45, 73, 76, 77, 78, 79], "partnership": [73, 76, 77, 78], "pascalcas": 18, "pass": [2, 18, 19, 20, 21, 26, 28, 32, 35, 49, 52, 54, 65, 77], "pass_threshold": 56, "passiv": [68, 74], "passive_asset": 68, "passive_discoveri": 68, "passwd": 53, "password": [8, 12, 13, 14, 16, 20, 28, 31, 40, 42, 46, 53, 54, 55, 64, 66, 68, 70, 77], "password123": 53, "password_complexity_requir": 42, "password_field": 53, "password_hash": 61, "password_hash_algorithm": 13, "password_hash_round": 13, "password_rotation_polici": 42, "passwordless": 78, "past": 0, "pasta": 78, "patch": [8, 14, 16, 17, 19, 21, 31, 33, 43, 44, 45, 48, 58, 68, 77, 78, 79], "patch_manag": 61, "patch_vulner": 42, "patent": 77, "path": [1, 3, 7, 13, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26, 27, 28, 30, 34, 35, 37, 47, 53, 54, 55, 62, 64, 65, 66, 68, 70, 76, 78, 80], "path_001": [6, 8], "path_001_web_server_001_database_001": 6, "path_cach": 59, "path_edg": [59, 61], "path_id": [6, 8, 61, 63], "path_length": [6, 61], "path_nod": [6, 59, 61, 71], "path_techniqu": 69, "path_typ": [6, 61], "path_type_enum": 61, "pathfind": [34, 62], "paths_found": 21, "paths_respons": 8, "pathwai": [3, 23, 27, 72, 76, 77, 78, 79], "patient": [19, 47, 77, 79, 80], "patient_notification_requir": 80, "patient_record": 80, "pattern": [0, 1, 4, 7, 8, 11, 18, 19, 22, 25, 26, 29, 32, 33, 35, 37, 41, 46, 54, 55, 62, 64, 70, 72, 74, 76, 77, 78, 79], "payload": [8, 21, 53, 54, 55, 64, 77], "payment": [41, 42, 63, 72, 77, 78, 79], "payment_data": 51, "payment_system": 80, "pci": [0, 23, 27, 36, 41, 51, 63, 68, 72, 74, 78], "pci dss": 72, "pci_dss": [68, 69], "pci_report": [68, 69], "pci_scop": 69, "pd": [35, 57], "pdb": [2, 20, 65], "pdf": [3, 38, 41, 68, 79], "peaceiri": 3, "peak": 71, "peer": [3, 62, 72, 73, 77, 78, 79], "pem": [11, 13, 28, 66], "penalti": [10, 59, 72, 73, 78, 79, 80], "pend": [4, 56], "penetr": [1, 2, 3, 14, 16, 39, 40, 44, 46, 52, 53, 55, 62, 63, 67, 69, 70, 72, 74, 76, 77, 78], "penetration test": 77, "peopl": 19, "pep": [18, 19], "per": [5, 6, 8, 9, 10, 13, 15, 16, 19, 34, 35, 40, 41, 48, 50, 62, 63, 71, 76, 77, 79], "percent": [17, 53], "percentag": [46, 48, 55, 65, 76, 77, 79], "percentil": 34, "perf": 21, "perfect": 36, "perform": [1, 8, 9, 10, 18, 19, 21, 23, 24, 26, 39, 43, 45, 46, 47, 54, 55, 57, 58, 72, 74, 76, 78, 79], "perform_analysi": 18, "performance_evalu": 42, "performance_metr": [6, 57], "performance_regress": 18, "performance_target": 29, "perimet": [42, 69, 78], "perimeter_firewal": 42, "period": [4, 6, 11, 16, 29, 35, 40, 47, 67, 70, 71, 78], "perman": 5, "permiss": [3, 4, 5, 8, 10, 11, 13, 15, 16, 17, 18, 21, 22, 29, 30, 33, 37, 40, 41, 42, 46, 47, 50, 62, 63, 67, 68, 71, 72, 73, 74, 75, 76, 77, 78, 80], "permission_deni": 5, "permissionerror": 18, "permissionservic": 42, "persist": [6, 16, 23, 30, 31, 34, 35, 65, 71, 76, 77, 78, 79, 80], "persistence_level": 80, "persistentvolumeclaim": 51, "persistentvolumeclaimnam": 31, "person": [39, 45, 47, 62, 77, 79], "persona": [74, 77], "personal_data": 80, "personal_identifi": 12, "personaldata": 42, "personnel": [40, 73, 74, 76, 77, 79], "perspect": [19, 78], "persuas": 78, "pfsens": 42, "pg_databas": 66, "pg_database_s": [32, 33], "pg_dump": [20, 31, 67], "pg_isreadi": [16, 17, 33, 53, 64, 66], "pg_reload_conf": [20, 66], "pg_restor": 31, "pg_rotate_logfil": 32, "pg_size_pretti": [32, 33, 61], "pg_stat_act": [32, 33, 34, 35, 38], "pg_stat_databas": 66, "pg_stat_stat": [17, 20, 32, 33, 34, 35, 61, 66], "pg_stat_statements_reset": 32, "pg_stat_user_index": [32, 35], "pg_stat_user_t": [32, 33], "pg_tabl": [32, 61], "pg_terminate_backend": 33, "pg_total_relation_s": [32, 61], "pgadmin": [2, 20], "pgp": [44, 47], "pgpassword": 16, "pgrep": 34, "phantom": 42, "pharmaceut": 77, "phase": [21, 23, 32, 34, 37, 42, 47, 74, 76, 77, 78, 79], "phi": [72, 78], "phi_records_affect": 80, "phish": [42, 47, 76, 77, 78, 79, 80], "phishing_click_r": 42, "phishing_resili": 42, "phishing_result": 42, "phone": [42, 43, 77, 79], "phone_system": 61, "phoni": [53, 55], "physic": [40, 47, 50, 56, 76, 77, 78, 79], "physical_security_test": 77, "pia": [12, 29, 72], "pickl": [52, 55], "pictur": 79, "pid": [2, 20, 28, 32, 33, 34], "pii": [40, 46, 49, 51, 71, 77, 80], "pilot": [11, 24, 78, 79], "pin": [11, 29], "ping": [14, 28, 43, 64, 66], "pinnacl": [3, 23], "pip": [2, 3, 8, 14, 18, 20, 30, 32, 35, 53, 54, 55, 64, 65], "pipelin": [0, 15, 18, 21, 22, 24, 25, 27, 29, 36, 37, 48, 49, 50, 51, 55, 62, 77, 78, 79], "pki": [77, 78], "place": [40, 46, 51], "placement": [63, 74, 78], "plai": 47, "plain": [12, 35], "plan": [0, 14, 15, 16, 21, 23, 25, 26, 31, 33, 40, 41, 42, 43, 45, 46, 47, 48, 50, 51, 55, 56, 63, 67, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80], "plane": 32, "planned_valu": 51, "planner": 35, "platform": [0, 1, 3, 5, 8, 13, 19, 22, 23, 24, 25, 28, 29, 30, 37, 40, 43, 47, 52, 58, 59, 62, 69, 74, 75], "playbook": [42, 63, 69, 74, 78, 79], "playbook_step": 69, "plc": 77, "pleas": [3, 18, 19, 27, 39, 44, 47, 67], "plpgsql": 61, "plugin": [50, 62], "plural": 18, "pluralsight": 79, "png": 7, "po": 77, "pod": [11, 12, 14, 15, 31, 32, 33, 41, 43, 51, 68], "podsecuritypolici": 51, "point": [15, 18, 19, 20, 24, 30, 36, 37, 46, 51, 62, 69, 70, 71, 74, 77, 78, 79], "poison": 77, "polici": [7, 14, 15, 27, 29, 30, 33, 34, 35, 37, 39, 41, 42, 43, 45, 46, 56, 61, 62, 66, 67, 70, 72, 73, 74, 76, 77, 78, 79], "policy_development_author": 72, "policy_docu": 56, "policy_review": 56, "poll": [8, 13], "poly1305": 11, "polymorph": 77, "pool": [0, 11, 13, 21, 34, 35, 41, 70], "pool_pre_p": 35, "pool_recycl": 35, "pool_siz": 35, "pool_timeout": 35, "poolclass": 35, "poor": 46, "pop": [35, 59], "popul": 71, "popular": [0, 3, 8], "port": [3, 5, 13, 14, 16, 17, 20, 24, 28, 33, 38, 53, 54, 55, 61, 66, 67, 68], "port_scan": [5, 68], "portabl": [12, 72, 78, 80], "portain": 2, "portal": [40, 44, 74, 79], "portfolio": [73, 76, 78], "posit": [3, 22, 23, 24, 33, 42, 45, 46, 48, 49, 50, 52, 53, 54, 69, 73, 75, 76, 77, 78, 79], "possibl": [6, 30, 45, 50, 54, 71, 75], "post": [4, 5, 6, 7, 8, 11, 12, 13, 14, 18, 20, 27, 30, 32, 33, 34, 35, 41, 47, 51, 53, 57, 58, 63, 64, 67, 71, 72, 73, 75, 77, 78, 79, 80], "postgr": [14, 31, 32, 34, 38, 53, 64, 66, 67], "postgres_password": [53, 64], "postgresql": [0, 2, 13, 14, 15, 16, 20, 24, 25, 26, 28, 29, 30, 34, 37, 56, 61, 63, 66, 67], "postman": [2, 8], "postur": [24, 25, 36, 38, 42, 45, 46, 49, 50, 52, 53, 54, 55, 63, 67, 69, 71, 73, 74, 76, 77, 78, 79, 80], "potenti": [0, 10, 18, 38, 40, 43, 45, 47, 51, 54, 55, 58, 67, 69, 71, 72, 74, 77, 78, 79, 80], "potential_penalti": 80, "power": [9, 23, 25, 26, 27, 36, 38, 42, 48, 52, 63, 65, 67, 71, 72, 74, 75, 76, 77, 78, 79], "powerpoint": 79, "powershel": [24, 25, 36, 75, 77, 80], "powersourc": 80, "pr": [19, 21, 50, 56], "practic": [3, 18, 19, 22, 23, 27, 28, 37, 38, 39, 40, 42, 43, 44, 45, 47, 48, 49, 50, 51, 52, 62, 72, 73, 76], "practition": [76, 77], "pragma": 65, "prd": [3, 26], "pre": [0, 18, 20, 21, 32, 46, 49, 50, 64, 67, 71, 74, 75, 77, 79], "precis": [57, 76], "precomput": 35, "predecessor": 59, "predefin": [4, 70, 79], "predeploy": 31, "predict": [24, 27, 29, 30, 36, 42, 49, 63, 71, 72, 74, 75, 76, 78, 79], "predict_asset_compromis": 26, "predict_proba": 57, "predict_threat": 26, "predict_threat_prob": 57, "predicted_count": 75, "prediction_d": 57, "prefer": [2, 13, 16, 18, 19, 20, 38, 47, 69, 70, 78, 79, 80], "preferred_techniqu": 80, "prefix": [18, 58], "preliminari": 79, "prematur": [18, 35], "premier": 78, "premis": [5, 38, 62, 68, 77, 78], "premium": [5, 6, 8, 9, 10, 73], "prepar": [11, 15, 21, 22, 23, 28, 37, 40, 42, 43, 45, 50, 52, 53, 57, 69, 71, 74, 76, 77, 78, 79], "prepared": 79, "preprocess": 57, "prerequisit": 27, "presenc": [78, 79], "presence_of_element_loc": 64, "present": [47, 67, 72, 73, 76, 77, 78, 79], "preserv": [0, 43, 45, 73, 78, 79], "preserve_evid": 42, "pressur": 34, "pretext": 77, "prettier": [18, 20], "preval": 78, "prevent": [6, 13, 18, 19, 21, 30, 39, 40, 43, 44, 45, 46, 48, 50, 51, 52, 53, 56, 62, 65, 70, 73, 76, 77, 78, 79], "previou": [16, 17, 47, 79], "previous": [10, 79], "previous_block_hash": 41, "previous_revis": 56, "price": 77, "primari": [12, 13, 15, 18, 20, 26, 41, 42, 43, 47, 56, 61, 63, 65, 75, 76, 77, 78, 80], "primarili": 70, "primary_kei": [26, 56, 57, 58], "primary_motiv": 80, "primary_techniqu": 75, "primarykeyconstraint": [18, 56], "princip": [30, 66, 70, 78], "principl": [19, 21, 30, 42, 46, 49, 50, 51, 62, 64, 70, 77, 78], "print": [3, 6, 14, 17, 18, 19, 20, 31, 32, 33, 35, 57, 65, 66, 68, 71, 75, 80], "print_stat": 35, "printer": 61, "prior": 47, "priorit": [4, 13, 23, 38, 40, 45, 47, 48, 55, 56, 63, 68, 70, 71, 72, 73, 74, 76, 77, 78, 79], "prioriti": [13, 25, 30, 38, 40, 46, 48, 50, 63, 69, 71, 74, 76, 77, 78, 79, 80], "privaci": [22, 29, 37, 39, 41, 42, 44, 45, 47, 48, 51, 52, 53, 63, 68, 72, 73, 77, 78], "privacy_by_design": 29, "privacy_impact_assess": 12, "privat": [7, 15, 16, 18, 19, 29, 30, 47, 78], "privateipaddress": 65, "privileg": [6, 11, 13, 15, 21, 27, 30, 38, 39, 40, 42, 44, 45, 46, 47, 48, 50, 51, 53, 55, 63, 68, 69, 70, 71, 76, 77, 78, 79], "privilege_escal": [4, 41, 61, 69], "privileged_access": 61, "privileged_access_manag": 42, "privileged_user_frequency_dai": 4, "privileges_requir": 69, "pro": 38, "proactiv": [15, 27, 39, 45, 52, 62, 63, 67, 69, 70, 71, 72, 73, 76, 77, 78, 79], "probabilist": 78, "probabl": [10, 25, 26, 27, 38, 57, 59, 63, 71, 74, 77, 78, 79, 80], "probe": 17, "problem": [0, 1, 3, 11, 12, 19, 70, 74, 76, 77, 78, 79], "procedur": [0, 1, 3, 4, 11, 14, 15, 22, 23, 27, 29, 37, 38, 39, 41, 42, 44, 47, 48, 51, 53, 55, 61, 62, 63, 67, 69, 70, 72, 74, 75, 76, 77, 78], "proceed": 45, "process": [0, 1, 2, 3, 4, 5, 11, 13, 15, 18, 20, 22, 24, 25, 26, 27, 28, 29, 30, 33, 34, 35, 36, 37, 39, 41, 42, 44, 48, 49, 50, 52, 54, 60, 62, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80], "process_asset_discoveri": 18, "process_assets_chunk": 35, "process_bandit_result": 55, "process_defin": 42, "process_execut": 75, "process_nam": 75, "process_personal_data": 42, "process_sast_result": 55, "process_security_ev": 26, "process_semgrep_result": 55, "process_webhook": 26, "processing_interval_m": 41, "processing_tim": 57, "processing_time_second": 57, "procur": [70, 78], "prod": [4, 28], "produc": 65, "product": [0, 1, 5, 7, 8, 17, 20, 21, 30, 31, 35, 38, 41, 43, 48, 52, 53, 55, 65, 67, 68, 71, 76, 77, 78, 79], "production_resourc": 15, "profess": [77, 79], "profession": [1, 19, 22, 23, 27, 28, 38, 45, 52, 62, 63, 67, 70, 74, 76, 77], "professional develop": 23, "profici": [74, 76, 77, 78, 79], "profil": [0, 6, 8, 10, 14, 17, 18, 19, 27, 34, 42, 53, 63, 67, 69, 71, 74, 75, 76, 77, 78, 79], "profile_funct": 35, "profit": 77, "profound": [72, 73, 76, 77], "program": [1, 3, 8, 20, 23, 35, 44, 45, 48, 62, 63, 70, 72, 73, 74, 76, 77, 78, 79], "programm": 77, "programmat": [8, 79], "progress": [0, 3, 5, 9, 16, 23, 25, 27, 36, 45, 47, 50, 68, 71, 72, 73, 74, 76, 77, 78, 79], "prohibit": 47, "project": [1, 13, 16, 18, 19, 21, 25, 27, 30, 50, 51, 55, 62, 65, 68, 77, 78, 79], "project_id": 30, "project_nam": [13, 16, 20, 28], "projectkei": 55, "projectnam": 55, "projectvers": 55, "prometheu": [0, 13, 16, 17, 24, 25, 26, 31, 34, 36, 37, 43], "prometheus_cli": [35, 53, 55], "prometheus_en": [13, 14], "prometheus_namespac": 13, "prometheus_port": 13, "promis": 18, "prompt": 38, "promptli": [19, 21, 47], "promtool": 33, "proof": [22, 29, 37, 40, 47, 77, 78], "prop": 18, "propag": [59, 62, 63, 71, 77, 78, 79], "propagation_prob": 59, "proper": [1, 7, 8, 13, 16, 27, 38, 41, 44, 45, 46, 51, 52, 53, 55, 71, 74, 79], "properli": [16, 46, 51, 53, 70, 77, 79], "properti": [40, 55, 61, 62, 73, 76, 77, 78, 79], "propos": [26, 43, 51, 71, 74, 78], "proposit": [62, 72, 73, 76], "proprietari": 79, "prosper": [73, 76, 77, 78], "protect": [1, 12, 15, 18, 27, 29, 31, 39, 41, 42, 44, 46, 47, 48, 50, 52, 53, 56, 61, 62, 68, 69, 70, 72, 73, 74, 76, 77, 78, 79, 80], "proto": 35, "protocol": [5, 17, 45, 50, 61, 70, 76, 77, 79], "proven": [24, 36], "provid": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 19, 20, 21, 23, 26, 27, 28, 29, 30, 31, 32, 33, 34, 37, 39, 40, 41, 42, 43, 44, 45, 47, 48, 50, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 65, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80], "provider_enum": 61, "provis": [22, 29, 37, 40, 45, 47, 70, 78], "proxi": [33, 53, 61], "proxy_add_x_forwarded_for": [34, 35], "proxy_buff": [34, 35], "proxy_buffer_s": [34, 35], "proxy_busy_buffers_s": [34, 35], "proxy_connect_timeout": 35, "proxy_pass": [34, 35], "proxy_read_timeout": 35, "proxy_send_timeout": 35, "proxy_set_head": [34, 35], "prune": [2, 32, 38, 59, 66], "psf": [18, 54, 64], "psm1": 26, "psp": 51, "psql": [16, 17, 20, 28, 31, 32, 33, 43, 66], "pstat": 35, "psutil": [17, 33, 35, 53], "psychologi": 77, "ptw": 20, "public": [16, 18, 19, 29, 30, 31, 32, 35, 40, 42, 45, 61, 62, 71, 73, 74, 76, 77, 78, 79, 80], "publication_d": 56, "publicli": 44, "publish": 25, "publish_dir": 3, "pull": [2, 3, 16, 20, 21, 32, 51, 54, 62, 67], "pull_request": [3, 14, 53, 55, 64, 65], "purpl": [1, 3, 22, 23, 25, 27, 37, 38, 63, 67, 70, 77], "purple team": [27, 76], "purple team collabor": 77, "purple_servic": 69, "purple_team_collabor": 77, "purple_team_coordin": 77, "purple_team_servic": 69, "purpleteam": 76, "purpleteamservic": 69, "purpos": [7, 12, 14, 31, 40, 42, 70, 73, 79], "pursu": [76, 77, 78, 79], "pursuit": [76, 77, 78], "purview": 42, "push": [2, 3, 19, 21, 32, 42, 53, 55, 64, 65, 79], "push_servic": 42, "pushnotificationservic": 42, "put": [5, 8, 13, 18, 32, 33], "pv": [17, 31], "pvc": [17, 31, 32], "pwa": 20, "py": [2, 3, 12, 14, 16, 19, 20, 21, 26, 32, 33, 51, 53, 54, 55, 56, 64, 65], "py311": 18, "pyc": 20, "pycqa": [18, 54, 64], "pydant": [13, 18, 25, 26, 51], "pyi": 18, "pylint": [20, 51], "pylinten": 20, "pylintrc": 51, "pyproject": [18, 21], "pyramid": [21, 64], "pytest": [0, 2, 18, 19, 20, 21, 43, 51, 53, 64], "python": [0, 1, 2, 3, 6, 8, 12, 13, 14, 16, 17, 20, 24, 25, 28, 32, 33, 35, 36, 38, 48, 50, 51, 53, 54, 55, 62, 63, 64, 65, 66, 67, 75, 80], "python3": [14, 18, 20, 28, 32, 54], "python_class": [64, 65], "python_fil": [64, 65], "python_funct": [64, 65], "pythondontwritebytecod": 35, "pythonhashse": 35, "pythonpath": 20, "pythonunbuff": 35, "q": [16, 32, 65], "q1": [37, 52], "q2": 37, "q4": [75, 80], "qr": [38, 67], "qr_code": 7, "qradar": 79, "qualit": 72, "qualiti": [19, 40, 47, 48, 50, 51, 55, 69, 73, 76, 77, 78, 79], "quality_g": 18, "quantif": [69, 73, 74, 77, 78, 79], "quantifi": [3, 27, 71, 74, 76, 77, 78, 79], "quantit": [8, 10, 12, 23, 27, 71, 72, 73, 74, 76, 77], "quantum": [11, 52, 73, 78], "quarterli": [40, 42, 43, 45, 46, 48, 50, 51, 52, 54, 72, 73, 75, 77, 78, 79, 80], "queri": [3, 6, 8, 11, 12, 13, 15, 16, 17, 18, 20, 25, 31, 32, 33, 34, 35, 39, 41, 54, 55, 57, 58, 61, 62, 63, 64, 66, 67, 69, 70, 79], "query_cache_s": [34, 35], "query_start": [33, 34], "question": [0, 1, 2, 3, 18, 19, 20, 47, 74, 78, 79], "queue": [4, 15, 24, 26, 59, 79], "queuepool": 35, "quick": [0, 1, 35, 65, 79], "quickest": 67, "quickli": [19, 38, 45, 65, 66], "quiet": 65, "quit": 64, "quota": [25, 26, 70], "r": [2, 3, 14, 16, 17, 18, 20, 28, 32, 35, 46, 48, 51, 53, 54, 55, 56, 64, 65, 66], "r5": 15, "r6g": 16, "ra": 40, "race": 53, "radiu": [0, 2, 4, 5, 7, 8, 9, 10, 11, 12, 13, 15, 18, 19, 20, 21, 22, 23, 28, 29, 30, 34, 35, 36, 37, 38, 39, 41, 42, 44, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 56, 57, 58, 61, 62, 64, 65, 66, 68, 70, 72, 73, 74, 75, 76, 77, 78, 79, 80], "radius_postgres_data": 66, "railwai": 77, "rais": [18, 19, 51, 57, 58, 65], "raise_for_statu": [7, 58], "ram": [14, 16, 20, 28, 35, 37, 38, 67], "rand": 16, "random": [24, 25, 35, 36, 51, 52, 57], "random_forest": [26, 57], "random_forest_scor": 57, "random_page_cost": [34, 35], "random_st": 57, "randomforestclassifi": [26, 57], "randomint": 34, "rang": [9, 35, 53, 59, 80], "rank": 79, "ransom": 79, "ransomwar": [69, 76, 77, 79], "ransomware_scenario": 69, "rapid": [45, 50, 63, 79], "rapidli": [71, 76, 77, 78], "rasp": [50, 78], "rate": [3, 4, 13, 21, 23, 24, 25, 26, 27, 30, 32, 33, 34, 39, 40, 42, 45, 46, 47, 48, 50, 51, 52, 53, 54, 55, 62, 63, 64, 67, 68, 69, 70, 75, 76, 77, 78, 79, 80], "rate_limit_burst": 13, "rate_limit_en": 13, "rate_limit_exceed": 10, "rate_limit_request": 13, "rate_limit_requests_per_minut": 13, "rate_limit_storag": 13, "rate_limit_window": 13, "ratelimit": [5, 6, 8, 10], "rather": 36, "ratio": 32, "rational": [4, 55, 74, 78], "raw": [16, 51, 79], "rbac": [7, 29, 30, 39, 42, 44, 49, 51, 52, 62, 63, 78], "rbac_analysi": 68, "rbacservic": 42, "rc": 56, "rc4": 55, "rcfile": 51, "rd": [5, 14, 16, 17, 29, 30, 31, 33, 68], "rdp": [69, 77], "rds_instanc": 68, "rds_snapshot": 31, "re": [18, 38, 51, 54, 66, 71], "reach": [2, 17, 18, 19, 48, 55, 65, 66, 67, 78], "react": [18, 19, 20, 24, 25, 26, 36], "react_app_api_url": [18, 20], "react_app_api_vers": 20, "react_app_debug": 20, "react_app_enable_debug_tool": 20, "react_app_enable_mock_data": 20, "react_app_environ": 20, "reactiv": 42, "read": [1, 4, 14, 15, 18, 19, 20, 21, 25, 28, 29, 30, 34, 41, 54, 64, 67], "readabl": [18, 65], "reader": [30, 67], "readi": [1, 17, 19, 20, 21, 23, 24, 25, 31, 32, 33, 38, 45, 67, 70, 72, 73, 76, 77, 78, 79], "readinessprob": 17, "readytous": 31, "real": [0, 1, 3, 4, 8, 9, 12, 15, 19, 21, 23, 24, 25, 27, 28, 29, 30, 34, 35, 36, 37, 39, 40, 42, 44, 48, 49, 52, 53, 58, 61, 62, 63, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78], "real_tim": 42, "real_time_alert": [4, 29, 41], "realist": [8, 27, 35, 53, 63, 69, 71, 74, 76, 77, 80], "realiti": 73, "realiz": 73, "reason": [18, 19, 47, 49, 53, 58], "reassess": 12, "rebas": 21, "reboot": 17, "rebuild": [2, 32, 66, 73], "recal": 57, "receipt": 47, "receiv": 40, "recent": [17, 27, 33, 35, 38, 42, 50, 57], "recept": 79, "recipi": [41, 68], "recogn": [19, 23, 27, 47], "recognit": [9, 11, 25, 26, 27, 72, 73, 74, 76, 77, 78, 79], "recommend": [1, 6, 10, 14, 16, 18, 20, 26, 27, 37, 41, 42, 46, 47, 50, 51, 52, 53, 54, 55, 56, 62, 63, 67, 69, 71, 72, 73, 74, 76, 77, 78, 79], "reconnaiss": [76, 77, 79], "reconstruct": [45, 79], "record": [5, 16, 17, 29, 31, 35, 40, 42, 46, 51, 53, 63, 74, 77, 79], "record_request": 53, "recov": [56, 78, 79], "recover": 62, "recoveri": [10, 14, 22, 25, 27, 29, 37, 38, 40, 42, 51, 53, 62, 63, 69, 71, 73, 74, 76, 77, 78, 79, 80], "recovery_namespac": 31, "recovery_region": 31, "recovery_time_estim": [6, 71], "recreat": 17, "recruit": 77, "rectif": 12, "recur": 79, "recurr": [43, 45], "recycl": 35, "red": [1, 3, 22, 23, 25, 27, 37, 38, 39, 58, 63, 67, 70, 75, 76, 78], "red team": [27, 77], "red team blue team integr": 76, "red_team_find": 69, "red_team_report": 69, "redeploi": 17, "redesign": 21, "redhat": 20, "redi": [0, 13, 14, 15, 16, 20, 24, 25, 26, 28, 29, 32, 33, 34, 36, 37, 38, 43, 59, 64, 66, 67], "redirect": [38, 66], "redis_cach": 34, "redis_cli": [35, 59], "redis_endpoint": 16, "redis_host": [32, 33, 43], "redis_pool_max_connect": 13, "redis_pool_s": [13, 28, 34], "redis_retry_on_timeout": 13, "redis_socket_connect_timeout": 13, "redis_socket_timeout": 13, "redis_url": [13, 14, 20, 28], "redoc": [13, 20], "redoc_url": [13, 20], "redteam": 77, "reduc": [0, 8, 17, 24, 30, 34, 49, 52, 63, 65, 66, 71, 75, 76, 79], "reduct": [3, 23, 24, 25, 36, 52, 63, 72, 73, 76, 77, 78, 79, 80], "redund": [15, 30, 39, 42, 46, 62, 68, 76, 77, 78, 79], "redux": 20, "ref": [3, 14], "refactor": [21, 65], "refer": [0, 1, 5, 18, 19, 22, 29, 39, 46, 47, 56, 61, 62, 63, 71, 72, 74, 75, 80], "referenc": [0, 72], "referr": 47, "refin": [72, 75, 76, 77, 78], "reflect": [19, 21, 44, 47, 48], "refresh": [66, 71, 73, 78], "refresh_token": 7, "refus": [38, 66], "regard": 56, "regener": 66, "regex": 51, "region": [14, 15, 16, 29, 30, 31, 42, 65, 67, 68], "regist": 40, "registr": [25, 40, 68], "registri": [16, 58, 77, 79], "registry_kei": 58, "regress": [62, 65, 78], "regul": [12, 40, 44, 51, 72, 73, 77, 78, 79, 80], "regular": [0, 1, 3, 4, 7, 11, 12, 15, 16, 18, 21, 22, 23, 32, 37, 39, 40, 41, 42, 45, 46, 47, 48, 49, 50, 53, 54, 62, 63, 64, 67, 68, 69, 70, 71, 73, 74, 76, 77, 78, 79, 80], "regular_us": 53, "regularli": [13, 20, 21, 30, 55, 64, 68, 70, 74, 75, 79], "regulatori": [4, 8, 10, 12, 22, 23, 24, 27, 37, 40, 43, 44, 46, 47, 50, 61, 62, 63, 70, 71, 74, 76, 77, 78, 79], "regulatory_change_manag": 72, "regulatory_compliance_oversight": 73, "regulatory_context": 80, "regulatory_fin": 80, "regulatory_framework_administr": 72, "regulatory_liaison_access": 78, "regulatory_relationship_manag": 72, "regulatory_requir": [42, 80], "reindex": [33, 61], "reinfect": 79, "reinforc": 76, "reinstal": 20, "reject": [46, 51, 53, 65], "rel": [9, 10], "rel_data": 59, "relat": [0, 1, 3, 6, 7, 13, 19, 21, 40, 44, 45, 47, 56, 65, 73, 75, 78, 79], "related_techniqu": 75, "relationship": [3, 8, 15, 24, 25, 26, 27, 30, 38, 45, 56, 57, 58, 59, 62, 63, 66, 67, 69, 71, 72, 73, 74, 76, 77, 78, 79, 80], "relationship_count": 66, "relationship_typ": [5, 61], "relationship_type_enum": 61, "relationship_weight": 59, "relax": [13, 14], "releas": [3, 14, 15, 16, 19, 62, 63, 67, 68, 70], "relev": [2, 18, 41, 42, 44, 51, 58, 68, 69, 76, 77, 78, 79], "relevance_scor": 42, "relevant_actor": 42, "reliabl": [15, 21, 41, 62, 65, 70, 76, 77, 79], "relief": 33, "reload": [3, 14, 20, 21, 33, 66], "remain": [5, 6, 8, 10, 17, 18, 35], "remedi": [12, 24, 25, 30, 37, 38, 40, 41, 42, 43, 45, 46, 47, 48, 51, 52, 55, 56, 67, 69, 70, 71, 72, 74, 76, 77, 78, 79], "remediation_r": 54, "remediation_step": 56, "rememb": [72, 73, 76, 77, 78, 79], "remember_m": 7, "remind": 4, "remot": [5, 6, 15, 19, 20, 21, 39, 40, 47, 49, 55, 77, 78], "remote_addr": [34, 35], "remote_sid": 56, "remov": [4, 5, 17, 22, 29, 35, 40, 45, 50, 58, 64, 66, 71, 79], "render": [18, 54, 79], "renew": [4, 12, 17, 52, 77], "renewal_notice_dai": 12, "reoccurr": 79, "repair": [17, 40], "repeat": [6, 21, 71, 79, 80], "repeatedli": 17, "replac": [22, 36, 49, 52, 54, 61, 78, 79], "replic": [15, 29, 61, 77], "replica": [14, 15, 17, 25, 29, 32, 33, 34, 41, 43], "repo": [16, 18, 54, 64], "report": [0, 1, 3, 4, 7, 8, 11, 15, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 29, 32, 33, 36, 37, 38, 39, 42, 43, 46, 48, 51, 53, 62, 63, 65, 67, 69, 70, 71, 72, 73, 74, 75, 76, 78, 80], "report_respons": 8, "report_schedul": 41, "reportpath": 55, "reports_dir": 54, "repositori": [3, 9, 16, 18, 19, 27, 28, 38, 55, 62, 67, 72, 75, 77, 78, 79], "repres": [3, 22, 23, 27, 72, 73, 76, 77, 78, 79], "represent": [58, 62, 71, 79], "reproduc": [19, 47, 66, 67], "reproduct": 77, "repudi": [46, 48, 51], "reput": [40, 72, 73, 77, 78, 79], "req": [34, 35, 66], "req_123456789": 18, "req_1234567890": 5, "req_1639234567": 8, "request": [0, 3, 5, 6, 7, 8, 9, 10, 12, 13, 15, 17, 18, 20, 21, 22, 24, 26, 27, 32, 33, 34, 35, 37, 39, 40, 41, 44, 46, 47, 51, 53, 54, 58, 60, 62, 63, 64, 65, 66, 67, 71, 72, 74, 77, 78], "request_count": [32, 35, 53], "request_dur": 35, "request_id": [4, 5, 8, 12, 18], "request_timeout": [13, 30], "request_typ": 12, "requests_per_second": 34, "requir": [0, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 18, 19, 22, 29, 30, 31, 35, 38, 39, 40, 41, 42, 44, 45, 47, 48, 50, 53, 64, 66, 68, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80], "require_approv": 4, "require_justif": 4, "require_permiss": 18, "required_field": 56, "required_privileg": [6, 61], "required_resourc": [6, 61], "requireddropcap": 51, "requires_approv": 4, "rerun": 71, "research": [23, 52, 72, 73, 76, 77, 78, 79], "research_innovation_access": 76, "reserv": [35, 47, 65], "reset": [2, 5, 6, 8, 10, 12, 20, 32, 33, 38, 66, 70, 79], "residu": [52, 78], "resili": [40, 56, 62, 73, 76, 77, 78, 79], "resist": [11, 52, 78], "resolut": [0, 1, 17, 33, 45, 49, 51, 62, 76, 77, 78, 79], "resolution_tim": 42, "resolv": [17, 18, 19, 21, 32, 33, 43, 47, 50, 52, 55, 58, 66], "resourc": [0, 3, 4, 11, 13, 14, 16, 18, 19, 23, 24, 25, 27, 29, 31, 32, 33, 35, 39, 40, 41, 42, 44, 45, 46, 50, 63, 66, 67, 68, 69, 70, 71, 72, 76, 77, 80], "resource_group": 30, "resource_id": [4, 41], "resource_level": 80, "resource_sensitivity_weight": 4, "resource_typ": [4, 41], "resources_alloc": 42, "respect": [19, 47, 68], "respond": [12, 19, 33, 42, 47, 52, 53, 56, 66, 71, 76, 77, 78, 79], "respons": [1, 4, 7, 10, 11, 12, 13, 14, 15, 18, 21, 22, 23, 24, 27, 29, 32, 34, 35, 36, 37, 38, 39, 40, 44, 48, 50, 53, 54, 58, 59, 62, 63, 64, 65, 67, 70, 80], "response1": 53, "response_act": 42, "response_data": 12, "response_model": [20, 57, 58], "response_procedur": 42, "response_team": 42, "response_tim": [32, 33, 42, 54], "response_time_avg": 32, "response_time_dai": 12, "response_time_p50": 34, "response_time_p95": 34, "response_time_p99": 34, "rest": [3, 7, 8, 11, 15, 24, 26, 27, 29, 30, 33, 39, 40, 41, 44, 46, 49, 51, 62, 63, 67, 68, 70, 80], "restart": [2, 13, 17, 20, 31, 33, 38, 66, 67], "restart_polici": 35, "restartcount": 33, "restaur": 80, "restor": [15, 16, 17, 20, 31, 40, 43, 45, 70, 73, 76, 77, 79], "restore_system": 42, "restore_tim": 31, "restrict": [4, 12, 42, 50, 51, 67, 70, 79], "restructuredtext": 3, "result": [0, 3, 7, 8, 14, 18, 19, 21, 24, 25, 32, 35, 40, 45, 46, 47, 50, 51, 54, 58, 59, 63, 64, 65, 66, 67, 70, 71, 72, 73, 76, 77, 78, 79, 80], "result_cache_s": 34, "results_fil": [53, 55], "resumpt": 45, "retail": 80, "retent": [12, 15, 16, 27, 30, 31, 39, 40, 41, 44, 51, 62, 67, 68, 70, 73, 78], "retention_period": 42, "retrain": [24, 57], "retrain_ml_model": 57, "retrain_model": 57, "retrained_at": 57, "retri": [8, 15, 30, 35, 53, 64], "retriev": [5, 6, 8, 9, 10, 18, 41, 64, 70], "retrospect": 46, "return": [6, 7, 9, 10, 18, 19, 21, 28, 35, 42, 45, 46, 49, 51, 53, 54, 55, 57, 58, 59, 61, 63, 64, 65, 66, 69, 72, 73, 76, 77, 78, 79], "return_except": 35, "return_valu": [19, 65], "reus": [19, 24, 26, 36, 64], "reusabl": [15, 22, 24, 25, 56], "rev": [18, 31, 54, 64], "reveal": 51, "reveng": 80, "revenu": [73, 78], "revers": [4, 36, 42, 53, 56, 57, 69, 77, 78], "revert": 4, "review": [0, 1, 3, 12, 16, 18, 22, 27, 28, 29, 30, 34, 36, 37, 38, 39, 40, 44, 45, 47, 48, 50, 53, 54, 55, 56, 58, 62, 64, 65, 67, 68, 70, 71, 72, 73, 74, 75, 77, 78, 79, 80], "review_frequ": 42, "review_frequency_month": 12, "review_id": [4, 46], "review_typ": [4, 46], "reviewer_id": 4, "revis": [18, 20, 43, 56, 78], "revoc": [4, 45], "revok": 4, "revolution": 27, "rf": [20, 31, 53], "rf_proba": 57, "rg": [30, 68], "right": [19, 29, 35, 38, 39, 44, 47, 48, 51, 63, 72, 78, 79, 80], "rigor": 2, "risk": [1, 3, 6, 11, 12, 15, 18, 22, 23, 24, 25, 27, 29, 35, 38, 39, 40, 41, 42, 43, 46, 47, 51, 53, 54, 55, 56, 57, 58, 62, 68, 70, 74, 75, 76, 77, 79], "risk assess": [48, 50, 78], "risk manag": [72, 73], "risk_appetite_approv": 73, "risk_appetite_definit": 73, "risk_assess": [4, 42], "risk_based_authent": 42, "risk_factor": 69, "risk_level": [8, 12, 57, 75], "risk_manag": 29, "risk_management_leadership": 73, "risk_quantification_author": 73, "risk_rat": 56, "risk_reduct": 80, "risk_scor": [5, 6, 8, 18, 19, 21, 35, 46, 59, 61, 65, 68, 69, 71], "risk_score_increas": 80, "risk_strategy_develop": 73, "risk_threshold": 54, "risk_toler": 80, "risk_treat": 42, "risk_trend_30d": 57, "risk_trend_7d": 57, "risk_weight": 35, "riskdesc": 53, "riskforecastingservic": 26, "riskscor": 18, "rm": [3, 14, 20, 31, 32, 40, 53, 66], "roadmap": [1, 24, 26, 27, 42, 69, 72, 73, 74, 76, 78, 79], "robust": [19, 25, 27, 29, 36, 48, 52, 54, 56, 57, 59, 61, 62, 78], "roc": 57, "roc_auc": 57, "roc_auc_scor": 57, "roe": 77, "roi": [3, 23, 27, 63, 72, 73, 74, 76, 77, 78, 79, 80], "role": [0, 7, 11, 16, 22, 23, 26, 27, 28, 29, 30, 37, 39, 40, 41, 42, 44, 45, 46, 47, 49, 51, 61, 62, 63, 66, 67, 69, 75, 80], "role_based_access_control": 42, "role_id": [4, 66], "role_permiss": 42, "role_templ": 4, "roles_respons": 42, "roll": [24, 70], "rollback": [4, 15, 20, 21, 27, 33, 43, 64, 70], "rollout": [14, 16, 17, 31, 32, 33, 36, 43], "root": [35, 43, 45, 73, 79], "root_caus": 42, "root_modul": 51, "rootkit": 77, "rop": 77, "rotat": [7, 11, 13, 29, 30, 33, 39, 40, 43, 53, 68, 70, 77], "round": [32, 78], "rout": [4, 11, 16, 20, 41, 61, 67, 68, 71, 74, 77, 79], "route53": [16, 17, 31], "router": [20, 24, 25, 58, 61, 68, 79], "routin": [4, 24, 45, 70, 79], "row": 61, "rp": 40, "rpc": 77, "rpo": [15, 29, 40, 70, 78], "rsa": [66, 79], "rss": 35, "rst": [1, 3], "rsync": 3, "rtd": [3, 18], "rto": [15, 29, 40, 70, 78], "rule": [4, 15, 16, 17, 18, 24, 25, 29, 36, 39, 40, 42, 46, 48, 50, 51, 53, 54, 62, 63, 66, 68, 69, 70, 75, 76, 77, 79], "rule_001": 69, "rule_002": 69, "rule_003": 69, "run": [3, 5, 6, 10, 14, 16, 17, 19, 20, 21, 28, 30, 31, 32, 33, 34, 35, 41, 43, 51, 53, 55, 57, 62, 63, 66, 68, 69, 71, 75], "run_analysi": 64, "run_batch_predict": 57, "run_batch_threat_predict": 57, "run_daily_threat_predict": 57, "run_in_executor": 59, "runasani": 51, "runasus": 51, "runbook": [14, 16, 17, 29, 32, 43], "runtim": [13, 30, 39, 48, 50, 52, 53, 78], "russia": 80, "s3": [5, 15, 16, 30, 31, 66, 68], "s3_bucket": 68, "s_client": [16, 17, 32, 66], "sa": [18, 30, 56], "saa": 77, "sabotag": [76, 77], "safari": [38, 67, 79], "safe": [39, 45, 51, 65], "safeguard": [39, 56, 77, 78, 79], "safer": 77, "safeti": [0, 2, 18, 20, 21, 32, 46, 47, 48, 50, 51, 54, 55, 64, 77, 79], "safety_result": 54, "sale": [74, 77], "sam": 53, "same": [2, 3, 5, 6], "saml": [7, 47, 79], "sampl": [14, 19, 20, 65], "sample_asset": [19, 64, 65], "sample_attack_path": 65, "san": [52, 79], "sandbox": [8, 61, 77], "sanit": [44, 46, 53, 64, 66], "sar": 46, "sarban": [72, 80], "sast": [0, 26, 39, 42, 44, 51, 52, 53, 64], "sast_scan_dur": 55, "sast_scan_duration_second": 55, "sast_scans_tot": 55, "sastresultsprocessor": 55, "satisfact": [3, 45, 63, 72, 73, 76, 78, 79], "satisfi": 52, "save": [26, 34, 35, 36, 38, 52, 65, 67, 71], "scada": 77, "scalabl": [22, 24, 29, 35, 41, 57, 59, 61, 62, 64, 70, 76, 77, 78], "scale": [0, 1, 3, 4, 6, 11, 14, 15, 17, 22, 24, 27, 28, 29, 32, 33, 35, 37, 41, 43, 47, 57, 61, 62, 63, 70, 74, 75, 76, 77, 79], "scaler": 57, "scaletargetref": 34, "scan": [5, 8, 14, 16, 18, 20, 21, 30, 32, 33, 38, 39, 40, 46, 47, 52, 53, 55, 62, 64, 67, 68, 70, 72, 74, 78, 79], "scan_opt": 68, "scan_rang": 68, "scan_result": 54, "scan_subnet": 68, "scan_typ": 53, "scanner": [43, 53, 61, 64, 70], "scenario": [1, 3, 7, 8, 11, 16, 19, 23, 26, 27, 28, 34, 45, 47, 53, 59, 61, 62, 65, 72, 74, 78, 80], "scenario_1639234567": 6, "scenario_development_author": 76, "scenario_document": 76, "scenario_id": [6, 61], "scenario_nam": [6, 63, 71, 80], "scenarionam": 8, "schedul": [4, 12, 14, 15, 16, 31, 41, 46, 48, 50, 54, 57, 61, 70, 72, 77, 79], "schedule_data_delet": 42, "schedule_discoveri": 68, "schema": [3, 16, 17, 20, 24, 25, 36], "schemanam": [32, 33, 35, 61], "scheme": [3, 35], "scienc": [76, 77], "scikit": [24, 25, 26], "scope": [3, 4, 21, 27, 30, 42, 43, 45, 46, 50, 51, 64, 67, 68, 69, 71, 72, 74, 77, 79], "scope_descript": 56, "score": [3, 4, 5, 6, 9, 12, 22, 25, 26, 27, 35, 37, 38, 41, 42, 45, 47, 48, 52, 56, 57, 58, 62, 63, 68, 70, 74, 75, 76, 77, 79, 80], "scorecard": 78, "scr": 46, "scrape": [16, 17, 33], "scrape_config": 34, "scrape_interv": 34, "screen": [38, 66], "screenshot": [1, 19, 47], "script": [0, 2, 3, 17, 20, 27, 28, 31, 32, 33, 36, 39, 43, 47, 48, 51, 53, 54, 55, 62, 64, 75, 77, 80], "scroll": 19, "sda": 45, "sdk": [9, 10, 24, 27, 36, 62, 63, 75, 80], "sdlc": [40, 54], "sdn": 78, "sdp": 78, "seaborn": 26, "sealed_at": 41, "seamless": [0, 15, 27, 36, 55, 59, 72, 76, 77, 79], "search": [1, 5, 8, 15, 18, 19, 27, 28, 33, 35, 38, 54, 58, 59, 64, 66, 71, 79], "search_cas": 58, "searchabl": 79, "sec": [34, 35], "second": [6, 9, 11, 13, 15, 24, 25, 27, 31, 34, 35, 38, 41, 62, 63, 65, 66, 67, 71, 74, 75], "secondari": 63, "secret": [3, 7, 8, 11, 13, 14, 17, 20, 25, 28, 30, 31, 36, 40, 43, 46, 48, 49, 50, 52, 54, 55, 66, 67, 68, 77], "secret_access_kei": [30, 68], "secret_detect": 51, "secret_kei": [13, 14, 20, 28, 66], "secrets_manag": 61, "secretsmanag": 16, "secretstr": 16, "section": [0, 2, 8, 19, 27, 34, 39, 52, 62, 71, 74], "sector": [75, 77, 80], "secur": [4, 5, 6, 8, 10, 12, 19, 20, 23, 28, 34, 35, 38, 41, 50, 56, 57, 58, 59, 61, 65, 66, 69, 72, 75, 79], "secure_app_password": 16, "secure_development_lifecycl": 78, "secure_password": 8, "secure_random": 49, "securepassword123": [7, 28], "security architectur": 78, "security assess": 48, "security autom": 54, "security fix": 49, "security improv": 76, "security oper": 79, "security patch": 50, "security platform": 27, "security postur": 52, "security process": 46, "security review": [46, 49], "security summari": 52, "security test": 48, "security_admin": 4, "security_analysi": 68, "security_analyst": [4, 8], "security_approv": 54, "security_architect": 7, "security_assess": 8, "security_awar": 42, "security_control": 42, "security_controls_effect": 80, "security_critical_issu": 54, "security_devic": 61, "security_factor": 59, "security_featur": 29, "security_g": 54, "security_gate_check": 54, "security_governance_oversight": 73, "security_incid": 58, "security_investment_approv": 73, "security_monitor": [12, 42, 54], "security_object": 42, "security_polici": 42, "security_posture_chang": 68, "security_report_format": 54, "security_report_gener": 54, "security_scan": [18, 48, 51, 54, 68], "security_scan_success": 54, "security_standard_develop": 78, "security_strategy_develop": 73, "security_team_manag": 73, "security_testing_framework": 78, "security_vulnerabilities_crit": 54, "securityautofix": 54, "securitycontext": [16, 17], "securitycontrol": 42, "securitycontrolassess": 46, "securityg": 54, "securityincid": [26, 42, 58], "securitymaturityassess": 42, "securitymonitor": 54, "securityreportgener": 54, "securityreviewtrack": 46, "securityscanfailur": 54, "securitytrainingservic": 42, "sed": 21, "see": [3, 7, 9, 10, 12, 13, 16, 19, 27, 38, 39, 75, 79, 80], "seed_compliance_framework": 56, "seed_nist_csf": 56, "seen": 75, "segment": [6, 15, 38, 39, 42, 44, 46, 50, 52, 59, 67, 68, 69, 70, 71, 76, 77, 78, 79], "segmented_by_funct": 42, "segreg": [4, 18, 40], "select": [14, 16, 17, 18, 20, 27, 28, 31, 32, 33, 34, 35, 38, 43, 53, 54, 55, 61, 64, 66, 71, 72, 76, 77, 78, 80], "selector": [14, 17, 32], "selenium": [0, 64], "self": [0, 7, 18, 19, 25, 26, 35, 36, 40, 42, 46, 50, 53, 54, 55, 57, 58, 59, 63, 64, 65, 66, 78, 79], "selinux": 51, "semant": [21, 48, 50, 55], "semgrep": [0, 21, 46, 48, 50, 51, 54, 55, 64], "semi": 40, "semi_autom": 56, "semver": 21, "send": [4, 8, 17, 18, 32, 33, 67, 68], "send_discovery_ev": 68, "send_kei": 64, "send_timeout": 35, "senior": [17, 33, 40, 43, 51, 72, 76, 77, 78, 79], "sensit": [4, 11, 19, 21, 30, 39, 44, 45, 46, 47, 49, 51, 53, 63, 68, 69, 71, 77, 78, 79, 80], "sensitive_asset": 69, "sensitive_data": 12, "sensitive_personal_data": 80, "sensitive_t": 53, "sentinel": 79, "sentinelon": 79, "separ": [4, 5, 6, 12, 24, 26, 36, 40, 41, 46, 63, 71, 79], "seq_scan": 32, "seq_tup_read": 32, "sequenc": [64, 65, 71, 74, 75, 79], "seri": [25, 26, 76, 78, 79], "serial": [49, 62], "serv": [3, 14, 26, 27, 52, 65, 74], "server": [1, 3, 5, 6, 7, 8, 13, 16, 18, 19, 20, 21, 24, 28, 30, 34, 35, 38, 41, 47, 53, 55, 61, 64, 65, 67, 68, 69, 70, 71, 77], "server_default": [18, 56], "server_nam": 35, "serverless": [30, 76, 77, 78, 79], "servernam": [16, 17, 32], "servic": [0, 2, 4, 5, 8, 13, 15, 16, 18, 19, 26, 28, 29, 31, 32, 33, 35, 36, 38, 42, 43, 45, 46, 47, 48, 50, 51, 53, 62, 63, 64, 65, 66, 67, 69, 70, 71, 72, 74, 76, 77, 78, 79], "service_account_frequency_dai": 4, "service_detect": [5, 68], "service_disruption_scor": 6, "serviceaccount": [16, 30], "servicemonitor": [16, 17], "servicenow": [28, 47, 67, 68], "servicenow_api_vers": [13, 28], "servicenow_batch_s": 13, "servicenow_create_incid": 13, "servicenow_incident_categori": 13, "servicenow_incident_subcategori": 13, "servicenow_inst": [13, 28], "servicenow_password": [13, 28], "servicenow_sync_en": 13, "servicenow_sync_interv": 13, "servicenow_timeout": 13, "servicenow_usernam": [13, 28], "servicenowcmdbsync": 68, "sess_123456789": 7, "session": [7, 13, 14, 15, 18, 20, 28, 34, 35, 39, 44, 46, 47, 50, 51, 53, 54, 55, 57, 58, 59, 64, 65, 66, 70, 71, 73, 77, 78, 79], "session1": 53, "session2": 53, "session_cookie_httponli": [13, 28], "session_cookie_max_ag": 13, "session_cookie_nam": 13, "session_cookie_samesit": [13, 28], "session_cookie_secur": [13, 28], "session_id": [7, 34, 41, 53], "session_invalidate_on_logout": 13, "session_manag": 29, "session_regenerate_on_login": 13, "session_ttl": 13, "sessionloc": [14, 57, 64, 65], "sessionmak": 64, "set": [0, 1, 2, 3, 4, 7, 11, 14, 17, 18, 19, 20, 21, 23, 24, 25, 26, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 42, 46, 51, 53, 54, 55, 57, 58, 59, 62, 64, 65, 66, 67, 68, 69, 71, 74, 76, 77, 78, 79, 80], "set_bad_file_permiss": [54, 55], "set_statu": 65, "set_trac": [2, 20, 65], "seterror": [18, 19], "setex": [35, 59], "setisanalyz": [18, 19], "setitem": 7, "setlevel": 30, "settlement": 77, "setup": [1, 3, 19, 21, 22, 25, 26, 27, 29, 37, 53, 55, 62, 65, 74, 77], "setup_18": 14, "setup_continuous_monitor": 80, "setuptool": 20, "sever": [5, 8, 16, 19, 21, 25, 26, 32, 33, 38, 40, 41, 42, 46, 48, 49, 50, 51, 52, 53, 54, 55, 58, 69, 76, 78, 79], "severity_bas": 42, "severity_map": 58, "severity_threshold": 58, "sh": [2, 3, 14, 16, 17, 31, 32, 33, 43, 68], "sha": [41, 49, 52, 54], "sha1": 55, "sha256": [8, 41, 49, 54], "shall": 63, "shape": 73, "shard": 34, "share": [3, 13, 20, 21, 23, 25, 40, 43, 46, 51, 63, 65, 67, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79], "shared_blks_hit": 61, "shared_blks_read": 61, "shared_buff": [34, 35, 66], "sharepoint": 77, "shell": [0, 28, 54, 66, 77], "shield": 42, "shift": [15, 23, 50, 54, 64, 79], "ship": 77, "short": [25, 31, 32, 45, 46, 53, 64, 65, 79], "short_term": 69, "shortcut": 79, "shorter": 71, "shortest": [35, 69, 71, 79], "shortest_path": 35, "should": [2, 19, 20, 21, 38, 44, 53, 64, 65], "show": [3, 14, 16, 17, 22, 38, 51, 60, 65, 66, 68, 79], "showcas": 78, "si": 77, "side": [8, 77], "side_by_sid": 75, "side_effect": 65, "siem": [0, 8, 29, 36, 40, 42, 43, 52, 61, 62, 68, 70, 74, 75, 77, 78, 79], "siem integr": 79, "siem_alert": 69, "siem_platform": 42, "siem_typ": 68, "siemintegr": [68, 69], "sig": 16, "sigma": 79, "sign": [7, 38, 39, 44, 66, 70, 77, 78, 79], "signatur": [8, 41, 46, 53, 58, 77, 79], "signature_algorithm": 41, "signature_and_anomali": 42, "signature_bas": 42, "signific": [2, 18, 42, 43, 45, 47, 50, 51, 76, 78, 79], "significantli": [0, 49, 79], "silver": 77, "sim_001": 80, "similar": [19, 75, 79, 80], "simpl": [35, 71], "simplifi": 79, "simul": [0, 1, 3, 8, 22, 23, 25, 27, 38, 42, 43, 45, 53, 63, 66, 67, 70, 71, 74, 76, 78, 79], "simulate_attack": [69, 80], "simulate_attack_custom": 80, "simulation_id": 80, "simulation_in_progress": 10, "simulation_paramet": 80, "simultan": [62, 63, 79], "sinc": [31, 32, 33], "singl": [7, 9, 14, 18, 19, 21, 35, 39, 44, 57, 62, 70, 77, 78, 79], "single_source_shortest_path": 35, "site": [3, 30, 39, 47, 53, 55], "situat": [4, 74, 76, 77, 78, 79], "six": 71, "size": [0, 3, 8, 26, 30, 32, 35, 41, 42, 61], "size_byt": 61, "skill": [45, 72, 73, 74, 76, 77, 78, 79], "skip": [2, 20, 53, 54, 55], "sklearn": 57, "sla": [0, 15, 40, 42, 48, 50, 51, 52, 62, 64], "slack": [43, 68, 77, 79], "sleep": [53, 68], "slim": [32, 35, 54], "slow": [17, 20, 21, 32, 33, 34, 35, 38, 61, 64, 65], "slow_queri": 61, "slowest": 65, "slowli": 66, "sm": [11, 42, 79], "small": [6, 19, 20, 21, 34, 38, 62, 67, 68], "smaller": [6, 14, 35], "smart": [3, 4, 77], "smb": 77, "smoke": 14, "sms_servic": 42, "smsservic": 42, "sn": 66, "snake_cas": 18, "snakeviz": 20, "snapshot": 16, "snapshot_id": 31, "snapshot_identifi": 16, "snapshotcreatetim": 31, "snyk": 50, "soap": 68, "soar": [0, 8, 22, 37, 42, 61, 62, 74, 78, 79], "soc": [1, 3, 22, 23, 24, 25, 26, 27, 37, 38, 39, 41, 44, 45, 46, 48, 49, 52, 56, 63, 67, 68, 69, 70, 77, 78], "soc oper": 27, "soc2": [24, 25, 29, 36, 39, 41, 45, 51, 68, 74], "soc2_control": 42, "soc2_report": 68, "soc_oper": [7, 42, 67], "social": [47, 73, 76, 77, 78, 79], "social_engineering_oper": 77, "societ": [77, 78], "sock": [14, 32], "sofaci": 80, "soft": [5, 25, 27, 61, 62, 63], "soft_delete_asset": 61, "softwar": [1, 40, 42, 52, 54, 61, 65, 67, 68, 70, 75, 76, 77, 78, 79], "solid": [1, 24, 36, 37], "solut": [0, 1, 2, 19, 27, 29, 38, 53, 62, 66, 67, 78, 79], "solv": 19, "some": [0, 13, 19, 21, 42, 43, 67], "some_funct": 65, "sonar": 55, "sonarqub": [42, 48, 50, 55], "soon": [8, 27, 38, 43, 44, 62, 74], "sophist": [23, 27, 69, 71, 72, 73, 75, 76, 77, 78, 79, 80], "sophistication_level": [42, 69, 80], "sort": [5, 16, 17, 21, 27, 32, 33, 34, 35, 42, 57, 59, 69], "sort_bi": 31, "sort_ord": 56, "sort_stat": 35, "sortimport": 20, "sourc": [2, 3, 6, 14, 16, 17, 18, 19, 20, 21, 23, 28, 31, 33, 34, 35, 36, 38, 40, 45, 47, 52, 53, 55, 58, 59, 62, 63, 64, 65, 66, 67, 68, 69, 71, 74, 75, 76, 77, 79], "source_asset": [18, 19, 69], "source_asset_id": [6, 8, 18, 34, 35, 61, 63, 64, 71], "source_asset_typ": 69, "source_control_id": 56, "source_dropdown": 64, "source_id": [35, 64, 65], "source_system": 58, "sourceasset": [18, 19], "sourceassetid": [8, 18, 19], "sourceid": [18, 35], "sourcetyp": 68, "sox": [6, 23, 27, 63, 71, 72, 78, 80], "sox_impact": 80, "sp": [30, 66], "space": [3, 14, 20, 28, 38, 66], "spam": 47, "span": 77, "spawn": 64, "speak": [47, 73], "spear": [76, 77, 79], "spec": [14, 16, 17, 22, 31, 32, 33, 34, 37, 51], "special": [3, 15, 19, 27, 72, 74, 76, 78, 79], "specialist": [72, 76, 77, 79], "specif": [0, 1, 3, 4, 5, 6, 7, 8, 9, 10, 12, 16, 17, 18, 19, 20, 21, 23, 25, 27, 30, 34, 37, 38, 39, 41, 45, 46, 53, 55, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 76, 77, 78, 79, 80], "specifi": [9, 10, 40, 79, 80], "speed": [21, 79], "speedup": 34, "spell": 3, "spent": 79, "sphinx": [0, 1, 18, 24, 26], "spike": [33, 34], "split": 57, "splunk": [42, 68, 79], "spoof": [46, 48, 51], "spot": [74, 76], "spread": 79, "sprint": [21, 24], "sql": [0, 13, 17, 18, 19, 20, 21, 30, 35, 39, 43, 44, 46, 47, 51, 53, 54, 55, 56, 64, 67, 68, 77], "sql_inject": 54, "sql_payload": 53, "sqlalchemi": [18, 35, 58, 64], "squash": 21, "src": [20, 53, 64], "ss": 68, "ssd": [16, 20, 28, 35], "ssh": 77, "ssl": [13, 14, 24, 25, 28, 32, 37, 38, 68, 70], "ssl_ca_cert_path": 13, "ssl_cert_path": 13, "ssl_key_path": 13, "ssl_verify_mod": 13, "sso": [7, 39, 44, 47, 62, 70, 77, 78, 79], "st": [16, 30, 66], "stabil": [21, 72], "stabl": [16, 20, 21, 42, 53, 54], "stack": [14, 16, 24, 25, 26, 29, 36, 46, 50, 55, 67, 76, 77, 78, 79], "staff": 12, "stage": [0, 5, 21, 23, 27, 28, 30, 35, 43, 48, 51, 54, 68, 70, 76, 77, 79], "stai": [20, 70, 76, 77, 78, 79], "stakehold": [17, 21, 23, 27, 33, 40, 43, 45, 51, 52, 70, 71, 72, 74, 76, 77, 78, 79, 80], "stakeholder_commun": [73, 76, 77], "stamp": 17, "standalon": 28, "standard": [4, 5, 6, 8, 9, 10, 12, 15, 20, 22, 23, 27, 34, 40, 41, 43, 44, 45, 46, 47, 48, 50, 51, 62, 63, 70, 72, 73, 76, 77, 78, 79], "standard_user_frequency_dai": 4, "standardscal": 57, "start": [6, 11, 13, 18, 21, 31, 32, 33, 34, 35, 43, 49, 53, 55, 57, 59, 64, 66, 67, 68, 69, 74], "start_continuous_monitor": 68, "start_dat": 41, "start_discoveri": [5, 68], "start_monitor": 53, "start_period": 35, "start_tim": [35, 41, 53, 57, 65], "started_at": 5, "starts_with": 31, "startswith": 65, "startup": [13, 77], "stat": [2, 20, 30, 31, 32, 33, 34, 35, 38, 66, 67], "state": [12, 15, 18, 20, 31, 32, 34, 35, 42, 53, 65, 69, 76, 77, 78, 79], "stateless": 15, "statement": [30, 65, 73], "static": [3, 13, 18, 21, 25, 34, 35, 36, 39, 44, 50, 51, 52, 53, 64, 77, 78], "static_config": 34, "staticmethod": 35, "statist": [4, 10, 17, 23, 30, 32, 33, 61, 62, 74, 75, 76, 78, 79, 80], "statsd": 54, "statu": [2, 4, 6, 7, 10, 11, 12, 14, 16, 17, 28, 30, 31, 32, 33, 35, 38, 40, 41, 45, 46, 49, 51, 53, 55, 56, 57, 58, 61, 63, 64, 65, 66, 68, 69, 70, 72, 73, 75, 78, 79], "status_cod": [53, 54, 57, 58, 64], "status_page_token": [32, 33], "status_upd": 33, "statuscod": 18, "statuspag": [32, 33], "stddev_tim": 32, "stdin": [14, 16], "stealth": [23, 27, 74, 76, 77], "stealth_operation_manag": 77, "steer": 78, "steganographi": 77, "step": [0, 3, 14, 23, 27, 31, 33, 43, 45, 46, 47, 53, 55, 62, 66, 67, 70, 77, 78, 79], "stewardship": 73, "stix": [9, 13, 27, 63, 67, 70, 74, 75, 80], "stop": [45, 65, 67, 77], "storag": [7, 12, 14, 15, 16, 20, 22, 24, 28, 29, 30, 31, 32, 34, 37, 40, 41, 42, 45, 57, 59, 61, 62, 63, 66, 67, 68, 70, 71, 76, 77, 79], "storage_capac": 29, "storage_s": 15, "storageaccount": 30, "storageclass": 17, "store": [7, 8, 13, 15, 24, 30, 35, 40, 42, 47, 51, 57, 58, 61, 67, 68, 71, 77], "stori": 50, "storytel": 78, "str": [7, 18, 26, 30, 35, 42, 53, 54, 55, 57, 58, 59, 65], "strateg": [3, 22, 23, 27, 37, 45, 46, 50, 62, 63, 74, 77, 79], "strategi": [0, 8, 14, 25, 27, 29, 34, 40, 43, 46, 55, 61, 62, 63, 64, 65, 70, 71, 72, 73, 74, 76, 77, 78, 79], "strategic secur": 73, "strategic_campaign_author": 77, "strategic_compliance_author": 72, "strategic_planning_access": 78, "strategic_program_author": 76, "strategic_risk_assess": 78, "strategic_security_approv": 73, "strategic_security_oversight": 73, "strategic_threat_analysi": 79, "stratifi": 57, "stream": [21, 30, 38, 66], "streamlin": [22, 24, 78, 79], "strength": [46, 62], "strengthen": 71, "stress": [2, 34, 62, 64, 72], "strict": [11, 13, 28, 42, 64, 65, 77], "strictli": 77, "stride": [48, 51, 78], "strike": 80, "string": [5, 6, 7, 8, 13, 18, 19, 26, 54, 56, 57, 58, 63, 65, 70], "string_to_arrai": 61, "stringifi": 7, "strip": [18, 53], "strive": 79, "strong": [4, 7, 11, 13, 29, 38, 41, 42, 46, 51, 53, 67, 70, 73, 78, 79], "structlog": 18, "structur": [4, 12, 13, 18, 21, 24, 25, 26, 30, 36, 45, 46, 47, 50, 59, 63, 70, 71, 72, 73, 76, 77, 78, 79], "studi": [23, 78], "studio": [76, 77], "style": [20, 21], "sub": [7, 9, 11, 15, 27, 34, 35, 41, 54, 62, 63, 71, 74, 75], "subgraph": 30, "subject": [16, 17, 22, 29, 32, 37, 39, 47, 48, 51, 72, 78, 79, 80], "subject_id": 42, "submiss": 72, "submit": [3, 4, 7, 12, 51, 62, 67, 74], "submitted_at": 12, "subnet": [15, 16, 29, 31, 68], "subprocess": 54, "subscript": [13, 30, 66, 67, 68], "subscription_id": 30, "subsect": 19, "subsequ": [7, 79], "subset": 56, "substitut": 18, "succe": 53, "success": [3, 5, 10, 16, 18, 19, 21, 23, 27, 28, 62, 65, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80], "success_count": 53, "success_prob": 80, "successfulli": [1, 5, 6, 7, 8, 14, 18, 31, 38, 49, 76, 77], "successor": 59, "sudo": [2, 14, 16, 28, 45, 66, 67], "suffici": 41, "suggest": [0, 3, 9, 10, 18, 19, 21, 26, 47, 66, 67, 71, 79], "suit": [0, 3, 11, 15, 19, 20, 22, 23, 26, 27, 48, 49, 50, 64, 65, 73, 74, 77, 78, 79], "suitabl": 0, "sum": [32, 42, 53, 55, 59], "summar": [0, 1, 22, 24, 29], "summari": [4, 5, 33, 41, 43, 44, 45, 46, 50, 53, 54, 55, 62, 69, 74], "sun": 16, "sundai": [5, 57], "super": [13, 18, 28], "superior": [72, 76, 77, 78, 79], "superset": 56, "superus": 14, "supervis": [57, 76, 77], "supervisori": 12, "suppli": [40, 63, 71, 73, 76, 77, 78, 79], "supplier": [76, 77, 78, 79], "supply_chain": 61, "supply_chain_attack_simul": 77, "support": [0, 1, 7, 9, 12, 15, 19, 21, 22, 24, 25, 26, 34, 36, 37, 38, 41, 42, 43, 45, 46, 49, 50, 52, 55, 56, 59, 61, 63, 64, 68, 69, 71, 75], "suppress": [42, 53, 55], "surfac": [49, 51, 69, 77, 78, 79], "suricata": 42, "survei": 63, "surveil": [42, 79], "suspect": 79, "suspici": [7, 43, 45, 76, 79], "sustain": 79, "sv": 68, "svg": [53, 75], "swagger": [5, 19, 62, 68], "swap": 66, "swap_usag": 34, "swarm": 25, "swift": 77, "switch": [3, 14, 15, 19, 61, 79], "sy": [20, 32, 54], "symptom": [17, 38, 66], "syn": 68, "sync": [13, 24, 58, 68, 75], "sync_asset": 68, "sync_result": 68, "sync_statu": 58, "sync_thehive_cas": 58, "synchron": [1, 9, 24, 38, 58, 62, 63, 67, 68, 70, 79], "syntax": [0, 3, 13, 66], "synthet": 53, "system": [0, 1, 2, 4, 6, 8, 13, 15, 22, 23, 24, 25, 26, 27, 29, 30, 34, 36, 37, 38, 42, 44, 45, 46, 47, 48, 49, 50, 51, 55, 56, 58, 60, 63, 65, 66, 68, 69, 71, 72, 74, 75, 76, 77, 78, 79, 80], "system32": 53, "system_imag": 45, "system_mainten": 70, "systemat": [17, 35, 42, 72, 76, 77, 79], "systemctl": [28, 45, 66, 68], "systemrandom": [49, 52], "t": [2, 14, 16, 19, 21, 31, 32, 34, 35, 42, 48, 51, 53, 54, 65, 66], "t1003": [69, 75], "t1005": 80, "t1021": 69, "t1055": [69, 75, 80], "t1059": [75, 80], "t1068": 69, "t1078": [69, 75, 80], "t1190": [69, 80], "t1204": 69, "t1566": [9, 69, 75, 80], "t2": 65, "t3": 16, "t4": 68, "t9999": 9, "ta0001": [6, 59], "ta0008": 6, "tab": 20, "tabl": [0, 1, 3, 12, 16, 30, 31, 32, 33, 36, 41, 53, 54, 62], "table_s": 61, "table_schema": 31, "tablenam": [32, 33, 35, 61], "tabletop": [42, 43, 45, 77, 79], "tactic": [6, 42, 59, 63, 69, 74, 75, 76, 77, 78, 79], "tactic_id": 35, "tag": [3, 8, 14, 16, 21, 31, 32, 51, 54, 58, 62, 65, 67, 68, 70], "tail": [17, 31, 32, 33, 43], "tailgat": 77, "tailor": [27, 74, 78], "tailwindcss": 20, "take": [13, 17, 18, 21, 35, 38, 61, 66], "takeawai": 43, "taken": [33, 43, 79], "talent": 73, "tamper": [22, 29, 37, 39, 46, 48, 51], "tar": [16, 31, 33, 43, 66, 67], "target": [0, 5, 6, 8, 10, 11, 15, 16, 17, 18, 19, 21, 31, 33, 34, 38, 40, 42, 46, 48, 50, 51, 52, 53, 54, 55, 57, 59, 62, 64, 65, 66, 67, 68, 69, 71, 74, 75, 77, 78, 79, 80], "target_15_minut": 42, "target_1_hour": 42, "target_95_perc": 42, "target_asset": [18, 19, 59, 69, 80], "target_asset_id": [5, 6, 8, 18, 34, 35, 61, 63, 64, 71], "target_asset_nam": 5, "target_asset_typ": 69, "target_column": 57, "target_control_id": 56, "target_databas": 31, "target_dropdown": 64, "target_group_arn": [16, 33], "target_id": [35, 64, 65], "target_industri": 42, "target_less_than_5_perc": 42, "target_matur": [42, 69], "target_region": 42, "target_rol": 4, "target_s": 42, "target_sector": [69, 75, 80], "targetasset": [18, 19], "targetassetid": [18, 19], "targetid": 35, "targets_complet": 5, "targets_tot": 5, "task": [0, 6, 17, 19, 24, 26, 29, 32, 35, 38, 58, 59, 63, 64, 65, 70, 77, 79], "task_data": 58, "taxii": [13, 63, 67, 70, 80], "taxii2": 13, "taxii_collection_id": 13, "taxii_password": 13, "taxii_server_url": 13, "taxii_usernam": 13, "tb": [30, 53, 64, 65], "tbd": 47, "tcp": [5, 17, 34, 35], "td": 3, "tdd": [2, 21], "team": [1, 2, 3, 5, 14, 16, 17, 21, 22, 23, 24, 25, 26, 27, 28, 29, 33, 37, 38, 39, 41, 42, 44, 46, 47, 48, 51, 52, 54, 62, 63, 67, 68, 70, 72, 73, 78, 79], "team_management_author": [76, 77], "teamer": 67, "techint": 76, "technic": [19, 21, 23, 38, 40, 45, 47, 50, 51, 59, 67, 69, 70, 71, 72, 74, 78, 79, 80], "techniqu": [0, 6, 8, 18, 22, 26, 27, 34, 35, 42, 47, 51, 53, 54, 59, 60, 61, 62, 63, 65, 69, 71, 72, 74, 76, 77, 78, 79, 80], "technique_id": [9, 34, 35, 59, 69, 75], "technique_map": [59, 69], "technique_nam": 59, "technique_not_found": 9, "technique_overlap": 69, "technique_upd": 80, "technologi": [3, 23, 45, 50, 55, 63, 72, 73, 76, 77, 78, 79, 80], "telemedicin": 77, "templat": [1, 3, 12, 14, 16, 17, 19, 20, 22, 25, 33, 37, 43, 45, 47, 53, 54, 68, 69, 77, 78, 79], "tempor": [57, 75, 77, 78, 79], "temporari": [4, 13, 29, 32, 43, 77], "temporarili": 8, "tenant": [13, 24, 25, 26, 28, 30, 66, 67, 68, 77], "tenant_id": [30, 68], "term": [3, 25, 41, 45, 46, 64, 65, 70, 73, 75, 76, 77, 78, 79], "termin": [20, 40, 70], "terraform": [15, 16, 29, 31, 36, 54], "terraform_1": 16, "test": [1, 3, 11, 12, 13, 14, 15, 17, 22, 23, 24, 25, 26, 29, 32, 33, 35, 36, 38, 40, 42, 43, 44, 45, 46, 47, 50, 63, 66, 67, 68, 69, 70, 71, 72, 74, 76, 78, 79], "test_": [64, 65], "test_analysi": 18, "test_analysis_endpoint": 65, "test_analyze_attack_path_raises_validation_error_for_invalid_asset": 18, "test_analyze_attack_path_respects_max_depth_paramet": 18, "test_analyze_attack_path_returns_valid_paths_for_connected_asset": 18, "test_analyze_path_invalid_asset": 19, "test_analyze_path_invalid_source_raises_validation_error": 65, "test_analyze_path_success": [19, 65], "test_api_integr": 64, "test_api_secur": [53, 64], "test_asset": [53, 65], "test_asset_cr": 65, "test_asset_crud_oper": 64, "test_asset_discovery_workflow": 64, "test_asset_endpoint": 65, "test_asset_servic": 65, "test_asset_status_transit": 65, "test_asset_stuff": 65, "test_asset_type_valid": 65, "test_asset_validation_accepts_valid_data": 65, "test_asset_validation_rejects_empty_nam": 65, "test_async_attack_path_analysi": 65, "test_attack_path": [18, 21, 64, 65], "test_attack_path_analysi": 21, "test_attack_path_analysis_perform": 65, "test_attack_path_servic": [19, 64, 65], "test_auth": 2, "test_auth_endpoint": 65, "test_authentication_bypass": [53, 54], "test_brute_force_protect": 53, "test_bulk_analysis_perform": 65, "test_cas": 54, "test_command_inject": 53, "test_complete_attack_path_analysi": 64, "test_concurrent_analysis_request": 65, "test_dat": 31, "test_database_connection_failur": 65, "test_database_integr": 64, "test_database_url": 64, "test_debug_exampl": 65, "test_discover_api_endpoints_handles_timeout": 65, "test_discover_aws_assets_success": 65, "test_e2": 2, "test_email_valid": 65, "test_find_attack_paths_no_path_exist": 64, "test_find_attack_paths_success": 64, "test_get_asset": 65, "test_get_technique_invalid_id_raises_validation_error": 65, "test_help": 65, "test_horizontal_privilege_escal": 53, "test_id": 55, "test_integration_": 2, "test_mitre_servic": 65, "test_multiple_exception_typ": 65, "test_nam": 55, "test_new_featur": 2, "test_password": 55, "test_path_travers": 53, "test_privilege_escal": 53, "test_properti": 55, "test_race_condit": 53, "test_session_fix": 53, "test_siz": 57, "test_sql_inject": 54, "test_sql_injection_attack": 53, "test_sql_injection_protect": 64, "test_typ": 31, "test_update_mitre_data_network_error_raises_mitre_data_error": 65, "test_us": 65, "test_user_workflow": 64, "test_valid": 65, "test_validate_ip_address": 65, "test_with_debugg": 65, "test_workflow_bypass": 53, "test_xss_prevent": 53, "test_xss_protect": 64, "testabl": [18, 65], "testalert": 16, "testapiintegr": 64, "testapisecur": 64, "testapisecuritydast": 53, "testassetdiscoveryservic": 65, "testasyncanalysisservic": 65, "testattackpathperform": 65, "testattackpathservic": [19, 64, 65], "testauthenticationsecur": 53, "testauthorizationsecur": 53, "testbusinesslog": 53, "testclient": [53, 64], "testdatabaseintegr": 64, "tester": 77, "testing_scor": 46, "testinputvalid": 53, "testipvalid": 65, "testmitreserviceexcept": 65, "testmon": 20, "testpassword": 64, "testpath": [64, 65], "testus": 53, "testuserworkflow": 64, "text": [3, 8, 11, 12, 13, 16, 18, 26, 31, 32, 35, 41, 53, 54, 55, 56, 58, 61, 64, 65, 66], "tfplan": [14, 16, 31, 51], "tfsec": 51, "tfstate": 31, "tfvar": [14, 16, 31], "than": [18, 35, 36, 61, 73, 76, 77, 78, 79], "thank": 19, "theft": [12, 45, 76, 77, 79], "thehiv": [25, 36], "thehive4pi": [24, 26], "thehive_cas": 58, "thehive_case_id": 58, "thehive_case_map": 58, "thehive_case_numb": 58, "thehive_connector": 58, "thehive_integration_config": 58, "thehivecas": 58, "thehivecasemap": 58, "thehiveconfig": 58, "thehiveconnector": [26, 58], "thehiveintegrationconfig": 58, "thei": [18, 19, 51, 55, 63, 64, 65, 71, 79], "them": [63, 77], "theme": [3, 18], "themselv": 44, "theoret": 0, "thi": [0, 1, 2, 3, 4, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 37, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 66, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80], "thick": 38, "thing": [35, 64, 65, 73, 78], "think": [76, 78, 79], "third": [8, 40, 42, 43, 45, 46, 47, 51, 52, 62, 71, 72, 76, 77, 78, 79], "thorough": 46, "thoroughli": [19, 21], "thought": [3, 23, 27, 72, 73, 76, 77, 78], "thread": [53, 59, 62, 63], "thread_pool_keep_al": 35, "thread_pool_max_s": [34, 35], "threadpoolexecutor": 59, "threat": [1, 4, 6, 11, 21, 23, 24, 27, 29, 33, 36, 39, 40, 41, 43, 44, 45, 47, 50, 52, 54, 60, 62, 67, 69, 70, 71, 73, 76, 77, 78], "threat hunt": 76, "threat model": [27, 78], "threat monitor": 79, "threat_actor": [6, 61, 63, 69, 71, 80], "threat_actor_emul": 77, "threat_actor_id": 80, "threat_actor_profil": 69, "threat_actor_upd": 80, "threat_fe": 42, "threat_group": 75, "threat_hunting_assist": 76, "threat_hunting_tool": 79, "threat_ind": 42, "threat_intel": 80, "threat_intel_en": 13, "threat_intel_update_interv": 13, "threat_intellig": [42, 61], "threat_intelligence_access": 79, "threat_intelligence_analysi": 76, "threat_landscap": 42, "threat_likelihood": 69, "threat_model": 80, "threat_predict": 57, "threat_threshold": 33, "threatactor": [8, 42], "threatfeedservic": 42, "threatintelligenceservic": 42, "threatpredict": [26, 57], "threatpredictionmodel": 57, "threatpredictionservic": [26, 57], "three": 25, "threshold": [33, 38, 40, 41, 54, 57, 59, 63, 69, 75, 79], "threshold_count": 41, "threshold_monitor": 41, "threshold_multipli": 41, "throttl": [25, 26, 30, 39, 62, 63, 70], "through": [0, 3, 6, 7, 8, 13, 18, 21, 26, 27, 28, 32, 35, 36, 39, 45, 46, 47, 48, 49, 50, 52, 53, 55, 60, 62, 63, 64, 67, 69, 71, 72, 73, 76, 77, 78, 79], "throughout": [1, 15, 22, 40, 47, 52, 54, 64, 76, 77, 79], "throughput": [15, 29, 34, 35, 38, 41, 62, 70, 78], "throw": [7, 18], "thursdai": 78, "ti": 66, "ticket": [0, 33, 43, 48, 62, 68, 77, 79], "tier": [0, 5, 8, 15, 16, 23, 34, 41, 72, 76, 77, 78, 79], "time": [3, 6, 8, 9, 12, 15, 18, 19, 21, 23, 24, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 39, 40, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 53, 54, 55, 56, 58, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 80], "time_horizon": 26, "time_to_fix": 55, "time_window_minut": 41, "timedelta": [42, 57], "timefram": [69, 75, 79], "timelin": [21, 22, 37, 40, 43, 45, 47, 50, 51, 67, 69, 74, 76, 77, 78, 79, 80], "timeout": [5, 7, 21, 30, 31, 32, 34, 35, 38, 39, 53, 58, 64, 65, 66, 68, 70], "timeout_r": 34, "timeouterror": 18, "timer": 79, "timestamp": [5, 6, 8, 12, 15, 18, 35, 41, 42, 43, 45, 54, 56, 57, 61, 75, 77, 79], "timezon": [18, 56, 57, 58], "tip": [38, 71], "titl": [19, 21, 22, 43, 58], "tl": [13, 24, 25, 29, 39, 40, 44, 49, 52, 62, 63, 67, 70], "tlp": 58, "tmp": [31, 32, 33, 43], "to_tsvector": 35, "toc": 3, "toctre": 22, "togeth": [19, 47], "token": [3, 5, 6, 8, 9, 10, 11, 19, 20, 21, 30, 46, 53, 64, 66, 67, 68, 71, 75, 77, 80], "token_typ": 7, "token_urlsaf": 66, "toler": [15, 27, 40, 72, 73, 76, 78], "toml": [18, 21], "too": [8, 10, 17, 18, 35, 66], "tool": [0, 4, 5, 7, 8, 9, 10, 11, 12, 13, 15, 19, 22, 23, 28, 29, 30, 34, 36, 37, 38, 39, 41, 42, 44, 45, 46, 47, 49, 52, 56, 57, 58, 61, 62, 63, 64, 65, 66, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80], "top": [2, 17, 32, 33, 34, 35, 38, 43, 44, 48, 50, 52, 57, 75], "top_degree_centr": 6, "top_risk_factor": 57, "top_trend": 75, "topic": [0, 3, 19, 45, 78], "topologi": [25, 67, 77, 78, 79], "total": [0, 5, 22, 23, 27, 35, 43, 49, 52, 53, 54, 55, 58, 68, 71, 78, 79, 80], "total_affect": 58, "total_asset": 5, "total_count": 8, "total_edg": 6, "total_impact": 80, "total_impact_scor": 6, "total_issu": [54, 55], "total_nod": 6, "total_review": 46, "total_risk": [35, 69], "total_risk_scor": [6, 58, 61, 71], "total_scan": 54, "total_second": [54, 57], "total_tim": [32, 34, 35, 61, 66], "total_vulner": 53, "totp": [7, 11, 42], "totp_secret": 42, "totp_servic": 42, "totpservic": 42, "tour": 27, "trace": [15, 79], "track": [3, 4, 8, 11, 12, 22, 24, 25, 26, 27, 29, 34, 35, 36, 37, 38, 41, 42, 47, 48, 49, 54, 55, 62, 63, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80], "track_campaign": 75, "track_configur": 68, "track_relationship": 68, "track_security_ev": 54, "track_training_effect": 42, "track_vulner": 68, "track_vulnerability_metr": 54, "trade": [77, 78], "tradit": 69, "traefik": [26, 37], "traffic": [11, 14, 15, 29, 30, 32, 33, 59, 61, 76, 77, 78, 79], "traffic_volume_anomali": 57, "trail": [4, 12, 25, 26, 27, 29, 39, 40, 41, 42, 45, 51, 52, 61, 62, 63, 64, 70, 72, 77, 78], "train": [0, 3, 11, 12, 14, 16, 18, 22, 23, 24, 25, 26, 27, 29, 36, 37, 40, 44, 46, 50, 52, 55, 57, 62, 63, 69, 70, 71, 72, 76, 77, 78], "train_model": 57, "train_test_split": 57, "trained_model": 57, "training_complet": 42, "training_d": 57, "training_data": 57, "training_environment_access": [76, 77], "training_histori": 42, "training_program": 42, "training_program_develop": 76, "training_sampl": 57, "transact": [11, 32, 40, 64, 77], "transfer": [39, 40, 67, 70, 72, 76, 77, 78, 79], "transform": [0, 22, 23, 24, 29, 57, 72, 74, 76, 77, 78], "transit": [11, 30, 39, 40, 41, 44, 46, 49, 51, 62, 65, 67, 68, 70, 77, 79], "translat": [19, 78], "transmiss": [40, 42, 46, 62, 77, 79], "transmit": 79, "transpar": [12, 40, 45, 47, 73], "transport": [11, 77], "travers": [53, 55, 62, 67, 77, 79], "traversal_payload": 53, "treat": 19, "treatment": [48, 72, 77, 78], "tree": [3, 41, 62], "trend": [4, 12, 24, 25, 32, 34, 36, 41, 42, 45, 48, 50, 52, 54, 55, 62, 70, 72, 73, 75, 76, 77, 78, 79], "triag": [47, 55, 58, 76, 79], "trigger": [12, 18, 30, 51, 57, 61, 66, 67, 68, 69, 77, 79], "trigger_soft_delete_asset": 61, "trivi": [14, 32, 48, 50, 54], "troubleshoot": [8, 23, 29, 38, 43, 62, 74, 78, 79], "true": [2, 4, 5, 6, 7, 8, 12, 13, 14, 15, 16, 17, 18, 19, 20, 24, 25, 26, 28, 29, 32, 35, 41, 42, 53, 54, 55, 56, 57, 58, 61, 64, 65, 68, 69, 75, 80], "trufflehog": 51, "trust": [23, 24, 25, 27, 36, 39, 41, 44, 49, 51, 52, 61, 62, 63, 72, 73, 76, 77, 78, 79], "trust_boundari": 69, "trust_service_criteria": 29, "trustboundari": 69, "try": [18, 19, 35, 49, 57, 58, 59, 66], "try_except_continu": 54, "try_except_pass": 54, "tsx": 26, "ttl": [13, 34, 35, 59, 70], "ttp": [9, 75, 76, 77, 78, 79], "tuesdai": 78, "tulpn": [28, 38, 66, 67], "tune": [0, 3, 13, 16, 24, 25, 26, 34, 36, 53, 68, 69, 70, 71, 74, 75, 76, 77, 78, 79], "tunnel": [61, 77], "tupl": [35, 54, 59], "tutori": [0, 1, 3, 19, 79], "tvm": 78, "two": [18, 19], "txt": [3, 14, 16, 20, 32, 35, 53, 55, 64, 65, 66, 68], "type": [0, 1, 3, 6, 7, 8, 9, 10, 13, 15, 18, 20, 22, 24, 27, 31, 32, 33, 34, 35, 37, 38, 39, 41, 44, 47, 48, 52, 53, 54, 55, 57, 58, 59, 62, 63, 64, 65, 67, 68, 70, 75, 77, 78, 79, 80], "type_coverag": 18, "typehint": 3, "typescript": [20, 24, 26], "typic": [6, 62, 63, 67, 71, 74, 77, 79], "typographi": 3, "u": [5, 13, 14, 16, 17, 19, 21, 28, 30, 31, 32, 33, 43, 47, 53, 65, 66, 67, 68], "uac": 77, "uba": 77, "ubuntu": [3, 5, 14, 19, 20, 28, 53, 55, 64, 65], "ueba": [52, 76, 77, 78], "uefi": 77, "ufw": 67, "ui": [5, 19, 21, 43, 50, 62, 64], "uid": 33, "uk": 65, "ul": 78, "unam": 14, "unauthor": [8, 12, 18, 33, 40, 43, 45, 46, 47, 79], "unauthorized_access": 80, "unauthorized_disclosur": 80, "unavail": [8, 33, 43, 45, 46, 65], "uncertainti": 78, "unchang": 50, "unclear": 65, "under": [5, 6, 8, 19, 27, 35, 64, 65, 76, 77, 79], "undergo": [50, 78], "underground": [76, 77], "underli": 79, "underlin": 22, "underscor": 18, "understand": [3, 18, 19, 20, 21, 40, 47, 56, 63, 69, 74, 78, 79], "understood": 40, "underwai": 50, "undetect": [77, 79], "undo": [16, 17, 33], "undu": [12, 40], "unexpect": [18, 71, 78, 79], "unicodedecodeerror": 49, "unifi": [22, 23, 63, 76, 78, 79], "uniform": 18, "union": [18, 44, 53, 54], "uniq": [32, 33], "uniqu": [5, 13, 42, 50, 56, 58, 61, 75, 79], "uniqueconstraint": 56, "unit": [0, 2, 18, 19, 20, 21, 49, 51, 54, 62, 73, 76, 77, 78, 79], "unittest": [19, 65], "unknown": [5, 9, 10, 31, 58, 76, 79, 80], "unknown_apt": 10, "unless": 47, "unlock": 70, "unnecessari": [4, 50, 70], "unnecessarili": 19, "unnest": 61, "unpatch": 78, "unpreced": 79, "unprocess": 8, "unreach": [35, 64, 66], "unsaf": [52, 55], "unsupervis": [57, 76], "untest": 64, "until": [32, 47, 64], "untrust": 49, "unus": [18, 35, 66], "unusu": [4, 11, 33, 41, 79], "unwav": [76, 77], "unzip": 16, "up": [2, 3, 4, 11, 12, 14, 19, 20, 21, 24, 25, 28, 32, 33, 34, 35, 36, 37, 38, 43, 45, 46, 53, 54, 55, 62, 63, 64, 65, 66, 67, 68, 70, 71, 72, 74, 79, 80], "upcom": 67, "updat": [0, 1, 2, 3, 4, 8, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 31, 33, 36, 38, 40, 41, 42, 44, 45, 46, 48, 51, 52, 53, 54, 55, 58, 59, 61, 62, 63, 64, 65, 66, 68, 69, 71, 73, 74, 75, 77, 78, 79, 80], "update_attack_data": 75, "update_cas": [26, 58], "update_data_accuraci": 42, "update_exist": [5, 68], "update_frequ": 80, "update_incident_not": 79, "update_incident_statu": 7, "update_mitre_data": 65, "update_playbook": 42, "updated_asset": 5, "updated_at": [5, 18, 56, 61], "updated_cas": 58, "upgrad": [0, 14, 17, 18, 20, 28, 32, 56, 62, 70], "upload": [5, 31, 51, 53, 55, 64, 65], "upon": [25, 36, 47], "upper": [57, 58], "upper_snake_cas": 18, "uppercas": 19, "upstream": [19, 34, 35], "uptim": [15, 24, 36, 42, 52, 63], "uptime_sla": 29, "uq_control_framework_control_id": 56, "uq_domain_framework_domain_id": 56, "uq_framework_name_vers": 56, "ur": 66, "urgent": 79, "url": [18, 20, 27, 34, 53, 58, 67, 68], "us": [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 18, 19, 20, 21, 22, 24, 25, 28, 29, 30, 31, 32, 34, 35, 36, 37, 38, 40, 41, 42, 43, 46, 47, 48, 49, 50, 51, 53, 54, 55, 57, 59, 61, 62, 63, 64, 65, 66, 70, 72, 73, 74, 75, 76, 77, 78, 79, 80], "usabl": [0, 39], "usag": [1, 2, 3, 8, 9, 10, 11, 13, 14, 16, 17, 29, 31, 32, 33, 34, 35, 38, 40, 41, 43, 46, 49, 51, 53, 55, 62, 63, 65, 66, 69, 70, 71, 77, 79, 80], "usage_track": 4, "usecallback": [18, 19], "useeffect": 18, "user": [2, 4, 5, 6, 7, 9, 10, 11, 12, 13, 14, 16, 18, 19, 20, 21, 24, 26, 29, 30, 34, 35, 37, 40, 41, 42, 43, 44, 45, 46, 47, 50, 51, 52, 53, 54, 56, 58, 61, 62, 64, 65, 66, 68, 69, 76, 77, 78, 79], "user guid": 23, "user1": 53, "user123": 65, "user1_token": 53, "user2": 53, "user_account": 58, "user_ag": 41, "user_behavior": 42, "user_behavior_weight": 4, "user_data": 51, "user_id": [4, 12, 34, 41, 66], "user_rol": [42, 66], "user_role_enum": 61, "user_sess": 34, "user_token": 53, "user_workflow": 18, "useradd": 28, "userfactori": [64, 65], "usermod": [14, 28, 45], "usernam": [7, 8, 14, 16, 28, 42, 53, 61, 68], "username_field": 53, "users_email_format": 61, "users_username_length": 61, "usest": [18, 19], "usr": [14, 16], "utc": 31, "utcnow": 57, "util": [0, 18, 25, 32, 34, 35, 41, 44, 50, 65, 70, 75, 77, 78, 79], "uuid": [12, 26, 41, 56, 57, 58, 61], "uuid4": [56, 57, 58], "uvicorn": [13, 14, 20, 34, 35, 53], "uvicornwork": [13, 34, 35], "ux": [19, 43], "v": [0, 2, 14, 17, 18, 19, 20, 32, 34, 35, 51, 53, 55, 64, 65, 66, 67, 71, 74, 75, 79], "v1": [4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 18, 20, 21, 25, 28, 30, 31, 32, 33, 34, 41, 53, 54, 63, 64, 66, 67, 68, 71, 75, 80], "v1beta1": 51, "v2": [14, 26, 34, 55], "v3": [3, 14, 16, 47, 48, 50, 53, 55, 64, 65], "v4": [3, 53, 55, 64, 65], "vacuum": [32, 33, 61, 66], "valid": [0, 1, 3, 4, 5, 7, 8, 10, 12, 14, 16, 17, 18, 19, 21, 24, 25, 26, 27, 29, 30, 32, 33, 35, 36, 40, 42, 44, 45, 46, 47, 48, 50, 52, 54, 55, 56, 58, 61, 62, 63, 64, 65, 66, 67, 68, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80], "valid_asset": 19, "valid_email": 65, "valid_target": 65, "validate_asset_typ": 65, "validate_email": 65, "validate_ip": 18, "validate_ip_address": 65, "validate_nam": 18, "validate_technique_data": 65, "validated_bi": 56, "validated_data": 51, "validation_d": 56, "validation_error": [5, 8, 18], "validation_scenario": 69, "validationerror": [18, 19, 65], "valu": [1, 6, 13, 16, 17, 18, 24, 27, 30, 31, 35, 38, 42, 47, 50, 53, 54, 55, 57, 58, 62, 65, 66, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80], "valuabl": [19, 78], "valuat": [77, 78], "valueerror": [18, 58], "var": [3, 13, 14, 16, 18, 31, 32, 33, 43, 55, 67, 78], "varchar": [12, 41, 56, 61], "vari": 47, "variabl": [1, 13, 14, 17, 18, 21, 30, 54, 65], "variat": 79, "variou": [6, 9, 65, 67, 79], "varoni": 42, "vcpu": 16, "ve": [19, 27, 38], "vector": [26, 27, 35, 38, 43, 45, 46, 50, 51, 63, 71, 74, 77, 78, 79], "vehicl": [61, 77], "veloc": [46, 54, 55, 79], "vendor": [42, 43, 47, 51, 53, 70, 71, 73, 76, 77, 78, 79], "vendor_evaluation_access": 78, "vendor_relationship_manag": 73, "ventur": 77, "venv": [2, 14, 18, 20, 28, 54, 55, 65], "venv_bin": [53, 55], "verbos": [31, 32, 65], "veri": 80, "verif": [14, 15, 22, 27, 29, 37, 38, 40, 41, 42, 43, 45, 47, 49, 50, 67, 70, 75, 78, 79], "verifi": [0, 2, 11, 12, 15, 16, 17, 19, 28, 30, 31, 32, 33, 35, 38, 39, 40, 41, 42, 43, 44, 50, 58, 62, 64, 66, 67, 68, 69, 70, 71, 74, 79], "verification_complet": 12, "verification_interval_hour": 41, "verification_method": 12, "verification_requir": 12, "verify_password": 42, "verify_ssl": 58, "version": [1, 3, 4, 5, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 25, 26, 28, 30, 31, 32, 35, 44, 46, 47, 49, 53, 54, 55, 56, 62, 63, 64, 65, 66, 68, 70, 72, 76, 77, 78], "versu": 26, "vertic": [0, 15, 17, 62, 78], "via": [3, 5, 17, 19, 20, 21, 25, 43, 67, 68, 77, 79], "victim": 76, "video": [0, 1, 3, 43, 47, 79], "view": [2, 3, 7, 41, 61, 64, 75, 78, 79], "view_advanced_attack_path": 79, "view_asset": [41, 64], "view_attack_path": [7, 64], "view_audit_log": [7, 70], "view_basic_attack_path": 79, "view_security_ev": [7, 79], "viewer": [30, 67], "vigil": 72, "violat": [10, 12, 23, 41, 45, 47, 50, 71, 72, 74, 77, 78, 79, 80], "violation_prob": 80, "virtual": [14, 20, 28, 30, 63, 77, 78, 79], "virtual_machin": 61, "virtual_memori": [17, 33, 53], "virtualmachin": 30, "viru": 77, "visibl": [19, 24, 25, 62, 63, 68, 73, 79], "vision": [62, 73, 78], "visionari": 78, "visit": [5, 17, 33, 59, 77], "visual": [0, 1, 3, 4, 7, 8, 9, 11, 12, 21, 24, 36, 41, 60, 62, 63, 67, 69, 71, 74, 77, 78], "visualization_typ": 75, "vlan": [42, 77], "vm": [30, 66, 68], "void": [18, 19, 26, 61], "volum": [4, 16, 30, 31, 33, 35, 41, 51, 66, 68], "volume_anomali": 41, "volumesnapshot": 31, "volumesnapshotclassnam": 31, "voluntari": 40, "vpc": [15, 16, 29, 30, 31, 32, 67, 68, 70], "vpn": [30, 39, 40, 61, 69, 77, 78], "vpn_connect": 61, "vscode": 20, "vuln_featur": 57, "vuln_scan": 32, "vulner": [2, 5, 14, 18, 19, 21, 23, 24, 25, 26, 30, 32, 39, 40, 42, 44, 45, 46, 49, 51, 54, 57, 62, 63, 64, 67, 68, 69, 70, 71, 74, 76, 77, 78, 79, 80], "vulnerabilities_curr": 55, "vulnerabilities_found": 55, "vulnerabilities_found_tot": 55, "vulnerability assess": 49, "vulnerability manag": [48, 50], "vulnerability_count": 35, "vulnerability_fix_time_hour": 55, "vulnerability_manag": [42, 56], "vulnerability_research_access": 77, "vulnerability_scan": 68, "vulnerability_scann": 61, "vulnerability_sever": 69, "w": 33, "w503": 18, "wa": [35, 43, 50, 79], "waf": [15, 29, 33, 42, 50, 61], "wafv2": 33, "wai": [28, 35, 67], "wait": [14, 20, 31, 32, 43, 58, 59, 64], "wait_tim": 64, "wal_buff": [34, 35], "walk": 28, "want": [12, 19], "war": 77, "warehous": 15, "warm_retention_dai": 41, "warn": [3, 13, 14, 16, 18, 19, 32, 37, 41, 48, 49, 54, 58, 64, 65, 72, 76], "watch": 20, "water": 77, "wc": [32, 33], "wcag": 3, "we": [18, 19, 21, 27, 38, 45, 46, 47, 48, 50, 54, 65, 74], "weak": [30, 40, 52, 54, 55, 76, 77, 78, 79, 80], "weak_crypto": 55, "weakref": 35, "weapon": 77, "web": [3, 5, 15, 16, 18, 19, 28, 30, 47, 48, 50, 53, 64, 65, 69, 74, 76, 77, 78, 79], "web01": 5, "web_appl": 80, "web_secur": 61, "web_serv": 3, "web_server_001": [6, 8, 71, 80], "web_servic": 61, "web_ui": 41, "webdriv": 64, "webdriverwait": 64, "webhook": [24, 25, 26, 27, 30, 62, 67, 68, 79], "webhook_data": [26, 58], "webinar": 78, "webroot": 20, "websit": 77, "websocket": [19, 21], "wednesdai": 78, "week": [4, 31, 47, 51, 69, 75, 76, 77, 78, 80], "weekli": [24, 25, 31, 48, 49, 50, 52, 54, 57, 70, 75, 78, 79], "weight": [34, 35, 50, 56, 57, 59, 62, 63, 67, 69, 71, 74], "weighted_asset_risk": 59, "weighted_averag": 56, "welcom": [3, 19, 27, 38, 44, 74], "well": [19, 21, 42, 65, 76, 77, 78, 79], "west": [5, 14, 16, 30, 31, 68, 78], "west1": 30, "westus2": 30, "wget": 16, "what": [0, 18, 19, 26, 27, 28, 35, 43, 64, 65, 71, 78, 79], "wheel": 20, "when": [2, 5, 6, 12, 18, 19, 21, 30, 40, 45, 50, 54, 61, 65, 66, 67, 69, 74, 75], "where": [18, 19, 31, 32, 33, 34, 35, 48, 51, 54, 61, 64, 66, 80], "whether": 18, "which": 19, "while": [0, 1, 3, 4, 15, 23, 24, 36, 39, 45, 46, 50, 54, 56, 59, 68, 72, 73, 76, 77, 78, 79], "white": [58, 66], "whitelist": [30, 78, 79], "whitespac": 64, "who": [47, 70, 72, 73, 76, 77, 78, 79], "whoami": [31, 53], "whose": 79, "why": 35, "wide": [33, 65, 70, 76, 77, 78], "widespread": 79, "widget": 79, "window": [0, 14, 16, 20, 24, 25, 26, 28, 36, 50, 53, 59, 70, 77, 79], "winrar": 80, "winword": 75, "wireless_access_point": 61, "wisdom": [73, 78], "wit": 45, "withdraw": [12, 29], "withdrawal_confirm": 12, "withdrawn_at": 12, "within": [6, 12, 30, 32, 40, 43, 51, 52, 56, 63, 65, 74, 79], "without": [2, 12, 13, 18, 21, 35, 40, 44, 47, 51, 54, 55, 62, 79], "wizard": 38, "wmi": 77, "won": 66, "word": 79, "wordlist": 68, "work": [3, 11, 19, 20, 22, 35, 38, 47, 62, 66, 72, 76, 77, 78, 79], "work_mem": [34, 35, 66], "workdir": 35, "worker": [13, 14, 16, 24, 28, 32, 34, 35, 63, 66], "worker_class": [13, 34], "worker_connect": 34, "worker_replica": 16, "workflow": [0, 1, 4, 8, 18, 22, 24, 26, 27, 29, 36, 37, 42, 47, 50, 52, 53, 55, 58, 60, 62, 63, 64, 67, 69, 72, 76, 78], "workload": [15, 25, 26, 35, 36, 41, 78, 79], "workpap": 72, "workshop": [0, 42, 51, 78], "workspac": [14, 16, 31, 76, 77, 78, 79], "workspacefold": 20, "workstat": [5, 18, 61, 64, 65, 68, 69, 71], "world": [0, 1, 3, 23, 27, 53, 69, 76, 77, 78], "would": [19, 53, 79], "wrap": [18, 35], "wrapper": [18, 26, 35], "write": [2, 4, 18, 20, 21, 34, 35, 62, 66], "writer": 35, "written": [21, 54], "wrk": 54, "wrong_password": 53, "wrong_password_": 53, "wsl2": [14, 20], "www": 3, "x": [2, 4, 5, 6, 8, 10, 12, 14, 20, 30, 32, 33, 34, 35, 41, 42, 53, 57, 64, 65, 66, 69, 75, 80], "x509": [11, 32, 66], "x_scale": 57, "x_test": 57, "x_train": 57, "xact_start": 32, "xarg": [32, 33, 66], "xdr": [78, 79], "xlarg": [15, 16], "xml": [35, 55, 64, 65], "xpath": 64, "xr": 73, "xss": [18, 39, 44, 46, 47, 51, 53, 55, 64], "xss_payload": [53, 64], "xxx": [43, 44], "xxxx": [44, 45, 46], "xzf": 31, "y": [14, 16, 21, 31, 32, 33, 57, 66], "y_pred": 57, "y_pred_proba": 57, "y_test": 57, "y_train": 57, "yaml": [0, 14, 16, 17, 18, 20, 31, 33, 43, 54, 64, 66, 68], "yara": 79, "yarn": [20, 28], "ye": [3, 40, 54, 67, 69], "year": [40, 77], "yellow": 38, "yelp": 54, "yield": [35, 64], "yml": [14, 20, 21, 24, 28, 33, 34, 53, 54, 55, 64, 66, 67], "you": [27, 28, 38, 44, 66, 70, 71, 72, 73, 75, 76, 77, 78, 79, 80], "your": [3, 5, 6, 8, 13, 14, 16, 20, 27, 28, 35, 47, 55, 64, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80], "your_api_token": [9, 10], "your_jwt_token": 5, "your_usernam": 19, "yourapp": 13, "yourcompani": [28, 67], "yourself": 21, "yyyi": [43, 46], "z": [14, 31, 32, 61], "z0": 61, "za": 61, "zap": [42, 48, 50, 51, 53, 54], "zap2dock": [16, 53, 54], "zebroci": 80, "zed": 53, "zero": [2, 23, 24, 25, 27, 36, 39, 44, 49, 50, 51, 52, 62, 63, 64, 77, 78, 79], "zero trust": 78, "zero_day_simul": 77, "zero_trust": 61, "zero_trust_architectur": 42, "zero_trust_deploy": 11, "zero_trust_network_access": 42, "zero_trust_servic": 69, "zerotrustservic": 69, "zip": [16, 59], "ziplist": 35, "zone": [15, 16, 17, 18, 29, 31, 38, 41, 46, 56, 61, 70, 78], "zoom": [38, 79], "zt": 69, "zt_roadmap": 69, "zt_servic": 69, "ztna": 78, "zv": [11, 16, 17], "\u00b2": 79}, "titles": ["Documentation Expansion Summary", "Blast-Radius Security Tool - Complete User Guide Documentation", "Local Development &amp; CI/CD Guide", "\ud83d\udcda Blast-Radius Security Tool Documentation", "Least Privilege Access Control Framework", "Asset Management API", "Attack Path Analysis API", "Authentication API", "API Reference", "MITRE ATT&amp;CK Integration API Reference", "Threat Modeling API Reference", "Zero-Trust Architecture Implementation", "GDPR Compliance Framework", "Configuration Guide", "Environment Setup Documentation - Blast-Radius Security Tool", "Production Architecture Overview", "Production Deployment Guide - Blast-Radius Security Tool", "Troubleshooting Guide - Blast-Radius Security Tool", "Code Standards &amp; Style Guide", "Contributing Guide", "Development Environment Setup", "Development Workflow", "Documentation Achievements Summary", "Documentation Overview and Achievements", "Enhanced Features Summary - Blast-Radius Security Tool", "Blast-Radius Security Tool - Enhanced PRD v2.0", "Implementation Gap Analysis - Blast-Radius Security Tool", "Blast-Radius Security Tool Documentation", "Installation Guide", "Latest Implementations Summary - Production Ready Release", "Multi-Cloud Integration Guide", "Backup and Recovery Runbooks - Blast-Radius Security Tool", "Maintenance Procedures - Blast-Radius Security Tool", "Monitoring Runbooks - Blast-Radius Security Tool", "Performance &amp; Scalability", "Performance Optimization", "Phase Integration Plan - Enhanced Features with Existing Roadmap", "Production Readiness Status", "Quick Start Guide", "Security Architecture Overview", "Compliance Documentation - Blast-Radius Security Tool", "Enhanced Audit Logging System", "Security Framework", "Incident Response Procedures - Blast-Radius Security Tool", "Security Documentation", "Security Incident Response", "Security Review Process", "Vulnerability Disclosure Policy", "Security Assessment Overview", "Security Review - June 14, 2025", "Vulnerability Management", "Security Review Processes - Blast-Radius Security Tool", "Security Documentation Summary", "Dynamic Application Security Testing (DAST)", "Security Testing Automation", "Static Application Security Testing (SAST)", "Compliance Framework Schema - Technical Specification", "Machine Learning Threat Prediction - Technical Specification", "TheHive Integration - Technical Specification", "Attack Path Analysis Architecture", "Attack Path Analysis Flows", "Database Design and Schema", "Technical Documentation", "Product Requirements Document (PRD)", "Testing &amp; Quality Assurance", "Unit Testing Guide", "Common Issues &amp; Troubleshooting", "Frequently Asked Questions (FAQ)", "Asset Discovery Use Cases", "Attack Path Analysis Use Cases", "Administrators Guide", "Attack Path Analysis User Guide", "Compliance Officers Comprehensive Guide", "Executive Leadership Comprehensive Guide", "User Guides", "MITRE ATT&amp;CK Integration User Guide", "Purple Team Members Comprehensive Guide", "Red Team Members Comprehensive Guide", "Security Architects Comprehensive Guide", "SOC Operators Comprehensive Guide", "Threat Modeling User Guide"], "titleterms": {"": 67, "0": [25, 43, 47], "00": [32, 33], "000": 22, "001": 63, "002": 63, "003": 63, "004": 63, "005": 63, "1": [0, 11, 14, 16, 17, 19, 20, 24, 25, 26, 28, 36, 38, 43, 45, 47, 49, 51, 56, 57, 58, 68, 69], "10": [16, 33, 40, 47], "100": 29, "11": 16, "12": 40, "13": 40, "14": [40, 49], "15": [40, 43], "16": 40, "17": 40, "18": [24, 40], "19": 24, "2": [0, 14, 16, 17, 19, 20, 24, 25, 26, 28, 32, 36, 38, 40, 42, 43, 45, 49, 51, 56, 57, 58, 63, 68, 69], "20": 40, "2025": [24, 25, 26, 36, 49, 52, 63], "2026": [24, 25, 26, 52], "21": 40, "24": [24, 43], "27001": [40, 42], "3": [0, 11, 14, 16, 17, 19, 20, 24, 25, 26, 28, 32, 36, 38, 43, 45, 47, 49, 51, 56, 57, 58, 63, 68, 69], "33": 40, "34": 40, "4": [0, 14, 16, 19, 20, 24, 25, 26, 32, 36, 43, 45, 47, 51, 56, 57, 58, 63, 69], "5": [0, 14, 16, 19, 20, 24, 25, 26, 36, 40, 43, 45, 51, 56, 69], "6": [0, 14, 16, 19, 22, 24, 25, 26, 32, 36, 40, 47, 56], "7": [16, 25, 43, 47], "72": 43, "8": [16, 24, 25, 40, 43, 47], "9": [16, 33, 40, 43, 47], "96": 36, "A": 40, "For": 0, "In": 47, "Not": [17, 66], "One": 2, "_static": 3, "a1": 40, "acceler": [24, 36], "access": [4, 13, 16, 17, 22, 29, 37, 38, 40, 67, 70, 79], "account": [11, 70], "accuraci": 75, "achiev": [22, 23, 29, 52], "acquisit": 40, "action": [14, 24, 29, 36, 37, 38, 43, 55], "activ": 45, "actor": [9, 10, 75, 80], "ad": [0, 3, 20], "add": [16, 38, 67], "addit": [1, 2, 3, 66], "admin": [28, 67], "administr": [0, 42, 70, 74], "adopt": 63, "advanc": [3, 22, 24, 25, 26, 34, 36, 37, 38, 63, 68, 69, 72, 73, 75, 76, 77, 78, 79, 80], "advisori": 73, "after": [0, 43], "agent": 68, "ai": 80, "air": 67, "alemb": 56, "alert": [11, 16, 17, 30, 33, 41, 43], "algorithm": [35, 59], "align": 73, "alreadi": 26, "am": [32, 33, 67], "an": [19, 67], "analysi": [6, 8, 9, 10, 17, 26, 33, 36, 38, 45, 46, 53, 55, 59, 60, 61, 62, 67, 69, 71, 74, 75, 78, 79, 80], "analyt": [3, 4, 24, 25, 26, 36, 63, 75, 78], "analyz": [6, 9], "ani": 67, "api": [1, 3, 4, 5, 6, 7, 8, 9, 10, 12, 18, 20, 25, 27, 30, 41, 57, 58, 62, 63, 67, 68, 71, 75], "applic": [2, 13, 14, 15, 16, 17, 18, 31, 32, 33, 34, 35, 39, 40, 43, 48, 53, 54, 55], "approach": [17, 52], "ar": 67, "architect": [0, 74, 78], "architectur": [4, 11, 12, 15, 22, 24, 25, 27, 29, 30, 34, 37, 39, 41, 42, 44, 46, 51, 52, 54, 57, 58, 59, 61, 62, 63, 69, 78, 79], "archiv": 61, "area": 46, "articl": 40, "ask": 67, "assess": [4, 8, 10, 12, 26, 30, 42, 43, 46, 48, 50, 51, 52, 53, 56, 59, 63, 69, 71, 72, 74, 78, 80], "assessment_criteria": 56, "asset": [0, 5, 8, 25, 26, 30, 38, 40, 62, 63, 68, 74], "assur": [0, 3, 18, 27, 46, 62, 64], "async": 65, "att": [6, 8, 9, 42, 59, 60, 63, 69, 74, 75], "attack": [6, 8, 9, 10, 38, 59, 60, 61, 63, 67, 69, 71, 74, 75, 77, 79, 80], "attackpathanalyz": 59, "attribut": [9, 75], "audit": [22, 29, 37, 41, 48, 67, 72], "authent": [5, 6, 7, 8, 9, 10, 13, 30, 38, 51, 53, 66, 67], "author": [7, 40, 51, 53, 66], "auto": 54, "autom": [2, 12, 18, 21, 24, 25, 26, 31, 32, 33, 36, 48, 50, 51, 52, 53, 54, 63, 64, 67, 68, 69, 72, 75], "automat": 75, "avail": [15, 40], "aw": [14, 16, 30, 68], "awar": [42, 51], "azur": 30, "backend": [2, 14, 20], "backup": [15, 16, 31, 67, 70], "balanc": [16, 35], "base": [4, 5, 6, 8, 9, 10, 50, 56, 68, 70, 74, 79], "basic": [3, 38, 65, 71, 75, 80], "batch": [9, 57], "befor": [0, 19], "behavior": 75, "benchmark": 34, "benefit": [24, 36, 49, 53, 55], "best": [2, 4, 6, 7, 8, 11, 12, 13, 15, 21, 35, 41, 46, 53, 54, 55, 64, 65, 68, 69, 70, 71, 74, 75, 77, 78, 79, 80], "blast": [1, 3, 6, 14, 16, 17, 24, 25, 26, 27, 31, 32, 33, 40, 43, 51, 59, 60, 63, 67, 69, 71], "blastradiusapidown": 33, "blue": [14, 15], "boundari": 69, "bounti": 47, "branch": [19, 21], "breach": [12, 40, 43], "bug": 47, "build": [1, 2, 3, 14, 16, 36, 37], "bulk": 5, "busi": [24, 25, 40, 49, 50, 52, 53, 63, 73], "c1": 40, "cach": [6, 30, 33, 35, 59, 60], "calcul": [6, 30, 48, 60, 63, 71, 80], "campaign": [9, 77], "can": [38, 67], "capabl": [24, 25, 27, 37, 54, 69, 72, 73, 76, 77, 78, 79], "capac": 32, "case": [0, 1, 27, 58, 68, 69, 71], "categori": [2, 45, 53, 64], "cc6": 40, "cd": [2, 14, 48, 53, 54, 55, 64], "celeri": 57, "centric": 0, "certif": [4, 16, 17, 32, 44, 66, 74], "chain": 41, "chang": [7, 19, 38, 47, 51], "channel": 66, "characterist": 29, "check": [2, 12, 16, 18, 28, 32, 33, 51, 54, 66], "checklist": [11, 14, 19, 46, 51], "ci": [2, 14, 48, 53, 54, 55, 64], "ck": [6, 8, 9, 42, 59, 60, 63, 69, 74, 75], "classif": [11, 43, 45, 47, 48, 55], "cleanup": [14, 32], "clear": 6, "cli": 16, "client": [24, 25, 26], "clone": [14, 19, 20], "cloud": [13, 30, 38, 63, 66, 67, 68], "cluster": [16, 32], "code": [0, 3, 5, 8, 15, 18, 19, 20, 21, 29, 35, 38, 46, 51, 65], "collabor": [21, 71, 72, 76], "collect": 66, "command": [2, 3, 43, 66], "commit": [21, 54], "common": [0, 2, 4, 5, 8, 11, 12, 16, 17, 20, 28, 30, 34, 35, 38, 41, 46, 65, 66, 68, 70, 71, 74], "commun": [3, 19, 33, 40, 43, 45, 47, 67, 73, 78, 79], "comparison": 49, "compens": 50, "complet": [1, 17, 27, 29, 31, 36, 37], "complianc": [1, 10, 12, 22, 24, 25, 26, 27, 29, 37, 39, 40, 41, 42, 44, 45, 48, 51, 52, 56, 62, 63, 67, 68, 69, 70, 72, 73, 74, 78, 80], "compliance_assess": 56, "compliance_control": 56, "compliance_control_assess": 56, "compliance_control_map": 56, "compliance_domain": 56, "compliance_framework": 56, "compon": [4, 11, 12, 15, 16, 25, 26, 41, 42, 57, 58, 59, 65], "comprehens": [0, 1, 22, 23, 27, 72, 73, 76, 77, 78, 79], "concept": 71, "conclus": [0, 1, 4, 11, 12, 15, 29, 37, 41, 45, 46, 47, 48, 49, 50, 52, 53, 54, 55, 70, 71, 72, 73, 76, 77, 78, 79], "concurr": 30, "condit": 47, "conduct": 19, "confidenti": 40, "configur": [0, 2, 3, 4, 12, 13, 14, 16, 18, 28, 30, 31, 33, 38, 41, 49, 50, 54, 55, 65, 66, 67, 70, 71, 79], "conflict": 2, "congratul": 38, "connect": [16, 17, 66], "connector": [26, 58], "consent": 12, "consider": [6, 25, 30, 45, 47, 68], "contact": [43, 44, 47], "contain": [2, 16, 35, 43, 45, 68], "content": [0, 1, 3, 5, 18, 19, 20, 21, 23, 34, 35, 37, 42, 64, 65, 66, 68, 69, 72, 73, 76, 77, 78, 79], "context": 9, "continu": [3, 10, 21, 39, 40, 42, 48, 49, 50, 53, 54, 64, 65, 68, 73, 80], "contribut": [3, 19, 62, 67, 74], "control": [4, 13, 22, 29, 37, 39, 40, 42, 50, 70, 76, 79], "convent": 21, "core": [3, 4, 11, 12, 13, 15, 16, 18, 25, 26, 41, 45, 56, 57, 58, 59, 61, 63, 70, 71, 79], "correl": [9, 11, 75], "coverag": [0, 1, 29, 37, 46, 48, 49, 50, 64, 65, 68], "cr": 63, "crashloopbackoff": 17, "creat": [5, 6, 16, 19, 28], "creation": [60, 71], "credenti": [30, 67], "crisi": 73, "criteria": [24, 25, 40, 51, 56], "critic": [33, 43, 47, 49], "cross": [3, 72], "crud": 5, "cryptograph": 49, "cryptographi": [40, 51], "csf": 56, "css": 3, "current": [24, 25, 36, 48], "custom": [3, 55, 62, 67], "cvss": [43, 47, 48, 50], "cybersecur": 40, "cycl": 2, "d3": 26, "daili": [2, 31, 32, 33, 79], "dashboard": [4, 8, 33, 38, 41, 55, 70, 79], "dast": [48, 50, 53, 54], "data": [6, 9, 11, 12, 14, 15, 16, 30, 31, 39, 40, 43, 49, 51, 53, 56, 61, 62, 63, 64, 65, 66, 67, 75], "databas": [2, 12, 13, 16, 17, 18, 20, 25, 31, 32, 33, 34, 35, 41, 50, 56, 57, 58, 61, 62, 63, 66], "databaseconnectionshigh": 33, "de": 40, "debug": [2, 20, 30, 65], "decis": 60, "default": 67, "defens": 3, "definit": [21, 50, 61, 72, 73, 76, 77, 78, 79], "delet": 5, "deliver": 25, "depend": [16, 24, 26, 28, 50, 54], "deploi": [14, 16], "deploy": [1, 3, 11, 14, 15, 16, 22, 24, 25, 27, 29, 31, 37, 54, 62], "deseri": 49, "design": [3, 12, 15, 18, 27, 51, 56, 57, 61, 62, 65, 78], "detail": [4, 12, 25, 36, 41], "detect": [40, 43, 45, 69, 76, 79], "develop": [0, 2, 3, 8, 13, 14, 16, 18, 20, 21, 25, 27, 28, 36, 40, 51, 54, 62, 64, 72, 73, 78, 79], "diagnost": 66, "diagram": [3, 61], "digit": 73, "disast": [15, 31, 70], "disclosur": 47, "discoveri": [0, 5, 24, 25, 30, 50, 59, 60, 63, 68, 71, 74, 77], "do": 67, "doc": 0, "docker": [3, 24, 25, 28, 66], "document": [0, 1, 2, 3, 14, 18, 19, 22, 23, 27, 29, 37, 40, 43, 44, 46, 51, 52, 62, 63, 79], "documentation_expansion_summari": 0, "doe": 67, "done": 21, "drill": 43, "driven": [21, 80], "dss": [42, 69], "dynam": [48, 53, 54], "ecosystem": [22, 23, 27], "effect": 69, "effort": 26, "element": [3, 68], "emerg": [16, 17, 43], "enabl": [2, 7, 67, 73], "encrypt": [11, 13], "end": [2, 64], "endpoint": [4, 6, 7, 8, 12, 30, 41, 57, 58], "enforc": [11, 18], "engin": [4, 26, 30, 57, 62, 63, 75, 76, 79], "enhanc": [3, 22, 23, 24, 25, 26, 29, 30, 36, 37, 41, 48, 49, 50, 75], "enrich": [9, 41, 75], "enterpris": [3, 22, 25, 26, 27, 52, 78], "entiti": 61, "enumer": 61, "environ": [0, 2, 13, 14, 16, 20, 28, 29, 34, 53, 62, 64, 66, 67], "erad": 45, "erasur": 40, "error": [5, 6, 8, 9, 10, 30, 51, 60, 66], "escal": [4, 17, 33, 79], "essenti": 38, "estim": 26, "event": [9, 11, 41, 75], "evid": 45, "evolut": 3, "exampl": [0, 3, 5, 6, 7, 8, 12, 51, 68, 71], "excel": [3, 15, 23, 27, 52, 70, 72, 73, 76, 77, 78, 79], "except": 65, "execut": [24, 25, 27, 36, 37, 49, 50, 52, 63, 65, 72, 73, 74, 76, 77, 78, 79], "exercis": [69, 71, 74], "exist": [0, 24, 25, 26, 36], "expans": 0, "expect": 24, "experi": 0, "exploit": 77, "export": [6, 9], "extend": 45, "extens": 56, "extern": [16, 17, 43, 45, 68], "factor": [7, 38, 59, 67], "factori": 64, "failur": 17, "fals": 55, "faq": 67, "featur": [1, 2, 3, 20, 21, 23, 24, 25, 26, 27, 30, 36, 38, 57, 67, 68, 69, 70, 74, 80], "feedback": 74, "field": [8, 50], "file": [1, 31], "filter": 8, "final": 14, "financi": 10, "find": 19, "first": [2, 32, 38], "fix": [43, 49, 52, 54], "fixtur": [64, 65], "flow": [7, 21, 60], "focu": [0, 1], "forecast": 26, "fork": 19, "format": [1, 5, 8], "forum": 67, "foundat": 25, "fr": 63, "framework": [4, 12, 22, 24, 25, 26, 29, 37, 40, 41, 42, 44, 46, 48, 51, 52, 56, 59, 62, 63, 65, 69, 72, 73, 76, 77, 78, 79], "frequent": 67, "friendli": 1, "frontend": [2, 14, 20, 25, 66], "full": [2, 31], "function": [16, 63, 72], "futur": [0, 1, 3, 23, 30, 48, 52, 63, 67], "gap": [26, 67], "gate": [21, 48, 54, 64], "gather": 17, "gcp": 30, "gdpr": [10, 12, 22, 29, 37, 40, 42, 51], "gener": [9, 10, 17, 18, 49, 67, 74, 80], "get": [0, 1, 3, 5, 6, 9, 10, 17, 19, 20, 27, 28, 38, 66, 67, 70, 71, 74, 75, 79, 80], "git": 21, "github": [14, 55], "goal": [25, 36, 52], "govern": [3, 72, 73, 78], "grade": 3, "grafana": 33, "graph": [6, 59, 62, 63], "graphengin": 59, "green": [14, 15], "guid": [0, 1, 2, 3, 13, 16, 17, 18, 19, 20, 22, 23, 27, 28, 30, 38, 65, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80], "guidelin": [4, 11, 12, 19, 35, 41, 46, 47, 54, 62, 64, 69, 71, 74], "handl": [5, 9, 10, 30, 45, 51, 60], "harbor": 47, "harden": [16, 50], "hash": 49, "health": [2, 16, 28, 32, 33, 66], "help": [3, 17, 19, 20, 28, 38, 66, 67, 70, 71, 74], "high": [0, 15, 17, 24, 26, 33, 43, 47], "highmemoryusag": 33, "hipaa": 10, "hook": 54, "horizont": 34, "hour": 43, "how": [47, 67], "http": [8, 16, 18], "i": 67, "iac": [29, 51], "ic": 43, "id": 40, "ident": 11, "identifi": 40, "ii": 42, "imag": [2, 14, 16], "immedi": [24, 26, 29, 36, 37, 38, 43], "impact": [0, 3, 10, 12, 23, 24, 49, 50, 51, 52, 53, 80], "implement": [0, 4, 11, 12, 15, 24, 25, 26, 29, 36, 39, 40, 41, 42, 43, 49, 52, 53, 55, 57, 76, 78], "import": 5, "improv": [0, 3, 39, 42, 46, 48, 49, 50, 52, 53], "incid": [25, 26, 40, 42, 43, 45, 52, 58, 69, 71, 74, 79], "incorrect": 66, "increment": 30, "index": [0, 61], "indic": [22, 25, 27, 34, 40, 50], "industri": [3, 23, 52], "inform": [9, 17, 40, 44, 47], "infrastructur": [14, 15, 16, 24, 26, 29, 35, 36, 37, 39, 51], "ingress": 16, "initi": [16, 28, 38, 43, 70, 79], "innov": 73, "input": [51, 53], "instal": [16, 20, 28, 30, 38, 66, 67], "integr": [0, 3, 7, 8, 9, 13, 21, 22, 24, 25, 26, 28, 30, 36, 37, 38, 40, 41, 42, 48, 50, 51, 53, 54, 55, 58, 59, 62, 63, 64, 65, 66, 67, 68, 69, 70, 72, 74, 75, 80], "intellig": [8, 9, 13, 25, 36, 38, 42, 63, 74, 75, 79, 80], "interact": [2, 3, 25], "interfac": [38, 67, 71], "intern": [17, 43, 45], "interpret": 71, "introduct": 48, "inventori": 68, "invest": [52, 73], "investig": [43, 79], "ioc": [9, 75], "ir": 63, "ism": 40, "iso": [40, 42], "issu": [0, 2, 4, 11, 12, 16, 17, 19, 20, 28, 30, 34, 38, 41, 49, 66, 68, 70, 71], "j": 26, "javascript": [5, 7, 18, 19], "job": 30, "jsonb": 56, "june": [49, 52], "just": 4, "jwt": 7, "kei": [0, 1, 3, 23, 25, 34, 40, 42, 50, 52, 55, 69, 70], "kpi": [24, 36, 48, 50, 51, 54, 63], "kubectl": 16, "kubernet": [11, 14, 15, 16, 32, 33, 51], "latest": 29, "layer": [9, 25, 39, 42, 52], "lead": 43, "leadership": [3, 23, 73, 74, 78], "leak": 17, "learn": [24, 25, 26, 38, 57], "least": [4, 22, 29, 37], "legal": [45, 47], "level": [30, 35, 42, 43, 45, 48, 50], "leverag": 36, "librari": [8, 24, 25, 26], "licens": 27, "lifecycl": 50, "limit": [5, 6, 8, 9, 10, 30], "line": 22, "list": [5, 10], "load": [16, 34, 35, 66, 80], "local": [2, 3, 14, 64], "log": [2, 13, 17, 22, 29, 30, 32, 33, 37, 39, 41, 51, 66, 67], "logic": [31, 53], "login": [7, 38, 66, 67], "logout": 7, "long": [29, 37, 52, 67], "low": [0, 43, 47], "lower": 26, "machin": [24, 25, 26, 57], "mainten": [0, 1, 16, 32, 40, 61, 64, 70], "major": 22, "make": 19, "makefil": [53, 55], "manag": [2, 4, 5, 7, 8, 9, 11, 12, 14, 20, 21, 26, 30, 33, 38, 40, 48, 50, 52, 53, 55, 63, 64, 70, 72, 73, 74, 75, 79], "manifest": 51, "manual": [31, 51], "map": [6, 30, 41, 58, 59, 60], "matrix": [24, 26, 33, 40, 50], "matur": 42, "md": 0, "measur": [3, 23], "medium": [0, 24, 26, 36, 43, 47], "meet": 43, "member": [0, 74, 76, 77], "memori": [17, 66], "mermaid": 3, "messag": 21, "metadata": 5, "method": [28, 68], "methodologi": [35, 48, 59, 71, 76, 77, 78, 79, 80], "metric": [0, 1, 2, 3, 15, 17, 22, 24, 25, 34, 36, 37, 42, 45, 46, 48, 49, 50, 51, 52, 54, 55, 63, 64, 65, 77], "mfa": [7, 67], "migrat": [16, 17, 24, 36, 56], "minimum": 16, "minut": 43, "miss": [17, 66], "mission": 63, "mitig": [10, 80], "mitr": [6, 8, 9, 42, 59, 60, 63, 69, 74, 75], "ml": [22, 25, 37, 57], "mock": 65, "mode": [2, 30], "model": [8, 10, 21, 25, 26, 42, 48, 51, 56, 57, 58, 62, 63, 71, 74, 80], "modul": 26, "mondai": 33, "monitor": [2, 3, 4, 8, 10, 11, 12, 13, 15, 16, 17, 26, 30, 31, 33, 34, 35, 38, 39, 40, 41, 42, 43, 52, 54, 61, 62, 68, 70, 72, 79, 80], "month": 24, "monthli": 32, "morn": 33, "mortem": 43, "multi": [2, 7, 26, 30, 35, 37, 38, 52, 59, 67, 68], "my": 67, "name": 21, "namespac": 16, "navig": [3, 9, 75], "neo4j": 35, "network": [11, 15, 16, 17, 30, 66, 68], "new": [0, 2, 3, 20, 24, 25, 26, 27, 67], "next": [1, 16, 20, 21, 24, 25, 26, 28, 29, 34, 35, 36, 37, 38, 42, 64, 65, 68, 69], "nfr": 63, "nist": [40, 56], "non": 63, "note": [1, 27], "notif": [40, 43], "number": 49, "object": [25, 36, 40, 45, 52], "offens": [3, 77], "offic": [72, 74], "onboard": 79, "ongo": [16, 43], "ons": 16, "oper": [0, 3, 4, 5, 12, 14, 15, 27, 29, 35, 40, 41, 49, 52, 62, 70, 74, 76, 77, 79], "opportun": 0, "optim": [4, 11, 12, 15, 24, 25, 30, 32, 33, 34, 35, 59, 60, 61, 68, 69, 70, 71, 75, 76, 80], "option": 2, "orchestr": [36, 63], "organ": [0, 1, 18, 21, 40], "origin": 36, "out": 47, "overview": [0, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 21, 23, 24, 26, 27, 29, 30, 31, 32, 33, 34, 35, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 74, 75, 80], "p0": [33, 43], "p1": [33, 40, 43], "p2": 43, "p3": 43, "pagin": 8, "parallel": 59, "paramet": 5, "parametr": 65, "partial": [25, 26], "password": [7, 38, 67], "patch": [32, 50, 65], "path": [0, 5, 6, 8, 38, 59, 60, 61, 63, 67, 69, 71, 74, 77, 79], "pattern": [9, 34, 51, 65, 75], "pci": [42, 69], "pend": 17, "perform": [0, 2, 3, 4, 6, 11, 12, 13, 14, 15, 16, 17, 20, 25, 27, 28, 29, 30, 32, 33, 34, 35, 36, 38, 40, 41, 50, 52, 53, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 73, 75, 77, 80], "period": 46, "permiss": [2, 7, 66, 70, 79], "persist": 17, "phase": [24, 25, 26, 36, 43, 45, 63], "philosophi": 64, "physic": 42, "pi1": 40, "pickl": 49, "pipelin": [2, 14, 41, 53, 54, 57, 64], "pitfal": [35, 46], "plan": [3, 24, 30, 32, 36, 69], "platform": [27, 38, 63, 67, 70, 72, 73, 76, 77, 78, 79], "pod": [16, 17], "point": 31, "polici": [4, 11, 16, 40, 47, 51], "popul": 56, "port": 2, "portabl": 40, "posit": 55, "post": [16, 43, 45], "postgresql": 35, "postur": 48, "powershel": 26, "pr": 40, "practic": [0, 1, 2, 4, 6, 7, 8, 11, 12, 13, 15, 21, 35, 41, 46, 53, 54, 55, 64, 65, 68, 69, 70, 71, 74, 75, 77, 78, 79, 80], "prd": [25, 63], "pre": [14, 31, 51, 54, 80], "predict": [22, 25, 26, 37, 57], "prepar": [14, 16, 72], "prepared": 45, "prerequisit": [2, 3, 14, 16, 20, 28, 30, 38, 71, 75, 80], "prevent": 32, "primari": [25, 53, 55, 79], "principl": [4, 11, 18, 39, 40, 44, 47, 65], "priorit": [50, 69], "prioriti": [0, 24, 26, 33, 36], "privaci": [12, 40], "privileg": [4, 22, 29, 37], "problem": [17, 66], "procedur": [16, 17, 31, 32, 33, 40, 43, 45, 46, 79], "process": [12, 14, 19, 21, 40, 43, 45, 46, 47, 51, 55, 57, 58, 59, 63], "product": [3, 13, 14, 15, 16, 22, 24, 25, 26, 27, 28, 29, 37, 62, 63], "profession": [0, 3, 47, 72, 73, 78, 79], "profil": [9, 20, 35, 38, 80], "program": [42, 47, 50, 52], "prometheu": 33, "proof": 41, "proposit": 63, "protect": [11, 30, 40, 49, 51, 63], "provid": [13, 38, 66, 67, 68], "provis": [4, 14, 16], "public": 47, "pull": 19, "purpl": [0, 69, 71, 74, 76], "purpos": 21, "push": [14, 16], "pytest": 65, "python": [5, 7, 18, 19, 26, 30], "q1": [24, 25, 26], "q2": 63, "q3": [24, 25, 26, 36, 52, 63], "q4": [24, 25, 26, 36, 52, 63], "qualit": 24, "qualiti": [0, 1, 3, 18, 20, 21, 22, 27, 37, 46, 54, 62, 64, 65], "quantit": [24, 78, 80], "queri": 5, "question": 67, "quick": [2, 3, 27, 28, 34, 38, 44, 66, 71], "r": 40, "radiu": [1, 3, 6, 14, 16, 17, 24, 25, 26, 27, 31, 32, 33, 40, 43, 51, 59, 60, 63, 67, 69, 71], "random": 49, "rate": [5, 6, 8, 9, 10], "rbac": [11, 70], "rc": 40, "readi": [14, 22, 27, 29, 37], "real": [11, 38, 41, 75, 79], "recent": 52, "recognit": [3, 19, 23, 47, 75], "recommend": [0, 2, 7, 28, 29, 34, 38, 80], "record": 12, "recov": 40, "recoveri": [15, 16, 17, 31, 43, 45, 60, 70], "rectif": 40, "red": [0, 69, 71, 74, 77], "redi": 35, "redirect": 16, "reduc": 36, "reduct": 49, "refer": [3, 8, 9, 10, 27, 44], "refresh": [6, 7], "regular": 43, "regulatori": [45, 48, 51, 52, 72, 73, 80], "relationship": [5, 61], "releas": [1, 21, 27, 29], "reliabl": 63, "remedi": [50, 54], "renew": 32, "report": [12, 40, 41, 45, 47, 50, 54, 55, 64, 68, 77, 79], "repositori": [14, 20], "request": [4, 16, 19], "requir": [14, 15, 16, 20, 21, 24, 26, 28, 37, 43, 46, 51, 62, 63, 65, 67], "research": 47, "reset": 67, "resolut": 43, "resolv": 49, "resourc": [2, 8, 15, 17, 30, 34, 38, 43, 51, 62, 73, 74, 78, 79], "respond": 40, "respons": [3, 5, 6, 8, 17, 25, 26, 33, 42, 43, 45, 47, 51, 52, 66, 69, 71, 72, 73, 74, 76, 77, 78, 79], "rest": [18, 75], "restrict": 40, "result": [5, 6, 10, 34, 38, 48, 49, 52, 53, 55, 57, 68, 69], "retent": [33, 61], "review": [4, 14, 19, 21, 32, 33, 43, 46, 49, 51, 52], "reward": 47, "right": [12, 40], "risk": [4, 8, 10, 26, 30, 36, 48, 49, 50, 52, 59, 60, 63, 67, 69, 71, 72, 73, 78, 80], "roadmap": [23, 25, 29, 36, 37, 52, 63, 67], "roi": 52, "role": [1, 3, 4, 38, 43, 70, 72, 73, 74, 76, 77, 78, 79], "rollback": [16, 17, 36], "rollout": 24, "room": 43, "rotat": 32, "routin": 32, "rst": 0, "rule": 55, "run": [2, 38, 64, 65, 67, 80], "runbook": [31, 33], "runtim": 54, "safe": 47, "sampl": 38, "sanit": 51, "sast": [48, 50, 54, 55], "scalabl": [0, 15, 25, 27, 34, 63], "scale": [25, 26, 34], "scan": [2, 49, 50, 51, 54], "scenario": [0, 6, 60, 63, 68, 69, 71, 76, 77, 79], "schedul": [32, 40, 68], "schema": [12, 18, 22, 26, 41, 56, 61, 62], "scope": 47, "score": [30, 50, 59, 60, 67, 69, 71], "script": [14, 56], "sdk": [5, 8, 25, 26, 30], "sdlc": 50, "search": 3, "secret": [16, 51], "section": [1, 3, 44], "secur": [0, 1, 2, 3, 7, 9, 11, 13, 14, 15, 16, 17, 18, 21, 22, 24, 25, 26, 27, 29, 30, 31, 32, 33, 36, 37, 39, 40, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 62, 63, 64, 67, 68, 70, 71, 73, 74, 76, 77, 78, 80], "securitythreatspik": 33, "see": 67, "seed": 56, "select": 8, "seo": 3, "servic": [11, 12, 14, 17, 20, 24, 25, 30, 40, 41, 57, 58, 68, 73], "servicenow": [13, 38], "set": [13, 16, 70], "setup": [0, 2, 10, 14, 16, 20, 24, 28, 30, 34, 38, 43, 64, 67, 70, 79], "sever": [43, 45, 47], "short": [36, 52], "should": 67, "siem": [24, 25, 26, 63, 67, 69], "simul": [10, 69, 77, 80], "size": 34, "slow": 66, "slowli": 67, "snapshot": 31, "soa": 40, "soar": [63, 69], "soc": [0, 40, 42, 51, 74, 79], "softwar": [14, 20, 28], "solid": 18, "solut": 17, "sort": 8, "special": 77, "specif": [2, 13, 14, 15, 22, 29, 43, 56, 57, 58, 74], "sphinx": [3, 22, 37], "sqlalchemi": 56, "ssl": [16, 17, 66], "stage": [2, 13, 14], "stakehold": 73, "standard": [0, 2, 3, 7, 18, 19, 21, 39, 52], "start": [0, 1, 2, 3, 5, 14, 19, 20, 27, 28, 30, 38, 70, 71, 75, 79, 80], "startup": [16, 17], "state": [17, 24, 36], "statement": [40, 63], "static": [48, 54, 55], "statist": [0, 3, 6], "statu": [5, 8, 9, 18, 25, 27, 36, 37, 43, 48, 50, 52], "step": [1, 16, 19, 20, 21, 25, 28, 29, 34, 35, 36, 37, 38, 42, 51, 64, 65, 68, 69], "storag": 17, "strateg": [52, 72, 73, 76, 78], "strategi": [2, 10, 15, 21, 24, 35, 36, 41, 50, 56, 59, 60, 68, 80], "strength": 36, "stride": 46, "structur": [0, 1, 3, 7, 19, 22, 41, 52, 56, 62, 65], "stuck": 17, "style": [3, 18, 19], "subject": [12, 40], "submit": 19, "success": [0, 8, 24, 25, 36, 63], "summari": [0, 1, 22, 24, 25, 26, 27, 29, 36, 37, 48, 49, 52, 63, 72, 73, 76, 77, 78, 79], "sundai": 32, "supervisori": 40, "support": [2, 3, 8, 17, 27, 30, 62, 66, 67, 70, 72, 73, 74, 76, 77, 78, 79], "sync": 9, "synchron": 75, "system": [3, 7, 12, 14, 16, 17, 20, 28, 31, 32, 33, 40, 41, 43, 59, 62, 67, 70], "t": [38, 67], "tabl": [5, 18, 19, 20, 21, 23, 27, 34, 35, 42, 56, 61, 64, 65, 66, 68, 69, 72, 73, 76, 77, 78, 79], "tag": 5, "take": 67, "tamper": 41, "tar": 63, "target": [25, 35, 36, 63], "task": [20, 57], "team": [0, 43, 45, 69, 71, 74, 76, 77], "technic": [0, 1, 3, 12, 22, 24, 25, 26, 27, 29, 36, 37, 42, 43, 56, 57, 58, 62, 63, 76, 77], "techniqu": [9, 75], "templat": [0, 4, 46, 51], "term": [29, 36, 37, 47, 52], "terraform": [14, 51], "test": [0, 2, 8, 16, 18, 19, 20, 21, 27, 28, 31, 34, 39, 48, 49, 51, 52, 53, 54, 55, 62, 64, 65, 77], "thehiv": [22, 24, 26, 37, 58], "thi": 67, "threat": [8, 9, 10, 13, 22, 25, 26, 37, 38, 42, 46, 48, 51, 57, 63, 74, 75, 79, 80], "through": 24, "time": [4, 11, 17, 31, 38, 41, 52, 66, 75, 79], "timelin": [24, 25, 26, 36], "timeout": 17, "tip": [11, 74], "tl": [11, 16, 17, 66], "token": 7, "tool": [1, 2, 3, 14, 16, 17, 18, 20, 21, 24, 25, 26, 27, 31, 32, 33, 35, 40, 43, 48, 50, 51, 53, 54, 55, 67, 68], "tour": 38, "track": [9, 46, 50], "traefik": [24, 25], "train": [42, 43, 45, 51, 74, 79], "transform": 73, "tree": 60, "trigger": 46, "troubleshoot": [0, 1, 2, 4, 11, 12, 13, 16, 17, 20, 27, 28, 30, 34, 41, 66, 67, 68, 70, 71, 72, 76, 77], "trust": [11, 15, 22, 29, 37, 40, 42, 69], "tune": [35, 41, 62], "type": [5, 14, 19, 21, 30, 42, 46, 51, 61, 69, 71], "typescript": [7, 18, 19], "ui": 66, "understand": 38, "unit": [64, 65], "unsaf": 49, "up": 16, "updat": [5, 30, 32, 43, 47, 50, 67, 70], "url": [5, 6, 8, 9, 10], "us": [0, 1, 27, 67, 68, 69, 71], "usabl": [1, 63], "usag": [0, 4, 18, 30, 67], "user": [0, 1, 3, 8, 22, 23, 25, 27, 28, 38, 63, 67, 70, 71, 74, 75, 80], "utc": 32, "v2": 25, "valid": [11, 13, 31, 39, 49, 51, 53, 69], "valu": [23, 52, 63], "variabl": [2, 16, 28, 66], "verif": [11, 16, 28, 31], "verifi": 14, "version": [21, 67], "vertic": 34, "view": 38, "vision": [25, 63], "visual": [25, 26, 38, 75, 79], "volum": [1, 17], "vulner": [43, 47, 48, 50, 52, 53, 55], "wai": 19, "war": 43, "warn": 22, "weak": 49, "web": [38, 67, 71], "webhook": [8, 58], "week": [24, 25, 26, 36], "weekli": [32, 33], "what": 67, "who": 67, "why": 67, "win": 34, "window": 32, "won": 38, "work": 67, "workflow": [2, 3, 12, 14, 19, 20, 21, 25, 51, 70, 71, 74, 75, 79, 80], "workstat": 54, "write": [19, 64, 65], "you": 19, "your": [19, 38], "zero": [11, 15, 22, 29, 37, 42, 69]}})