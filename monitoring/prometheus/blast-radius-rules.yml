# Prometheus Alerting Rules for Blast-Radius Security Tool

groups:
  - name: blast-radius-api
    interval: 30s
    rules:
      # API Health and Availability
      - alert: BlastRadiusAPIDown
        expr: up{job="blast-radius-backend"} == 0
        for: 1m
        labels:
          severity: critical
          service: api
          team: platform
        annotations:
          summary: "Blast-Radius API is down"
          description: "The Blast-Radius API has been down for more than 1 minute."
          runbook_url: "https://docs.blast-radius.com/runbooks/api-down"

      - alert: BlastRadiusAPIHighErrorRate
        expr: rate(http_requests_total{job="blast-radius-backend",status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
          service: api
          team: platform
        annotations:
          summary: "High error rate in Blast-Radius API"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes."

      - alert: BlastRadiusAPIHighLatency
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="blast-radius-backend"}[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: api
          team: platform
        annotations:
          summary: "High latency in Blast-Radius API"
          description: "95th percentile latency is {{ $value }}s for the last 5 minutes."

      - alert: BlastRadiusAPIHighRequestRate
        expr: rate(http_requests_total{job="blast-radius-backend"}[5m]) > 100
        for: 5m
        labels:
          severity: info
          service: api
          team: platform
        annotations:
          summary: "High request rate in Blast-Radius API"
          description: "Request rate is {{ $value }} requests/second for the last 5 minutes."

  - name: blast-radius-database
    interval: 30s
    rules:
      # Database Health
      - alert: PostgreSQLDown
        expr: up{job="postgres-exporter"} == 0
        for: 1m
        labels:
          severity: critical
          service: database
          team: platform
        annotations:
          summary: "PostgreSQL database is down"
          description: "PostgreSQL database has been down for more than 1 minute."

      - alert: PostgreSQLHighConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: database
          team: platform
        annotations:
          summary: "PostgreSQL connection usage is high"
          description: "Connection usage is {{ $value | humanizePercentage }}."

      - alert: PostgreSQLSlowQueries
        expr: rate(pg_stat_database_tup_returned[5m]) / rate(pg_stat_database_tup_fetched[5m]) < 0.1
        for: 10m
        labels:
          severity: warning
          service: database
          team: platform
        annotations:
          summary: "PostgreSQL has slow queries"
          description: "Query efficiency is low: {{ $value | humanizePercentage }}."

      - alert: PostgreSQLHighDiskUsage
        expr: (pg_database_size_bytes / (1024^3)) > 50
        for: 5m
        labels:
          severity: warning
          service: database
          team: platform
        annotations:
          summary: "PostgreSQL disk usage is high"
          description: "Database size is {{ $value }}GB."

  - name: blast-radius-redis
    interval: 30s
    rules:
      # Redis Health
      - alert: RedisDown
        expr: up{job="redis-exporter"} == 0
        for: 1m
        labels:
          severity: critical
          service: cache
          team: platform
        annotations:
          summary: "Redis cache is down"
          description: "Redis cache has been down for more than 1 minute."

      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 90
        for: 5m
        labels:
          severity: warning
          service: cache
          team: platform
        annotations:
          summary: "Redis memory usage is high"
          description: "Memory usage is {{ $value | humanizePercentage }}."

      - alert: RedisHighConnections
        expr: redis_connected_clients > 100
        for: 5m
        labels:
          severity: warning
          service: cache
          team: platform
        annotations:
          summary: "Redis has high number of connections"
          description: "Connected clients: {{ $value }}."

  - name: blast-radius-security
    interval: 30s
    rules:
      # Security-specific alerts
      - alert: HighRiskAssetsDetected
        expr: blast_radius_high_risk_assets_total > 10
        for: 2m
        labels:
          severity: warning
          service: security
          team: security
        annotations:
          summary: "High number of high-risk assets detected"
          description: "{{ $value }} high-risk assets detected in the security graph."

      - alert: SecurityThreatSpike
        expr: increase(blast_radius_security_threats_total[10m]) > 5
        for: 1m
        labels:
          severity: critical
          service: security
          team: security
        annotations:
          summary: "Security threat spike detected"
          description: "{{ $value }} new security threats detected in the last 10 minutes."

      - alert: BlastRadiusAnalysisFailure
        expr: increase(blast_radius_analysis_failures_total[5m]) > 3
        for: 2m
        labels:
          severity: warning
          service: analysis
          team: platform
        annotations:
          summary: "Multiple blast radius analysis failures"
          description: "{{ $value }} analysis failures in the last 5 minutes."

      - alert: VulnerabilityDatabaseOutdated
        expr: time() - blast_radius_vulnerability_db_last_update > 86400
        for: 1m
        labels:
          severity: warning
          service: security
          team: security
        annotations:
          summary: "Vulnerability database is outdated"
          description: "Vulnerability database hasn't been updated for more than 24 hours."

  - name: blast-radius-system
    interval: 30s
    rules:
      # System Resource Alerts
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: system
          team: platform
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}."

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
          team: platform
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}."

      - alert: HighDiskUsage
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
          team: platform
        annotations:
          summary: "High disk usage detected"
          description: "Disk usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}."

      - alert: DiskSpaceCritical
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 95
        for: 1m
        labels:
          severity: critical
          service: system
          team: platform
        annotations:
          summary: "Critical disk space usage"
          description: "Disk usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}."

  - name: blast-radius-business
    interval: 60s
    rules:
      # Business Logic Alerts
      - alert: LowAssetDiscoveryRate
        expr: rate(blast_radius_assets_discovered_total[1h]) < 10
        for: 10m
        labels:
          severity: info
          service: discovery
          team: platform
        annotations:
          summary: "Low asset discovery rate"
          description: "Only {{ $value }} assets discovered per hour in the last hour."

      - alert: GraphGrowthStagnation
        expr: increase(blast_radius_graph_nodes_total[6h]) < 50
        for: 30m
        labels:
          severity: info
          service: graph
          team: platform
        annotations:
          summary: "Graph growth has stagnated"
          description: "Only {{ $value }} new nodes added to the graph in the last 6 hours."

      - alert: HighAnalysisQueueDepth
        expr: blast_radius_analysis_queue_depth > 100
        for: 5m
        labels:
          severity: warning
          service: analysis
          team: platform
        annotations:
          summary: "High analysis queue depth"
          description: "{{ $value }} analyses queued for processing."
