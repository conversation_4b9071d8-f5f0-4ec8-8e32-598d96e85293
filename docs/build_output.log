Running Sphinx v7.4.7
Warning: sphinxcontrib.mermaid not available. Mermaid diagrams will be shown as code blocks.
loading translations [en]... done
Converting `source_suffix = ['.rst', '.md']` to `source_suffix = {'.rst': 'restructuredtext', '.md': 'restructuredtext'}`.
loading pickled environment... done
[autosummary] generating autosummary for: DOCUMENTATION_EXPANSION_SUMMARY.md, DOCUMENTATION_SUMMARY.md, LOCAL-DEVELOPMENT.md, README.md, access-control/least-privilege-framework.rst, api/asset-management.rst, api/attack-path-analysis.rst, api/authentication.rst, api/index.rst, api/mitre-attack-integration.rst, ..., user-guides/attack-path-analysis.rst, user-guides/compliance-officers.rst, user-guides/executive-leadership.rst, user-guides/index.rst, user-guides/mitre-attack-integration.rst, user-guides/purple-team-members.rst, user-guides/red-team-members.rst, user-guides/security-architects.rst, user-guides/soc-operators.rst, user-guides/threat-modeling.rst
myst v4.0.0: MdParserConfig(commonmark_only=False, gfm_only=False, enable_extensions={'colon_fence', 'deflist', 'smartquotes', 'substitution', 'tasklist', 'dollarmath', 'html_image', 'html_admonition', 'replacements', 'fieldlist', 'strikethrough'}, disable_syntax=[], all_links_external=False, links_external_new_tab=False, url_schemes=('http', 'https', 'mailto', 'ftp'), ref_domains=None, fence_as_directive=set(), number_code_blocks=[], title_to_header=False, heading_anchors=0, heading_slug_func=None, html_meta={}, footnote_sort=True, footnote_transition=True, words_per_minute=200, substitutions={}, linkify_fuzzy_links=True, dmath_allow_labels=True, dmath_allow_space=True, dmath_allow_digits=True, dmath_double_inline=False, update_mathjax=True, mathjax_classes='tex2jax_process|mathjax_process|math|output_area', enable_checkboxes=False, suppress_warnings=[], highlight_code_blocks=True)
building [mo]: targets for 0 po files that are out of date
writing output... 
building [html]: targets for 90 source files that are out of date
updating environment: 0 added, 2 changed, 0 removed
reading sources... [ 50%] index
reading sources... [100%] user-guides/index

/home/<USER>/dev/work/blast-radius/docs/user-guides/index.rst:244: WARNING: Title underline too short.

Threat Modeling
~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/index.rst:244: WARNING: Title underline too short.

Threat Modeling
~~~~~~~~~~~~~~
/home/<USER>/dev/work/blast-radius/docs/user-guides/index.rst:294: WARNING: Title underline too short.

Common Workflows
---------------
/home/<USER>/dev/work/blast-radius/docs/user-guides/index.rst:294: WARNING: Title underline too short.

Common Workflows
---------------
looking for now-outdated files... none found
pickling environment... done
checking consistency... done
preparing documents... done
copying assets... 
copying static files... done
copying extra files... done
copying assets: done
writing output... [  1%] DOCUMENTATION_EXPANSION_SUMMARY
writing output... [  2%] DOCUMENTATION_SUMMARY
writing output... [  3%] LOCAL-DEVELOPMENT
writing output... [  4%] README
writing output... [  6%] access-control/least-privilege-framework
writing output... [  7%] api/asset-management
writing output... [  8%] api/attack-path-analysis
writing output... [  9%] api/authentication
writing output... [ 10%] api/index
writing output... [ 11%] api/mitre-attack-integration
writing output... [ 12%] api/threat-modeling
writing output... [ 13%] architecture/zero-trust-architecture
writing output... [ 14%] business/competitive-landscape
writing output... [ 16%] business/enterprise-saas-model
writing output... [ 17%] business/index
writing output... [ 18%] business/managed-security-services
writing output... [ 19%] business/market-analysis
writing output... [ 20%] business/open-source-enterprise
writing output... [ 21%] business/pricing-strategy
writing output... [ 22%] business/revenue-models
writing output... [ 23%] business/strategic-next-phases
writing output... [ 24%] compliance/gdpr-compliance-framework
writing output... [ 26%] configuration
writing output... [ 27%] deployment/environment-setup
writing output... [ 28%] deployment/production-architecture
writing output... [ 29%] deployment/production-deployment-guide
writing output... [ 30%] deployment/troubleshooting-guide
writing output... [ 31%] development/code-standards
writing output... [ 32%] development/contributing
writing output... [ 33%] development/setup
writing output... [ 34%] development/workflow
writing output... [ 36%] documentation-achievements-summary
writing output... [ 37%] documentation-overview
writing output... [ 38%] enhanced-features-summary
writing output... [ 39%] enhanced-prd-v2
writing output... [ 40%] implementation-gap-analysis
writing output... [ 41%] index
writing output... [ 42%] installation
writing output... [ 43%] latest-implementations-summary
writing output... [ 44%] multi-cloud-integration
writing output... [ 46%] operations/runbooks/backup-recovery-runbooks
writing output... [ 47%] operations/runbooks/maintenance-procedures
writing output... [ 48%] operations/runbooks/monitoring-runbooks
writing output... [ 49%] performance/index
writing output... [ 50%] performance/optimization
writing output... [ 51%] phase-integration-plan
writing output... [ 52%] production-readiness-status
writing output... [ 53%] quick-start-guide
writing output... [ 54%] security/architecture/overview
writing output... [ 56%] security/compliance-documentation
writing output... [ 57%] security/enhanced-audit-logging
writing output... [ 58%] security/framework
writing output... [ 59%] security/incident-response-procedures
writing output... [ 60%] security/index
writing output... [ 61%] security/operations/incident-response
writing output... [ 62%] security/procedures/security-review-process
writing output... [ 63%] security/procedures/vulnerability-disclosure
writing output... [ 64%] security/reviews/security-assessment
writing output... [ 66%] security/reviews/security-review-2025-06-14
writing output... [ 67%] security/reviews/vulnerability-management
writing output... [ 68%] security/security-review-processes
writing output... [ 69%] security/security-summary
writing output... [ 70%] security/testing/dynamic-testing
writing output... [ 71%] security/testing/security-automation
writing output... [ 72%] security/testing/static-analysis
writing output... [ 73%] technical-specifications/compliance-framework-schema
writing output... [ 74%] technical-specifications/ml-threat-prediction
writing output... [ 76%] technical-specifications/thehive-integration
writing output... [ 77%] technical/attack-path-architecture
writing output... [ 78%] technical/attack-path-flows
writing output... [ 79%] technical/database-design
writing output... [ 80%] technical/index
writing output... [ 81%] technical/product-requirements
writing output... [ 82%] testing/index
writing output... [ 83%] testing/unit-tests
writing output... [ 84%] troubleshooting/common-issues
writing output... [ 86%] troubleshooting/faq
writing output... [ 87%] use-cases/asset-discovery
writing output... [ 88%] use-cases/attack-path-analysis
writing output... [ 89%] user-guides/administrators
writing output... [ 90%] user-guides/attack-path-analysis
writing output... [ 91%] user-guides/compliance-officers
writing output... [ 92%] user-guides/executive-leadership
writing output... [ 93%] user-guides/index
writing output... [ 94%] user-guides/mitre-attack-integration
writing output... [ 96%] user-guides/purple-team-members
writing output... [ 97%] user-guides/red-team-members
writing output... [ 98%] user-guides/security-architects
writing output... [ 99%] user-guides/soc-operators
writing output... [100%] user-guides/threat-modeling

/home/<USER>/dev/work/blast-radius/docs/LOCAL-DEVELOPMENT.md:392: WARNING: 'myst' cross-reference target not found: '../ARCHITECTURE.md'
/home/<USER>/dev/work/blast-radius/docs/LOCAL-DEVELOPMENT.md:393: WARNING: 'myst' cross-reference target not found: '../SECURITY.md'
/home/<USER>/dev/work/blast-radius/docs/LOCAL-DEVELOPMENT.md:394: WARNING: 'myst' cross-reference target not found: '../DEPLOYMENT.md'
/home/<USER>/dev/work/blast-radius/docs/README.md:195: WARNING: 'myst' cross-reference target not found: '../CONTRIBUTING.md'
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:9: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:63: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:103: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:142: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:180: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:214: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:332: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:375: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:417: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/access-control/least-privilege-framework.rst:468: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/api/authentication.rst:323: WARNING: unknown document: '../security/access-control'
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:9: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:63: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:103: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:141: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:177: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:217: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:258: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:290: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:330: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:359: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/architecture/zero-trust-architecture.rst:438: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/business/enterprise-saas-model.rst:9: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/business/managed-security-services.rst:9: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/business/open-source-enterprise.rst:9: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:9: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:62: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:101: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:141: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:178: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:221: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:258: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:349: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:375: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:399: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/compliance/gdpr-compliance-framework.rst:436: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:9: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:61: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:122: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:190: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:249: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:308: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:369: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:433: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:474: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:536: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:625: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/deployment/production-architecture.rst:665: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/deployment/production-deployment-guide.md:541: WARNING: 'myst' cross-reference target not found: '../operations/'
/home/<USER>/dev/work/blast-radius/docs/development/code-standards.rst:303: WARNING: Lexing literal_block "// Interfaces - PascalCase with 'I' prefix (optional)\ninterface AttackPathResult {\n  paths: AttackPath[];\n  riskScore: number;\n}\n\n// Types - PascalCase\ntype AssetType = 'server' | 'database' | 'workstation';\n\n// Components - PascalCase\nconst AttackPathVisualization: React.FC<Props> = ({ data }) => {\n  return <div>{/* Component content */}</div>;\n};\n\n// Functions and variables - camelCase\nconst analyzeAttackPath = async (sourceId: number): Promise<AttackPath[]> => {\n  const analysisResult = await api.analyzeAttackPath(sourceId);\n  return analysisResult.paths;\n};\n\n// Constants - UPPER_SNAKE_CASE\nconst MAX_RETRY_ATTEMPTS = 3;\nconst API_BASE_URL = process.env.REACT_APP_API_URL;" as "typescript" resulted in an error at token: '/'. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/development/code-standards.rst:331: WARNING: Lexing literal_block "import React, { useState, useCallback, useEffect } from 'react';\nimport { AttackPath, Asset } from '../types';\nimport { attackPathService } from '../services';\n\ninterface AttackPathAnalyzerProps {\n  sourceAsset: Asset;\n  targetAsset: Asset;\n  onAnalysisComplete: (paths: AttackPath[]) => void;\n  className?: string;\n}\n\nexport const AttackPathAnalyzer: React.FC<AttackPathAnalyzerProps> = ({\n  sourceAsset,\n  targetAsset,\n  onAnalysisComplete,\n  className = ''\n}) => {\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const handleAnalysis = useCallback(async () => {\n    if (!sourceAsset || !targetAsset) {\n      setError('Source and target assets are required');\n      return;\n    }\n\n    setIsAnalyzing(true);\n    setError(null);\n\n    try {\n      const paths = await attackPathService.analyze({\n        sourceAssetId: sourceAsset.id,\n        targetAssetId: targetAsset.id\n      });\n      onAnalysisComplete(paths);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Analysis failed';\n      setError(errorMessage);\n    } finally {\n      setIsAnalyzing(false);\n    }\n  }, [sourceAsset, targetAsset, onAnalysisComplete]);\n\n  useEffect(() => {\n    // Auto-analyze when assets change\n    if (sourceAsset && targetAsset) {\n      handleAnalysis();\n    }\n  }, [sourceAsset?.id, targetAsset?.id, handleAnalysis]);\n\n  return (\n    <div className={`attack-path-analyzer ${className}`}>\n      {/* Component JSX */}\n    </div>\n  );\n};" as "typescript" resulted in an error at token: '/'. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/development/contributing.rst:288: WARNING: Lexing literal_block '// Good examples\n\ninterface AttackPathProps {\n  sourceAsset: Asset;\n  targetAsset: Asset;\n  onAnalysisComplete: (result: AttackPathResult) => void;\n}\n\nconst AttackPathAnalyzer: React.FC<AttackPathProps> = ({\n  sourceAsset,\n  targetAsset,\n  onAnalysisComplete\n}) => {\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const analyzeAttackPath = useCallback(async () => {\n    setIsAnalyzing(true);\n    setError(null);\n\n    try {\n      const result = await attackPathService.analyze({\n        sourceAssetId: sourceAsset.id,\n        targetAssetId: targetAsset.id\n      });\n      onAnalysisComplete(result);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \'Analysis failed\');\n    } finally {\n      setIsAnalyzing(false);\n    }\n  }, [sourceAsset.id, targetAsset.id, onAnalysisComplete]);\n\n  return (\n    <div className="attack-path-analyzer">\n      {/* Component JSX */}\n    </div>\n  );\n};' as "typescript" resulted in an error at token: '/'. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/development/workflow.rst:612: WARNING: unknown document: 'testing'
/home/<USER>/dev/work/blast-radius/docs/development/workflow.rst:29: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/installation.rst:405: WARNING: unknown document: 'security/access-control'
/home/<USER>/dev/work/blast-radius/docs/installation.rst:406: WARNING: unknown document: 'security/best-practices'
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:9: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:65: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:102: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:138: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:171: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:206: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:242: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/latest-implementations-summary.rst:373: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/multi-cloud-integration.md:11: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/performance/index.rst:502: WARNING: unknown document: 'monitoring'
/home/<USER>/dev/work/blast-radius/docs/performance/index.rst:503: WARNING: unknown document: 'scalability'
/home/<USER>/dev/work/blast-radius/docs/performance/index.rst:504: WARNING: unknown document: 'benchmarks'
/home/<USER>/dev/work/blast-radius/docs/performance/index.rst:26: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/performance/optimization.rst:289: WARNING: Lexing literal_block '// Before: Inefficient query\nMATCH (source:Asset)-[*1..5]-(target:Asset)\nWHERE source.id = $sourceId AND target.id = $targetId\nRETURN path;\n\n// After: Optimized query with constraints\nMATCH path = (source:Asset)-[*1..5]-(target:Asset)\nWHERE source.id = $sourceId\nAND target.id = $targetId\nAND ALL(n IN nodes(path) WHERE n.is_active = true)\nRETURN path\nLIMIT 100;\n\n// Use indexes for better performance\nCREATE INDEX asset_id_index FOR (a:Asset) ON (a.id);\nCREATE INDEX asset_type_index FOR (a:Asset) ON (a.type);' as "cypher" resulted in an error at token: '$'. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/quick-start-guide.rst:310: WARNING: unknown document: 'security/best-practices'
/home/<USER>/dev/work/blast-radius/docs/quick-start-guide.rst:311: WARNING: unknown document: 'technical/monitoring'
/home/<USER>/dev/work/blast-radius/docs/quick-start-guide.rst:312: WARNING: unknown document: 'technical/deployment'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:127: WARNING: unknown document: 'threat-model'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:128: WARNING: unknown document: 'security-controls'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:129: WARNING: unknown document: 'data-flow'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:130: WARNING: unknown document: '../access-control/authentication'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:131: WARNING: unknown document: '../data-protection/encryption'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:132: WARNING: unknown document: '../infrastructure/network-security'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:147: WARNING: unknown document: '../compliance/soc2'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:148: WARNING: unknown document: '../compliance/gdpr'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:149: WARNING: unknown document: '../compliance/iso27001'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:164: WARNING: unknown document: '../testing/overview'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:165: WARNING: unknown document: '../testing/penetration-testing'
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst:181: WARNING: unknown document: '../operations/vulnerability-management'
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:9: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:69: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:136: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:174: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:215: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:265: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:320: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:362: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:429: WARNING: Lexing literal_block '# Get audit trail\nGET /api/v1/audit/events?user_id=user-123&start_time=2024-01-01&limit=100\n\n# Get specific event\nGET /api/v1/audit/events/{event_id}' as "python" resulted in an error at token: '?'. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/security/enhanced-audit-logging.rst:556: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/security/framework.rst:27: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/security/index.rst:208: WARNING: unknown document: 'testing/penetration-testing'
/home/<USER>/dev/work/blast-radius/docs/security/index.rst:35: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/security/operations/incident-response.rst:457: WARNING: unknown document: 'security-monitoring'
/home/<USER>/dev/work/blast-radius/docs/security/operations/incident-response.rst:458: WARNING: unknown document: 'vulnerability-management'
/home/<USER>/dev/work/blast-radius/docs/security/operations/incident-response.rst:460: WARNING: unknown document: '../compliance/soc2'
/home/<USER>/dev/work/blast-radius/docs/security/procedures/security-review-process.rst:18: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/security/procedures/vulnerability-disclosure.rst:432: WARNING: unknown document: '../testing/overview'
/home/<USER>/dev/work/blast-radius/docs/security/procedures/vulnerability-disclosure.rst:433: WARNING: unknown document: '../best-practices/secure-development'
/home/<USER>/dev/work/blast-radius/docs/security/security-summary.rst:23: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:602: WARNING: unknown document: 'penetration-testing'
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst:604: WARNING: unknown document: 'overview'
/home/<USER>/dev/work/blast-radius/docs/security/testing/security-automation.rst:18: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/security/testing/static-analysis.rst:488: WARNING: unknown document: 'penetration-testing'
/home/<USER>/dev/work/blast-radius/docs/security/testing/static-analysis.rst:490: WARNING: unknown document: 'overview'
/home/<USER>/dev/work/blast-radius/docs/technical/attack-path-architecture.rst:11: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/technical/attack-path-flows.rst:11: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/technical/attack-path-flows.rst:72: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/technical/attack-path-flows.rst:135: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/technical/attack-path-flows.rst:184: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/technical/attack-path-flows.rst:246: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/technical/attack-path-flows.rst:310: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/technical/attack-path-flows.rst:351: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/technical/attack-path-flows.rst:407: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/technical/database-design.rst:20: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/technical/database-design.rst:77: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/technical/database-design.rst:220: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/technical/database-design.rst:573: WARNING: Lexing literal_block '-- Soft delete trigger function\nCREATE OR REPLACE FUNCTION soft_delete_asset()\nRETURNS TRIGGER AS $$\nBEGIN\n    IF NEW.is_deleted = true AND OLD.is_deleted = false THEN\n        NEW.deleted_at = CURRENT_TIMESTAMP;\n    ELSIF NEW.is_deleted = false AND OLD.is_deleted = true THEN\n        NEW.deleted_at = NULL;\n    END IF;\n    RETURN NEW;\nEND;\n$$ LANGUAGE plpgsql;\n\n-- Apply soft delete trigger\nCREATE TRIGGER trigger_soft_delete_asset\n    BEFORE UPDATE ON assets\n    FOR EACH ROW\n    EXECUTE FUNCTION soft_delete_asset();' as "sql" resulted in an error at token: '$'. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/technical/database-design.rst:596: WARNING: Lexing literal_block "-- Audit log retention (90 days)\nCREATE OR REPLACE FUNCTION cleanup_audit_logs()\nRETURNS void AS $$\nBEGIN\n    DELETE FROM audit_logs\n    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days';\nEND;\n$$ LANGUAGE plpgsql;\n\n-- Attack path cache cleanup (30 days)\nCREATE OR REPLACE FUNCTION cleanup_attack_paths()\nRETURNS void AS $$\nBEGIN\n    DELETE FROM attack_paths\n    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '30 days'\n    AND path_id NOT IN (\n        SELECT DISTINCT unnest(string_to_array(path_nodes::text, ','))\n        FROM attack_scenarios\n        WHERE created_at > CURRENT_TIMESTAMP - INTERVAL '7 days'\n    );\nEND;\n$$ LANGUAGE plpgsql;" as "sql" resulted in an error at token: '$'. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/technical/database-design.rst:626: WARNING: Lexing literal_block '-- Automated maintenance procedures\nCREATE OR REPLACE FUNCTION maintenance_vacuum_analyze()\nRETURNS void AS $$\nBEGIN\n    -- Vacuum and analyze high-traffic tables\n    VACUUM ANALYZE assets;\n    VACUUM ANALYZE asset_relationships;\n    VACUUM ANALYZE attack_paths;\n    VACUUM ANALYZE audit_logs;\n\n    -- Update table statistics\n    ANALYZE assets;\n    ANALYZE asset_relationships;\n    ANALYZE attack_paths;\n    ANALYZE attack_scenarios;\nEND;\n$$ LANGUAGE plpgsql;' as "sql" resulted in an error at token: '$'. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/technical/database-design.rst:648: WARNING: Lexing literal_block '-- Reindex heavily used indexes\nCREATE OR REPLACE FUNCTION maintenance_reindex()\nRETURNS void AS $$\nBEGIN\n    REINDEX INDEX CONCURRENTLY idx_assets_type_provider;\n    REINDEX INDEX CONCURRENTLY idx_asset_relationships_source;\n    REINDEX INDEX CONCURRENTLY idx_attack_paths_source_target;\n    REINDEX INDEX CONCURRENTLY idx_attack_paths_criticality;\nEND;\n$$ LANGUAGE plpgsql;' as "sql" resulted in an error at token: '$'. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:27: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst:467: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/testing/index.rst:645: WARNING: unknown document: 'integration-tests'
/home/<USER>/dev/work/blast-radius/docs/testing/index.rst:646: WARNING: unknown document: 'security-tests'
/home/<USER>/dev/work/blast-radius/docs/testing/index.rst:647: WARNING: unknown document: 'performance-tests'
/home/<USER>/dev/work/blast-radius/docs/testing/index.rst:34: WARNING: Pygments lexer name 'mermaid' is not known
/home/<USER>/dev/work/blast-radius/docs/testing/index.rst:332: WARNING: Lexing literal_block '# locustfile.py\nfrom locust import HttpUser, task, between\n\nclass BlastRadiusUser(HttpUser):\n    wait_time = between(1, 3)\n\n    def on_start(self):\n        # Login\n        response = self.client.post("/api/v1/auth/login", json={\n            "email": "<EMAIL>",\n            "password": "testpassword"\n        })\n        self.token = response.json()["access_token"]\n        self.headers = {"Authorization": f"Bearer {self.token}"}\n\n    @task(3)\n    def view_assets(self):\n        self.client.get("/api/v1/assets", headers=self.headers)\n\n    @task(2)\n    def view_attack_paths(self):\n        self.client.get("/api/v1/attack-paths", headers=self.headers)\n\n    @task(1)\n    def run_analysis(self):\n        self.client.post("/api/v1/analysis/attack-paths",\n            json={\n                "source_asset_id": 1,\n                "target_asset_id": 2,\n                "max_depth": 5\n            },\n            headers=self.headers\n        )' as "yaml" resulted in an error at token: ','. Retrying in relaxed mode.
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:335: WARNING: unknown document: '../releases/changelog'
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst:525: WARNING: unknown document: '../releases/roadmap'
generating indices... genindex done
highlighting module code... 
writing additional pages... search opensearch done
dumping search index in English (code: en)... done
dumping object inventory... done
build succeeded, 143 warnings.

The HTML pages are in _build/html.
