#!/bin/bash

# Blue-Green Deployment Script for Blast-Radius Security Tool
# This script implements zero-downtime blue-green deployment

set -e

# Configuration
PROJECT_NAME="blast-radius"
NAMESPACE="blast-radius"
NEW_VERSION=${1}
ENVIRONMENT=${2:-production}
ROLLBACK=${3:-false}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    cat << EOF
Blue-Green Deployment Script for Blast-Radius Security Tool

Usage: $0 <version> [environment] [rollback]

Arguments:
    version     - Application version to deploy (e.g., v1.2.3)
    environment - Target environment (default: production)
    rollback    - Set to 'true' to rollback to previous version

Examples:
    $0 v1.2.3                    # Deploy v1.2.3 to production
    $0 v1.2.3 staging            # Deploy v1.2.3 to staging
    $0 v1.2.3 production true    # Rollback production deployment

EOF
}

# Validate inputs
validate_inputs() {
    if [ -z "$NEW_VERSION" ]; then
        log_error "Version is required"
        show_help
        exit 1
    fi

    if [ "$ROLLBACK" = "true" ]; then
        log_info "Rollback mode enabled"
    fi

    log_info "Deployment configuration:"
    log_info "  Version: $NEW_VERSION"
    log_info "  Environment: $ENVIRONMENT"
    log_info "  Namespace: $NAMESPACE"
    log_info "  Rollback: $ROLLBACK"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."

    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi

    # Check cluster connectivity
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi

    # Check namespace exists
    if ! kubectl get namespace $NAMESPACE &> /dev/null; then
        log_error "Namespace $NAMESPACE does not exist"
        exit 1
    fi

    log_success "Prerequisites check passed"
}

# Get current deployment color
get_current_color() {
    local current_selector=$(kubectl get service $PROJECT_NAME-backend -n $NAMESPACE -o jsonpath='{.spec.selector.version}' 2>/dev/null || echo "")
    
    if [ "$current_selector" = "blue" ]; then
        echo "blue"
    elif [ "$current_selector" = "green" ]; then
        echo "green"
    else
        # Default to blue if no color is set
        echo "blue"
    fi
}

# Get target deployment color
get_target_color() {
    local current_color=$(get_current_color)
    
    if [ "$current_color" = "blue" ]; then
        echo "green"
    else
        echo "blue"
    fi
}

# Create pre-deployment backup
create_backup() {
    log_info "Creating pre-deployment backup..."
    
    local backup_name="pre-deploy-$NEW_VERSION-$(date +%Y%m%d%H%M%S)"
    
    # Create database backup
    if ! ./scripts/manual-db-backup.sh $backup_name; then
        log_error "Database backup failed"
        exit 1
    fi
    
    # Create configuration backup
    if ! ./scripts/config-backup.sh; then
        log_error "Configuration backup failed"
        exit 1
    fi
    
    log_success "Backup created: $backup_name"
    echo "$backup_name" > /tmp/last-backup-name
}

# Deploy to target environment
deploy_target_environment() {
    local target_color=$1
    local version=$2
    
    log_info "Deploying $version to $target_color environment..."
    
    # Update image tags in deployment manifests
    sed -e "s/{{VERSION}}/$version/g" \
        -e "s/{{COLOR}}/$target_color/g" \
        k8s/templates/deployment-template.yaml > /tmp/deployment-$target_color.yaml
    
    sed -e "s/{{COLOR}}/$target_color/g" \
        k8s/templates/service-template.yaml > /tmp/service-$target_color.yaml
    
    # Apply deployment
    kubectl apply -f /tmp/deployment-$target_color.yaml -n $NAMESPACE
    kubectl apply -f /tmp/service-$target_color.yaml -n $NAMESPACE
    
    # Wait for deployment to be ready
    log_info "Waiting for $target_color deployment to be ready..."
    kubectl rollout status deployment/$PROJECT_NAME-backend-$target_color -n $NAMESPACE --timeout=600s
    kubectl rollout status deployment/$PROJECT_NAME-frontend-$target_color -n $NAMESPACE --timeout=600s
    
    # Verify pods are running
    local ready_pods=$(kubectl get pods -n $NAMESPACE -l version=$target_color,app=$PROJECT_NAME-backend --field-selector=status.phase=Running --no-headers | wc -l)
    if [ "$ready_pods" -eq 0 ]; then
        log_error "No pods are running in $target_color environment"
        exit 1
    fi
    
    log_success "$target_color environment deployed successfully"
}

# Run health checks
run_health_checks() {
    local target_color=$1
    
    log_info "Running health checks on $target_color environment..."
    
    # Get service endpoint
    local service_name="$PROJECT_NAME-backend-$target_color"
    local service_port=$(kubectl get service $service_name -n $NAMESPACE -o jsonpath='{.spec.ports[0].port}')
    
    # Port forward for testing
    kubectl port-forward service/$service_name $service_port:$service_port -n $NAMESPACE &
    local port_forward_pid=$!
    
    # Wait for port forward to be ready
    sleep 5
    
    # Health check
    local health_check_passed=false
    for i in {1..10}; do
        if curl -f http://localhost:$service_port/health &> /dev/null; then
            health_check_passed=true
            break
        fi
        log_info "Health check attempt $i/10..."
        sleep 10
    done
    
    # Cleanup port forward
    kill $port_forward_pid 2>/dev/null || true
    
    if [ "$health_check_passed" = false ]; then
        log_error "Health checks failed for $target_color environment"
        return 1
    fi
    
    log_success "Health checks passed for $target_color environment"
    return 0
}

# Run smoke tests
run_smoke_tests() {
    local target_color=$1
    
    log_info "Running smoke tests on $target_color environment..."
    
    # Run smoke tests
    kubectl exec -it deployment/$PROJECT_NAME-backend-$target_color -n $NAMESPACE -- \
        python manage.py smoke-test
    
    if [ $? -ne 0 ]; then
        log_error "Smoke tests failed for $target_color environment"
        return 1
    fi
    
    log_success "Smoke tests passed for $target_color environment"
    return 0
}

# Switch traffic to target environment
switch_traffic() {
    local target_color=$1
    
    log_info "Switching traffic to $target_color environment..."
    
    # Update main service selector
    kubectl patch service $PROJECT_NAME-backend -n $NAMESPACE -p \
        '{"spec":{"selector":{"version":"'$target_color'"}}}'
    
    kubectl patch service $PROJECT_NAME-frontend -n $NAMESPACE -p \
        '{"spec":{"selector":{"version":"'$target_color'"}}}'
    
    # Wait for service update
    sleep 10
    
    # Verify traffic switch
    local current_selector=$(kubectl get service $PROJECT_NAME-backend -n $NAMESPACE -o jsonpath='{.spec.selector.version}')
    if [ "$current_selector" != "$target_color" ]; then
        log_error "Traffic switch failed"
        return 1
    fi
    
    log_success "Traffic switched to $target_color environment"
    return 0
}

# Monitor deployment
monitor_deployment() {
    local target_color=$1
    local monitor_duration=${2:-300} # 5 minutes default
    
    log_info "Monitoring $target_color deployment for $monitor_duration seconds..."
    
    local start_time=$(date +%s)
    local end_time=$((start_time + monitor_duration))
    
    while [ $(date +%s) -lt $end_time ]; do
        # Check pod health
        local unhealthy_pods=$(kubectl get pods -n $NAMESPACE -l version=$target_color --field-selector=status.phase!=Running --no-headers | wc -l)
        if [ "$unhealthy_pods" -gt 0 ]; then
            log_error "Unhealthy pods detected in $target_color environment"
            return 1
        fi
        
        # Check error rates
        local error_count=$(kubectl logs deployment/$PROJECT_NAME-backend-$target_color -n $NAMESPACE --since=60s | grep -i error | wc -l)
        if [ "$error_count" -gt 10 ]; then
            log_warning "High error rate detected: $error_count errors in last minute"
        fi
        
        sleep 30
    done
    
    log_success "Monitoring completed successfully"
    return 0
}

# Cleanup old environment
cleanup_old_environment() {
    local old_color=$1
    
    log_info "Cleaning up $old_color environment..."
    
    # Scale down old deployment
    kubectl scale deployment $PROJECT_NAME-backend-$old_color --replicas=0 -n $NAMESPACE
    kubectl scale deployment $PROJECT_NAME-frontend-$old_color --replicas=0 -n $NAMESPACE
    
    # Wait for pods to terminate
    kubectl wait --for=delete pod -l version=$old_color -n $NAMESPACE --timeout=300s
    
    log_success "$old_color environment cleaned up"
}

# Rollback deployment
rollback_deployment() {
    local current_color=$(get_current_color)
    local target_color=$(get_target_color)
    
    log_warning "Rolling back deployment..."
    
    # Switch traffic back
    if switch_traffic $target_color; then
        log_success "Rollback completed successfully"
    else
        log_error "Rollback failed"
        exit 1
    fi
}

# Main deployment function
main() {
    log_info "Starting blue-green deployment..."
    
    validate_inputs
    check_prerequisites
    
    if [ "$ROLLBACK" = "true" ]; then
        rollback_deployment
        exit 0
    fi
    
    local current_color=$(get_current_color)
    local target_color=$(get_target_color)
    
    log_info "Current active environment: $current_color"
    log_info "Target deployment environment: $target_color"
    
    # Create backup
    create_backup
    
    # Deploy to target environment
    deploy_target_environment $target_color $NEW_VERSION
    
    # Run health checks
    if ! run_health_checks $target_color; then
        log_error "Health checks failed, aborting deployment"
        cleanup_old_environment $target_color
        exit 1
    fi
    
    # Run smoke tests
    if ! run_smoke_tests $target_color; then
        log_error "Smoke tests failed, aborting deployment"
        cleanup_old_environment $target_color
        exit 1
    fi
    
    # Switch traffic
    if ! switch_traffic $target_color; then
        log_error "Traffic switch failed, aborting deployment"
        cleanup_old_environment $target_color
        exit 1
    fi
    
    # Monitor deployment
    if ! monitor_deployment $target_color 300; then
        log_error "Deployment monitoring failed, rolling back"
        switch_traffic $current_color
        cleanup_old_environment $target_color
        exit 1
    fi
    
    # Cleanup old environment
    cleanup_old_environment $current_color
    
    log_success "Blue-green deployment completed successfully!"
    log_info "Active environment: $target_color"
    log_info "Version: $NEW_VERSION"
}

# Trap for cleanup on exit
cleanup_on_exit() {
    # Kill any background processes
    jobs -p | xargs -r kill 2>/dev/null || true
    
    # Remove temporary files
    rm -f /tmp/deployment-*.yaml /tmp/service-*.yaml
}

trap cleanup_on_exit EXIT

# Run main function
main "$@"
