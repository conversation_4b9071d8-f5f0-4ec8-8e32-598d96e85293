<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />
<meta content="Comprehensive security assessment methodology and results for the Blast-Radius Security Tool" name="description" />
<meta content="security assessment, vulnerability management, security testing, risk assessment" name="keywords" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Security Assessment Overview &mdash; Blast-Radius Security Tool v1.0.0 - Enterprise Security Platform Documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=fa44fd50" />
      <link rel="stylesheet" type="text/css" href="../../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../../_static/custom.css?v=ac1674a3" />
      <link rel="stylesheet" type="text/css" href="../../_static/custom.css?v=ac1674a3" />

  
    <link rel="canonical" href="https://blast-radius.readthedocs.io/security/reviews/security-assessment.html" />
  <!--[if lt IE 9]>
    <script src="../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../../_static/jquery.js?v=5d32c60e"></script>
        <script src="../../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../../_static/documentation_options.js?v=528dc5d9"></script>
        <script src="../../_static/doctools.js?v=9a2dae69"></script>
        <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
        <script>window.MathJax = {"options": {"processHtmlClass": "tex2jax_process|mathjax_process|math|output_area"}}</script>
        <script defer="defer" src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script src="../../_static/js/theme.js"></script>
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Blast-Radius Security Tool v1.0.0 - Enterprise Security Platform Documentation"
          href="../../_static/opensearch.xml"/>
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
    <link rel="next" title="Vulnerability Management" href="vulnerability-management.html" />
    <link rel="prev" title="Security Review - June 14, 2025" href="security-review-2025-06-14.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../../index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🎉 Latest Release</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../latest-implementations-summary.html">Latest Implementations Summary - Production Ready Release</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../latest-implementations-summary.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../latest-implementations-summary.html#implementation-achievements">Implementation Achievements</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../latest-implementations-summary.html#infrastructure-as-code-iac-100-complete">Infrastructure as Code (IaC) - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../latest-implementations-summary.html#zero-trust-architecture-100-complete">Zero-Trust Architecture - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../latest-implementations-summary.html#gdpr-compliance-framework-100-complete">GDPR Compliance Framework - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../latest-implementations-summary.html#enhanced-audit-logging-100-complete">Enhanced Audit Logging - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../latest-implementations-summary.html#least-privilege-access-control-100-complete">Least Privilege Access Control - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../latest-implementations-summary.html#production-deployment-infrastructure-100-complete">Production Deployment Infrastructure - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../latest-implementations-summary.html#documentation-operations-100-complete">Documentation &amp; Operations - 100% Complete ✅</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../latest-implementations-summary.html#technical-specifications">Technical Specifications</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../latest-implementations-summary.html#performance-characteristics">Performance Characteristics</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../latest-implementations-summary.html#security-specifications">Security Specifications</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../latest-implementations-summary.html#compliance-coverage">Compliance Coverage</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../latest-implementations-summary.html#deployment-architecture">Deployment Architecture</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../latest-implementations-summary.html#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../latest-implementations-summary.html#next-steps-and-recommendations">Next Steps and Recommendations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../latest-implementations-summary.html#immediate-actions">Immediate Actions</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../latest-implementations-summary.html#long-term-roadmap">Long-term Roadmap</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../latest-implementations-summary.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🚀 Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../installation.html">Installation Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../installation.html#prerequisites">Prerequisites</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../installation.html#system-requirements">System Requirements</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../installation.html#software-dependencies">Software Dependencies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../installation.html#installation-methods">Installation Methods</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../installation.html#method-1-quick-start-with-docker-recommended">Method 1: Quick Start with Docker (Recommended)</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../installation.html#method-2-development-installation">Method 2: Development Installation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../installation.html#method-3-production-installation">Method 3: Production Installation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../installation.html#configuration">Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../installation.html#environment-variables">Environment Variables</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../installation.html#initial-setup">Initial Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../installation.html#creating-admin-user">Creating Admin User</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../installation.html#configuring-integrations">Configuring Integrations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../installation.html#verification">Verification</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../installation.html#health-checks">Health Checks</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../installation.html#performance-testing">Performance Testing</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../installation.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../installation.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../installation.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../installation.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../configuration.html">Configuration Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../configuration.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../configuration.html#core-configuration">Core Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../configuration.html#application-settings">Application Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../configuration.html#database-configuration">Database Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../configuration.html#security-configuration">Security Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../configuration.html#authentication-settings">Authentication Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../configuration.html#encryption-settings">Encryption Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../configuration.html#access-control-settings">Access Control Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../configuration.html#integration-configuration">Integration Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../configuration.html#cloud-provider-settings">Cloud Provider Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../configuration.html#servicenow-integration">ServiceNow Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../configuration.html#threat-intelligence-settings">Threat Intelligence Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../configuration.html#monitoring-and-logging">Monitoring and Logging</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../configuration.html#logging-configuration">Logging Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../configuration.html#monitoring-settings">Monitoring Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../configuration.html#environment-specific-configuration">Environment-Specific Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../configuration.html#development-environment">Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../configuration.html#staging-environment">Staging Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../configuration.html#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../configuration.html#configuration-validation">Configuration Validation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../configuration.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../configuration.html#security-best-practices">Security Best Practices</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../configuration.html#performance-best-practices">Performance Best Practices</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../configuration.html#troubleshooting">Troubleshooting</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../quick-start-guide.html">Quick Start Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../quick-start-guide.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../quick-start-guide.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../quick-start-guide.html#quick-installation">Quick Installation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#step-1-get-the-code">Step 1: Get the Code</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#step-2-start-the-platform">Step 2: Start the Platform</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#step-3-access-the-platform">Step 3: Access the Platform</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../quick-start-guide.html#first-login-and-setup">First Login and Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#initial-login">Initial Login</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#password-change">Password Change</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#dashboard-overview">Dashboard Overview</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../quick-start-guide.html#basic-configuration">Basic Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#user-profile-setup">User Profile Setup</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#multi-factor-authentication-recommended">Multi-Factor Authentication (Recommended)</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../quick-start-guide.html#your-first-attack-path-analysis">Your First Attack Path Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#step-1-add-sample-assets">Step 1: Add Sample Assets</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#step-2-run-attack-path-analysis">Step 2: Run Attack Path Analysis</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#step-3-view-results">Step 3: View Results</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#understanding-the-visualization">Understanding the Visualization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../quick-start-guide.html#essential-features-tour">Essential Features Tour</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#real-time-monitoring">Real-Time Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#asset-management">Asset Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#user-and-role-management">User and Role Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#threat-intelligence">Threat Intelligence</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../quick-start-guide.html#integration-setup">Integration Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#cloud-provider-integration">Cloud Provider Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#servicenow-integration">ServiceNow Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../quick-start-guide.html#next-steps">Next Steps</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#immediate-actions">Immediate Actions</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#learning-resources">Learning Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#advanced-configuration">Advanced Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../quick-start-guide.html#common-quick-start-issues">Common Quick Start Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#platform-won-t-start">Platform Won’t Start</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#can-t-access-web-interface">Can’t Access Web Interface</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#login-issues">Login Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../quick-start-guide.html#performance-issues">Performance Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../quick-start-guide.html#getting-help">Getting Help</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../quick-start-guide.html#congratulations">Congratulations!</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📊 Documentation Excellence</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../documentation-overview.html">Documentation Overview and Achievements</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../documentation-overview.html#documentation-excellence-achievements">🏆 Documentation Excellence Achievements</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../documentation-overview.html#comprehensive-user-guide-ecosystem">📚 Comprehensive User Guide Ecosystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../documentation-overview.html#key-documentation-features">🎯 Key Documentation Features</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../documentation-overview.html#measurable-impact-and-value">📈 Measurable Impact and Value</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../documentation-overview.html#industry-leadership-and-recognition">🌟 Industry Leadership and Recognition</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../documentation-overview.html#future-enhancements-and-roadmap">🚀 Future Enhancements and Roadmap</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📚 Enterprise User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../user-guides/index.html">User Guides</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../user-guides/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../user-guides/index.html#role-based-guides">Role-Based Guides</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#soc-operators">SOC Operators</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../user-guides/soc-operators.html">SOC Operators Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/soc-operators.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/soc-operators.html#role-definition-and-responsibilities">Role Definition and Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/soc-operators.html#core-platform-capabilities-for-soc-operations">Core Platform Capabilities for SOC Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/soc-operators.html#getting-started-soc-operator-onboarding">Getting Started: SOC Operator Onboarding</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/soc-operators.html#advanced-real-time-monitoring-and-threat-detection">Advanced Real-time Monitoring and Threat Detection</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/soc-operators.html#comprehensive-incident-response-framework">Comprehensive Incident Response Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/soc-operators.html#advanced-reporting-and-documentation-framework">Advanced Reporting and Documentation Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/soc-operators.html#soc-operations-best-practices-and-excellence-framework">SOC Operations Best Practices and Excellence Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/soc-operators.html#comprehensive-scenario-based-training-and-response-procedures">Comprehensive Scenario-Based Training and Response Procedures</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/soc-operators.html#comprehensive-support-resources-and-professional-development">Comprehensive Support Resources and Professional Development</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/soc-operators.html#conclusion-excellence-in-soc-operations">Conclusion: Excellence in SOC Operations</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#security-architects">Security Architects</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../user-guides/security-architects.html">Security Architects Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/security-architects.html#executive-summary-for-security-architects">Executive Summary for Security Architects</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/security-architects.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/security-architects.html#advanced-platform-capabilities-for-security-architecture">Advanced Platform Capabilities for Security Architecture</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/security-architects.html#enterprise-security-architecture-methodology">Enterprise Security Architecture Methodology</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/security-architects.html#advanced-risk-assessment-and-quantitative-analysis-framework">Advanced Risk Assessment and Quantitative Analysis Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/security-architects.html#advanced-security-architecture-design-and-implementation">Advanced Security Architecture Design and Implementation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/security-architects.html#comprehensive-compliance-and-governance-framework">Comprehensive Compliance and Governance Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/security-architects.html#advanced-analytics-and-executive-communication-framework">Advanced Analytics and Executive Communication Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/security-architects.html#enterprise-security-architecture-best-practices-and-excellence">Enterprise Security Architecture Best Practices and Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/security-architects.html#professional-development-and-support-resources">Professional Development and Support Resources</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/security-architects.html#conclusion-excellence-in-security-architecture-leadership">Conclusion: Excellence in Security Architecture Leadership</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#red-team-members">Red Team Members</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../user-guides/red-team-members.html">Red Team Members Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/red-team-members.html#executive-summary-for-red-team-operations">Executive Summary for Red Team Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/red-team-members.html#role-definition-and-offensive-security-responsibilities">Role Definition and Offensive Security Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/red-team-members.html#advanced-red-team-platform-capabilities">Advanced Red Team Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/red-team-members.html#comprehensive-red-team-methodology-and-campaign-framework">Comprehensive Red Team Methodology and Campaign Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/red-team-members.html#advanced-attack-path-discovery-and-exploitation-framework">Advanced Attack Path Discovery and Exploitation Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/red-team-members.html#advanced-attack-simulation-and-specialized-testing-scenarios">Advanced Attack Simulation and Specialized Testing Scenarios</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/red-team-members.html#advanced-reporting-and-performance-metrics-framework">Advanced Reporting and Performance Metrics Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/red-team-members.html#red-team-best-practices-and-operational-excellence">Red Team Best Practices and Operational Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/red-team-members.html#advanced-troubleshooting-and-technical-support">Advanced Troubleshooting and Technical Support</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/red-team-members.html#conclusion-excellence-in-red-team-operations">Conclusion: Excellence in Red Team Operations</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#purple-team-members">Purple Team Members</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../user-guides/purple-team-members.html">Purple Team Members Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/purple-team-members.html#executive-summary-for-purple-team-excellence">Executive Summary for Purple Team Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/purple-team-members.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/purple-team-members.html#advanced-purple-team-platform-capabilities">Advanced Purple Team Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/purple-team-members.html#comprehensive-purple-team-methodology-and-framework">Comprehensive Purple Team Methodology and Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/purple-team-members.html#advanced-detection-engineering-and-security-control-optimization">Advanced Detection Engineering and Security Control Optimization</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/purple-team-members.html#comprehensive-purple-team-scenarios-and-implementation-framework">Comprehensive Purple Team Scenarios and Implementation Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/purple-team-members.html#advanced-troubleshooting-and-technical-support-framework">Advanced Troubleshooting and Technical Support Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/purple-team-members.html#conclusion-excellence-in-purple-team-operations-and-collaborative-security">Conclusion: Excellence in Purple Team Operations and Collaborative Security</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#compliance-officers">Compliance Officers</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../user-guides/compliance-officers.html">Compliance Officers Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/compliance-officers.html#executive-summary-for-compliance-excellence">Executive Summary for Compliance Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/compliance-officers.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/compliance-officers.html#advanced-compliance-platform-capabilities">Advanced Compliance Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/compliance-officers.html#advanced-regulatory-framework-management">Advanced Regulatory Framework Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/compliance-officers.html#automated-compliance-monitoring-and-management">Automated Compliance Monitoring and Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/compliance-officers.html#comprehensive-audit-management-and-preparation">Comprehensive Audit Management and Preparation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/compliance-officers.html#advanced-risk-assessment-and-management-framework">Advanced Risk Assessment and Management Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/compliance-officers.html#cross-functional-collaboration-and-integration">Cross-Functional Collaboration and Integration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/compliance-officers.html#professional-development-and-excellence-framework">Professional Development and Excellence Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/compliance-officers.html#advanced-support-and-troubleshooting-framework">Advanced Support and Troubleshooting Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/compliance-officers.html#conclusion-excellence-in-compliance-and-governance">Conclusion: Excellence in Compliance and Governance</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#executive-leadership">Executive Leadership</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../user-guides/executive-leadership.html">Executive Leadership Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/executive-leadership.html#executive-summary-for-strategic-security-leadership">Executive Summary for Strategic Security Leadership</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/executive-leadership.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/executive-leadership.html#advanced-executive-security-platform-capabilities">Advanced Executive Security Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/executive-leadership.html#strategic-security-risk-management-framework">Strategic Security Risk Management Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/executive-leadership.html#business-aligned-security-performance-management">Business-Aligned Security Performance Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/executive-leadership.html#strategic-security-investment-and-resource-management">Strategic Security Investment and Resource Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/executive-leadership.html#crisis-management-and-business-continuity">Crisis Management and Business Continuity</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/executive-leadership.html#regulatory-compliance-and-governance-excellence">Regulatory Compliance and Governance Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/executive-leadership.html#strategic-security-communication-and-stakeholder-management">Strategic Security Communication and Stakeholder Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/executive-leadership.html#digital-transformation-and-innovation-enablement">Digital Transformation and Innovation Enablement</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/executive-leadership.html#professional-development-and-executive-excellence">Professional Development and Executive Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/executive-leadership.html#advanced-support-and-advisory-services">Advanced Support and Advisory Services</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/executive-leadership.html#conclusion-excellence-in-executive-security-leadership">Conclusion: Excellence in Executive Security Leadership</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#administrators">Administrators</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../user-guides/administrators.html">Administrators Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/administrators.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/administrators.html#dashboard-overview">Dashboard Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/administrators.html#getting-started">Getting Started</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/administrators.html#user-and-access-management">User and Access Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/administrators.html#system-configuration-and-management">System Configuration and Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/administrators.html#monitoring-and-maintenance">Monitoring and Maintenance</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/administrators.html#security-and-compliance">Security and Compliance</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/administrators.html#platform-updates-and-maintenance">Platform Updates and Maintenance</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/administrators.html#troubleshooting-and-support">Troubleshooting and Support</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/administrators.html#best-practices-for-platform-administration">Best Practices for Platform Administration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/administrators.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../user-guides/index.html#feature-specific-guides">Feature-Specific Guides</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#attack-path-analysis">Attack Path Analysis</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../user-guides/attack-path-analysis.html">Attack Path Analysis User Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/attack-path-analysis.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/attack-path-analysis.html#getting-started">Getting Started</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/attack-path-analysis.html#core-concepts">Core Concepts</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/attack-path-analysis.html#using-the-web-interface">Using the Web Interface</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/attack-path-analysis.html#using-the-api">Using the API</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/attack-path-analysis.html#use-cases-and-workflows">Use Cases and Workflows</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/attack-path-analysis.html#best-practices">Best Practices</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/attack-path-analysis.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/attack-path-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../user-guides/mitre-attack-integration.html">MITRE ATT&amp;CK Integration User Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/mitre-attack-integration.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/mitre-attack-integration.html#getting-started">Getting Started</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/mitre-attack-integration.html#mitre-att-ck-data-management">MITRE ATT&amp;CK Data Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/mitre-attack-integration.html#technique-correlation-engine">Technique Correlation Engine</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/mitre-attack-integration.html#threat-actor-attribution">Threat Actor Attribution</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/mitre-attack-integration.html#attack-pattern-analysis">Attack Pattern Analysis</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/mitre-attack-integration.html#att-ck-navigator-integration">ATT&amp;CK Navigator Integration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/mitre-attack-integration.html#threat-intelligence-enrichment">Threat Intelligence Enrichment</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/mitre-attack-integration.html#advanced-analytics">Advanced Analytics</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/mitre-attack-integration.html#api-integration">API Integration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/mitre-attack-integration.html#best-practices">Best Practices</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#threat-modeling">Threat Modeling</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../user-guides/threat-modeling.html">Threat Modeling User Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/threat-modeling.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/threat-modeling.html#getting-started">Getting Started</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/threat-modeling.html#threat-actor-profiles">Threat Actor Profiles</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/threat-modeling.html#attack-simulation">Attack Simulation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/threat-modeling.html#quantitative-risk-assessment">Quantitative Risk Assessment</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/threat-modeling.html#compliance-impact-analysis">Compliance Impact Analysis</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/threat-modeling.html#mitigation-strategy-generation">Mitigation Strategy Generation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/threat-modeling.html#advanced-features">Advanced Features</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../user-guides/threat-modeling.html#best-practices">Best Practices</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#asset-discovery-and-management">Asset Discovery and Management</a><ul class="simple">
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#threat-intelligence-integration">Threat Intelligence Integration</a><ul class="simple">
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../user-guides/index.html#common-workflows">Common Workflows</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#security-assessment-workflow">Security Assessment Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#incident-response-workflow">Incident Response Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#red-team-exercise-workflow">Red Team Exercise Workflow</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../user-guides/index.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#general-guidelines">General Guidelines</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#role-specific-tips">Role-Specific Tips</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../user-guides/index.html#getting-help">Getting Help</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#support-resources">Support Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#training-and-certification">Training and Certification</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../user-guides/index.html#feedback-and-contributions">Feedback and Contributions</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../api/index.html">API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../api/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../api/index.html#base-url">Base URL</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../api/index.html#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../api/index.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../api/index.html#api-endpoints">API Endpoints</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../api/index.html#authentication-user-management">Authentication &amp; User Management</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../api/authentication.html">Authentication API</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../../api/authentication.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/authentication.html#authentication-flow">Authentication Flow</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/authentication.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/authentication.html#security-and-authorization">Security and Authorization</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/authentication.html#integration-examples">Integration Examples</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/authentication.html#best-practices">Best Practices</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../api/index.html#asset-management">Asset Management</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../api/asset-management.html">Asset Management API</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../../api/asset-management.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/asset-management.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/asset-management.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/asset-management.html#asset-crud-operations">Asset CRUD Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/asset-management.html#asset-discovery">Asset Discovery</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/asset-management.html#bulk-operations">Bulk Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/asset-management.html#asset-relationships">Asset Relationships</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/asset-management.html#asset-tags-and-metadata">Asset Tags and Metadata</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/asset-management.html#error-handling">Error Handling</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/asset-management.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/asset-management.html#sdk-examples">SDK Examples</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../api/index.html#attack-path-analysis">Attack Path Analysis</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../api/attack-path-analysis.html">Attack Path Analysis API</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../../api/attack-path-analysis.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/attack-path-analysis.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/attack-path-analysis.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/attack-path-analysis.html#endpoints">Endpoints</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/attack-path-analysis.html#error-responses">Error Responses</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/attack-path-analysis.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/attack-path-analysis.html#performance-considerations">Performance Considerations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/attack-path-analysis.html#best-practices">Best Practices</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/attack-path-analysis.html#examples">Examples</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../api/index.html#threat-modeling-risk-assessment">Threat Modeling &amp; Risk Assessment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../api/threat-modeling.html">Threat Modeling API Reference</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../../api/threat-modeling.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/threat-modeling.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/threat-modeling.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/threat-modeling.html#threat-actor-simulation">Threat Actor Simulation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/threat-modeling.html#risk-assessment">Risk Assessment</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/threat-modeling.html#compliance-impact-analysis">Compliance Impact Analysis</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/threat-modeling.html#mitigation-strategies">Mitigation Strategies</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/threat-modeling.html#continuous-monitoring">Continuous Monitoring</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/threat-modeling.html#error-handling">Error Handling</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/threat-modeling.html#rate-limiting">Rate Limiting</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../api/index.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../api/mitre-attack-integration.html">MITRE ATT&amp;CK Integration API Reference</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../../api/mitre-attack-integration.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/mitre-attack-integration.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/mitre-attack-integration.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/mitre-attack-integration.html#att-ck-data-management">ATT&amp;CK Data Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/mitre-attack-integration.html#technique-correlation">Technique Correlation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/mitre-attack-integration.html#threat-actor-attribution">Threat Actor Attribution</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/mitre-attack-integration.html#attack-pattern-analysis">Attack Pattern Analysis</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/mitre-attack-integration.html#att-ck-navigator-integration">ATT&amp;CK Navigator Integration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/mitre-attack-integration.html#threat-intelligence-enrichment">Threat Intelligence Enrichment</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/mitre-attack-integration.html#error-handling">Error Handling</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../api/mitre-attack-integration.html#rate-limiting">Rate Limiting</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../api/index.html#threat-intelligence">Threat Intelligence</a><ul class="simple">
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../api/index.html#monitoring-dashboards">Monitoring &amp; Dashboards</a><ul class="simple">
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../api/index.html#integrations">Integrations</a><ul class="simple">
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../api/index.html#common-response-formats">Common Response Formats</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../api/index.html#success-response">Success Response</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../api/index.html#error-response">Error Response</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../api/index.html#http-status-codes">HTTP Status Codes</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../api/index.html#pagination">Pagination</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../api/index.html#filtering-and-sorting">Filtering and Sorting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../api/index.html#field-selection">Field Selection</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../api/index.html#webhooks">Webhooks</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../api/index.html#sdk-and-libraries">SDK and Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../api/index.html#testing-and-development">Testing and Development</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../api/index.html#support-and-resources">Support and Resources</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../api/index.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../api/index.html#examples">Examples</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Technical Docs</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../technical/index.html">Technical Documentation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../technical/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../technical/index.html#architecture-and-design">Architecture and Design</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../technical/index.html#system-architecture">System Architecture</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../technical/attack-path-architecture.html">Attack Path Analysis Architecture</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../../technical/attack-path-architecture.html#system-overview">System Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/attack-path-architecture.html#core-components">Core Components</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/attack-path-architecture.html#graph-algorithms">Graph Algorithms</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/attack-path-architecture.html#risk-scoring-methodology">Risk Scoring Methodology</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/attack-path-architecture.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/attack-path-architecture.html#performance-optimizations">Performance Optimizations</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../technical/index.html#database-design">Database Design</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../technical/database-design.html">Database Design and Schema</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../../technical/database-design.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/database-design.html#database-architecture">Database Architecture</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/database-design.html#core-entity-relationship-diagram">Core Entity Relationship Diagram</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/database-design.html#attack-path-analysis-schema">Attack Path Analysis Schema</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/database-design.html#table-definitions">Table Definitions</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/database-design.html#enumeration-types">Enumeration Types</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/database-design.html#indexes-and-performance-optimization">Indexes and Performance Optimization</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/database-design.html#data-retention-and-archival">Data Retention and Archival</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/database-design.html#database-maintenance">Database Maintenance</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/database-design.html#performance-monitoring">Performance Monitoring</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../technical/index.html#graph-analysis-engine">Graph Analysis Engine</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../technical/attack-path-flows.html">Attack Path Analysis Flows</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../../technical/attack-path-flows.html#attack-path-discovery-flow">Attack Path Discovery Flow</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/attack-path-flows.html#blast-radius-calculation-flow">Blast Radius Calculation Flow</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/attack-path-flows.html#attack-scenario-creation-flow">Attack Scenario Creation Flow</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/attack-path-flows.html#mitre-att-ck-mapping-decision-tree">MITRE ATT&amp;CK Mapping Decision Tree</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/attack-path-flows.html#risk-scoring-decision-flow">Risk Scoring Decision Flow</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/attack-path-flows.html#caching-strategy-decision-flow">Caching Strategy Decision Flow</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/attack-path-flows.html#error-handling-and-recovery-flow">Error Handling and Recovery Flow</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/attack-path-flows.html#performance-optimization-flow">Performance Optimization Flow</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../technical/index.html#product-requirements-document">Product Requirements Document</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../technical/product-requirements.html">Product Requirements Document (PRD)</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../../technical/product-requirements.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/product-requirements.html#product-overview">Product Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/product-requirements.html#functional-requirements">Functional Requirements</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/product-requirements.html#non-functional-requirements">Non-Functional Requirements</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/product-requirements.html#technical-architecture-requirements">Technical Architecture Requirements</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/product-requirements.html#integration-requirements">Integration Requirements</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/product-requirements.html#compliance-requirements">Compliance Requirements</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/product-requirements.html#success-metrics-and-kpis">Success Metrics and KPIs</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/product-requirements.html#risk-assessment">Risk Assessment</a></li>
<li class="toctree-l5"><a class="reference internal" href="../../technical/product-requirements.html#future-roadmap">Future Roadmap</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../technical/index.html#security-and-compliance">Security and Compliance</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../technical/index.html#security-model">Security Model</a><ul class="simple">
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../technical/index.html#performance-and-operations">Performance and Operations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../technical/index.html#performance-tuning">Performance Tuning</a><ul class="simple">
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../technical/index.html#deployment">Deployment</a><ul class="simple">
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../technical/index.html#monitoring">Monitoring</a><ul class="simple">
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../technical/index.html#development-and-integration">Development and Integration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../technical/index.html#api-development">API Development</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../technical/index.html#custom-integrations">Custom Integrations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../technical/index.html#data-models-and-schemas">Data Models and Schemas</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../technical/index.html#asset-data-models">Asset Data Models</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../technical/index.html#graph-data-structures">Graph Data Structures</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../technical/index.html#testing-and-quality-assurance">Testing and Quality Assurance</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../technical/index.html#testing-framework">Testing Framework</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../technical/index.html#quality-assurance">Quality Assurance</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../technical/index.html#contributing-and-development">Contributing and Development</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../technical/index.html#development-environment">Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../technical/index.html#contributing-guidelines">Contributing Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../technical/index.html#support-and-resources">Support and Resources</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../technical/index.html#technical-support">Technical Support</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Development</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../development/setup.html">Development Environment Setup</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../development/setup.html#prerequisites">Prerequisites</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/setup.html#system-requirements">System Requirements</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/setup.html#required-software">Required Software</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/setup.html#installation-guide">Installation Guide</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/setup.html#step-1-clone-repository">Step 1: Clone Repository</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/setup.html#step-2-backend-setup">Step 2: Backend Setup</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/setup.html#step-3-database-setup">Step 3: Database Setup</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/setup.html#step-4-frontend-setup">Step 4: Frontend Setup</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/setup.html#step-5-development-tools-setup">Step 5: Development Tools Setup</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/setup.html#development-workflow">Development Workflow</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/setup.html#starting-development-services">Starting Development Services</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/setup.html#code-quality-tools">Code Quality Tools</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/setup.html#testing-in-development">Testing in Development</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/setup.html#database-management">Database Management</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/setup.html#debugging">Debugging</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/setup.html#backend-debugging">Backend Debugging</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/setup.html#frontend-debugging">Frontend Debugging</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/setup.html#performance-profiling">Performance Profiling</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/setup.html#common-development-tasks">Common Development Tasks</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/setup.html#adding-new-features">Adding New Features</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/setup.html#api-development">API Development</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/setup.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/setup.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/setup.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/setup.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../development/workflow.html">Development Workflow</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../development/workflow.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../development/workflow.html#branching-strategy">Branching Strategy</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/workflow.html#git-flow-model">Git Flow Model</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/workflow.html#branch-naming-conventions">Branch Naming Conventions</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/workflow.html#development-process">Development Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/workflow.html#feature-development-workflow">Feature Development Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/workflow.html#commit-message-standards">Commit Message Standards</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/workflow.html#code-review-process">Code Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/workflow.html#review-requirements">Review Requirements</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/workflow.html#review-process">Review Process</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/workflow.html#testing-strategy">Testing Strategy</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/workflow.html#test-driven-development">Test-Driven Development</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/workflow.html#continuous-integration">Continuous Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/workflow.html#release-management">Release Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/workflow.html#release-process">Release Process</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/workflow.html#versioning-strategy">Versioning Strategy</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/workflow.html#quality-gates">Quality Gates</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/workflow.html#definition-of-done">Definition of Done</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/workflow.html#automation-tools">Automation Tools</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/workflow.html#development-tools">Development Tools</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/workflow.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/workflow.html#code-organization">Code Organization</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/workflow.html#collaboration">Collaboration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/workflow.html#security">Security</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/workflow.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../development/contributing.html">Contributing Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../development/contributing.html#getting-started">Getting Started</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/contributing.html#ways-to-contribute">Ways to Contribute</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/contributing.html#before-you-start">Before You Start</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/contributing.html#contribution-workflow">Contribution Workflow</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/contributing.html#step-1-find-or-create-an-issue">Step 1: Find or Create an Issue</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/contributing.html#step-2-fork-and-clone">Step 2: Fork and Clone</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/contributing.html#step-3-create-a-branch">Step 3: Create a Branch</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/contributing.html#step-4-make-changes">Step 4: Make Changes</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/contributing.html#step-5-test-your-changes">Step 5: Test Your Changes</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/contributing.html#step-6-submit-pull-request">Step 6: Submit Pull Request</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/contributing.html#code-standards">Code Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/contributing.html#python-code-style">Python Code Style</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/contributing.html#javascript-typescript-style">JavaScript/TypeScript Style</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/contributing.html#testing-guidelines">Testing Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/contributing.html#test-structure">Test Structure</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/contributing.html#documentation-standards">Documentation Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/contributing.html#documentation-types">Documentation Types</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/contributing.html#writing-guidelines">Writing Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/contributing.html#review-process">Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/contributing.html#code-review-checklist">Code Review Checklist</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/contributing.html#review-guidelines">Review Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/contributing.html#community-guidelines">Community Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/contributing.html#code-of-conduct">Code of Conduct</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/contributing.html#communication">Communication</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/contributing.html#recognition">Recognition</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../development/contributing.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../development/code-standards.html">Code Standards &amp; Style Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../development/code-standards.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../development/code-standards.html#general-principles">General Principles</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/code-standards.html#core-principles">Core Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/code-standards.html#solid-principles">SOLID Principles</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/code-standards.html#python-standards">Python Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/code-standards.html#code-style">Code Style</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../development/code-standards.html#security-standards">Security Standards</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/code-standards.html#javascript-typescript-standards">JavaScript/TypeScript Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/code-standards.html#id1">Code Style</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/code-standards.html#database-standards">Database Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/code-standards.html#schema-design">Schema Design</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/code-standards.html#api-standards">API Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/code-standards.html#rest-api-design">REST API Design</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/code-standards.html#documentation-standards">Documentation Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/code-standards.html#code-documentation">Code Documentation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/code-standards.html#testing-standards">Testing Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/code-standards.html#test-organization">Test Organization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/code-standards.html#quality-assurance">Quality Assurance</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/code-standards.html#automated-checks">Automated Checks</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/code-standards.html#tools-and-configuration">Tools and Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../development/code-standards.html#development-tools">Development Tools</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../development/code-standards.html#enforcement">Enforcement</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Use Cases</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../use-cases/asset-discovery.html">Asset Discovery Use Cases</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../use-cases/asset-discovery.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../use-cases/asset-discovery.html#discovery-methods">Discovery Methods</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/asset-discovery.html#cloud-provider-discovery">Cloud Provider Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/asset-discovery.html#network-based-discovery">Network-Based Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/asset-discovery.html#api-discovery">API Discovery</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../use-cases/asset-discovery.html#use-case-scenarios">Use Case Scenarios</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/asset-discovery.html#scenario-1-multi-cloud-asset-inventory">Scenario 1: Multi-Cloud Asset Inventory</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/asset-discovery.html#scenario-2-continuous-asset-monitoring">Scenario 2: Continuous Asset Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/asset-discovery.html#scenario-3-compliance-asset-reporting">Scenario 3: Compliance Asset Reporting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../use-cases/asset-discovery.html#advanced-discovery-features">Advanced Discovery Features</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/asset-discovery.html#agent-based-discovery">Agent-Based Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/asset-discovery.html#container-discovery">Container Discovery</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../use-cases/asset-discovery.html#discovery-automation">Discovery Automation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/asset-discovery.html#scheduled-discovery">Scheduled Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/asset-discovery.html#integration-with-external-tools">Integration with External Tools</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../use-cases/asset-discovery.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/asset-discovery.html#discovery-strategy">Discovery Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/asset-discovery.html#performance-optimization">Performance Optimization</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/asset-discovery.html#security-considerations">Security Considerations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../use-cases/asset-discovery.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/asset-discovery.html#common-issues">Common Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../use-cases/asset-discovery.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../use-cases/attack-path-analysis.html">Attack Path Analysis Use Cases</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../use-cases/attack-path-analysis.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../use-cases/attack-path-analysis.html#key-capabilities">Key Capabilities</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/attack-path-analysis.html#analysis-types">Analysis Types</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../use-cases/attack-path-analysis.html#use-case-scenarios">Use Case Scenarios</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/attack-path-analysis.html#scenario-1-red-team-attack-simulation">Scenario 1: Red Team Attack Simulation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/attack-path-analysis.html#scenario-2-purple-team-validation-exercise">Scenario 2: Purple Team Validation Exercise</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/attack-path-analysis.html#scenario-3-compliance-risk-assessment">Scenario 3: Compliance Risk Assessment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/attack-path-analysis.html#scenario-4-incident-response-planning">Scenario 4: Incident Response Planning</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/attack-path-analysis.html#scenario-5-zero-trust-architecture-planning">Scenario 5: Zero Trust Architecture Planning</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../use-cases/attack-path-analysis.html#advanced-analysis-features">Advanced Analysis Features</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/attack-path-analysis.html#risk-scoring-and-prioritization">Risk Scoring and Prioritization</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/attack-path-analysis.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../use-cases/attack-path-analysis.html#integration-and-automation">Integration and Automation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/attack-path-analysis.html#siem-integration">SIEM Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/attack-path-analysis.html#soar-integration">SOAR Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../use-cases/attack-path-analysis.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/attack-path-analysis.html#analysis-guidelines">Analysis Guidelines</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../use-cases/attack-path-analysis.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../use-cases/attack-path-analysis.html#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Architecture</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../architecture/zero-trust-architecture.html">Zero-Trust Architecture Implementation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#identity-verification-service">Identity Verification Service</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#service-account-management">Service Account Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#tls-1-3-enforcement">TLS 1.3+ Enforcement</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#real-time-security-event-correlation">Real-Time Security Event Correlation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#network-security">Network Security</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#kubernetes-network-policies">Kubernetes Network Policies</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#rbac-implementation">RBAC Implementation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#data-protection">Data Protection</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#encryption-implementation">Encryption Implementation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#data-classification">Data Classification</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#monitoring-and-alerting">Monitoring and Alerting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#security-monitoring">Security Monitoring</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#implementation-guidelines">Implementation Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#deployment-checklist">Deployment Checklist</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#security-validation">Security Validation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#security-principles">Security Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#implementation-tips">Implementation Tips</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../architecture/zero-trust-architecture.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../deployment/production-architecture.html">Production Architecture Overview</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/production-architecture.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/production-architecture.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-architecture.html#kubernetes-infrastructure">Kubernetes Infrastructure</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-architecture.html#application-architecture">Application Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-architecture.html#data-architecture">Data Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-architecture.html#network-architecture">Network Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/production-architecture.html#security-architecture">Security Architecture</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-architecture.html#zero-trust-security-implementation">Zero-Trust Security Implementation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-architecture.html#monitoring-architecture">Monitoring Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/production-architecture.html#deployment-strategy">Deployment Strategy</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-architecture.html#blue-green-deployment">Blue-Green Deployment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-architecture.html#infrastructure-as-code">Infrastructure as Code</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/production-architecture.html#performance-specifications">Performance Specifications</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-architecture.html#scalability-metrics">Scalability Metrics</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-architecture.html#resource-requirements">Resource Requirements</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/production-architecture.html#disaster-recovery">Disaster Recovery</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-architecture.html#backup-and-recovery-strategy">Backup and Recovery Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-architecture.html#high-availability-design">High Availability Design</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/production-architecture.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-architecture.html#operational-excellence">Operational Excellence</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-architecture.html#security-best-practices">Security Best Practices</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-architecture.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/production-architecture.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Security &amp; Compliance</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../enhanced-audit-logging.html">Enhanced Audit Logging System</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../enhanced-audit-logging.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../enhanced-audit-logging.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../enhanced-audit-logging.html#audit-event-structure">Audit Event Structure</a></li>
<li class="toctree-l3"><a class="reference internal" href="../enhanced-audit-logging.html#tamper-proof-audit-chain">Tamper-Proof Audit Chain</a></li>
<li class="toctree-l3"><a class="reference internal" href="../enhanced-audit-logging.html#event-enrichment-pipeline">Event Enrichment Pipeline</a></li>
<li class="toctree-l3"><a class="reference internal" href="../enhanced-audit-logging.html#real-time-monitoring">Real-Time Monitoring</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../enhanced-audit-logging.html#compliance-integration">Compliance Integration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../enhanced-audit-logging.html#framework-mapping">Framework Mapping</a></li>
<li class="toctree-l3"><a class="reference internal" href="../enhanced-audit-logging.html#compliance-reporting">Compliance Reporting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../enhanced-audit-logging.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../enhanced-audit-logging.html#audit-service-architecture">Audit Service Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../enhanced-audit-logging.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../enhanced-audit-logging.html#database-schema">Database Schema</a></li>
<li class="toctree-l3"><a class="reference internal" href="../enhanced-audit-logging.html#configuration">Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../enhanced-audit-logging.html#monitoring-and-alerting">Monitoring and Alerting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../enhanced-audit-logging.html#audit-monitoring-dashboard">Audit Monitoring Dashboard</a></li>
<li class="toctree-l3"><a class="reference internal" href="../enhanced-audit-logging.html#alert-configuration">Alert Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../enhanced-audit-logging.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../enhanced-audit-logging.html#audit-strategy">Audit Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="../enhanced-audit-logging.html#operational-guidelines">Operational Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../enhanced-audit-logging.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../enhanced-audit-logging.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../enhanced-audit-logging.html#performance-tuning">Performance Tuning</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../enhanced-audit-logging.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../incident-response-procedures.html">Incident Response Procedures - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../incident-response-procedures.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../incident-response-procedures.html#incident-classification">Incident Classification</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../incident-response-procedures.html#severity-levels">Severity Levels</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#critical-p0">Critical (P0)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#high-p1">High (P1)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#medium-p2">Medium (P2)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#low-p3">Low (P3)</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../incident-response-procedures.html#incident-response-team">Incident Response Team</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../incident-response-procedures.html#roles-and-responsibilities">Roles and Responsibilities</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#incident-commander-ic">Incident Commander (IC)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#technical-lead">Technical Lead</a></li>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#security-lead">Security Lead</a></li>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#communications-lead">Communications Lead</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../incident-response-procedures.html#incident-response-process">Incident Response Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../incident-response-procedures.html#phase-1-detection-and-initial-response-0-15-minutes">Phase 1: Detection and Initial Response (0-15 minutes)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#incident-detection">1.1 Incident Detection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#initial-assessment">1.2 Initial Assessment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#team-notification">1.3 Team Notification</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../incident-response-procedures.html#phase-2-investigation-and-containment-15-minutes-2-hours">Phase 2: Investigation and Containment (15 minutes - 2 hours)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#incident-war-room-setup">2.1 Incident War Room Setup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#technical-investigation">2.2 Technical Investigation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#security-assessment-if-applicable">2.3 Security Assessment (if applicable)</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../incident-response-procedures.html#phase-3-resolution-and-recovery-2-8-hours">Phase 3: Resolution and Recovery (2-8 hours)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#fix-implementation">3.1 Fix Implementation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#system-recovery">3.2 System Recovery</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../incident-response-procedures.html#phase-4-communication-and-documentation-ongoing">Phase 4: Communication and Documentation (Ongoing)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#status-updates">4.1 Status Updates</a></li>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#resolution-communication">4.2 Resolution Communication</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../incident-response-procedures.html#phase-5-post-incident-review-24-72-hours-after-resolution">Phase 5: Post-Incident Review (24-72 hours after resolution)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#post-mortem-meeting">5.1 Post-Mortem Meeting</a></li>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#documentation">5.2 Documentation</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../incident-response-procedures.html#security-specific-procedures">Security-Specific Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../incident-response-procedures.html#data-breach-response">Data Breach Response</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#immediate-actions-0-1-hour">Immediate Actions (0-1 hour)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#notification-requirements">Notification Requirements</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../incident-response-procedures.html#vulnerability-response">Vulnerability Response</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#critical-vulnerabilities-cvss-9-0">Critical Vulnerabilities (CVSS 9.0+)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../incident-response-procedures.html#high-vulnerabilities-cvss-7-0-8-9">High Vulnerabilities (CVSS 7.0-8.9)</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../incident-response-procedures.html#emergency-contacts">Emergency Contacts</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../incident-response-procedures.html#internal-team">Internal Team</a></li>
<li class="toctree-l3"><a class="reference internal" href="../incident-response-procedures.html#external-contacts">External Contacts</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../incident-response-procedures.html#tools-and-resources">Tools and Resources</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../incident-response-procedures.html#monitoring-and-alerting">Monitoring and Alerting</a></li>
<li class="toctree-l3"><a class="reference internal" href="../incident-response-procedures.html#communication">Communication</a></li>
<li class="toctree-l3"><a class="reference internal" href="../incident-response-procedures.html#id1">Documentation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../incident-response-procedures.html#training-and-drills">Training and Drills</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../incident-response-procedures.html#regular-training">Regular Training</a></li>
<li class="toctree-l3"><a class="reference internal" href="../incident-response-procedures.html#documentation-updates">Documentation Updates</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../security-review-processes.html">Security Review Processes - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security-review-processes.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security-review-processes.html#security-review-framework">Security Review Framework</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security-review-processes.html#review-types">Review Types</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#code-security-review">1. Code Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#infrastructure-security-review">2. Infrastructure Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#architecture-security-review">3. Architecture Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#compliance-review">4. Compliance Review</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security-review-processes.html#code-security-review-process">Code Security Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security-review-processes.html#pre-review-checklist">Pre-Review Checklist</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#developer-responsibilities">Developer Responsibilities</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security-review-processes.html#review-criteria">Review Criteria</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#input-validation-and-sanitization">1. Input Validation and Sanitization</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#authentication-and-authorization">2. Authentication and Authorization</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#data-protection">3. Data Protection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#error-handling-and-logging">4. Error Handling and Logging</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#cryptography-and-secrets">5. Cryptography and Secrets</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security-review-processes.html#review-process-workflow">Review Process Workflow</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#step-1-automated-security-checks">Step 1: Automated Security Checks</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#step-2-manual-security-review">Step 2: Manual Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#step-3-security-testing">Step 3: Security Testing</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security-review-processes.html#id1">Infrastructure Security Review</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security-review-processes.html#infrastructure-as-code-iac-review">Infrastructure as Code (IaC) Review</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#terraform-security-checklist">Terraform Security Checklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#example-review-process">Example Review Process</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security-review-processes.html#kubernetes-security-review">Kubernetes Security Review</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#manifest-security-checklist">Manifest Security Checklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#example-security-policies">Example Security Policies</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security-review-processes.html#id2">Architecture Security Review</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security-review-processes.html#design-review-process">Design Review Process</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#threat-modeling">1. Threat Modeling</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#security-architecture-patterns">2. Security Architecture Patterns</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#integration-security">3. Integration Security</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security-review-processes.html#review-documentation-template">Review Documentation Template</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security-review-processes.html#compliance-review-process">Compliance Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security-review-processes.html#regulatory-requirements">Regulatory Requirements</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#gdpr-compliance-checklist">GDPR Compliance Checklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#soc-2-compliance-checklist">SOC 2 Compliance Checklist</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security-review-processes.html#compliance-review-workflow">Compliance Review Workflow</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#change-impact-assessment">1. Change Impact Assessment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-review-processes.html#documentation-requirements">2. Documentation Requirements</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security-review-processes.html#security-review-tools-and-automation">Security Review Tools and Automation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security-review-processes.html#automated-security-scanning">Automated Security Scanning</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security-review-processes.html#security-metrics-and-kpis">Security Metrics and KPIs</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security-review-processes.html#training-and-awareness">Training and Awareness</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security-review-processes.html#security-review-training">Security Review Training</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security-review-processes.html#documentation-and-resources">Documentation and Resources</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html">GDPR Compliance Framework</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#data-subject-rights-management">Data Subject Rights Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#consent-management-system">Consent Management System</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#data-processing-records">Data Processing Records</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#data-breach-management">Data Breach Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#privacy-impact-assessment">Privacy Impact Assessment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#gdpr-service-architecture">GDPR Service Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#data-processing-workflows">Data Processing Workflows</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#compliance-monitoring">Compliance Monitoring</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#automated-compliance-checks">Automated Compliance Checks</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#compliance-reporting">Compliance Reporting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#privacy-by-design-implementation">Privacy by Design Implementation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#operational-guidelines">Operational Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#technical-implementation">Technical Implementation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#database-schema">Database Schema</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#configuration-example">Configuration Example</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../compliance/gdpr-compliance-framework.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../access-control/least-privilege-framework.html">Least Privilege Access Control Framework</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../access-control/least-privilege-framework.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../access-control/least-privilege-framework.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../access-control/least-privilege-framework.html#access-request-management">Access Request Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../access-control/least-privilege-framework.html#just-in-time-access-provisioning">Just-in-Time Access Provisioning</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../access-control/least-privilege-framework.html#privilege-escalation-management">Privilege Escalation Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../access-control/least-privilege-framework.html#access-review-and-certification">Access Review and Certification</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../access-control/least-privilege-framework.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../access-control/least-privilege-framework.html#access-control-architecture">Access Control Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../access-control/least-privilege-framework.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../access-control/least-privilege-framework.html#role-based-access-templates">Role-Based Access Templates</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../access-control/least-privilege-framework.html#risk-assessment-engine">Risk Assessment Engine</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../access-control/least-privilege-framework.html#monitoring-and-analytics">Monitoring and Analytics</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../access-control/least-privilege-framework.html#access-usage-monitoring">Access Usage Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../access-control/least-privilege-framework.html#access-analytics-dashboard">Access Analytics Dashboard</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../access-control/least-privilege-framework.html#configuration-management">Configuration Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../access-control/least-privilege-framework.html#policy-configuration">Policy Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../access-control/least-privilege-framework.html#role-template-configuration">Role Template Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../access-control/least-privilege-framework.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../access-control/least-privilege-framework.html#access-management-principles">Access Management Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../access-control/least-privilege-framework.html#operational-guidelines">Operational Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../access-control/least-privilege-framework.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../access-control/least-privilege-framework.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../access-control/least-privilege-framework.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../access-control/least-privilege-framework.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1 current"><a class="reference internal" href="../index.html">Security Documentation</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../security-summary.html">Security Documentation Summary</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security-summary.html#executive-overview">Executive Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security-summary.html#security-documentation-structure">Security Documentation Structure</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security-summary.html#recent-security-achievements">Recent Security Achievements</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security-summary.html#june-2025-security-review-results">June 2025 Security Review Results</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-summary.html#key-security-fixes-implemented">Key Security Fixes Implemented</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security-summary.html#security-architecture-excellence">Security Architecture Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security-summary.html#multi-layered-security-approach">Multi-Layered Security Approach</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-summary.html#security-testing-excellence">Security Testing Excellence</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security-summary.html#compliance-and-regulatory-excellence">Compliance and Regulatory Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security-summary.html#regulatory-compliance-status">Regulatory Compliance Status</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-summary.html#industry-standards-compliance">Industry Standards Compliance</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security-summary.html#security-operations-excellence">Security Operations Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security-summary.html#vulnerability-management-program">Vulnerability Management Program</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-summary.html#security-monitoring-and-incident-response">Security Monitoring and Incident Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security-summary.html#risk-management-excellence">Risk Management Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security-summary.html#enterprise-risk-assessment">Enterprise Risk Assessment</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security-summary.html#business-impact-and-value">Business Impact and Value</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security-summary.html#security-investment-roi">Security Investment ROI</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security-summary.html#future-security-roadmap">Future Security Roadmap</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security-summary.html#short-term-objectives-q3-q4-2025">Short-Term Objectives (Q3-Q4 2025)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security-summary.html#long-term-strategic-goals-2026">Long-Term Strategic Goals (2026)</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security-summary.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../index.html#overview">Overview</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../index.html#security-principles">Security Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../index.html#security-framework">Security Framework</a></li>
<li class="toctree-l3"><a class="reference internal" href="../index.html#security-architecture">Security Architecture</a></li>
</ul>
</li>
<li class="toctree-l2 current"><a class="reference internal" href="../index.html#security-documentation-sections">Security Documentation Sections</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="../architecture/overview.html">Security Architecture Overview</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../architecture/overview.html#architecture-principles">Architecture Principles</a></li>
<li class="toctree-l4"><a class="reference internal" href="../architecture/overview.html#security-architecture-layers">Security Architecture Layers</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../architecture/overview.html#application-security-layer">Application Security Layer</a></li>
<li class="toctree-l5"><a class="reference internal" href="../architecture/overview.html#infrastructure-security-layer">Infrastructure Security Layer</a></li>
<li class="toctree-l5"><a class="reference internal" href="../architecture/overview.html#data-security-layer">Data Security Layer</a></li>
<li class="toctree-l5"><a class="reference internal" href="../architecture/overview.html#monitoring-and-logging-layer">Monitoring and Logging Layer</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../architecture/overview.html#security-controls-implementation">Security Controls Implementation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../architecture/overview.html#compliance-and-standards">Compliance and Standards</a></li>
<li class="toctree-l4"><a class="reference internal" href="../architecture/overview.html#security-testing-and-validation">Security Testing and Validation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../architecture/overview.html#continuous-improvement">Continuous Improvement</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../testing/security-automation.html">Security Testing Automation</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../testing/security-automation.html#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="../testing/security-automation.html#security-automation-architecture">Security Automation Architecture</a></li>
<li class="toctree-l4"><a class="reference internal" href="../testing/security-automation.html#pre-commit-security-hooks">Pre-commit Security Hooks</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/security-automation.html#developer-workstation-security">Developer Workstation Security</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/security-automation.html#ci-cd-security-pipeline">CI/CD Security Pipeline</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/security-automation.html#pipeline-configuration">Pipeline Configuration</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/security-automation.html#security-testing-tools-integration">Security Testing Tools Integration</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/security-automation.html#static-application-security-testing-sast">Static Application Security Testing (SAST)</a></li>
<li class="toctree-l5"><a class="reference internal" href="../testing/security-automation.html#dynamic-application-security-testing-dast">Dynamic Application Security Testing (DAST)</a></li>
<li class="toctree-l5"><a class="reference internal" href="../testing/security-automation.html#dependency-security-scanning">Dependency Security Scanning</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/security-automation.html#security-quality-gates">Security Quality Gates</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/security-automation.html#deployment-gates-configuration">Deployment Gates Configuration</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/security-automation.html#automated-remediation">Automated Remediation</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/security-automation.html#auto-fix-capabilities">Auto-fix Capabilities</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/security-automation.html#continuous-security-monitoring">Continuous Security Monitoring</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/security-automation.html#runtime-security-monitoring">Runtime Security Monitoring</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/security-automation.html#metrics-and-reporting">Metrics and Reporting</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/security-automation.html#security-automation-metrics">Security Automation Metrics</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/security-automation.html#best-practices-and-guidelines">Best Practices and Guidelines</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/security-automation.html#security-automation-best-practices">Security Automation Best Practices</a></li>
<li class="toctree-l5"><a class="reference internal" href="../testing/security-automation.html#developer-guidelines">Developer Guidelines</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/security-automation.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../testing/static-analysis.html">Static Application Security Testing (SAST)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../testing/static-analysis.html#overview">Overview</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/static-analysis.html#sast-benefits">SAST Benefits</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/static-analysis.html#sast-tools-and-implementation">SAST Tools and Implementation</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/static-analysis.html#primary-sast-tools">Primary SAST Tools</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/static-analysis.html#sast-integration-in-ci-cd">SAST Integration in CI/CD</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/static-analysis.html#github-actions-integration">GitHub Actions Integration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../testing/static-analysis.html#makefile-integration">Makefile Integration</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/static-analysis.html#sast-rule-configuration">SAST Rule Configuration</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/static-analysis.html#custom-security-rules">Custom Security Rules</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/static-analysis.html#sast-results-analysis">SAST Results Analysis</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/static-analysis.html#vulnerability-classification">Vulnerability Classification</a></li>
<li class="toctree-l5"><a class="reference internal" href="../testing/static-analysis.html#results-processing">Results Processing</a></li>
<li class="toctree-l5"><a class="reference internal" href="../testing/static-analysis.html#false-positive-management">False Positive Management</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/static-analysis.html#sast-metrics-and-reporting">SAST Metrics and Reporting</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/static-analysis.html#key-metrics">Key Metrics</a></li>
<li class="toctree-l5"><a class="reference internal" href="../testing/static-analysis.html#reporting-dashboard">Reporting Dashboard</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/static-analysis.html#best-practices">Best Practices</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/static-analysis.html#sast-implementation-best-practices">SAST Implementation Best Practices</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/static-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../testing/dynamic-testing.html">Dynamic Application Security Testing (DAST)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../testing/dynamic-testing.html#overview">Overview</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/dynamic-testing.html#dast-benefits">DAST Benefits</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/dynamic-testing.html#dast-tools-and-implementation">DAST Tools and Implementation</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/dynamic-testing.html#primary-dast-tools">Primary DAST Tools</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/dynamic-testing.html#dast-test-categories">DAST Test Categories</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/dynamic-testing.html#authentication-security-tests">Authentication Security Tests</a></li>
<li class="toctree-l5"><a class="reference internal" href="../testing/dynamic-testing.html#authorization-security-tests">Authorization Security Tests</a></li>
<li class="toctree-l5"><a class="reference internal" href="../testing/dynamic-testing.html#input-validation-tests">Input Validation Tests</a></li>
<li class="toctree-l5"><a class="reference internal" href="../testing/dynamic-testing.html#business-logic-tests">Business Logic Tests</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/dynamic-testing.html#dast-automation-and-ci-cd-integration">DAST Automation and CI/CD Integration</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/dynamic-testing.html#automated-dast-pipeline">Automated DAST Pipeline</a></li>
<li class="toctree-l5"><a class="reference internal" href="../testing/dynamic-testing.html#makefile-integration">Makefile Integration</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/dynamic-testing.html#dast-results-analysis">DAST Results Analysis</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/dynamic-testing.html#vulnerability-assessment">Vulnerability Assessment</a></li>
<li class="toctree-l5"><a class="reference internal" href="../testing/dynamic-testing.html#performance-impact-assessment">Performance Impact Assessment</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/dynamic-testing.html#dast-best-practices">DAST Best Practices</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../testing/dynamic-testing.html#test-environment-management">Test Environment Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../testing/dynamic-testing.html#test-data-management">Test Data Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../testing/dynamic-testing.html#continuous-improvement">Continuous Improvement</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../testing/dynamic-testing.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="security-review-2025-06-14.html">Security Review - June 14, 2025</a><ul>
<li class="toctree-l4"><a class="reference internal" href="security-review-2025-06-14.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l4"><a class="reference internal" href="security-review-2025-06-14.html#security-scan-results">Security Scan Results</a></li>
<li class="toctree-l4"><a class="reference internal" href="security-review-2025-06-14.html#critical-security-issues-resolved">Critical Security Issues Resolved</a><ul>
<li class="toctree-l5"><a class="reference internal" href="security-review-2025-06-14.html#weak-cryptographic-hash-fixed">1. Weak Cryptographic Hash ✅ <strong>FIXED</strong></a></li>
<li class="toctree-l5"><a class="reference internal" href="security-review-2025-06-14.html#unsafe-pickle-deserialization-fixed">2. Unsafe Pickle Deserialization ✅ <strong>FIXED</strong></a></li>
<li class="toctree-l5"><a class="reference internal" href="security-review-2025-06-14.html#weak-random-number-generation-fixed">3. Weak Random Number Generation ✅ <strong>FIXED</strong></a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="security-review-2025-06-14.html#security-improvements-implemented">Security Improvements Implemented</a><ul>
<li class="toctree-l5"><a class="reference internal" href="security-review-2025-06-14.html#enhanced-security-configuration">Enhanced Security Configuration</a></li>
<li class="toctree-l5"><a class="reference internal" href="security-review-2025-06-14.html#data-protection">Data Protection</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="security-review-2025-06-14.html#testing-and-validation">Testing and Validation</a><ul>
<li class="toctree-l5"><a class="reference internal" href="security-review-2025-06-14.html#security-test-coverage">Security Test Coverage</a></li>
<li class="toctree-l5"><a class="reference internal" href="security-review-2025-06-14.html#test-results">Test Results</a></li>
<li class="toctree-l5"><a class="reference internal" href="security-review-2025-06-14.html#continuous-security">Continuous Security</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="security-review-2025-06-14.html#business-impact">Business Impact</a><ul>
<li class="toctree-l5"><a class="reference internal" href="security-review-2025-06-14.html#risk-reduction">Risk Reduction</a></li>
<li class="toctree-l5"><a class="reference internal" href="security-review-2025-06-14.html#operational-benefits">Operational Benefits</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="security-review-2025-06-14.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">Security Assessment Overview</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#introduction">Introduction</a></li>
<li class="toctree-l4"><a class="reference internal" href="#assessment-methodology">Assessment Methodology</a><ul>
<li class="toctree-l5"><a class="reference internal" href="#static-application-security-testing-sast">Static Application Security Testing (SAST)</a></li>
<li class="toctree-l5"><a class="reference internal" href="#dynamic-application-security-testing-dast">Dynamic Application Security Testing (DAST)</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="#assessment-results-summary">Assessment Results Summary</a><ul>
<li class="toctree-l5"><a class="reference internal" href="#current-security-posture">Current Security Posture</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="#vulnerability-management">Vulnerability Management</a><ul>
<li class="toctree-l5"><a class="reference internal" href="#vulnerability-classification">Vulnerability Classification</a></li>
<li class="toctree-l5"><a class="reference internal" href="#current-vulnerability-status">Current Vulnerability Status</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="#risk-assessment-framework">Risk Assessment Framework</a><ul>
<li class="toctree-l5"><a class="reference internal" href="#threat-modeling">Threat Modeling</a></li>
<li class="toctree-l5"><a class="reference internal" href="#risk-calculation">Risk Calculation</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="#security-testing-automation">Security Testing Automation</a><ul>
<li class="toctree-l5"><a class="reference internal" href="#ci-cd-security-integration">CI/CD Security Integration</a></li>
<li class="toctree-l5"><a class="reference internal" href="#security-gates">Security Gates</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="#compliance-and-audit">Compliance and Audit</a><ul>
<li class="toctree-l5"><a class="reference internal" href="#regulatory-compliance">Regulatory Compliance</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="#continuous-improvement">Continuous Improvement</a><ul>
<li class="toctree-l5"><a class="reference internal" href="#security-metrics-and-kpis">Security Metrics and KPIs</a></li>
<li class="toctree-l5"><a class="reference internal" href="#future-enhancements">Future Enhancements</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="vulnerability-management.html">Vulnerability Management</a><ul>
<li class="toctree-l4"><a class="reference internal" href="vulnerability-management.html#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="vulnerability-management.html#vulnerability-management-lifecycle">Vulnerability Management Lifecycle</a></li>
<li class="toctree-l4"><a class="reference internal" href="vulnerability-management.html#vulnerability-discovery">Vulnerability Discovery</a><ul>
<li class="toctree-l5"><a class="reference internal" href="vulnerability-management.html#automated-vulnerability-scanning">Automated Vulnerability Scanning</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="vulnerability-management.html#vulnerability-assessment">Vulnerability Assessment</a><ul>
<li class="toctree-l5"><a class="reference internal" href="vulnerability-management.html#cvss-scoring">CVSS Scoring</a></li>
<li class="toctree-l5"><a class="reference internal" href="vulnerability-management.html#risk-assessment-matrix">Risk Assessment Matrix</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="vulnerability-management.html#vulnerability-prioritization">Vulnerability Prioritization</a><ul>
<li class="toctree-l5"><a class="reference internal" href="vulnerability-management.html#risk-based-prioritization">Risk-Based Prioritization</a></li>
<li class="toctree-l5"><a class="reference internal" href="vulnerability-management.html#business-impact-assessment">Business Impact Assessment</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="vulnerability-management.html#remediation-strategies">Remediation Strategies</a><ul>
<li class="toctree-l5"><a class="reference internal" href="vulnerability-management.html#patching-and-updates">Patching and Updates</a></li>
<li class="toctree-l5"><a class="reference internal" href="vulnerability-management.html#configuration-hardening">Configuration Hardening</a></li>
<li class="toctree-l5"><a class="reference internal" href="vulnerability-management.html#compensating-controls">Compensating Controls</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="vulnerability-management.html#remediation-tracking">Remediation Tracking</a><ul>
<li class="toctree-l5"><a class="reference internal" href="vulnerability-management.html#vulnerability-database">Vulnerability Database</a></li>
<li class="toctree-l5"><a class="reference internal" href="vulnerability-management.html#status-tracking">Status Tracking</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="vulnerability-management.html#metrics-and-reporting">Metrics and Reporting</a><ul>
<li class="toctree-l5"><a class="reference internal" href="vulnerability-management.html#key-performance-indicators">Key Performance Indicators</a></li>
<li class="toctree-l5"><a class="reference internal" href="vulnerability-management.html#executive-reporting">Executive Reporting</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="vulnerability-management.html#continuous-improvement">Continuous Improvement</a><ul>
<li class="toctree-l5"><a class="reference internal" href="vulnerability-management.html#program-enhancement">Program Enhancement</a></li>
<li class="toctree-l5"><a class="reference internal" href="vulnerability-management.html#integration-with-sdlc">Integration with SDLC</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="vulnerability-management.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../operations/incident-response.html">Security Incident Response</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../operations/incident-response.html#overview">Overview</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../operations/incident-response.html#incident-response-objectives">Incident Response Objectives</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../operations/incident-response.html#incident-classification">Incident Classification</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../operations/incident-response.html#security-incident-categories">Security Incident Categories</a></li>
<li class="toctree-l5"><a class="reference internal" href="../operations/incident-response.html#severity-levels">Severity Levels</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../operations/incident-response.html#incident-response-team">Incident Response Team</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../operations/incident-response.html#core-response-team">Core Response Team</a></li>
<li class="toctree-l5"><a class="reference internal" href="../operations/incident-response.html#extended-response-team">Extended Response Team</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../operations/incident-response.html#incident-response-process">Incident Response Process</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../operations/incident-response.html#phase-1-detection-and-analysis">Phase 1: Detection and Analysis</a></li>
<li class="toctree-l5"><a class="reference internal" href="../operations/incident-response.html#phase-2-containment">Phase 2: Containment</a></li>
<li class="toctree-l5"><a class="reference internal" href="../operations/incident-response.html#phase-3-eradication">Phase 3: Eradication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../operations/incident-response.html#phase-4-recovery">Phase 4: Recovery</a></li>
<li class="toctree-l5"><a class="reference internal" href="../operations/incident-response.html#phase-5-post-incident-analysis">Phase 5: Post-Incident Analysis</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../operations/incident-response.html#communication-procedures">Communication Procedures</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../operations/incident-response.html#internal-communications">Internal Communications</a></li>
<li class="toctree-l5"><a class="reference internal" href="../operations/incident-response.html#external-communications">External Communications</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../operations/incident-response.html#legal-and-regulatory-considerations">Legal and Regulatory Considerations</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../operations/incident-response.html#evidence-handling">Evidence Handling</a></li>
<li class="toctree-l5"><a class="reference internal" href="../operations/incident-response.html#regulatory-compliance">Regulatory Compliance</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../operations/incident-response.html#training-and-preparedness">Training and Preparedness</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../operations/incident-response.html#incident-response-training">Incident Response Training</a></li>
<li class="toctree-l5"><a class="reference internal" href="../operations/incident-response.html#preparedness-activities">Preparedness Activities</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../operations/incident-response.html#metrics-and-reporting">Metrics and Reporting</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../operations/incident-response.html#incident-response-metrics">Incident Response Metrics</a></li>
<li class="toctree-l5"><a class="reference internal" href="../operations/incident-response.html#reporting-and-analysis">Reporting and Analysis</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../operations/incident-response.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../procedures/security-review-process.html">Security Review Process</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../procedures/security-review-process.html#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="../procedures/security-review-process.html#security-review-framework">Security Review Framework</a></li>
<li class="toctree-l4"><a class="reference internal" href="../procedures/security-review-process.html#review-types-and-triggers">Review Types and Triggers</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../procedures/security-review-process.html#code-security-reviews">Code Security Reviews</a></li>
<li class="toctree-l5"><a class="reference internal" href="../procedures/security-review-process.html#architecture-security-reviews">Architecture Security Reviews</a></li>
<li class="toctree-l5"><a class="reference internal" href="../procedures/security-review-process.html#periodic-security-assessments">Periodic Security Assessments</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../procedures/security-review-process.html#security-review-procedures">Security Review Procedures</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../procedures/security-review-process.html#code-review-process">Code Review Process</a></li>
<li class="toctree-l5"><a class="reference internal" href="../procedures/security-review-process.html#architecture-review-process">Architecture Review Process</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../procedures/security-review-process.html#review-documentation-and-tracking">Review Documentation and Tracking</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../procedures/security-review-process.html#security-review-templates">Security Review Templates</a></li>
<li class="toctree-l5"><a class="reference internal" href="../procedures/security-review-process.html#review-tracking-and-metrics">Review Tracking and Metrics</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../procedures/security-review-process.html#quality-assurance-and-improvement">Quality Assurance and Improvement</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../procedures/security-review-process.html#review-quality-metrics">Review Quality Metrics</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../procedures/security-review-process.html#best-practices-and-guidelines">Best Practices and Guidelines</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../procedures/security-review-process.html#security-review-best-practices">Security Review Best Practices</a></li>
<li class="toctree-l5"><a class="reference internal" href="../procedures/security-review-process.html#common-security-review-pitfalls">Common Security Review Pitfalls</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../procedures/security-review-process.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../procedures/vulnerability-disclosure.html">Vulnerability Disclosure Policy</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#overview">Overview</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#responsible-disclosure-principles">Responsible Disclosure Principles</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#scope">Scope</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#in-scope">In Scope</a></li>
<li class="toctree-l5"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#out-of-scope">Out of Scope</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#vulnerability-reporting-process">Vulnerability Reporting Process</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#how-to-report">How to Report</a></li>
<li class="toctree-l5"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#response-process">Response Process</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#severity-classification">Severity Classification</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#critical-cvss-9-0-10-0">Critical (CVSS 9.0-10.0)</a></li>
<li class="toctree-l5"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#high-cvss-7-0-8-9">High (CVSS 7.0-8.9)</a></li>
<li class="toctree-l5"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#medium-cvss-4-0-6-9">Medium (CVSS 4.0-6.9)</a></li>
<li class="toctree-l5"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#low-cvss-0-1-3-9">Low (CVSS 0.1-3.9)</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#recognition-and-rewards">Recognition and Rewards</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#security-researcher-recognition">Security Researcher Recognition</a></li>
<li class="toctree-l5"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#bug-bounty-program">Bug Bounty Program</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#legal-considerations">Legal Considerations</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#safe-harbor">Safe Harbor</a></li>
<li class="toctree-l5"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#terms-and-conditions">Terms and Conditions</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#communication-guidelines">Communication Guidelines</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#professional-communication">Professional Communication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#public-disclosure">Public Disclosure</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#contact-information">Contact Information</a></li>
<li class="toctree-l4"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#updates-and-changes">Updates and Changes</a></li>
<li class="toctree-l4"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../index.html#security-certifications-and-compliance">Security Certifications and Compliance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../index.html#security-contact-information">Security Contact Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="../index.html#quick-security-reference">Quick Security Reference</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../framework.html">Security Framework</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../framework.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../framework.html#security-architecture">Security Architecture</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../framework.html#layered-security-model">Layered Security Model</a></li>
<li class="toctree-l3"><a class="reference internal" href="../framework.html#zero-trust-implementation">Zero Trust Implementation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../framework.html#security-controls-framework">Security Controls Framework</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../framework.html#administrative-controls">Administrative Controls</a></li>
<li class="toctree-l3"><a class="reference internal" href="../framework.html#technical-controls">Technical Controls</a></li>
<li class="toctree-l3"><a class="reference internal" href="../framework.html#physical-controls">Physical Controls</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../framework.html#compliance-framework">Compliance Framework</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../framework.html#soc-2-type-ii-compliance">SOC 2 Type II Compliance</a></li>
<li class="toctree-l3"><a class="reference internal" href="../framework.html#iso-27001-compliance">ISO 27001 Compliance</a></li>
<li class="toctree-l3"><a class="reference internal" href="../framework.html#pci-dss-compliance">PCI DSS Compliance</a></li>
<li class="toctree-l3"><a class="reference internal" href="../framework.html#gdpr-compliance">GDPR Compliance</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../framework.html#threat-intelligence-integration">Threat Intelligence Integration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../framework.html#mitre-att-ck-framework">MITRE ATT&amp;CK Framework</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../framework.html#security-monitoring">Security Monitoring</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../framework.html#continuous-monitoring">Continuous Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="../framework.html#incident-response">Incident Response</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../framework.html#security-training-and-awareness">Security Training and Awareness</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../framework.html#training-program">Training Program</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../framework.html#continuous-improvement">Continuous Improvement</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../framework.html#security-maturity-assessment">Security Maturity Assessment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../framework.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../architecture/overview.html">Security Architecture Overview</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../architecture/overview.html#architecture-principles">Architecture Principles</a></li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/overview.html#security-architecture-layers">Security Architecture Layers</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/overview.html#application-security-layer">Application Security Layer</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/overview.html#infrastructure-security-layer">Infrastructure Security Layer</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/overview.html#data-security-layer">Data Security Layer</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/overview.html#monitoring-and-logging-layer">Monitoring and Logging Layer</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/overview.html#security-controls-implementation">Security Controls Implementation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/overview.html#compliance-and-standards">Compliance and Standards</a></li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/overview.html#security-testing-and-validation">Security Testing and Validation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/overview.html#continuous-improvement">Continuous Improvement</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../operations/incident-response.html">Security Incident Response</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../operations/incident-response.html#overview">Overview</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../operations/incident-response.html#incident-response-objectives">Incident Response Objectives</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../operations/incident-response.html#incident-classification">Incident Classification</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../operations/incident-response.html#security-incident-categories">Security Incident Categories</a></li>
<li class="toctree-l3"><a class="reference internal" href="../operations/incident-response.html#severity-levels">Severity Levels</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../operations/incident-response.html#incident-response-team">Incident Response Team</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../operations/incident-response.html#core-response-team">Core Response Team</a></li>
<li class="toctree-l3"><a class="reference internal" href="../operations/incident-response.html#extended-response-team">Extended Response Team</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../operations/incident-response.html#incident-response-process">Incident Response Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../operations/incident-response.html#phase-1-detection-and-analysis">Phase 1: Detection and Analysis</a></li>
<li class="toctree-l3"><a class="reference internal" href="../operations/incident-response.html#phase-2-containment">Phase 2: Containment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../operations/incident-response.html#phase-3-eradication">Phase 3: Eradication</a></li>
<li class="toctree-l3"><a class="reference internal" href="../operations/incident-response.html#phase-4-recovery">Phase 4: Recovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../operations/incident-response.html#phase-5-post-incident-analysis">Phase 5: Post-Incident Analysis</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../operations/incident-response.html#communication-procedures">Communication Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../operations/incident-response.html#internal-communications">Internal Communications</a></li>
<li class="toctree-l3"><a class="reference internal" href="../operations/incident-response.html#external-communications">External Communications</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../operations/incident-response.html#legal-and-regulatory-considerations">Legal and Regulatory Considerations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../operations/incident-response.html#evidence-handling">Evidence Handling</a></li>
<li class="toctree-l3"><a class="reference internal" href="../operations/incident-response.html#regulatory-compliance">Regulatory Compliance</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../operations/incident-response.html#training-and-preparedness">Training and Preparedness</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../operations/incident-response.html#incident-response-training">Incident Response Training</a></li>
<li class="toctree-l3"><a class="reference internal" href="../operations/incident-response.html#preparedness-activities">Preparedness Activities</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../operations/incident-response.html#metrics-and-reporting">Metrics and Reporting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../operations/incident-response.html#incident-response-metrics">Incident Response Metrics</a></li>
<li class="toctree-l3"><a class="reference internal" href="../operations/incident-response.html#reporting-and-analysis">Reporting and Analysis</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../operations/incident-response.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../procedures/vulnerability-disclosure.html">Vulnerability Disclosure Policy</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#overview">Overview</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#responsible-disclosure-principles">Responsible Disclosure Principles</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#scope">Scope</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#in-scope">In Scope</a></li>
<li class="toctree-l3"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#out-of-scope">Out of Scope</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#vulnerability-reporting-process">Vulnerability Reporting Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#how-to-report">How to Report</a></li>
<li class="toctree-l3"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#response-process">Response Process</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#severity-classification">Severity Classification</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#critical-cvss-9-0-10-0">Critical (CVSS 9.0-10.0)</a></li>
<li class="toctree-l3"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#high-cvss-7-0-8-9">High (CVSS 7.0-8.9)</a></li>
<li class="toctree-l3"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#medium-cvss-4-0-6-9">Medium (CVSS 4.0-6.9)</a></li>
<li class="toctree-l3"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#low-cvss-0-1-3-9">Low (CVSS 0.1-3.9)</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#recognition-and-rewards">Recognition and Rewards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#security-researcher-recognition">Security Researcher Recognition</a></li>
<li class="toctree-l3"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#bug-bounty-program">Bug Bounty Program</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#legal-considerations">Legal Considerations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#safe-harbor">Safe Harbor</a></li>
<li class="toctree-l3"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#terms-and-conditions">Terms and Conditions</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#communication-guidelines">Communication Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#professional-communication">Professional Communication</a></li>
<li class="toctree-l3"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#public-disclosure">Public Disclosure</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#contact-information">Contact Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#updates-and-changes">Updates and Changes</a></li>
<li class="toctree-l2"><a class="reference internal" href="../procedures/vulnerability-disclosure.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../testing/dynamic-testing.html">Dynamic Application Security Testing (DAST)</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../testing/dynamic-testing.html#overview">Overview</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../testing/dynamic-testing.html#dast-benefits">DAST Benefits</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../testing/dynamic-testing.html#dast-tools-and-implementation">DAST Tools and Implementation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../testing/dynamic-testing.html#primary-dast-tools">Primary DAST Tools</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../testing/dynamic-testing.html#dast-test-categories">DAST Test Categories</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../testing/dynamic-testing.html#authentication-security-tests">Authentication Security Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../testing/dynamic-testing.html#authorization-security-tests">Authorization Security Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../testing/dynamic-testing.html#input-validation-tests">Input Validation Tests</a></li>
<li class="toctree-l3"><a class="reference internal" href="../testing/dynamic-testing.html#business-logic-tests">Business Logic Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../testing/dynamic-testing.html#dast-automation-and-ci-cd-integration">DAST Automation and CI/CD Integration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../testing/dynamic-testing.html#automated-dast-pipeline">Automated DAST Pipeline</a></li>
<li class="toctree-l3"><a class="reference internal" href="../testing/dynamic-testing.html#makefile-integration">Makefile Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../testing/dynamic-testing.html#dast-results-analysis">DAST Results Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../testing/dynamic-testing.html#vulnerability-assessment">Vulnerability Assessment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../testing/dynamic-testing.html#performance-impact-assessment">Performance Impact Assessment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../testing/dynamic-testing.html#dast-best-practices">DAST Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../testing/dynamic-testing.html#test-environment-management">Test Environment Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../testing/dynamic-testing.html#test-data-management">Test Data Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../testing/dynamic-testing.html#continuous-improvement">Continuous Improvement</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../testing/dynamic-testing.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../testing/static-analysis.html">Static Application Security Testing (SAST)</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../testing/static-analysis.html#overview">Overview</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../testing/static-analysis.html#sast-benefits">SAST Benefits</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../testing/static-analysis.html#sast-tools-and-implementation">SAST Tools and Implementation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../testing/static-analysis.html#primary-sast-tools">Primary SAST Tools</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../testing/static-analysis.html#sast-integration-in-ci-cd">SAST Integration in CI/CD</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../testing/static-analysis.html#github-actions-integration">GitHub Actions Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../testing/static-analysis.html#makefile-integration">Makefile Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../testing/static-analysis.html#sast-rule-configuration">SAST Rule Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../testing/static-analysis.html#custom-security-rules">Custom Security Rules</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../testing/static-analysis.html#sast-results-analysis">SAST Results Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../testing/static-analysis.html#vulnerability-classification">Vulnerability Classification</a></li>
<li class="toctree-l3"><a class="reference internal" href="../testing/static-analysis.html#results-processing">Results Processing</a></li>
<li class="toctree-l3"><a class="reference internal" href="../testing/static-analysis.html#false-positive-management">False Positive Management</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../testing/static-analysis.html#sast-metrics-and-reporting">SAST Metrics and Reporting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../testing/static-analysis.html#key-metrics">Key Metrics</a></li>
<li class="toctree-l3"><a class="reference internal" href="../testing/static-analysis.html#reporting-dashboard">Reporting Dashboard</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../testing/static-analysis.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../testing/static-analysis.html#sast-implementation-best-practices">SAST Implementation Best Practices</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../testing/static-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Deployment &amp; Operations</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../deployment/production-deployment-guide.html">Production Deployment Guide - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/production-deployment-guide.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/production-deployment-guide.html#prerequisites">Prerequisites</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-deployment-guide.html#infrastructure-requirements">Infrastructure Requirements</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#minimum-system-requirements">Minimum System Requirements</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#network-requirements">Network Requirements</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#external-dependencies">External Dependencies</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-deployment-guide.html#required-tools-and-access">Required Tools and Access</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#development-tools">Development Tools</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#aws-access-requirements">AWS Access Requirements</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/production-deployment-guide.html#infrastructure-deployment">Infrastructure Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-deployment-guide.html#step-1-environment-configuration">Step 1: Environment Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#configure-aws-cli">1.1 Configure AWS CLI</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#set-environment-variables">1.2 Set Environment Variables</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-deployment-guide.html#step-2-infrastructure-provisioning">Step 2: Infrastructure Provisioning</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#deploy-core-infrastructure">2.1 Deploy Core Infrastructure</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#configure-kubectl-access">2.2 Configure kubectl Access</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#install-cluster-add-ons">2.3 Install Cluster Add-ons</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-deployment-guide.html#step-3-database-setup">Step 3: Database Setup</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#database-initialization">3.1 Database Initialization</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#database-migration">3.2 Database Migration</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/production-deployment-guide.html#application-deployment">Application Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-deployment-guide.html#step-4-container-image-preparation">Step 4: Container Image Preparation</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#build-and-push-images">4.1 Build and Push Images</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-deployment-guide.html#step-5-kubernetes-deployment">Step 5: Kubernetes Deployment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#create-namespace-and-secrets">5.1 Create Namespace and Secrets</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#deploy-application-components">5.2 Deploy Application Components</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#configure-ingress-and-load-balancer">5.3 Configure Ingress and Load Balancer</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-deployment-guide.html#step-6-ssl-tls-configuration">Step 6: SSL/TLS Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#request-ssl-certificate">6.1 Request SSL Certificate</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#configure-https-redirect">6.2 Configure HTTPS Redirect</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/production-deployment-guide.html#post-deployment-configuration">Post-Deployment Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-deployment-guide.html#step-7-application-configuration">Step 7: Application Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#initialize-application-data">7.1 Initialize Application Data</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#configure-monitoring-and-alerting">7.2 Configure Monitoring and Alerting</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-deployment-guide.html#step-8-security-hardening">Step 8: Security Hardening</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#network-policies">8.1 Network Policies</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#pod-security-policies">8.2 Pod Security Policies</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/production-deployment-guide.html#verification-and-testing">Verification and Testing</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-deployment-guide.html#step-9-deployment-verification">Step 9: Deployment Verification</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#health-checks">9.1 Health Checks</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#functional-testing">9.2 Functional Testing</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#security-testing">9.3 Security Testing</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/production-deployment-guide.html#backup-and-recovery-setup">Backup and Recovery Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-deployment-guide.html#step-10-backup-configuration">Step 10: Backup Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#database-backup">10.1 Database Backup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#application-data-backup">10.2 Application Data Backup</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/production-deployment-guide.html#monitoring-and-maintenance">Monitoring and Maintenance</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-deployment-guide.html#step-11-ongoing-monitoring">Step 11: Ongoing Monitoring</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#set-up-alerts">11.1 Set Up Alerts</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/production-deployment-guide.html#performance-monitoring">11.2 Performance Monitoring</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/production-deployment-guide.html#troubleshooting-common-issues">Troubleshooting Common Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-deployment-guide.html#database-connection-issues">Database Connection Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-deployment-guide.html#pod-startup-issues">Pod Startup Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-deployment-guide.html#load-balancer-issues">Load Balancer Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/production-deployment-guide.html#rollback-procedures">Rollback Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-deployment-guide.html#emergency-rollback">Emergency Rollback</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/production-deployment-guide.html#database-rollback">Database Rollback</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/production-deployment-guide.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../deployment/environment-setup.html">Environment Setup Documentation - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/environment-setup.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/environment-setup.html#environment-types">Environment Types</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/environment-setup.html#development-environment">Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/environment-setup.html#staging-environment">Staging Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/environment-setup.html#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/environment-setup.html#local-development-setup">Local Development Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/environment-setup.html#prerequisites">Prerequisites</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#system-requirements">System Requirements</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#required-software">Required Software</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/environment-setup.html#development-environment-setup">Development Environment Setup</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#clone-repository">1. Clone Repository</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#environment-configuration">2. Environment Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#backend-setup">3. Backend Setup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#frontend-setup">4. Frontend Setup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#start-development-services">5. Start Development Services</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#verify-development-setup">6. Verify Development Setup</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/environment-setup.html#staging-environment-setup">Staging Environment Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/environment-setup.html#infrastructure-provisioning">Infrastructure Provisioning</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#aws-configuration">1. AWS Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#terraform-deployment">2. Terraform Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#kubernetes-configuration">3. Kubernetes Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/environment-setup.html#application-deployment">Application Deployment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#build-and-push-images">1. Build and Push Images</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#deploy-to-staging">2. Deploy to Staging</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#configure-staging-data">3. Configure Staging Data</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/environment-setup.html#production-environment-setup">Production Environment Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/environment-setup.html#pre-production-checklist">Pre-Production Checklist</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#security-review">Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#performance-review">Performance Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#operational-readiness">Operational Readiness</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/environment-setup.html#production-deployment-process">Production Deployment Process</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#final-infrastructure-review">1. Final Infrastructure Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#production-image-preparation">2. Production Image Preparation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/environment-setup.html#blue-green-deployment">3. Blue-Green Deployment</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/environment-setup.html#environment-specific-configurations">Environment-Specific Configurations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/environment-setup.html#development-configuration">Development Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/environment-setup.html#staging-configuration">Staging Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/environment-setup.html#production-configuration">Production Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/environment-setup.html#ci-cd-pipeline-configuration">CI/CD Pipeline Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/environment-setup.html#github-actions-workflow">GitHub Actions Workflow</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/environment-setup.html#environment-management-scripts">Environment Management Scripts</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/environment-setup.html#deployment-script">Deployment Script</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/environment-setup.html#environment-cleanup-script">Environment Cleanup Script</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../deployment/troubleshooting-guide.html">Troubleshooting Guide - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#general-troubleshooting-approach">General Troubleshooting Approach</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#information-gathering">1. Information Gathering</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#log-analysis">2. Log Analysis</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#network-connectivity">3. Network Connectivity</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#common-issues-and-solutions">Common Issues and Solutions</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#application-startup-issues">Application Startup Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#issue-pod-stuck-in-pending-state">Issue: Pod Stuck in Pending State</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#issue-pod-stuck-in-crashloopbackoff">Issue: Pod Stuck in CrashLoopBackOff</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#database-issues">Database Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#issue-database-connection-timeout">Issue: Database Connection Timeout</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#issue-database-migration-failures">Issue: Database Migration Failures</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#network-and-connectivity-issues">Network and Connectivity Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#issue-service-not-accessible">Issue: Service Not Accessible</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#issue-ssl-tls-certificate-problems">Issue: SSL/TLS Certificate Problems</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#performance-issues">Performance Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#issue-high-response-times">Issue: High Response Times</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#issue-memory-leaks">Issue: Memory Leaks</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#storage-issues">Storage Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#issue-persistent-volume-problems">Issue: Persistent Volume Problems</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#monitoring-and-alerting-issues">Monitoring and Alerting Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#issue-missing-metrics">Issue: Missing Metrics</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#emergency-procedures">Emergency Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#complete-system-recovery">Complete System Recovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#rollback-procedure">Rollback Procedure</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#getting-help">Getting Help</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#internal-resources">Internal Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#external-support">External Support</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../deployment/troubleshooting-guide.html#escalation-procedures">Escalation Procedures</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html">Monitoring Runbooks - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#alert-response-procedures">Alert Response Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#critical-alerts-p0">Critical Alerts (P0)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#blastradiusapidown">BlastRadiusAPIDown</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#securitythreatspike">SecurityThreatSpike</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#high-priority-alerts-p1">High Priority Alerts (P1)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#highmemoryusage">HighMemoryUsage</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#databaseconnectionshigh">DatabaseConnectionsHigh</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#performance-monitoring-procedures">Performance Monitoring Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#daily-performance-checks">Daily Performance Checks</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#morning-health-check-9-00-am">Morning Health Check (9:00 AM)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#weekly-performance-review-monday-10-00-am">Weekly Performance Review (Monday 10:00 AM)</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#performance-optimization-procedures">Performance Optimization Procedures</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#database-optimization">Database Optimization</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#cache-optimization">Cache Optimization</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#system-health-monitoring">System Health Monitoring</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#automated-health-checks">Automated Health Checks</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#kubernetes-health-check">Kubernetes Health Check</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#application-health-check">Application Health Check</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#monitoring-dashboard-procedures">Monitoring Dashboard Procedures</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#grafana-dashboard-management">Grafana Dashboard Management</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#prometheus-configuration-management">Prometheus Configuration Management</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#log-management-procedures">Log Management Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#log-analysis">Log Analysis</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#log-retention-management">Log Retention Management</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#escalation-procedures">Escalation Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#alert-escalation-matrix">Alert Escalation Matrix</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/monitoring-runbooks.html#communication-procedures">Communication Procedures</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html">Backup and Recovery Runbooks - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#backup-procedures">Backup Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#database-backup-procedures">Database Backup Procedures</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#automated-daily-backup">Automated Daily Backup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#manual-database-backup">Manual Database Backup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#pre-deployment-backup">Pre-Deployment Backup</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#application-data-backup">Application Data Backup</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#file-system-backup">File System Backup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#configuration-backup">Configuration Backup</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#recovery-procedures">Recovery Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#database-recovery">Database Recovery</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#point-in-time-recovery">Point-in-Time Recovery</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#snapshot-recovery">Snapshot Recovery</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#logical-backup-recovery">Logical Backup Recovery</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#application-recovery">Application Recovery</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#full-application-recovery">Full Application Recovery</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#configuration-recovery">Configuration Recovery</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#disaster-recovery-procedures">Disaster Recovery Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#complete-system-recovery">Complete System Recovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#recovery-testing">Recovery Testing</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#backup-monitoring-and-validation">Backup Monitoring and Validation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/backup-recovery-runbooks.html#backup-verification">Backup Verification</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html">Maintenance Procedures - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#routine-maintenance-schedule">Routine Maintenance Schedule</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#daily-maintenance-automated">Daily Maintenance (Automated)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#system-health-check-6-00-am-utc">System Health Check (6:00 AM UTC)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#log-rotation-and-cleanup-2-00-am-utc">Log Rotation and Cleanup (2:00 AM UTC)</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#weekly-maintenance-sunday-3-00-am-utc">Weekly Maintenance (Sunday 3:00 AM UTC)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#database-maintenance">Database Maintenance</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#security-updates-check">Security Updates Check</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#monthly-maintenance-first-sunday-4-00-am-utc">Monthly Maintenance (First Sunday 4:00 AM UTC)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#performance-optimization">Performance Optimization</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#capacity-planning-review">Capacity Planning Review</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#system-updates-and-patches">System Updates and Patches</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#kubernetes-cluster-updates">Kubernetes Cluster Updates</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#application-updates">Application Updates</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#security-patches">Security Patches</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#preventive-maintenance">Preventive Maintenance</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#certificate-renewal">Certificate Renewal</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#id1">Database Maintenance</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#maintenance-windows">Maintenance Windows</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../operations/runbooks/maintenance-procedures.html#scheduled-maintenance-window">Scheduled Maintenance Window</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Performance</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../performance/index.html">Performance &amp; Scalability</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../performance/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../performance/index.html#performance-architecture">Performance Architecture</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../performance/index.html#performance-optimization">Performance Optimization</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../performance/index.html#database-optimization">Database Optimization</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../performance/index.html#application-optimization">Application Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../performance/index.html#scalability-patterns">Scalability Patterns</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../performance/index.html#horizontal-scaling">Horizontal Scaling</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../performance/index.html#vertical-scaling">Vertical Scaling</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../performance/index.html#performance-monitoring">Performance Monitoring</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../performance/index.html#key-performance-indicators">Key Performance Indicators</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../performance/index.html#monitoring-setup">Monitoring Setup</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../performance/index.html#performance-testing">Performance Testing</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../performance/index.html#load-testing">Load Testing</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../performance/index.html#benchmark-results">Benchmark Results</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../performance/index.html#optimization-recommendations">Optimization Recommendations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../performance/index.html#quick-wins">Quick Wins</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../performance/index.html#advanced-optimizations">Advanced Optimizations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../performance/index.html#performance-troubleshooting">Performance Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../performance/index.html#common-issues">Common Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../performance/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../performance/optimization.html">Performance Optimization</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../performance/optimization.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../performance/optimization.html#optimization-strategy">Optimization Strategy</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../performance/optimization.html#performance-methodology">Performance Methodology</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../performance/optimization.html#database-optimization">Database Optimization</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../performance/optimization.html#postgresql-tuning">PostgreSQL Tuning</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../performance/optimization.html#redis-optimization">Redis Optimization</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../performance/optimization.html#neo4j-optimization">Neo4j Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../performance/optimization.html#application-optimization">Application Optimization</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../performance/optimization.html#code-level-optimizations">Code-Level Optimizations</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../performance/optimization.html#algorithm-optimization">Algorithm Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../performance/optimization.html#caching-strategies">Caching Strategies</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../performance/optimization.html#multi-level-caching">Multi-Level Caching</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../performance/optimization.html#infrastructure-optimization">Infrastructure Optimization</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../performance/optimization.html#load-balancing">Load Balancing</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../performance/optimization.html#container-optimization">Container Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../performance/optimization.html#monitoring-and-profiling">Monitoring and Profiling</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../performance/optimization.html#performance-monitoring">Performance Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../performance/optimization.html#profiling-tools">Profiling Tools</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../performance/optimization.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../performance/optimization.html#optimization-guidelines">Optimization Guidelines</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../performance/optimization.html#common-pitfalls">Common Pitfalls</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../performance/optimization.html#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Testing</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../testing/index.html">Testing &amp; Quality Assurance</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../testing/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../testing/index.html#testing-philosophy">Testing Philosophy</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../testing/index.html#test-categories">Test Categories</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../testing/index.html#unit-testing">Unit Testing</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/index.html#integration-testing">Integration Testing</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/index.html#end-to-end-testing">End-to-End Testing</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/index.html#security-testing">Security Testing</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/index.html#performance-testing">Performance Testing</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../testing/index.html#test-automation">Test Automation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../testing/index.html#ci-cd-pipeline">CI/CD Pipeline</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/index.html#quality-gates">Quality Gates</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../testing/index.html#test-data-management">Test Data Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../testing/index.html#fixtures-and-factories">Fixtures and Factories</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/index.html#test-environment-setup">Test Environment Setup</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../testing/index.html#running-tests">Running Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../testing/index.html#local-development">Local Development</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/index.html#continuous-integration">Continuous Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../testing/index.html#test-reporting">Test Reporting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../testing/index.html#coverage-reports">Coverage Reports</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/index.html#quality-metrics">Quality Metrics</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../testing/index.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../testing/index.html#test-writing-guidelines">Test Writing Guidelines</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/index.html#maintenance">Maintenance</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../testing/index.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../testing/unit-tests.html">Unit Testing Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../testing/unit-tests.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../testing/unit-tests.html#testing-framework">Testing Framework</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../testing/unit-tests.html#pytest-configuration">pytest Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/unit-tests.html#test-structure">Test Structure</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../testing/unit-tests.html#writing-unit-tests">Writing Unit Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../testing/unit-tests.html#basic-test-structure">Basic Test Structure</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/unit-tests.html#fixtures-and-test-data">Fixtures and Test Data</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/unit-tests.html#mocking-and-patching">Mocking and Patching</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/unit-tests.html#parametrized-testing">Parametrized Testing</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/unit-tests.html#testing-async-code">Testing Async Code</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/unit-tests.html#exception-testing">Exception Testing</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/unit-tests.html#performance-testing">Performance Testing</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../testing/unit-tests.html#coverage-and-quality">Coverage and Quality</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../testing/unit-tests.html#coverage-requirements">Coverage Requirements</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/unit-tests.html#test-quality-metrics">Test Quality Metrics</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../testing/unit-tests.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../testing/unit-tests.html#test-design-principles">Test Design Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/unit-tests.html#common-testing-patterns">Common Testing Patterns</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../testing/unit-tests.html#running-tests">Running Tests</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../testing/unit-tests.html#test-execution">Test Execution</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/unit-tests.html#continuous-integration">Continuous Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../testing/unit-tests.html#debugging-tests">Debugging Tests</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../testing/unit-tests.html#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Troubleshooting</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../troubleshooting/common-issues.html">Common Issues &amp; Troubleshooting</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../troubleshooting/common-issues.html#quick-diagnostics">Quick Diagnostics</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/common-issues.html#health-check-commands">Health Check Commands</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../troubleshooting/common-issues.html#installation-issues">Installation Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/common-issues.html#docker-installation-problems">Docker Installation Problems</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/common-issues.html#database-connection-issues">Database Connection Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../troubleshooting/common-issues.html#configuration-issues">Configuration Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/common-issues.html#environment-variable-problems">Environment Variable Problems</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/common-issues.html#ssl-tls-certificate-issues">SSL/TLS Certificate Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../troubleshooting/common-issues.html#performance-issues">Performance Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/common-issues.html#slow-response-times">Slow Response Times</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/common-issues.html#memory-issues">Memory Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../troubleshooting/common-issues.html#authentication-authorization-issues">Authentication &amp; Authorization Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/common-issues.html#login-problems">Login Problems</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/common-issues.html#permission-errors">Permission Errors</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../troubleshooting/common-issues.html#integration-issues">Integration Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/common-issues.html#cloud-provider-integration">Cloud Provider Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/common-issues.html#network-connectivity-issues">Network Connectivity Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../troubleshooting/common-issues.html#frontend-issues">Frontend Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/common-issues.html#ui-not-loading">UI Not Loading</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../troubleshooting/common-issues.html#data-issues">Data Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/common-issues.html#missing-or-incorrect-data">Missing or Incorrect Data</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../troubleshooting/common-issues.html#getting-additional-help">Getting Additional Help</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/common-issues.html#log-collection">Log Collection</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/common-issues.html#support-channels">Support Channels</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../troubleshooting/faq.html">Frequently Asked Questions (FAQ)</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../troubleshooting/faq.html#general-questions">General Questions</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#what-is-the-blast-radius-security-tool">What is the Blast-Radius Security Tool?</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#who-should-use-this-tool">Who should use this tool?</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#what-are-the-system-requirements">What are the system requirements?</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../troubleshooting/faq.html#installation-and-setup">Installation and Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#how-do-i-install-the-blast-radius-security-tool">How do I install the Blast-Radius Security Tool?</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#what-are-the-default-login-credentials">What are the default login credentials?</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#how-do-i-reset-the-admin-password">How do I reset the admin password?</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#why-can-t-i-access-the-web-interface">Why can’t I access the web interface?</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../troubleshooting/faq.html#configuration-and-usage">Configuration and Usage</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#how-do-i-configure-cloud-provider-integrations">How do I configure cloud provider integrations?</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#how-do-i-add-new-users">How do I add new users?</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#how-do-i-enable-multi-factor-authentication-mfa">How do I enable Multi-Factor Authentication (MFA)?</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../troubleshooting/faq.html#attack-path-analysis">Attack Path Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#how-does-attack-path-analysis-work">How does attack path analysis work?</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#how-long-does-attack-path-analysis-take">How long does attack path analysis take?</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#why-am-i-not-seeing-any-attack-paths">Why am I not seeing any attack paths?</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#can-i-customize-risk-scoring">Can I customize risk scoring?</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../troubleshooting/faq.html#performance-and-troubleshooting">Performance and Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#why-is-the-platform-running-slowly">Why is the platform running slowly?</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#how-do-i-backup-my-data">How do I backup my data?</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#how-do-i-update-to-a-new-version">How do I update to a new version?</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../troubleshooting/faq.html#integration-and-api">Integration and API</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#how-do-i-integrate-with-my-siem">How do I integrate with my SIEM?</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#can-i-use-the-api-for-automation">Can I use the API for automation?</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#how-do-i-get-api-credentials">How do I get API credentials?</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../troubleshooting/faq.html#security-and-compliance">Security and Compliance</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#is-my-data-secure">Is my data secure?</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#how-do-i-configure-audit-logging">How do I configure audit logging?</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#can-i-run-this-in-an-air-gapped-environment">Can I run this in an air-gapped environment?</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../troubleshooting/faq.html#support-and-community">Support and Community</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#how-do-i-get-help">How do I get help?</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#is-there-a-community-or-forum">Is there a community or forum?</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#how-can-i-contribute">How can I contribute?</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../troubleshooting/faq.html#what-s-the-roadmap-for-future-features">What’s the roadmap for future features?</a></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content style-external-links">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../index.html">Security Documentation</a></li>
      <li class="breadcrumb-item active">Security Assessment Overview</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/security/reviews/security-assessment.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="security-assessment-overview">
<h1>Security Assessment Overview<a class="headerlink" href="#security-assessment-overview" title="Link to this heading"></a></h1>
<section id="introduction">
<h2>Introduction<a class="headerlink" href="#introduction" title="Link to this heading"></a></h2>
<p>This document provides an overview of the security assessment methodology, tools, and processes used to evaluate the security posture of the Blast-Radius Security Tool. Regular security assessments are critical for maintaining a robust security posture and ensuring compliance with industry standards.</p>
</section>
<section id="assessment-methodology">
<h2>Assessment Methodology<a class="headerlink" href="#assessment-methodology" title="Link to this heading"></a></h2>
<p>Our security assessment follows a comprehensive approach based on industry best practices:</p>
<p><strong>Assessment Types</strong>:</p>
<ul class="simple">
<li><p><strong>Static Application Security Testing (SAST)</strong>: Code analysis for vulnerabilities</p></li>
<li><p><strong>Dynamic Application Security Testing (DAST)</strong>: Runtime security testing</p></li>
<li><p><strong>Interactive Application Security Testing (IAST)</strong>: Real-time analysis</p></li>
<li><p><strong>Container Security</strong>: Container image and runtime security</p></li>
<li><p><strong>Infrastructure Security</strong>: Network and system security assessment</p></li>
</ul>
<section id="static-application-security-testing-sast">
<h3>Static Application Security Testing (SAST)<a class="headerlink" href="#static-application-security-testing-sast" title="Link to this heading"></a></h3>
<p><strong>Tools Used</strong>:</p>
<table class="docutils align-default" id="id1">
<caption><span class="caption-text">SAST Tools and Coverage</span><a class="headerlink" href="#id1" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 25.0%" />
<col style="width: 25.0%" />
<col style="width: 25.0%" />
<col style="width: 25.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Tool</p></th>
<th class="head"><p>Language</p></th>
<th class="head"><p>Frequency</p></th>
<th class="head"><p>Coverage</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>Bandit</strong></p></td>
<td><p>Python</p></td>
<td><p>Every commit</p></td>
<td><p>Security issues, hardcoded secrets</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Semgrep</strong></p></td>
<td><p>Multi-language</p></td>
<td><p>Daily</p></td>
<td><p>Custom security rules</p></td>
</tr>
<tr class="row-even"><td><p><strong>SonarQube</strong></p></td>
<td><p>All languages</p></td>
<td><p>Every build</p></td>
<td><p>Code quality, security hotspots</p></td>
</tr>
<tr class="row-odd"><td><p><strong>CodeQL</strong></p></td>
<td><p>Python, JavaScript</p></td>
<td><p>Weekly</p></td>
<td><p>Deep semantic analysis</p></td>
</tr>
</tbody>
</table>
</section>
<section id="dynamic-application-security-testing-dast">
<h3>Dynamic Application Security Testing (DAST)<a class="headerlink" href="#dynamic-application-security-testing-dast" title="Link to this heading"></a></h3>
<p><strong>Tools Used</strong>:</p>
<table class="docutils align-default" id="id2">
<caption><span class="caption-text">DAST Tools and Coverage</span><a class="headerlink" href="#id2" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 25.0%" />
<col style="width: 25.0%" />
<col style="width: 25.0%" />
<col style="width: 25.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Tool</p></th>
<th class="head"><p>Target</p></th>
<th class="head"><p>Frequency</p></th>
<th class="head"><p>Coverage</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>OWASP ZAP</strong></p></td>
<td><p>Web application</p></td>
<td><p>Weekly</p></td>
<td><p>OWASP Top 10, API security</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Burp Suite</strong></p></td>
<td><p>Web application</p></td>
<td><p>Monthly</p></td>
<td><p>Advanced manual testing</p></td>
</tr>
<tr class="row-even"><td><p><strong>Nessus</strong></p></td>
<td><p>Infrastructure</p></td>
<td><p>Weekly</p></td>
<td><p>Network vulnerabilities</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Trivy</strong></p></td>
<td><p>Containers</p></td>
<td><p>Every build</p></td>
<td><p>Container image vulnerabilities</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="assessment-results-summary">
<h2>Assessment Results Summary<a class="headerlink" href="#assessment-results-summary" title="Link to this heading"></a></h2>
<section id="current-security-posture">
<h3>Current Security Posture<a class="headerlink" href="#current-security-posture" title="Link to this heading"></a></h3>
<p><strong>Overall Security Rating</strong>: <strong>A</strong> (Excellent)</p>
<table class="docutils align-default" id="id3">
<caption><span class="caption-text">Security Metrics</span><a class="headerlink" href="#id3" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 40.0%" />
<col style="width: 30.0%" />
<col style="width: 30.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Security Domain</p></th>
<th class="head"><p>Score</p></th>
<th class="head"><p>Status</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>Application Security</strong></p></td>
<td><p>95/100</p></td>
<td><p>✅ Excellent</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Infrastructure Security</strong></p></td>
<td><p>92/100</p></td>
<td><p>✅ Excellent</p></td>
</tr>
<tr class="row-even"><td><p><strong>Data Protection</strong></p></td>
<td><p>98/100</p></td>
<td><p>✅ Excellent</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Access Control</strong></p></td>
<td><p>94/100</p></td>
<td><p>✅ Excellent</p></td>
</tr>
<tr class="row-even"><td><p><strong>Monitoring &amp; Logging</strong></p></td>
<td><p>90/100</p></td>
<td><p>✅ Excellent</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Compliance</strong></p></td>
<td><p>96/100</p></td>
<td><p>✅ Excellent</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="vulnerability-management">
<h2>Vulnerability Management<a class="headerlink" href="#vulnerability-management" title="Link to this heading"></a></h2>
<section id="vulnerability-classification">
<h3>Vulnerability Classification<a class="headerlink" href="#vulnerability-classification" title="Link to this heading"></a></h3>
<p>We use the Common Vulnerability Scoring System (CVSS) v3.1 for risk assessment:</p>
<table class="docutils align-default" id="id4">
<caption><span class="caption-text">CVSS Risk Levels</span><a class="headerlink" href="#id4" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 20.0%" />
<col style="width: 20.0%" />
<col style="width: 20.0%" />
<col style="width: 40.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Risk Level</p></th>
<th class="head"><p>CVSS Score</p></th>
<th class="head"><p>SLA</p></th>
<th class="head"><p>Response Actions</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>Critical</strong></p></td>
<td><p>9.0 - 10.0</p></td>
<td><p>24 hours</p></td>
<td><p>Immediate patching, emergency response</p></td>
</tr>
<tr class="row-odd"><td><p><strong>High</strong></p></td>
<td><p>7.0 - 8.9</p></td>
<td><p>72 hours</p></td>
<td><p>Priority patching, risk assessment</p></td>
</tr>
<tr class="row-even"><td><p><strong>Medium</strong></p></td>
<td><p>4.0 - 6.9</p></td>
<td><p>30 days</p></td>
<td><p>Scheduled patching, monitoring</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Low</strong></p></td>
<td><p>0.1 - 3.9</p></td>
<td><p>90 days</p></td>
<td><p>Planned remediation, documentation</p></td>
</tr>
</tbody>
</table>
</section>
<section id="current-vulnerability-status">
<h3>Current Vulnerability Status<a class="headerlink" href="#current-vulnerability-status" title="Link to this heading"></a></h3>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>📊 VULNERABILITY DASHBOARD
═══════════════════════════
Critical:    0 issues  ✅
High:        0 issues  ✅
Medium:      0 issues  ✅
Low:        12 issues  ⚠️

📈 TREND: 62.5% improvement
🎯 TARGET: &lt;5 low-severity issues
</pre></div>
</div>
</section>
</section>
<section id="risk-assessment-framework">
<h2>Risk Assessment Framework<a class="headerlink" href="#risk-assessment-framework" title="Link to this heading"></a></h2>
<section id="threat-modeling">
<h3>Threat Modeling<a class="headerlink" href="#threat-modeling" title="Link to this heading"></a></h3>
<p>Our threat modeling process follows the STRIDE methodology:</p>
<p><strong>S</strong> - Spoofing Identity
<strong>T</strong> - Tampering with Data
<strong>R</strong> - Repudiation
<strong>I</strong> - Information Disclosure
<strong>D</strong> - Denial of Service
<strong>E</strong> - Elevation of Privilege</p>
</section>
<section id="risk-calculation">
<h3>Risk Calculation<a class="headerlink" href="#risk-calculation" title="Link to this heading"></a></h3>
<p>Risk is calculated using the formula:</p>
<div class="math notranslate nohighlight">
\[Risk = Threat \times Vulnerability \times Impact\]</div>
<p>Where each factor is scored from 1-5:</p>
<ul class="simple">
<li><p><strong>Threat</strong>: Likelihood of attack</p></li>
<li><p><strong>Vulnerability</strong>: Ease of exploitation</p></li>
<li><p><strong>Impact</strong>: Business consequence</p></li>
</ul>
</section>
</section>
<section id="security-testing-automation">
<h2>Security Testing Automation<a class="headerlink" href="#security-testing-automation" title="Link to this heading"></a></h2>
<section id="ci-cd-security-integration">
<h3>CI/CD Security Integration<a class="headerlink" href="#ci-cd-security-integration" title="Link to this heading"></a></h3>
<p>Our security testing is fully integrated into the development pipeline:</p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="c1"># Security Pipeline Example</span>
<span class="nt">security_scan</span><span class="p">:</span>
<span class="w">  </span><span class="nt">stage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">security</span>
<span class="w">  </span><span class="nt">script</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bandit -r app/ -f json -o bandit-results.json</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">safety check --json --output safety-results.json</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">semgrep --config=auto --json --output=semgrep-results.json</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">trivy image --format json --output trivy-results.json $IMAGE</span>
<span class="w">  </span><span class="nt">artifacts</span><span class="p">:</span>
<span class="w">    </span><span class="nt">reports</span><span class="p">:</span>
<span class="w">      </span><span class="nt">security</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">security-results.json</span>
</pre></div>
</div>
</section>
<section id="security-gates">
<h3>Security Gates<a class="headerlink" href="#security-gates" title="Link to this heading"></a></h3>
<p>Quality gates prevent insecure code from reaching production:</p>
<ul class="simple">
<li><p><strong>Critical vulnerabilities</strong>: Block deployment</p></li>
<li><p><strong>High vulnerabilities</strong>: Require security team approval</p></li>
<li><p><strong>Medium vulnerabilities</strong>: Generate warnings and tracking tickets</p></li>
<li><p><strong>Low vulnerabilities</strong>: Log for future remediation</p></li>
</ul>
</section>
</section>
<section id="compliance-and-audit">
<h2>Compliance and Audit<a class="headerlink" href="#compliance-and-audit" title="Link to this heading"></a></h2>
<section id="regulatory-compliance">
<h3>Regulatory Compliance<a class="headerlink" href="#regulatory-compliance" title="Link to this heading"></a></h3>
<p>The Blast-Radius Security Tool maintains compliance with:</p>
<dl class="simple">
<dt><strong>SOC 2 Type II</strong></dt><dd><ul class="simple">
<li><p>Security controls audit</p></li>
<li><p>Availability monitoring</p></li>
<li><p>Processing integrity validation</p></li>
<li><p>Confidentiality protection</p></li>
</ul>
</dd>
<dt><strong>GDPR</strong></dt><dd><ul class="simple">
<li><p>Data protection by design</p></li>
<li><p>Privacy impact assessments</p></li>
<li><p>Data subject rights implementation</p></li>
<li><p>Breach notification procedures</p></li>
</ul>
</dd>
<dt><strong>ISO 27001</strong></dt><dd><ul class="simple">
<li><p>Information security management</p></li>
<li><p>Risk assessment and treatment</p></li>
<li><p>Security controls implementation</p></li>
<li><p>Continuous improvement</p></li>
</ul>
</dd>
</dl>
</section>
</section>
<section id="continuous-improvement">
<h2>Continuous Improvement<a class="headerlink" href="#continuous-improvement" title="Link to this heading"></a></h2>
<section id="security-metrics-and-kpis">
<h3>Security Metrics and KPIs<a class="headerlink" href="#security-metrics-and-kpis" title="Link to this heading"></a></h3>
<p>We track the following security metrics:</p>
<p><strong>Vulnerability Metrics</strong>:
- Mean time to detection (MTTD)
- Mean time to remediation (MTTR)
- Vulnerability density per KLOC
- False positive rate</p>
<p><strong>Assessment Metrics</strong>:
- Assessment coverage percentage
- Tool effectiveness scores
- Manual testing depth
- Compliance audit results</p>
</section>
<section id="future-enhancements">
<h3>Future Enhancements<a class="headerlink" href="#future-enhancements" title="Link to this heading"></a></h3>
<p>Planned improvements to our security assessment program:</p>
<ul class="simple">
<li><p><strong>AI-powered vulnerability analysis</strong> for better prioritization</p></li>
<li><p><strong>Behavioral security testing</strong> using machine learning</p></li>
<li><p><strong>Continuous compliance monitoring</strong> with real-time dashboards</p></li>
<li><p><strong>Threat intelligence integration</strong> for contextual risk assessment</p></li>
</ul>
</section>
</section>
<section id="conclusion">
<h2>Conclusion<a class="headerlink" href="#conclusion" title="Link to this heading"></a></h2>
<p>The Blast-Radius Security Tool maintains an excellent security posture through comprehensive, continuous security assessment. Our multi-layered approach combining automated tools, manual testing, and expert analysis ensures robust protection against evolving threats.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Security assessments are living documents that evolve with the threat landscape and business requirements. This overview is updated quarterly to reflect current practices and results.</p>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<ul class="simple">
<li><p><a class="reference internal" href="security-review-2025-06-14.html"><span class="doc">Security Review - June 14, 2025</span></a></p></li>
<li><p><a class="reference internal" href="vulnerability-management.html"><span class="doc">Vulnerability Management</span></a></p></li>
<li><p><a class="reference internal" href="../testing/security-automation.html"><span class="doc">Security Testing Automation</span></a></p></li>
<li><p><a class="reference internal" href="../procedures/security-review-process.html"><span class="doc">Security Review Process</span></a></p></li>
</ul>
</div>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="security-review-2025-06-14.html" class="btn btn-neutral float-left" title="Security Review - June 14, 2025" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="vulnerability-management.html" class="btn btn-neutral float-right" title="Vulnerability Management" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, Blast-Radius Security Tool Team.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>