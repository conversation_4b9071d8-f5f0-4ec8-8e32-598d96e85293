<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Production Deployment Guide - Blast-Radius Security Tool &mdash; Blast-Radius Security Tool v1.0.0 - Enterprise Security Platform Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ac1674a3" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ac1674a3" />

  
    <link rel="canonical" href="https://blast-radius.readthedocs.io/deployment/production-deployment-guide.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=528dc5d9"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Blast-Radius Security Tool v1.0.0 - Enterprise Security Platform Documentation"
          href="../_static/opensearch.xml"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Environment Setup Documentation - Blast-Radius Security Tool" href="environment-setup.html" />
    <link rel="prev" title="Security Documentation Summary" href="../security/security-summary.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🎉 Latest Release</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../latest-implementations-summary.html">Latest Implementations Summary - Production Ready Release</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#implementation-achievements">Implementation Achievements</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#infrastructure-as-code-iac-100-complete">Infrastructure as Code (IaC) - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#zero-trust-architecture-100-complete">Zero-Trust Architecture - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#gdpr-compliance-framework-100-complete">GDPR Compliance Framework - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#enhanced-audit-logging-100-complete">Enhanced Audit Logging - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#least-privilege-access-control-100-complete">Least Privilege Access Control - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#production-deployment-infrastructure-100-complete">Production Deployment Infrastructure - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#documentation-operations-100-complete">Documentation &amp; Operations - 100% Complete ✅</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#technical-specifications">Technical Specifications</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#performance-characteristics">Performance Characteristics</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#security-specifications">Security Specifications</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#compliance-coverage">Compliance Coverage</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#deployment-architecture">Deployment Architecture</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#next-steps-and-recommendations">Next Steps and Recommendations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#immediate-actions">Immediate Actions</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#long-term-roadmap">Long-term Roadmap</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🚀 Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#prerequisites">Prerequisites</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#system-requirements">System Requirements</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#software-dependencies">Software Dependencies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#installation-methods">Installation Methods</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#method-1-quick-start-with-docker-recommended">Method 1: Quick Start with Docker (Recommended)</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#method-2-development-installation">Method 2: Development Installation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#method-3-production-installation">Method 3: Production Installation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#configuration">Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#environment-variables">Environment Variables</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#initial-setup">Initial Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#creating-admin-user">Creating Admin User</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#configuring-integrations">Configuring Integrations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#verification">Verification</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#health-checks">Health Checks</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#performance-testing">Performance Testing</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#core-configuration">Core Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#application-settings">Application Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#database-configuration">Database Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#security-configuration">Security Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#authentication-settings">Authentication Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#encryption-settings">Encryption Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#access-control-settings">Access Control Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#integration-configuration">Integration Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#cloud-provider-settings">Cloud Provider Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#servicenow-integration">ServiceNow Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#threat-intelligence-settings">Threat Intelligence Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#monitoring-and-logging">Monitoring and Logging</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#logging-configuration">Logging Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#monitoring-settings">Monitoring Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#environment-specific-configuration">Environment-Specific Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#development-environment">Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#staging-environment">Staging Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#configuration-validation">Configuration Validation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#security-best-practices">Security Best Practices</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#performance-best-practices">Performance Best Practices</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#troubleshooting">Troubleshooting</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../quick-start-guide.html">Quick Start Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#quick-installation">Quick Installation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-1-get-the-code">Step 1: Get the Code</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-2-start-the-platform">Step 2: Start the Platform</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-3-access-the-platform">Step 3: Access the Platform</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#first-login-and-setup">First Login and Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#initial-login">Initial Login</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#password-change">Password Change</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#dashboard-overview">Dashboard Overview</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#basic-configuration">Basic Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#user-profile-setup">User Profile Setup</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#multi-factor-authentication-recommended">Multi-Factor Authentication (Recommended)</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#your-first-attack-path-analysis">Your First Attack Path Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-1-add-sample-assets">Step 1: Add Sample Assets</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-2-run-attack-path-analysis">Step 2: Run Attack Path Analysis</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-3-view-results">Step 3: View Results</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#understanding-the-visualization">Understanding the Visualization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#essential-features-tour">Essential Features Tour</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#real-time-monitoring">Real-Time Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#asset-management">Asset Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#user-and-role-management">User and Role Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#threat-intelligence">Threat Intelligence</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#integration-setup">Integration Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#cloud-provider-integration">Cloud Provider Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#servicenow-integration">ServiceNow Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#next-steps">Next Steps</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#immediate-actions">Immediate Actions</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#learning-resources">Learning Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#advanced-configuration">Advanced Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#common-quick-start-issues">Common Quick Start Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#platform-won-t-start">Platform Won’t Start</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#can-t-access-web-interface">Can’t Access Web Interface</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#login-issues">Login Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#performance-issues">Performance Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#getting-help">Getting Help</a></li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#congratulations">Congratulations!</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📊 Documentation Excellence</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../documentation-overview.html">Documentation Overview and Achievements</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#documentation-excellence-achievements">🏆 Documentation Excellence Achievements</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#comprehensive-user-guide-ecosystem">📚 Comprehensive User Guide Ecosystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#key-documentation-features">🎯 Key Documentation Features</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#measurable-impact-and-value">📈 Measurable Impact and Value</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#industry-leadership-and-recognition">🌟 Industry Leadership and Recognition</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#future-enhancements-and-roadmap">🚀 Future Enhancements and Roadmap</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📚 Enterprise User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guides/index.html">User Guides</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#role-based-guides">Role-Based Guides</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#soc-operators">SOC Operators</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/soc-operators.html">SOC Operators Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#role-definition-and-responsibilities">Role Definition and Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#core-platform-capabilities-for-soc-operations">Core Platform Capabilities for SOC Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#getting-started-soc-operator-onboarding">Getting Started: SOC Operator Onboarding</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#advanced-real-time-monitoring-and-threat-detection">Advanced Real-time Monitoring and Threat Detection</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#comprehensive-incident-response-framework">Comprehensive Incident Response Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#advanced-reporting-and-documentation-framework">Advanced Reporting and Documentation Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#soc-operations-best-practices-and-excellence-framework">SOC Operations Best Practices and Excellence Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#comprehensive-scenario-based-training-and-response-procedures">Comprehensive Scenario-Based Training and Response Procedures</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#comprehensive-support-resources-and-professional-development">Comprehensive Support Resources and Professional Development</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#conclusion-excellence-in-soc-operations">Conclusion: Excellence in SOC Operations</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#security-architects">Security Architects</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/security-architects.html">Security Architects Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#executive-summary-for-security-architects">Executive Summary for Security Architects</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-platform-capabilities-for-security-architecture">Advanced Platform Capabilities for Security Architecture</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#enterprise-security-architecture-methodology">Enterprise Security Architecture Methodology</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-risk-assessment-and-quantitative-analysis-framework">Advanced Risk Assessment and Quantitative Analysis Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-security-architecture-design-and-implementation">Advanced Security Architecture Design and Implementation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#comprehensive-compliance-and-governance-framework">Comprehensive Compliance and Governance Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-analytics-and-executive-communication-framework">Advanced Analytics and Executive Communication Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#enterprise-security-architecture-best-practices-and-excellence">Enterprise Security Architecture Best Practices and Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#professional-development-and-support-resources">Professional Development and Support Resources</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#conclusion-excellence-in-security-architecture-leadership">Conclusion: Excellence in Security Architecture Leadership</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#red-team-members">Red Team Members</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/red-team-members.html">Red Team Members Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#executive-summary-for-red-team-operations">Executive Summary for Red Team Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#role-definition-and-offensive-security-responsibilities">Role Definition and Offensive Security Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-red-team-platform-capabilities">Advanced Red Team Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#comprehensive-red-team-methodology-and-campaign-framework">Comprehensive Red Team Methodology and Campaign Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-attack-path-discovery-and-exploitation-framework">Advanced Attack Path Discovery and Exploitation Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-attack-simulation-and-specialized-testing-scenarios">Advanced Attack Simulation and Specialized Testing Scenarios</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-reporting-and-performance-metrics-framework">Advanced Reporting and Performance Metrics Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#red-team-best-practices-and-operational-excellence">Red Team Best Practices and Operational Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-troubleshooting-and-technical-support">Advanced Troubleshooting and Technical Support</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#conclusion-excellence-in-red-team-operations">Conclusion: Excellence in Red Team Operations</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#purple-team-members">Purple Team Members</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/purple-team-members.html">Purple Team Members Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#executive-summary-for-purple-team-excellence">Executive Summary for Purple Team Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#advanced-purple-team-platform-capabilities">Advanced Purple Team Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#comprehensive-purple-team-methodology-and-framework">Comprehensive Purple Team Methodology and Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#advanced-detection-engineering-and-security-control-optimization">Advanced Detection Engineering and Security Control Optimization</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#comprehensive-purple-team-scenarios-and-implementation-framework">Comprehensive Purple Team Scenarios and Implementation Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#advanced-troubleshooting-and-technical-support-framework">Advanced Troubleshooting and Technical Support Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#conclusion-excellence-in-purple-team-operations-and-collaborative-security">Conclusion: Excellence in Purple Team Operations and Collaborative Security</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#compliance-officers">Compliance Officers</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/compliance-officers.html">Compliance Officers Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#executive-summary-for-compliance-excellence">Executive Summary for Compliance Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-compliance-platform-capabilities">Advanced Compliance Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-regulatory-framework-management">Advanced Regulatory Framework Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#automated-compliance-monitoring-and-management">Automated Compliance Monitoring and Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#comprehensive-audit-management-and-preparation">Comprehensive Audit Management and Preparation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-risk-assessment-and-management-framework">Advanced Risk Assessment and Management Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#cross-functional-collaboration-and-integration">Cross-Functional Collaboration and Integration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#professional-development-and-excellence-framework">Professional Development and Excellence Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-support-and-troubleshooting-framework">Advanced Support and Troubleshooting Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#conclusion-excellence-in-compliance-and-governance">Conclusion: Excellence in Compliance and Governance</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#executive-leadership">Executive Leadership</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/executive-leadership.html">Executive Leadership Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#executive-summary-for-strategic-security-leadership">Executive Summary for Strategic Security Leadership</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#advanced-executive-security-platform-capabilities">Advanced Executive Security Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#strategic-security-risk-management-framework">Strategic Security Risk Management Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#business-aligned-security-performance-management">Business-Aligned Security Performance Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#strategic-security-investment-and-resource-management">Strategic Security Investment and Resource Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#crisis-management-and-business-continuity">Crisis Management and Business Continuity</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#regulatory-compliance-and-governance-excellence">Regulatory Compliance and Governance Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#strategic-security-communication-and-stakeholder-management">Strategic Security Communication and Stakeholder Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#digital-transformation-and-innovation-enablement">Digital Transformation and Innovation Enablement</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#professional-development-and-executive-excellence">Professional Development and Executive Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#advanced-support-and-advisory-services">Advanced Support and Advisory Services</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#conclusion-excellence-in-executive-security-leadership">Conclusion: Excellence in Executive Security Leadership</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#administrators">Administrators</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/administrators.html">Administrators Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#dashboard-overview">Dashboard Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#getting-started">Getting Started</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#user-and-access-management">User and Access Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#system-configuration-and-management">System Configuration and Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#monitoring-and-maintenance">Monitoring and Maintenance</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#security-and-compliance">Security and Compliance</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#platform-updates-and-maintenance">Platform Updates and Maintenance</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#troubleshooting-and-support">Troubleshooting and Support</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#best-practices-for-platform-administration">Best Practices for Platform Administration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#feature-specific-guides">Feature-Specific Guides</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#attack-path-analysis">Attack Path Analysis</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/attack-path-analysis.html">Attack Path Analysis User Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#getting-started">Getting Started</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#core-concepts">Core Concepts</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#using-the-web-interface">Using the Web Interface</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#using-the-api">Using the API</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#use-cases-and-workflows">Use Cases and Workflows</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#best-practices">Best Practices</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#threat-modeling">Threat Modeling</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#asset-discovery-and-management">Asset Discovery and Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#threat-intelligence-integration">Threat Intelligence Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#common-workflows">Common Workflows</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#security-assessment-workflow">Security Assessment Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#incident-response-workflow">Incident Response Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#red-team-exercise-workflow">Red Team Exercise Workflow</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#general-guidelines">General Guidelines</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#role-specific-tips">Role-Specific Tips</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#getting-help">Getting Help</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#support-resources">Support Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#training-and-certification">Training and Certification</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#feedback-and-contributions">Feedback and Contributions</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#base-url">Base URL</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#api-endpoints">API Endpoints</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#authentication-user-management">Authentication &amp; User Management</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/authentication.html">Authentication API</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#authentication-flow">Authentication Flow</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#security-and-authorization">Security and Authorization</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#integration-examples">Integration Examples</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#best-practices">Best Practices</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#asset-management">Asset Management</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/asset-management.html">Asset Management API</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-crud-operations">Asset CRUD Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-discovery">Asset Discovery</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#bulk-operations">Bulk Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-relationships">Asset Relationships</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-tags-and-metadata">Asset Tags and Metadata</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#error-handling">Error Handling</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#sdk-examples">SDK Examples</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#attack-path-analysis">Attack Path Analysis</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/attack-path-analysis.html">Attack Path Analysis API</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#endpoints">Endpoints</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#error-responses">Error Responses</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#performance-considerations">Performance Considerations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#best-practices">Best Practices</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#examples">Examples</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#threat-modeling-risk-assessment">Threat Modeling &amp; Risk Assessment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/threat-modeling.html">Threat Modeling API Reference</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#threat-actor-simulation">Threat Actor Simulation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#risk-assessment">Risk Assessment</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#compliance-impact-analysis">Compliance Impact Analysis</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#mitigation-strategies">Mitigation Strategies</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#continuous-monitoring">Continuous Monitoring</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#error-handling">Error Handling</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#rate-limiting">Rate Limiting</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/mitre-attack-integration.html">MITRE ATT&amp;CK Integration API Reference</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#att-ck-data-management">ATT&amp;CK Data Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#technique-correlation">Technique Correlation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#threat-actor-attribution">Threat Actor Attribution</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#attack-pattern-analysis">Attack Pattern Analysis</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#att-ck-navigator-integration">ATT&amp;CK Navigator Integration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#threat-intelligence-enrichment">Threat Intelligence Enrichment</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#error-handling">Error Handling</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#rate-limiting">Rate Limiting</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#threat-intelligence">Threat Intelligence</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#monitoring-dashboards">Monitoring &amp; Dashboards</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#integrations">Integrations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#common-response-formats">Common Response Formats</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#success-response">Success Response</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#error-response">Error Response</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#http-status-codes">HTTP Status Codes</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#pagination">Pagination</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#filtering-and-sorting">Filtering and Sorting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#field-selection">Field Selection</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#webhooks">Webhooks</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#sdk-and-libraries">SDK and Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#testing-and-development">Testing and Development</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#support-and-resources">Support and Resources</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#examples">Examples</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Development</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#getting-started">Getting Started</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#ways-to-contribute">Ways to Contribute</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#before-you-start">Before You Start</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#contribution-workflow">Contribution Workflow</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-1-find-or-create-an-issue">Step 1: Find or Create an Issue</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-2-fork-and-clone">Step 2: Fork and Clone</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-3-create-a-branch">Step 3: Create a Branch</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-4-make-changes">Step 4: Make Changes</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-5-test-your-changes">Step 5: Test Your Changes</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-6-submit-pull-request">Step 6: Submit Pull Request</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#code-standards">Code Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#python-code-style">Python Code Style</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#javascript-typescript-style">JavaScript/TypeScript Style</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#testing-guidelines">Testing Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#test-structure">Test Structure</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#documentation-standards">Documentation Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#documentation-types">Documentation Types</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#writing-guidelines">Writing Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#review-process">Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#code-review-checklist">Code Review Checklist</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#review-guidelines">Review Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#community-guidelines">Community Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#code-of-conduct">Code of Conduct</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#communication">Communication</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#recognition">Recognition</a></li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#getting-help">Getting Help</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Use Cases</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../use-cases/asset-discovery.html">Asset Discovery Use Cases</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#discovery-methods">Discovery Methods</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#cloud-provider-discovery">Cloud Provider Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#network-based-discovery">Network-Based Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#api-discovery">API Discovery</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#use-case-scenarios">Use Case Scenarios</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#scenario-1-multi-cloud-asset-inventory">Scenario 1: Multi-Cloud Asset Inventory</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#scenario-2-continuous-asset-monitoring">Scenario 2: Continuous Asset Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#scenario-3-compliance-asset-reporting">Scenario 3: Compliance Asset Reporting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#advanced-discovery-features">Advanced Discovery Features</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#agent-based-discovery">Agent-Based Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#container-discovery">Container Discovery</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#discovery-automation">Discovery Automation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#scheduled-discovery">Scheduled Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#integration-with-external-tools">Integration with External Tools</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#discovery-strategy">Discovery Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#performance-optimization">Performance Optimization</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#security-considerations">Security Considerations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#common-issues">Common Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../use-cases/attack-path-analysis.html">Attack Path Analysis Use Cases</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#key-capabilities">Key Capabilities</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#analysis-types">Analysis Types</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#use-case-scenarios">Use Case Scenarios</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-1-red-team-attack-simulation">Scenario 1: Red Team Attack Simulation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-2-purple-team-validation-exercise">Scenario 2: Purple Team Validation Exercise</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-3-compliance-risk-assessment">Scenario 3: Compliance Risk Assessment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-4-incident-response-planning">Scenario 4: Incident Response Planning</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-5-zero-trust-architecture-planning">Scenario 5: Zero Trust Architecture Planning</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#advanced-analysis-features">Advanced Analysis Features</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#risk-scoring-and-prioritization">Risk Scoring and Prioritization</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#integration-and-automation">Integration and Automation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#siem-integration">SIEM Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#soar-integration">SOAR Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#analysis-guidelines">Analysis Guidelines</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Architecture</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../architecture/zero-trust-architecture.html">Zero-Trust Architecture Implementation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#identity-verification-service">Identity Verification Service</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#service-account-management">Service Account Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#tls-1-3-enforcement">TLS 1.3+ Enforcement</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#real-time-security-event-correlation">Real-Time Security Event Correlation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#network-security">Network Security</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#kubernetes-network-policies">Kubernetes Network Policies</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#rbac-implementation">RBAC Implementation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#data-protection">Data Protection</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#encryption-implementation">Encryption Implementation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#data-classification">Data Classification</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#monitoring-and-alerting">Monitoring and Alerting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#security-monitoring">Security Monitoring</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#implementation-guidelines">Implementation Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#deployment-checklist">Deployment Checklist</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#security-validation">Security Validation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#security-principles">Security Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#implementation-tips">Implementation Tips</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="production-architecture.html">Production Architecture Overview</a><ul>
<li class="toctree-l2"><a class="reference internal" href="production-architecture.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="production-architecture.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="production-architecture.html#kubernetes-infrastructure">Kubernetes Infrastructure</a></li>
<li class="toctree-l3"><a class="reference internal" href="production-architecture.html#application-architecture">Application Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="production-architecture.html#data-architecture">Data Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="production-architecture.html#network-architecture">Network Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="production-architecture.html#security-architecture">Security Architecture</a><ul>
<li class="toctree-l3"><a class="reference internal" href="production-architecture.html#zero-trust-security-implementation">Zero-Trust Security Implementation</a></li>
<li class="toctree-l3"><a class="reference internal" href="production-architecture.html#monitoring-architecture">Monitoring Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="production-architecture.html#deployment-strategy">Deployment Strategy</a><ul>
<li class="toctree-l3"><a class="reference internal" href="production-architecture.html#blue-green-deployment">Blue-Green Deployment</a></li>
<li class="toctree-l3"><a class="reference internal" href="production-architecture.html#infrastructure-as-code">Infrastructure as Code</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="production-architecture.html#performance-specifications">Performance Specifications</a><ul>
<li class="toctree-l3"><a class="reference internal" href="production-architecture.html#scalability-metrics">Scalability Metrics</a></li>
<li class="toctree-l3"><a class="reference internal" href="production-architecture.html#resource-requirements">Resource Requirements</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="production-architecture.html#disaster-recovery">Disaster Recovery</a><ul>
<li class="toctree-l3"><a class="reference internal" href="production-architecture.html#backup-and-recovery-strategy">Backup and Recovery Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="production-architecture.html#high-availability-design">High Availability Design</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="production-architecture.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="production-architecture.html#operational-excellence">Operational Excellence</a></li>
<li class="toctree-l3"><a class="reference internal" href="production-architecture.html#security-best-practices">Security Best Practices</a></li>
<li class="toctree-l3"><a class="reference internal" href="production-architecture.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="production-architecture.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Security &amp; Compliance</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../security/enhanced-audit-logging.html">Enhanced Audit Logging System</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-event-structure">Audit Event Structure</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#tamper-proof-audit-chain">Tamper-Proof Audit Chain</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#event-enrichment-pipeline">Event Enrichment Pipeline</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#real-time-monitoring">Real-Time Monitoring</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#compliance-integration">Compliance Integration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#framework-mapping">Framework Mapping</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#compliance-reporting">Compliance Reporting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-service-architecture">Audit Service Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#database-schema">Database Schema</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#configuration">Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#monitoring-and-alerting">Monitoring and Alerting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-monitoring-dashboard">Audit Monitoring Dashboard</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#alert-configuration">Alert Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-strategy">Audit Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#operational-guidelines">Operational Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#performance-tuning">Performance Tuning</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../security/incident-response-procedures.html">Incident Response Procedures - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#incident-classification">Incident Classification</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#severity-levels">Severity Levels</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#critical-p0">Critical (P0)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#high-p1">High (P1)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#medium-p2">Medium (P2)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#low-p3">Low (P3)</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#incident-response-team">Incident Response Team</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#roles-and-responsibilities">Roles and Responsibilities</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#incident-commander-ic">Incident Commander (IC)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#technical-lead">Technical Lead</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#security-lead">Security Lead</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#communications-lead">Communications Lead</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#incident-response-process">Incident Response Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-1-detection-and-initial-response-0-15-minutes">Phase 1: Detection and Initial Response (0-15 minutes)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#incident-detection">1.1 Incident Detection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#initial-assessment">1.2 Initial Assessment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#team-notification">1.3 Team Notification</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-2-investigation-and-containment-15-minutes-2-hours">Phase 2: Investigation and Containment (15 minutes - 2 hours)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#incident-war-room-setup">2.1 Incident War Room Setup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#technical-investigation">2.2 Technical Investigation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#security-assessment-if-applicable">2.3 Security Assessment (if applicable)</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-3-resolution-and-recovery-2-8-hours">Phase 3: Resolution and Recovery (2-8 hours)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#fix-implementation">3.1 Fix Implementation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#system-recovery">3.2 System Recovery</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-4-communication-and-documentation-ongoing">Phase 4: Communication and Documentation (Ongoing)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#status-updates">4.1 Status Updates</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#resolution-communication">4.2 Resolution Communication</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-5-post-incident-review-24-72-hours-after-resolution">Phase 5: Post-Incident Review (24-72 hours after resolution)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#post-mortem-meeting">5.1 Post-Mortem Meeting</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#documentation">5.2 Documentation</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#security-specific-procedures">Security-Specific Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#data-breach-response">Data Breach Response</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#immediate-actions-0-1-hour">Immediate Actions (0-1 hour)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#notification-requirements">Notification Requirements</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#vulnerability-response">Vulnerability Response</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#critical-vulnerabilities-cvss-9-0">Critical Vulnerabilities (CVSS 9.0+)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#high-vulnerabilities-cvss-7-0-8-9">High Vulnerabilities (CVSS 7.0-8.9)</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#emergency-contacts">Emergency Contacts</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#internal-team">Internal Team</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#external-contacts">External Contacts</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#tools-and-resources">Tools and Resources</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#monitoring-and-alerting">Monitoring and Alerting</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#communication">Communication</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#id1">Documentation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#training-and-drills">Training and Drills</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#regular-training">Regular Training</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#documentation-updates">Documentation Updates</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../security/security-review-processes.html">Security Review Processes - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#security-review-framework">Security Review Framework</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-types">Review Types</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#code-security-review">1. Code Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#infrastructure-security-review">2. Infrastructure Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#architecture-security-review">3. Architecture Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#compliance-review">4. Compliance Review</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#code-security-review-process">Code Security Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#pre-review-checklist">Pre-Review Checklist</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#developer-responsibilities">Developer Responsibilities</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-criteria">Review Criteria</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#input-validation-and-sanitization">1. Input Validation and Sanitization</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#authentication-and-authorization">2. Authentication and Authorization</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#data-protection">3. Data Protection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#error-handling-and-logging">4. Error Handling and Logging</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#cryptography-and-secrets">5. Cryptography and Secrets</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-process-workflow">Review Process Workflow</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#step-1-automated-security-checks">Step 1: Automated Security Checks</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#step-2-manual-security-review">Step 2: Manual Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#step-3-security-testing">Step 3: Security Testing</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#id1">Infrastructure Security Review</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#infrastructure-as-code-iac-review">Infrastructure as Code (IaC) Review</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#terraform-security-checklist">Terraform Security Checklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#example-review-process">Example Review Process</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#kubernetes-security-review">Kubernetes Security Review</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#manifest-security-checklist">Manifest Security Checklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#example-security-policies">Example Security Policies</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#id2">Architecture Security Review</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#design-review-process">Design Review Process</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#threat-modeling">1. Threat Modeling</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#security-architecture-patterns">2. Security Architecture Patterns</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#integration-security">3. Integration Security</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-documentation-template">Review Documentation Template</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#compliance-review-process">Compliance Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#regulatory-requirements">Regulatory Requirements</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#gdpr-compliance-checklist">GDPR Compliance Checklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#soc-2-compliance-checklist">SOC 2 Compliance Checklist</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#compliance-review-workflow">Compliance Review Workflow</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#change-impact-assessment">1. Change Impact Assessment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#documentation-requirements">2. Documentation Requirements</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#security-review-tools-and-automation">Security Review Tools and Automation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#automated-security-scanning">Automated Security Scanning</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#security-metrics-and-kpis">Security Metrics and KPIs</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#training-and-awareness">Training and Awareness</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#security-review-training">Security Review Training</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#documentation-and-resources">Documentation and Resources</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html">GDPR Compliance Framework</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-subject-rights-management">Data Subject Rights Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#consent-management-system">Consent Management System</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-processing-records">Data Processing Records</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-breach-management">Data Breach Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#privacy-impact-assessment">Privacy Impact Assessment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#gdpr-service-architecture">GDPR Service Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-processing-workflows">Data Processing Workflows</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#compliance-monitoring">Compliance Monitoring</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#automated-compliance-checks">Automated Compliance Checks</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#compliance-reporting">Compliance Reporting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#privacy-by-design-implementation">Privacy by Design Implementation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#operational-guidelines">Operational Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#technical-implementation">Technical Implementation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#database-schema">Database Schema</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#configuration-example">Configuration Example</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../access-control/least-privilege-framework.html">Least Privilege Access Control Framework</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-request-management">Access Request Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#just-in-time-access-provisioning">Just-in-Time Access Provisioning</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#privilege-escalation-management">Privilege Escalation Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-review-and-certification">Access Review and Certification</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-control-architecture">Access Control Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#role-based-access-templates">Role-Based Access Templates</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#risk-assessment-engine">Risk Assessment Engine</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#monitoring-and-analytics">Monitoring and Analytics</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-usage-monitoring">Access Usage Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-analytics-dashboard">Access Analytics Dashboard</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#configuration-management">Configuration Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#policy-configuration">Policy Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#role-template-configuration">Role Template Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-management-principles">Access Management Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#operational-guidelines">Operational Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../security/index.html">Security Documentation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/security-summary.html">Security Documentation Summary</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#executive-overview">Executive Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#security-documentation-structure">Security Documentation Structure</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#recent-security-achievements">Recent Security Achievements</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#june-2025-security-review-results">June 2025 Security Review Results</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#key-security-fixes-implemented">Key Security Fixes Implemented</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#security-architecture-excellence">Security Architecture Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#multi-layered-security-approach">Multi-Layered Security Approach</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#security-testing-excellence">Security Testing Excellence</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#compliance-and-regulatory-excellence">Compliance and Regulatory Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#regulatory-compliance-status">Regulatory Compliance Status</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#industry-standards-compliance">Industry Standards Compliance</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#security-operations-excellence">Security Operations Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#vulnerability-management-program">Vulnerability Management Program</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#security-monitoring-and-incident-response">Security Monitoring and Incident Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#risk-management-excellence">Risk Management Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#enterprise-risk-assessment">Enterprise Risk Assessment</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#business-impact-and-value">Business Impact and Value</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#security-investment-roi">Security Investment ROI</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#future-security-roadmap">Future Security Roadmap</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#short-term-objectives-q3-q4-2025">Short-Term Objectives (Q3-Q4 2025)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#long-term-strategic-goals-2026">Long-Term Strategic Goals (2026)</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#overview">Overview</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/index.html#security-principles">Security Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/index.html#security-framework">Security Framework</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/index.html#security-architecture">Security Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#security-documentation-sections">Security Documentation Sections</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#security-certifications-and-compliance">Security Certifications and Compliance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#security-contact-information">Security Contact Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#quick-security-reference">Quick Security Reference</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Deployment &amp; Operations</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">Production Deployment Guide - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#prerequisites">Prerequisites</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#infrastructure-requirements">Infrastructure Requirements</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#minimum-system-requirements">Minimum System Requirements</a></li>
<li class="toctree-l4"><a class="reference internal" href="#network-requirements">Network Requirements</a></li>
<li class="toctree-l4"><a class="reference internal" href="#external-dependencies">External Dependencies</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#required-tools-and-access">Required Tools and Access</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#development-tools">Development Tools</a></li>
<li class="toctree-l4"><a class="reference internal" href="#aws-access-requirements">AWS Access Requirements</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#infrastructure-deployment">Infrastructure Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#step-1-environment-configuration">Step 1: Environment Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#configure-aws-cli">1.1 Configure AWS CLI</a></li>
<li class="toctree-l4"><a class="reference internal" href="#set-environment-variables">1.2 Set Environment Variables</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#step-2-infrastructure-provisioning">Step 2: Infrastructure Provisioning</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#deploy-core-infrastructure">2.1 Deploy Core Infrastructure</a></li>
<li class="toctree-l4"><a class="reference internal" href="#configure-kubectl-access">2.2 Configure kubectl Access</a></li>
<li class="toctree-l4"><a class="reference internal" href="#install-cluster-add-ons">2.3 Install Cluster Add-ons</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#step-3-database-setup">Step 3: Database Setup</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#database-initialization">3.1 Database Initialization</a></li>
<li class="toctree-l4"><a class="reference internal" href="#database-migration">3.2 Database Migration</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#application-deployment">Application Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#step-4-container-image-preparation">Step 4: Container Image Preparation</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#build-and-push-images">4.1 Build and Push Images</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#step-5-kubernetes-deployment">Step 5: Kubernetes Deployment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#create-namespace-and-secrets">5.1 Create Namespace and Secrets</a></li>
<li class="toctree-l4"><a class="reference internal" href="#deploy-application-components">5.2 Deploy Application Components</a></li>
<li class="toctree-l4"><a class="reference internal" href="#configure-ingress-and-load-balancer">5.3 Configure Ingress and Load Balancer</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#step-6-ssl-tls-configuration">Step 6: SSL/TLS Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#request-ssl-certificate">6.1 Request SSL Certificate</a></li>
<li class="toctree-l4"><a class="reference internal" href="#configure-https-redirect">6.2 Configure HTTPS Redirect</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#post-deployment-configuration">Post-Deployment Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#step-7-application-configuration">Step 7: Application Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#initialize-application-data">7.1 Initialize Application Data</a></li>
<li class="toctree-l4"><a class="reference internal" href="#configure-monitoring-and-alerting">7.2 Configure Monitoring and Alerting</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#step-8-security-hardening">Step 8: Security Hardening</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#network-policies">8.1 Network Policies</a></li>
<li class="toctree-l4"><a class="reference internal" href="#pod-security-policies">8.2 Pod Security Policies</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#verification-and-testing">Verification and Testing</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#step-9-deployment-verification">Step 9: Deployment Verification</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#health-checks">9.1 Health Checks</a></li>
<li class="toctree-l4"><a class="reference internal" href="#functional-testing">9.2 Functional Testing</a></li>
<li class="toctree-l4"><a class="reference internal" href="#security-testing">9.3 Security Testing</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#backup-and-recovery-setup">Backup and Recovery Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#step-10-backup-configuration">Step 10: Backup Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#database-backup">10.1 Database Backup</a></li>
<li class="toctree-l4"><a class="reference internal" href="#application-data-backup">10.2 Application Data Backup</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#monitoring-and-maintenance">Monitoring and Maintenance</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#step-11-ongoing-monitoring">Step 11: Ongoing Monitoring</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#set-up-alerts">11.1 Set Up Alerts</a></li>
<li class="toctree-l4"><a class="reference internal" href="#performance-monitoring">11.2 Performance Monitoring</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#troubleshooting-common-issues">Troubleshooting Common Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#database-connection-issues">Database Connection Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="#pod-startup-issues">Pod Startup Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="#load-balancer-issues">Load Balancer Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#rollback-procedures">Rollback Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#emergency-rollback">Emergency Rollback</a></li>
<li class="toctree-l3"><a class="reference internal" href="#database-rollback">Database Rollback</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="environment-setup.html">Environment Setup Documentation - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="environment-setup.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="environment-setup.html#environment-types">Environment Types</a><ul>
<li class="toctree-l3"><a class="reference internal" href="environment-setup.html#development-environment">Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="environment-setup.html#staging-environment">Staging Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="environment-setup.html#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="environment-setup.html#local-development-setup">Local Development Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="environment-setup.html#prerequisites">Prerequisites</a><ul>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#system-requirements">System Requirements</a></li>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#required-software">Required Software</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="environment-setup.html#development-environment-setup">Development Environment Setup</a><ul>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#clone-repository">1. Clone Repository</a></li>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#environment-configuration">2. Environment Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#backend-setup">3. Backend Setup</a></li>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#frontend-setup">4. Frontend Setup</a></li>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#start-development-services">5. Start Development Services</a></li>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#verify-development-setup">6. Verify Development Setup</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="environment-setup.html#staging-environment-setup">Staging Environment Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="environment-setup.html#infrastructure-provisioning">Infrastructure Provisioning</a><ul>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#aws-configuration">1. AWS Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#terraform-deployment">2. Terraform Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#kubernetes-configuration">3. Kubernetes Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="environment-setup.html#application-deployment">Application Deployment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#build-and-push-images">1. Build and Push Images</a></li>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#deploy-to-staging">2. Deploy to Staging</a></li>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#configure-staging-data">3. Configure Staging Data</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="environment-setup.html#production-environment-setup">Production Environment Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="environment-setup.html#pre-production-checklist">Pre-Production Checklist</a><ul>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#security-review">Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#performance-review">Performance Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#operational-readiness">Operational Readiness</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="environment-setup.html#production-deployment-process">Production Deployment Process</a><ul>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#final-infrastructure-review">1. Final Infrastructure Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#production-image-preparation">2. Production Image Preparation</a></li>
<li class="toctree-l4"><a class="reference internal" href="environment-setup.html#blue-green-deployment">3. Blue-Green Deployment</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="environment-setup.html#environment-specific-configurations">Environment-Specific Configurations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="environment-setup.html#development-configuration">Development Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="environment-setup.html#staging-configuration">Staging Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="environment-setup.html#production-configuration">Production Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="environment-setup.html#ci-cd-pipeline-configuration">CI/CD Pipeline Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="environment-setup.html#github-actions-workflow">GitHub Actions Workflow</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="environment-setup.html#environment-management-scripts">Environment Management Scripts</a><ul>
<li class="toctree-l3"><a class="reference internal" href="environment-setup.html#deployment-script">Deployment Script</a></li>
<li class="toctree-l3"><a class="reference internal" href="environment-setup.html#environment-cleanup-script">Environment Cleanup Script</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="troubleshooting-guide.html">Troubleshooting Guide - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="troubleshooting-guide.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="troubleshooting-guide.html#general-troubleshooting-approach">General Troubleshooting Approach</a><ul>
<li class="toctree-l3"><a class="reference internal" href="troubleshooting-guide.html#information-gathering">1. Information Gathering</a></li>
<li class="toctree-l3"><a class="reference internal" href="troubleshooting-guide.html#log-analysis">2. Log Analysis</a></li>
<li class="toctree-l3"><a class="reference internal" href="troubleshooting-guide.html#network-connectivity">3. Network Connectivity</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="troubleshooting-guide.html#common-issues-and-solutions">Common Issues and Solutions</a><ul>
<li class="toctree-l3"><a class="reference internal" href="troubleshooting-guide.html#application-startup-issues">Application Startup Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="troubleshooting-guide.html#issue-pod-stuck-in-pending-state">Issue: Pod Stuck in Pending State</a></li>
<li class="toctree-l4"><a class="reference internal" href="troubleshooting-guide.html#issue-pod-stuck-in-crashloopbackoff">Issue: Pod Stuck in CrashLoopBackOff</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="troubleshooting-guide.html#database-issues">Database Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="troubleshooting-guide.html#issue-database-connection-timeout">Issue: Database Connection Timeout</a></li>
<li class="toctree-l4"><a class="reference internal" href="troubleshooting-guide.html#issue-database-migration-failures">Issue: Database Migration Failures</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="troubleshooting-guide.html#network-and-connectivity-issues">Network and Connectivity Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="troubleshooting-guide.html#issue-service-not-accessible">Issue: Service Not Accessible</a></li>
<li class="toctree-l4"><a class="reference internal" href="troubleshooting-guide.html#issue-ssl-tls-certificate-problems">Issue: SSL/TLS Certificate Problems</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="troubleshooting-guide.html#performance-issues">Performance Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="troubleshooting-guide.html#issue-high-response-times">Issue: High Response Times</a></li>
<li class="toctree-l4"><a class="reference internal" href="troubleshooting-guide.html#issue-memory-leaks">Issue: Memory Leaks</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="troubleshooting-guide.html#storage-issues">Storage Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="troubleshooting-guide.html#issue-persistent-volume-problems">Issue: Persistent Volume Problems</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="troubleshooting-guide.html#monitoring-and-alerting-issues">Monitoring and Alerting Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="troubleshooting-guide.html#issue-missing-metrics">Issue: Missing Metrics</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="troubleshooting-guide.html#emergency-procedures">Emergency Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="troubleshooting-guide.html#complete-system-recovery">Complete System Recovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="troubleshooting-guide.html#rollback-procedure">Rollback Procedure</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="troubleshooting-guide.html#getting-help">Getting Help</a><ul>
<li class="toctree-l3"><a class="reference internal" href="troubleshooting-guide.html#internal-resources">Internal Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="troubleshooting-guide.html#external-support">External Support</a></li>
<li class="toctree-l3"><a class="reference internal" href="troubleshooting-guide.html#escalation-procedures">Escalation Procedures</a></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content style-external-links">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Production Deployment Guide - Blast-Radius Security Tool</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/deployment/production-deployment-guide.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="production-deployment-guide-blast-radius-security-tool">
<h1>Production Deployment Guide - Blast-Radius Security Tool<a class="headerlink" href="#production-deployment-guide-blast-radius-security-tool" title="Link to this heading"></a></h1>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>This guide provides comprehensive instructions for deploying the Blast-Radius Security Tool in a production environment, including infrastructure setup, application deployment, and post-deployment verification.</p>
</section>
<section id="prerequisites">
<h2>Prerequisites<a class="headerlink" href="#prerequisites" title="Link to this heading"></a></h2>
<section id="infrastructure-requirements">
<h3>Infrastructure Requirements<a class="headerlink" href="#infrastructure-requirements" title="Link to this heading"></a></h3>
<section id="minimum-system-requirements">
<h4>Minimum System Requirements<a class="headerlink" href="#minimum-system-requirements" title="Link to this heading"></a></h4>
<ul class="simple">
<li><p><strong>Kubernetes Cluster</strong>: EKS 1.28+ with 3+ nodes</p></li>
<li><p><strong>Node Specifications</strong>:</p>
<ul>
<li><p>CPU: 4 vCPUs per node</p></li>
<li><p>Memory: 16 GB RAM per node</p></li>
<li><p>Storage: 100 GB SSD per node</p></li>
</ul>
</li>
<li><p><strong>Database</strong>: PostgreSQL 15+ (RDS recommended)</p></li>
<li><p><strong>Cache</strong>: Redis 7+ (ElastiCache recommended)</p></li>
<li><p><strong>Load Balancer</strong>: Application Load Balancer (ALB)</p></li>
</ul>
</section>
<section id="network-requirements">
<h4>Network Requirements<a class="headerlink" href="#network-requirements" title="Link to this heading"></a></h4>
<ul class="simple">
<li><p><strong>VPC</strong>: Dedicated VPC with public and private subnets</p></li>
<li><p><strong>Security Groups</strong>: Properly configured for web, app, and database tiers</p></li>
<li><p><strong>DNS</strong>: Route 53 hosted zone for domain management</p></li>
<li><p><strong>SSL/TLS</strong>: Valid SSL certificates (ACM recommended)</p></li>
</ul>
</section>
<section id="external-dependencies">
<h4>External Dependencies<a class="headerlink" href="#external-dependencies" title="Link to this heading"></a></h4>
<ul class="simple">
<li><p><strong>Container Registry</strong>: ECR for Docker images</p></li>
<li><p><strong>Secrets Management</strong>: AWS Secrets Manager</p></li>
<li><p><strong>Monitoring</strong>: CloudWatch, Prometheus, Grafana</p></li>
<li><p><strong>Backup</strong>: S3 for database backups and file storage</p></li>
</ul>
</section>
</section>
<section id="required-tools-and-access">
<h3>Required Tools and Access<a class="headerlink" href="#required-tools-and-access" title="Link to this heading"></a></h3>
<section id="development-tools">
<h4>Development Tools<a class="headerlink" href="#development-tools" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Install required CLI tools</span>
curl<span class="w"> </span>-LO<span class="w"> </span><span class="s2">&quot;https://dl.k8s.io/release/</span><span class="k">$(</span>curl<span class="w"> </span>-L<span class="w"> </span>-s<span class="w"> </span>https://dl.k8s.io/release/stable.txt<span class="k">)</span><span class="s2">/bin/linux/amd64/kubectl&quot;</span>
curl<span class="w"> </span>-o<span class="w"> </span>aws-iam-authenticator<span class="w"> </span>https://amazon-eks.s3.us-west-2.amazonaws.com/1.21.2/2021-07-05/bin/linux/amd64/aws-iam-authenticator
curl<span class="w"> </span>-LO<span class="w"> </span>https://get.helm.sh/helm-v3.12.0-linux-amd64.tar.gz

<span class="c1"># Install Terraform</span>
wget<span class="w"> </span>https://releases.hashicorp.com/terraform/1.5.0/terraform_1.5.0_linux_amd64.zip
unzip<span class="w"> </span>terraform_1.5.0_linux_amd64.zip
sudo<span class="w"> </span>mv<span class="w"> </span>terraform<span class="w"> </span>/usr/local/bin/
</pre></div>
</div>
</section>
<section id="aws-access-requirements">
<h4>AWS Access Requirements<a class="headerlink" href="#aws-access-requirements" title="Link to this heading"></a></h4>
<ul class="simple">
<li><p><strong>IAM Permissions</strong>: Administrator access or specific permissions for:</p>
<ul>
<li><p>EKS cluster management</p></li>
<li><p>RDS instance management</p></li>
<li><p>VPC and networking</p></li>
<li><p>IAM role creation</p></li>
<li><p>Secrets Manager access</p></li>
<li><p>ECR repository access</p></li>
</ul>
</li>
</ul>
</section>
</section>
</section>
<section id="infrastructure-deployment">
<h2>Infrastructure Deployment<a class="headerlink" href="#infrastructure-deployment" title="Link to this heading"></a></h2>
<section id="step-1-environment-configuration">
<h3>Step 1: Environment Configuration<a class="headerlink" href="#step-1-environment-configuration" title="Link to this heading"></a></h3>
<section id="configure-aws-cli">
<h4>1.1 Configure AWS CLI<a class="headerlink" href="#configure-aws-cli" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Configure AWS credentials</span>
aws<span class="w"> </span>configure
<span class="c1"># AWS Access Key ID: [Your Access Key]</span>
<span class="c1"># AWS Secret Access Key: [Your Secret Key]</span>
<span class="c1"># Default region name: us-west-2</span>
<span class="c1"># Default output format: json</span>

<span class="c1"># Verify access</span>
aws<span class="w"> </span>sts<span class="w"> </span>get-caller-identity
</pre></div>
</div>
</section>
<section id="set-environment-variables">
<h4>1.2 Set Environment Variables<a class="headerlink" href="#set-environment-variables" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Create environment configuration</span>
cat<span class="w"> </span>&gt;<span class="w"> </span>.env.production<span class="w"> </span><span class="s">&lt;&lt; &#39;EOF&#39;</span>
<span class="s"># AWS Configuration</span>
<span class="s">AWS_REGION=us-west-2</span>
<span class="s">AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)</span>

<span class="s"># Project Configuration</span>
<span class="s">PROJECT_NAME=blast-radius</span>
<span class="s">ENVIRONMENT=production</span>
<span class="s">DOMAIN_NAME=blast-radius.com</span>

<span class="s"># Database Configuration</span>
<span class="s">DB_INSTANCE_CLASS=db.r6g.xlarge</span>
<span class="s">DB_ALLOCATED_STORAGE=500</span>
<span class="s">DB_MAX_ALLOCATED_STORAGE=1000</span>

<span class="s"># EKS Configuration</span>
<span class="s">EKS_CLUSTER_VERSION=1.28</span>
<span class="s">EKS_NODE_INSTANCE_TYPE=t3.large</span>
<span class="s">EKS_DESIRED_CAPACITY=3</span>
<span class="s">EKS_MAX_CAPACITY=10</span>

<span class="s"># Application Configuration</span>
<span class="s">APP_REPLICAS=3</span>
<span class="s">WORKER_REPLICAS=2</span>
<span class="s">EOF</span>

<span class="nb">source</span><span class="w"> </span>.env.production
</pre></div>
</div>
</section>
</section>
<section id="step-2-infrastructure-provisioning">
<h3>Step 2: Infrastructure Provisioning<a class="headerlink" href="#step-2-infrastructure-provisioning" title="Link to this heading"></a></h3>
<section id="deploy-core-infrastructure">
<h4>2.1 Deploy Core Infrastructure<a class="headerlink" href="#deploy-core-infrastructure" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Initialize Terraform</span>
<span class="nb">cd</span><span class="w"> </span>infrastructure/terraform
terraform<span class="w"> </span>init

<span class="c1"># Create workspace for production</span>
terraform<span class="w"> </span>workspace<span class="w"> </span>new<span class="w"> </span>production
terraform<span class="w"> </span>workspace<span class="w"> </span><span class="k">select</span><span class="w"> </span>production

<span class="c1"># Plan infrastructure deployment</span>
terraform<span class="w"> </span>plan<span class="w"> </span>-var-file<span class="o">=</span><span class="s2">&quot;environments/production.tfvars&quot;</span><span class="w"> </span>-out<span class="o">=</span>production.tfplan

<span class="c1"># Review the plan carefully</span>
terraform<span class="w"> </span>show<span class="w"> </span>production.tfplan

<span class="c1"># Apply infrastructure changes</span>
terraform<span class="w"> </span>apply<span class="w"> </span>production.tfplan
</pre></div>
</div>
</section>
<section id="configure-kubectl-access">
<h4>2.2 Configure kubectl Access<a class="headerlink" href="#configure-kubectl-access" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Update kubeconfig for EKS cluster</span>
aws<span class="w"> </span>eks<span class="w"> </span>update-kubeconfig<span class="w"> </span>--region<span class="w"> </span><span class="nv">$AWS_REGION</span><span class="w"> </span>--name<span class="w"> </span><span class="nv">$PROJECT_NAME</span>-cluster

<span class="c1"># Verify cluster access</span>
kubectl<span class="w"> </span>cluster-info
kubectl<span class="w"> </span>get<span class="w"> </span>nodes
</pre></div>
</div>
</section>
<section id="install-cluster-add-ons">
<h4>2.3 Install Cluster Add-ons<a class="headerlink" href="#install-cluster-add-ons" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Install AWS Load Balancer Controller</span>
helm<span class="w"> </span>repo<span class="w"> </span>add<span class="w"> </span>eks<span class="w"> </span>https://aws.github.io/eks-charts
helm<span class="w"> </span>repo<span class="w"> </span>update

helm<span class="w"> </span>install<span class="w"> </span>aws-load-balancer-controller<span class="w"> </span>eks/aws-load-balancer-controller<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-n<span class="w"> </span>kube-system<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--set<span class="w"> </span><span class="nv">clusterName</span><span class="o">=</span><span class="nv">$PROJECT_NAME</span>-cluster<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--set<span class="w"> </span>serviceAccount.create<span class="o">=</span><span class="nb">false</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--set<span class="w"> </span>serviceAccount.name<span class="o">=</span>aws-load-balancer-controller

<span class="c1"># Install Cluster Autoscaler</span>
helm<span class="w"> </span>install<span class="w"> </span>cluster-autoscaler<span class="w"> </span>autoscaler/cluster-autoscaler<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--namespace<span class="w"> </span>kube-system<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--set<span class="w"> </span>autoDiscovery.clusterName<span class="o">=</span><span class="nv">$PROJECT_NAME</span>-cluster<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--set<span class="w"> </span><span class="nv">awsRegion</span><span class="o">=</span><span class="nv">$AWS_REGION</span>

<span class="c1"># Install Metrics Server</span>
kubectl<span class="w"> </span>apply<span class="w"> </span>-f<span class="w"> </span>https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml
</pre></div>
</div>
</section>
</section>
<section id="step-3-database-setup">
<h3>Step 3: Database Setup<a class="headerlink" href="#step-3-database-setup" title="Link to this heading"></a></h3>
<section id="database-initialization">
<h4>3.1 Database Initialization<a class="headerlink" href="#database-initialization" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Get database endpoint from Terraform output</span>
<span class="nv">DB_ENDPOINT</span><span class="o">=</span><span class="k">$(</span>terraform<span class="w"> </span>output<span class="w"> </span>-raw<span class="w"> </span>database_endpoint<span class="k">)</span>
<span class="nv">DB_USERNAME</span><span class="o">=</span><span class="k">$(</span>terraform<span class="w"> </span>output<span class="w"> </span>-raw<span class="w"> </span>database_username<span class="k">)</span>

<span class="c1"># Get database password from Secrets Manager</span>
<span class="nv">DB_PASSWORD</span><span class="o">=</span><span class="k">$(</span>aws<span class="w"> </span>secretsmanager<span class="w"> </span>get-secret-value<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--secret-id<span class="w"> </span><span class="nv">$PROJECT_NAME</span>-database-credentials<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--query<span class="w"> </span>SecretString<span class="w"> </span>--output<span class="w"> </span>text<span class="w"> </span><span class="p">|</span><span class="w"> </span>jq<span class="w"> </span>-r<span class="w"> </span>.password<span class="k">)</span>

<span class="c1"># Connect to database and create initial schema</span>
<span class="nv">PGPASSWORD</span><span class="o">=</span><span class="nv">$DB_PASSWORD</span><span class="w"> </span>psql<span class="w"> </span>-h<span class="w"> </span><span class="nv">$DB_ENDPOINT</span><span class="w"> </span>-U<span class="w"> </span><span class="nv">$DB_USERNAME</span><span class="w"> </span>-d<span class="w"> </span>blast_radius<span class="w"> </span><span class="s">&lt;&lt; &#39;EOF&#39;</span>
<span class="s">-- Verify database connection</span>
<span class="s">SELECT version();</span>

<span class="s">-- Create application user</span>
<span class="s">CREATE USER blast_radius_app WITH PASSWORD &#39;secure_app_password&#39;;</span>

<span class="s">-- Grant necessary permissions</span>
<span class="s">GRANT CONNECT ON DATABASE blast_radius TO blast_radius_app;</span>
<span class="s">GRANT USAGE ON SCHEMA public TO blast_radius_app;</span>
<span class="s">GRANT CREATE ON SCHEMA public TO blast_radius_app;</span>

<span class="s">-- Create initial tables (will be managed by Alembic migrations)</span>
<span class="s">\q</span>
<span class="s">EOF</span>
</pre></div>
</div>
</section>
<section id="database-migration">
<h4>3.2 Database Migration<a class="headerlink" href="#database-migration" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run database migrations</span>
kubectl<span class="w"> </span>create<span class="w"> </span>job<span class="w"> </span>--from<span class="o">=</span>cronjob/database-migration<span class="w"> </span>migration-<span class="k">$(</span>date<span class="w"> </span>+%s<span class="k">)</span><span class="w"> </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Monitor migration progress</span>
kubectl<span class="w"> </span>logs<span class="w"> </span>-f<span class="w"> </span>job/migration-<span class="k">$(</span>date<span class="w"> </span>+%s<span class="k">)</span><span class="w"> </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Verify migration completion</span>
kubectl<span class="w"> </span>get<span class="w"> </span><span class="nb">jobs</span><span class="w"> </span>-n<span class="w"> </span>blast-radius
</pre></div>
</div>
</section>
</section>
</section>
<section id="application-deployment">
<h2>Application Deployment<a class="headerlink" href="#application-deployment" title="Link to this heading"></a></h2>
<section id="step-4-container-image-preparation">
<h3>Step 4: Container Image Preparation<a class="headerlink" href="#step-4-container-image-preparation" title="Link to this heading"></a></h3>
<section id="build-and-push-images">
<h4>4.1 Build and Push Images<a class="headerlink" href="#build-and-push-images" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Login to ECR</span>
aws<span class="w"> </span>ecr<span class="w"> </span>get-login-password<span class="w"> </span>--region<span class="w"> </span><span class="nv">$AWS_REGION</span><span class="w"> </span><span class="p">|</span><span class="w"> </span>docker<span class="w"> </span>login<span class="w"> </span>--username<span class="w"> </span>AWS<span class="w"> </span>--password-stdin<span class="w"> </span><span class="nv">$AWS_ACCOUNT_ID</span>.dkr.ecr.<span class="nv">$AWS_REGION</span>.amazonaws.com

<span class="c1"># Build backend image</span>
docker<span class="w"> </span>build<span class="w"> </span>-t<span class="w"> </span>blast-radius-backend:latest<span class="w"> </span>-f<span class="w"> </span>backend/Dockerfile<span class="w"> </span>backend/
docker<span class="w"> </span>tag<span class="w"> </span>blast-radius-backend:latest<span class="w"> </span><span class="nv">$AWS_ACCOUNT_ID</span>.dkr.ecr.<span class="nv">$AWS_REGION</span>.amazonaws.com/blast-radius-backend:latest
docker<span class="w"> </span>push<span class="w"> </span><span class="nv">$AWS_ACCOUNT_ID</span>.dkr.ecr.<span class="nv">$AWS_REGION</span>.amazonaws.com/blast-radius-backend:latest

<span class="c1"># Build frontend image</span>
docker<span class="w"> </span>build<span class="w"> </span>-t<span class="w"> </span>blast-radius-frontend:latest<span class="w"> </span>-f<span class="w"> </span>frontend/Dockerfile<span class="w"> </span>frontend/
docker<span class="w"> </span>tag<span class="w"> </span>blast-radius-frontend:latest<span class="w"> </span><span class="nv">$AWS_ACCOUNT_ID</span>.dkr.ecr.<span class="nv">$AWS_REGION</span>.amazonaws.com/blast-radius-frontend:latest
docker<span class="w"> </span>push<span class="w"> </span><span class="nv">$AWS_ACCOUNT_ID</span>.dkr.ecr.<span class="nv">$AWS_REGION</span>.amazonaws.com/blast-radius-frontend:latest

<span class="c1"># Build worker image</span>
docker<span class="w"> </span>build<span class="w"> </span>-t<span class="w"> </span>blast-radius-worker:latest<span class="w"> </span>-f<span class="w"> </span>backend/Dockerfile.worker<span class="w"> </span>backend/
docker<span class="w"> </span>tag<span class="w"> </span>blast-radius-worker:latest<span class="w"> </span><span class="nv">$AWS_ACCOUNT_ID</span>.dkr.ecr.<span class="nv">$AWS_REGION</span>.amazonaws.com/blast-radius-worker:latest
docker<span class="w"> </span>push<span class="w"> </span><span class="nv">$AWS_ACCOUNT_ID</span>.dkr.ecr.<span class="nv">$AWS_REGION</span>.amazonaws.com/blast-radius-worker:latest
</pre></div>
</div>
</section>
</section>
<section id="step-5-kubernetes-deployment">
<h3>Step 5: Kubernetes Deployment<a class="headerlink" href="#step-5-kubernetes-deployment" title="Link to this heading"></a></h3>
<section id="create-namespace-and-secrets">
<h4>5.1 Create Namespace and Secrets<a class="headerlink" href="#create-namespace-and-secrets" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Create application namespace</span>
kubectl<span class="w"> </span>create<span class="w"> </span>namespace<span class="w"> </span>blast-radius

<span class="c1"># Create database secret</span>
kubectl<span class="w"> </span>create<span class="w"> </span>secret<span class="w"> </span>generic<span class="w"> </span>database-credentials<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--from-literal<span class="o">=</span><span class="nv">host</span><span class="o">=</span><span class="nv">$DB_ENDPOINT</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--from-literal<span class="o">=</span><span class="nv">username</span><span class="o">=</span>blast_radius_app<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--from-literal<span class="o">=</span><span class="nv">password</span><span class="o">=</span><span class="nv">$DB_PASSWORD</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--from-literal<span class="o">=</span><span class="nv">database</span><span class="o">=</span>blast_radius<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Create Redis secret</span>
<span class="nv">REDIS_ENDPOINT</span><span class="o">=</span><span class="k">$(</span>terraform<span class="w"> </span>output<span class="w"> </span>-raw<span class="w"> </span>redis_endpoint<span class="k">)</span>
kubectl<span class="w"> </span>create<span class="w"> </span>secret<span class="w"> </span>generic<span class="w"> </span>redis-credentials<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--from-literal<span class="o">=</span><span class="nv">host</span><span class="o">=</span><span class="nv">$REDIS_ENDPOINT</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--from-literal<span class="o">=</span><span class="nv">port</span><span class="o">=</span><span class="m">6379</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Create application secrets</span>
kubectl<span class="w"> </span>create<span class="w"> </span>secret<span class="w"> </span>generic<span class="w"> </span>app-secrets<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--from-literal<span class="o">=</span>secret-key<span class="o">=</span><span class="k">$(</span>openssl<span class="w"> </span>rand<span class="w"> </span>-base64<span class="w"> </span><span class="m">32</span><span class="k">)</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--from-literal<span class="o">=</span>jwt-secret<span class="o">=</span><span class="k">$(</span>openssl<span class="w"> </span>rand<span class="w"> </span>-base64<span class="w"> </span><span class="m">32</span><span class="k">)</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-n<span class="w"> </span>blast-radius
</pre></div>
</div>
</section>
<section id="deploy-application-components">
<h4>5.2 Deploy Application Components<a class="headerlink" href="#deploy-application-components" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Deploy backend API</span>
kubectl<span class="w"> </span>apply<span class="w"> </span>-f<span class="w"> </span>k8s/backend/<span class="w"> </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Deploy frontend</span>
kubectl<span class="w"> </span>apply<span class="w"> </span>-f<span class="w"> </span>k8s/frontend/<span class="w"> </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Deploy worker</span>
kubectl<span class="w"> </span>apply<span class="w"> </span>-f<span class="w"> </span>k8s/worker/<span class="w"> </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Deploy monitoring stack</span>
kubectl<span class="w"> </span>apply<span class="w"> </span>-f<span class="w"> </span>k8s/monitoring/<span class="w"> </span>-n<span class="w"> </span>blast-radius
</pre></div>
</div>
</section>
<section id="configure-ingress-and-load-balancer">
<h4>5.3 Configure Ingress and Load Balancer<a class="headerlink" href="#configure-ingress-and-load-balancer" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Deploy ingress controller</span>
kubectl<span class="w"> </span>apply<span class="w"> </span>-f<span class="w"> </span>k8s/ingress/<span class="w"> </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Get load balancer DNS name</span>
kubectl<span class="w"> </span>get<span class="w"> </span>ingress<span class="w"> </span>blast-radius-ingress<span class="w"> </span>-n<span class="w"> </span>blast-radius<span class="w"> </span>-o<span class="w"> </span><span class="nv">jsonpath</span><span class="o">=</span><span class="s1">&#39;{.status.loadBalancer.ingress[0].hostname}&#39;</span>

<span class="c1"># Update DNS records in Route 53</span>
aws<span class="w"> </span>route53<span class="w"> </span>change-resource-record-sets<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--hosted-zone-id<span class="w"> </span><span class="nv">$HOSTED_ZONE_ID</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--change-batch<span class="w"> </span>file://dns-update.json
</pre></div>
</div>
</section>
</section>
<section id="step-6-ssl-tls-configuration">
<h3>Step 6: SSL/TLS Configuration<a class="headerlink" href="#step-6-ssl-tls-configuration" title="Link to this heading"></a></h3>
<section id="request-ssl-certificate">
<h4>6.1 Request SSL Certificate<a class="headerlink" href="#request-ssl-certificate" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Request certificate from ACM</span>
<span class="nv">CERT_ARN</span><span class="o">=</span><span class="k">$(</span>aws<span class="w"> </span>acm<span class="w"> </span>request-certificate<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--domain-name<span class="w"> </span><span class="nv">$DOMAIN_NAME</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--subject-alternative-names<span class="w"> </span><span class="s2">&quot;*.</span><span class="nv">$DOMAIN_NAME</span><span class="s2">&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--validation-method<span class="w"> </span>DNS<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--query<span class="w"> </span>CertificateArn<span class="w"> </span>--output<span class="w"> </span>text<span class="k">)</span>

<span class="c1"># Get validation records</span>
aws<span class="w"> </span>acm<span class="w"> </span>describe-certificate<span class="w"> </span>--certificate-arn<span class="w"> </span><span class="nv">$CERT_ARN</span>

<span class="c1"># Add DNS validation records to Route 53</span>
<span class="c1"># (Follow the validation instructions from ACM)</span>
</pre></div>
</div>
</section>
<section id="configure-https-redirect">
<h4>6.2 Configure HTTPS Redirect<a class="headerlink" href="#configure-https-redirect" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Update ingress with SSL certificate</span>
kubectl<span class="w"> </span>patch<span class="w"> </span>ingress<span class="w"> </span>blast-radius-ingress<span class="w"> </span>-n<span class="w"> </span>blast-radius<span class="w"> </span>-p<span class="w"> </span><span class="s1">&#39;{</span>
<span class="s1">  &quot;metadata&quot;: {</span>
<span class="s1">    &quot;annotations&quot;: {</span>
<span class="s1">      &quot;alb.ingress.kubernetes.io/certificate-arn&quot;: &quot;&#39;</span><span class="nv">$CERT_ARN</span><span class="s1">&#39;&quot;,</span>
<span class="s1">      &quot;alb.ingress.kubernetes.io/ssl-redirect&quot;: &quot;443&quot;</span>
<span class="s1">    }</span>
<span class="s1">  }</span>
<span class="s1">}&#39;</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="post-deployment-configuration">
<h2>Post-Deployment Configuration<a class="headerlink" href="#post-deployment-configuration" title="Link to this heading"></a></h2>
<section id="step-7-application-configuration">
<h3>Step 7: Application Configuration<a class="headerlink" href="#step-7-application-configuration" title="Link to this heading"></a></h3>
<section id="initialize-application-data">
<h4>7.1 Initialize Application Data<a class="headerlink" href="#initialize-application-data" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Create initial admin user</span>
kubectl<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>deployment/blast-radius-backend<span class="w"> </span>-n<span class="w"> </span>blast-radius<span class="w"> </span>--<span class="w"> </span>python<span class="w"> </span>manage.py<span class="w"> </span>create-admin<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--email<span class="w"> </span><EMAIL><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--password<span class="w"> </span><span class="k">$(</span>openssl<span class="w"> </span>rand<span class="w"> </span>-base64<span class="w"> </span><span class="m">16</span><span class="k">)</span>

<span class="c1"># Load initial data</span>
kubectl<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>deployment/blast-radius-backend<span class="w"> </span>-n<span class="w"> </span>blast-radius<span class="w"> </span>--<span class="w"> </span>python<span class="w"> </span>manage.py<span class="w"> </span>load-fixtures

<span class="c1"># Run initial asset discovery</span>
kubectl<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>deployment/blast-radius-backend<span class="w"> </span>-n<span class="w"> </span>blast-radius<span class="w"> </span>--<span class="w"> </span>python<span class="w"> </span>manage.py<span class="w"> </span>discover-assets
</pre></div>
</div>
</section>
<section id="configure-monitoring-and-alerting">
<h4>7.2 Configure Monitoring and Alerting<a class="headerlink" href="#configure-monitoring-and-alerting" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Import Grafana dashboards</span>
kubectl<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>deployment/grafana<span class="w"> </span>-n<span class="w"> </span>blast-radius<span class="w"> </span>--<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>grafana-cli<span class="w"> </span>admin<span class="w"> </span>import-dashboard<span class="w"> </span>/var/lib/grafana/dashboards/blast-radius-overview.json

<span class="c1"># Configure Prometheus targets</span>
kubectl<span class="w"> </span>apply<span class="w"> </span>-f<span class="w"> </span>monitoring/prometheus/servicemonitors/<span class="w"> </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Test alerting</span>
kubectl<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>deployment/alertmanager<span class="w"> </span>-n<span class="w"> </span>blast-radius<span class="w"> </span>--<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>amtool<span class="w"> </span>alert<span class="w"> </span>add<span class="w"> </span><span class="nv">alertname</span><span class="o">=</span>TestAlert<span class="w"> </span><span class="nv">severity</span><span class="o">=</span>warning
</pre></div>
</div>
</section>
</section>
<section id="step-8-security-hardening">
<h3>Step 8: Security Hardening<a class="headerlink" href="#step-8-security-hardening" title="Link to this heading"></a></h3>
<section id="network-policies">
<h4>8.1 Network Policies<a class="headerlink" href="#network-policies" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Apply network policies</span>
kubectl<span class="w"> </span>apply<span class="w"> </span>-f<span class="w"> </span>k8s/security/network-policies/<span class="w"> </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Verify network isolation</span>
kubectl<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>deployment/blast-radius-backend<span class="w"> </span>-n<span class="w"> </span>blast-radius<span class="w"> </span>--<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>nc<span class="w"> </span>-zv<span class="w"> </span>blast-radius-database<span class="w"> </span><span class="m">5432</span>
</pre></div>
</div>
</section>
<section id="pod-security-policies">
<h4>8.2 Pod Security Policies<a class="headerlink" href="#pod-security-policies" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Apply pod security policies</span>
kubectl<span class="w"> </span>apply<span class="w"> </span>-f<span class="w"> </span>k8s/security/pod-security-policies/

<span class="c1"># Verify security contexts</span>
kubectl<span class="w"> </span>get<span class="w"> </span>pods<span class="w"> </span>-n<span class="w"> </span>blast-radius<span class="w"> </span>-o<span class="w"> </span><span class="nv">jsonpath</span><span class="o">=</span><span class="s1">&#39;{.items[*].spec.securityContext}&#39;</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="verification-and-testing">
<h2>Verification and Testing<a class="headerlink" href="#verification-and-testing" title="Link to this heading"></a></h2>
<section id="step-9-deployment-verification">
<h3>Step 9: Deployment Verification<a class="headerlink" href="#step-9-deployment-verification" title="Link to this heading"></a></h3>
<section id="health-checks">
<h4>9.1 Health Checks<a class="headerlink" href="#health-checks" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check pod status</span>
kubectl<span class="w"> </span>get<span class="w"> </span>pods<span class="w"> </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Check service endpoints</span>
kubectl<span class="w"> </span>get<span class="w"> </span>endpoints<span class="w"> </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Test application health</span>
curl<span class="w"> </span>-f<span class="w"> </span>https://<span class="nv">$DOMAIN_NAME</span>/health
curl<span class="w"> </span>-f<span class="w"> </span>https://<span class="nv">$DOMAIN_NAME</span>/api/health
</pre></div>
</div>
</section>
<section id="functional-testing">
<h4>9.2 Functional Testing<a class="headerlink" href="#functional-testing" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run integration tests</span>
kubectl<span class="w"> </span>create<span class="w"> </span>job<span class="w"> </span>integration-tests<span class="w"> </span>--image<span class="o">=</span><span class="nv">$AWS_ACCOUNT_ID</span>.dkr.ecr.<span class="nv">$AWS_REGION</span>.amazonaws.com/blast-radius-tests:latest<span class="w"> </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Monitor test execution</span>
kubectl<span class="w"> </span>logs<span class="w"> </span>-f<span class="w"> </span>job/integration-tests<span class="w"> </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Run performance tests</span>
kubectl<span class="w"> </span>apply<span class="w"> </span>-f<span class="w"> </span>k8s/tests/performance-test.yaml<span class="w"> </span>-n<span class="w"> </span>blast-radius
</pre></div>
</div>
</section>
<section id="security-testing">
<h4>9.3 Security Testing<a class="headerlink" href="#security-testing" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Run security scans</span>
kubectl<span class="w"> </span>create<span class="w"> </span>job<span class="w"> </span>security-scan<span class="w"> </span>--image<span class="o">=</span>owasp/zap2docker-stable:latest<span class="w"> </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Check SSL configuration</span>
curl<span class="w"> </span>-I<span class="w"> </span>https://<span class="nv">$DOMAIN_NAME</span>
openssl<span class="w"> </span>s_client<span class="w"> </span>-connect<span class="w"> </span><span class="nv">$DOMAIN_NAME</span>:443<span class="w"> </span>-servername<span class="w"> </span><span class="nv">$DOMAIN_NAME</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="backup-and-recovery-setup">
<h2>Backup and Recovery Setup<a class="headerlink" href="#backup-and-recovery-setup" title="Link to this heading"></a></h2>
<section id="step-10-backup-configuration">
<h3>Step 10: Backup Configuration<a class="headerlink" href="#step-10-backup-configuration" title="Link to this heading"></a></h3>
<section id="database-backup">
<h4>10.1 Database Backup<a class="headerlink" href="#database-backup" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Configure automated database backups</span>
aws<span class="w"> </span>rds<span class="w"> </span>modify-db-instance<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--db-instance-identifier<span class="w"> </span><span class="nv">$PROJECT_NAME</span>-database<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--backup-retention-period<span class="w"> </span><span class="m">30</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--preferred-backup-window<span class="w"> </span><span class="s2">&quot;03:00-04:00&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--preferred-maintenance-window<span class="w"> </span><span class="s2">&quot;sun:04:00-sun:05:00&quot;</span>

<span class="c1"># Create manual backup</span>
aws<span class="w"> </span>rds<span class="w"> </span>create-db-snapshot<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--db-instance-identifier<span class="w"> </span><span class="nv">$PROJECT_NAME</span>-database<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--db-snapshot-identifier<span class="w"> </span><span class="nv">$PROJECT_NAME</span>-manual-snapshot-<span class="k">$(</span>date<span class="w"> </span>+%Y%m%d<span class="k">)</span>
</pre></div>
</div>
</section>
<section id="application-data-backup">
<h4>10.2 Application Data Backup<a class="headerlink" href="#application-data-backup" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Configure persistent volume backups</span>
kubectl<span class="w"> </span>apply<span class="w"> </span>-f<span class="w"> </span>k8s/backup/volume-snapshots.yaml<span class="w"> </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Schedule regular backups</span>
kubectl<span class="w"> </span>apply<span class="w"> </span>-f<span class="w"> </span>k8s/backup/backup-cronjob.yaml<span class="w"> </span>-n<span class="w"> </span>blast-radius
</pre></div>
</div>
</section>
</section>
</section>
<section id="monitoring-and-maintenance">
<h2>Monitoring and Maintenance<a class="headerlink" href="#monitoring-and-maintenance" title="Link to this heading"></a></h2>
<section id="step-11-ongoing-monitoring">
<h3>Step 11: Ongoing Monitoring<a class="headerlink" href="#step-11-ongoing-monitoring" title="Link to this heading"></a></h3>
<section id="set-up-alerts">
<h4>11.1 Set Up Alerts<a class="headerlink" href="#set-up-alerts" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Configure critical alerts</span>
kubectl<span class="w"> </span>apply<span class="w"> </span>-f<span class="w"> </span>monitoring/alertmanager/critical-alerts.yaml<span class="w"> </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Test alert delivery</span>
kubectl<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>deployment/alertmanager<span class="w"> </span>-n<span class="w"> </span>blast-radius<span class="w"> </span>--<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>amtool<span class="w"> </span>alert<span class="w"> </span>add<span class="w"> </span><span class="nv">alertname</span><span class="o">=</span>DeploymentTest<span class="w"> </span><span class="nv">severity</span><span class="o">=</span>critical
</pre></div>
</div>
</section>
<section id="performance-monitoring">
<h4>11.2 Performance Monitoring<a class="headerlink" href="#performance-monitoring" title="Link to this heading"></a></h4>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Enable detailed monitoring</span>
kubectl<span class="w"> </span>patch<span class="w"> </span>deployment<span class="w"> </span>blast-radius-backend<span class="w"> </span>-n<span class="w"> </span>blast-radius<span class="w"> </span>-p<span class="w"> </span><span class="s1">&#39;{</span>
<span class="s1">  &quot;spec&quot;: {</span>
<span class="s1">    &quot;template&quot;: {</span>
<span class="s1">      &quot;metadata&quot;: {</span>
<span class="s1">        &quot;annotations&quot;: {</span>
<span class="s1">          &quot;prometheus.io/scrape&quot;: &quot;true&quot;,</span>
<span class="s1">          &quot;prometheus.io/port&quot;: &quot;8000&quot;,</span>
<span class="s1">          &quot;prometheus.io/path&quot;: &quot;/metrics&quot;</span>
<span class="s1">        }</span>
<span class="s1">      }</span>
<span class="s1">    }</span>
<span class="s1">  }</span>
<span class="s1">}&#39;</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="troubleshooting-common-issues">
<h2>Troubleshooting Common Issues<a class="headerlink" href="#troubleshooting-common-issues" title="Link to this heading"></a></h2>
<section id="database-connection-issues">
<h3>Database Connection Issues<a class="headerlink" href="#database-connection-issues" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check database connectivity</span>
kubectl<span class="w"> </span><span class="nb">exec</span><span class="w"> </span>-it<span class="w"> </span>deployment/blast-radius-backend<span class="w"> </span>-n<span class="w"> </span>blast-radius<span class="w"> </span>--<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>pg_isready<span class="w"> </span>-h<span class="w"> </span><span class="nv">$DB_ENDPOINT</span><span class="w"> </span>-p<span class="w"> </span><span class="m">5432</span>

<span class="c1"># Check security group rules</span>
aws<span class="w"> </span>ec2<span class="w"> </span>describe-security-groups<span class="w"> </span>--group-ids<span class="w"> </span><span class="nv">$DB_SECURITY_GROUP_ID</span>
</pre></div>
</div>
</section>
<section id="pod-startup-issues">
<h3>Pod Startup Issues<a class="headerlink" href="#pod-startup-issues" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check pod logs</span>
kubectl<span class="w"> </span>logs<span class="w"> </span>-f<span class="w"> </span>deployment/blast-radius-backend<span class="w"> </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Check resource constraints</span>
kubectl<span class="w"> </span>describe<span class="w"> </span>pod<span class="w"> </span>-l<span class="w"> </span><span class="nv">app</span><span class="o">=</span>blast-radius-backend<span class="w"> </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Check image pull issues</span>
kubectl<span class="w"> </span>get<span class="w"> </span>events<span class="w"> </span>-n<span class="w"> </span>blast-radius<span class="w"> </span>--sort-by<span class="o">=</span><span class="s1">&#39;.lastTimestamp&#39;</span>
</pre></div>
</div>
</section>
<section id="load-balancer-issues">
<h3>Load Balancer Issues<a class="headerlink" href="#load-balancer-issues" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Check ALB status</span>
aws<span class="w"> </span>elbv2<span class="w"> </span>describe-load-balancers<span class="w"> </span>--names<span class="w"> </span><span class="nv">$PROJECT_NAME</span>-alb

<span class="c1"># Check target group health</span>
aws<span class="w"> </span>elbv2<span class="w"> </span>describe-target-health<span class="w"> </span>--target-group-arn<span class="w"> </span><span class="nv">$TARGET_GROUP_ARN</span>

<span class="c1"># Check ingress configuration</span>
kubectl<span class="w"> </span>describe<span class="w"> </span>ingress<span class="w"> </span>blast-radius-ingress<span class="w"> </span>-n<span class="w"> </span>blast-radius
</pre></div>
</div>
</section>
</section>
<section id="rollback-procedures">
<h2>Rollback Procedures<a class="headerlink" href="#rollback-procedures" title="Link to this heading"></a></h2>
<section id="emergency-rollback">
<h3>Emergency Rollback<a class="headerlink" href="#emergency-rollback" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Rollback to previous deployment</span>
kubectl<span class="w"> </span>rollout<span class="w"> </span>undo<span class="w"> </span>deployment/blast-radius-backend<span class="w"> </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Check rollback status</span>
kubectl<span class="w"> </span>rollout<span class="w"> </span>status<span class="w"> </span>deployment/blast-radius-backend<span class="w"> </span>-n<span class="w"> </span>blast-radius

<span class="c1"># Verify application functionality</span>
curl<span class="w"> </span>-f<span class="w"> </span>https://<span class="nv">$DOMAIN_NAME</span>/health
</pre></div>
</div>
</section>
<section id="database-rollback">
<h3>Database Rollback<a class="headerlink" href="#database-rollback" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Restore from snapshot (if needed)</span>
aws<span class="w"> </span>rds<span class="w"> </span>restore-db-instance-from-db-snapshot<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--db-instance-identifier<span class="w"> </span><span class="nv">$PROJECT_NAME</span>-database-restored<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--db-snapshot-identifier<span class="w"> </span><span class="nv">$SNAPSHOT_IDENTIFIER</span>
</pre></div>
</div>
<p>This production deployment guide provides comprehensive instructions for deploying the Blast-Radius Security Tool in a production environment with proper security, monitoring, and backup configurations.</p>
</section>
</section>
<section id="next-steps">
<h2>Next Steps<a class="headerlink" href="#next-steps" title="Link to this heading"></a></h2>
<p>After successful deployment:</p>
<ol class="arabic simple">
<li><p><strong>Review Security Configuration</strong>: Conduct security review and penetration testing</p></li>
<li><p><strong>Performance Tuning</strong>: Monitor and optimize application performance</p></li>
<li><p><strong>Backup Testing</strong>: Verify backup and recovery procedures</p></li>
<li><p><strong>Documentation Updates</strong>: Update operational runbooks and procedures</p></li>
<li><p><strong>Team Training</strong>: Train operations team on monitoring and maintenance procedures</p></li>
</ol>
<p>For additional deployment scenarios, see:</p>
<ul class="simple">
<li><p><a class="reference internal" href="environment-setup.html"><span class="std std-doc">Environment Setup Documentation</span></a></p></li>
<li><p><a class="reference internal" href="troubleshooting-guide.html"><span class="std std-doc">Troubleshooting Guide</span></a></p></li>
<li><p><a class="reference internal" href="#../operations/"><span class="xref myst">Operational Runbooks</span></a></p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="../security/security-summary.html" class="btn btn-neutral float-left" title="Security Documentation Summary" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="environment-setup.html" class="btn btn-neutral float-right" title="Environment Setup Documentation - Blast-Radius Security Tool" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, Blast-Radius Security Tool Team.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>