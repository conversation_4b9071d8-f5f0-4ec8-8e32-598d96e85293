<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Attack Path Analysis Use Cases &mdash; Blast-Radius Security Tool v1.0.0 - Enterprise Security Platform Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ac1674a3" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ac1674a3" />

  
    <link rel="canonical" href="https://blast-radius.readthedocs.io/use-cases/attack-path-analysis.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=528dc5d9"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Blast-Radius Security Tool v1.0.0 - Enterprise Security Platform Documentation"
          href="../_static/opensearch.xml"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" />
    <link rel="next" title="Zero-Trust Architecture Implementation" href="../architecture/zero-trust-architecture.html" />
    <link rel="prev" title="Asset Discovery Use Cases" href="asset-discovery.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🎉 Latest Release</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../latest-implementations-summary.html">Latest Implementations Summary - Production Ready Release</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#implementation-achievements">Implementation Achievements</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#infrastructure-as-code-iac-100-complete">Infrastructure as Code (IaC) - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#zero-trust-architecture-100-complete">Zero-Trust Architecture - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#gdpr-compliance-framework-100-complete">GDPR Compliance Framework - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#enhanced-audit-logging-100-complete">Enhanced Audit Logging - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#least-privilege-access-control-100-complete">Least Privilege Access Control - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#production-deployment-infrastructure-100-complete">Production Deployment Infrastructure - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#documentation-operations-100-complete">Documentation &amp; Operations - 100% Complete ✅</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#technical-specifications">Technical Specifications</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#performance-characteristics">Performance Characteristics</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#security-specifications">Security Specifications</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#compliance-coverage">Compliance Coverage</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#deployment-architecture">Deployment Architecture</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#next-steps-and-recommendations">Next Steps and Recommendations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#immediate-actions">Immediate Actions</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#long-term-roadmap">Long-term Roadmap</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🚀 Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#prerequisites">Prerequisites</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#system-requirements">System Requirements</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#software-dependencies">Software Dependencies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#installation-methods">Installation Methods</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#method-1-quick-start-with-docker-recommended">Method 1: Quick Start with Docker (Recommended)</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#method-2-development-installation">Method 2: Development Installation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#method-3-production-installation">Method 3: Production Installation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#configuration">Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#environment-variables">Environment Variables</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#initial-setup">Initial Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#creating-admin-user">Creating Admin User</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#configuring-integrations">Configuring Integrations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#verification">Verification</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#health-checks">Health Checks</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#performance-testing">Performance Testing</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#core-configuration">Core Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#application-settings">Application Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#database-configuration">Database Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#security-configuration">Security Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#authentication-settings">Authentication Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#encryption-settings">Encryption Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#access-control-settings">Access Control Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#integration-configuration">Integration Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#cloud-provider-settings">Cloud Provider Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#servicenow-integration">ServiceNow Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#threat-intelligence-settings">Threat Intelligence Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#monitoring-and-logging">Monitoring and Logging</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#logging-configuration">Logging Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#monitoring-settings">Monitoring Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#environment-specific-configuration">Environment-Specific Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#development-environment">Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#staging-environment">Staging Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#configuration-validation">Configuration Validation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#security-best-practices">Security Best Practices</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#performance-best-practices">Performance Best Practices</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#troubleshooting">Troubleshooting</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../quick-start-guide.html">Quick Start Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#quick-installation">Quick Installation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-1-get-the-code">Step 1: Get the Code</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-2-start-the-platform">Step 2: Start the Platform</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-3-access-the-platform">Step 3: Access the Platform</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#first-login-and-setup">First Login and Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#initial-login">Initial Login</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#password-change">Password Change</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#dashboard-overview">Dashboard Overview</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#basic-configuration">Basic Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#user-profile-setup">User Profile Setup</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#multi-factor-authentication-recommended">Multi-Factor Authentication (Recommended)</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#your-first-attack-path-analysis">Your First Attack Path Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-1-add-sample-assets">Step 1: Add Sample Assets</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-2-run-attack-path-analysis">Step 2: Run Attack Path Analysis</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-3-view-results">Step 3: View Results</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#understanding-the-visualization">Understanding the Visualization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#essential-features-tour">Essential Features Tour</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#real-time-monitoring">Real-Time Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#asset-management">Asset Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#user-and-role-management">User and Role Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#threat-intelligence">Threat Intelligence</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#integration-setup">Integration Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#cloud-provider-integration">Cloud Provider Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#servicenow-integration">ServiceNow Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#next-steps">Next Steps</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#immediate-actions">Immediate Actions</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#learning-resources">Learning Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#advanced-configuration">Advanced Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#common-quick-start-issues">Common Quick Start Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#platform-won-t-start">Platform Won’t Start</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#can-t-access-web-interface">Can’t Access Web Interface</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#login-issues">Login Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#performance-issues">Performance Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#getting-help">Getting Help</a></li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#congratulations">Congratulations!</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📊 Documentation Excellence</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../documentation-overview.html">Documentation Overview and Achievements</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#documentation-excellence-achievements">🏆 Documentation Excellence Achievements</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#comprehensive-user-guide-ecosystem">📚 Comprehensive User Guide Ecosystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#key-documentation-features">🎯 Key Documentation Features</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#measurable-impact-and-value">📈 Measurable Impact and Value</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#industry-leadership-and-recognition">🌟 Industry Leadership and Recognition</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#future-enhancements-and-roadmap">🚀 Future Enhancements and Roadmap</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📚 Enterprise User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guides/index.html">User Guides</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#role-based-guides">Role-Based Guides</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#soc-operators">SOC Operators</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/soc-operators.html">SOC Operators Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#role-definition-and-responsibilities">Role Definition and Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#core-platform-capabilities-for-soc-operations">Core Platform Capabilities for SOC Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#getting-started-soc-operator-onboarding">Getting Started: SOC Operator Onboarding</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#advanced-real-time-monitoring-and-threat-detection">Advanced Real-time Monitoring and Threat Detection</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#comprehensive-incident-response-framework">Comprehensive Incident Response Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#advanced-reporting-and-documentation-framework">Advanced Reporting and Documentation Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#soc-operations-best-practices-and-excellence-framework">SOC Operations Best Practices and Excellence Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#comprehensive-scenario-based-training-and-response-procedures">Comprehensive Scenario-Based Training and Response Procedures</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#comprehensive-support-resources-and-professional-development">Comprehensive Support Resources and Professional Development</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#conclusion-excellence-in-soc-operations">Conclusion: Excellence in SOC Operations</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#security-architects">Security Architects</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/security-architects.html">Security Architects Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#executive-summary-for-security-architects">Executive Summary for Security Architects</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-platform-capabilities-for-security-architecture">Advanced Platform Capabilities for Security Architecture</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#enterprise-security-architecture-methodology">Enterprise Security Architecture Methodology</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-risk-assessment-and-quantitative-analysis-framework">Advanced Risk Assessment and Quantitative Analysis Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-security-architecture-design-and-implementation">Advanced Security Architecture Design and Implementation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#comprehensive-compliance-and-governance-framework">Comprehensive Compliance and Governance Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-analytics-and-executive-communication-framework">Advanced Analytics and Executive Communication Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#enterprise-security-architecture-best-practices-and-excellence">Enterprise Security Architecture Best Practices and Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#professional-development-and-support-resources">Professional Development and Support Resources</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#conclusion-excellence-in-security-architecture-leadership">Conclusion: Excellence in Security Architecture Leadership</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#red-team-members">Red Team Members</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/red-team-members.html">Red Team Members Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#executive-summary-for-red-team-operations">Executive Summary for Red Team Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#role-definition-and-offensive-security-responsibilities">Role Definition and Offensive Security Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-red-team-platform-capabilities">Advanced Red Team Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#comprehensive-red-team-methodology-and-campaign-framework">Comprehensive Red Team Methodology and Campaign Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-attack-path-discovery-and-exploitation-framework">Advanced Attack Path Discovery and Exploitation Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-attack-simulation-and-specialized-testing-scenarios">Advanced Attack Simulation and Specialized Testing Scenarios</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-reporting-and-performance-metrics-framework">Advanced Reporting and Performance Metrics Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#red-team-best-practices-and-operational-excellence">Red Team Best Practices and Operational Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-troubleshooting-and-technical-support">Advanced Troubleshooting and Technical Support</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#conclusion-excellence-in-red-team-operations">Conclusion: Excellence in Red Team Operations</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#purple-team-members">Purple Team Members</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/purple-team-members.html">Purple Team Members Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#executive-summary-for-purple-team-excellence">Executive Summary for Purple Team Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#advanced-purple-team-platform-capabilities">Advanced Purple Team Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#comprehensive-purple-team-methodology-and-framework">Comprehensive Purple Team Methodology and Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#advanced-detection-engineering-and-security-control-optimization">Advanced Detection Engineering and Security Control Optimization</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#comprehensive-purple-team-scenarios-and-implementation-framework">Comprehensive Purple Team Scenarios and Implementation Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#advanced-troubleshooting-and-technical-support-framework">Advanced Troubleshooting and Technical Support Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#conclusion-excellence-in-purple-team-operations-and-collaborative-security">Conclusion: Excellence in Purple Team Operations and Collaborative Security</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#compliance-officers">Compliance Officers</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/compliance-officers.html">Compliance Officers Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#executive-summary-for-compliance-excellence">Executive Summary for Compliance Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-compliance-platform-capabilities">Advanced Compliance Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-regulatory-framework-management">Advanced Regulatory Framework Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#automated-compliance-monitoring-and-management">Automated Compliance Monitoring and Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#comprehensive-audit-management-and-preparation">Comprehensive Audit Management and Preparation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-risk-assessment-and-management-framework">Advanced Risk Assessment and Management Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#cross-functional-collaboration-and-integration">Cross-Functional Collaboration and Integration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#professional-development-and-excellence-framework">Professional Development and Excellence Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-support-and-troubleshooting-framework">Advanced Support and Troubleshooting Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#conclusion-excellence-in-compliance-and-governance">Conclusion: Excellence in Compliance and Governance</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#executive-leadership">Executive Leadership</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/executive-leadership.html">Executive Leadership Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#executive-summary-for-strategic-security-leadership">Executive Summary for Strategic Security Leadership</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#advanced-executive-security-platform-capabilities">Advanced Executive Security Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#strategic-security-risk-management-framework">Strategic Security Risk Management Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#business-aligned-security-performance-management">Business-Aligned Security Performance Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#strategic-security-investment-and-resource-management">Strategic Security Investment and Resource Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#crisis-management-and-business-continuity">Crisis Management and Business Continuity</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#regulatory-compliance-and-governance-excellence">Regulatory Compliance and Governance Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#strategic-security-communication-and-stakeholder-management">Strategic Security Communication and Stakeholder Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#digital-transformation-and-innovation-enablement">Digital Transformation and Innovation Enablement</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#professional-development-and-executive-excellence">Professional Development and Executive Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#advanced-support-and-advisory-services">Advanced Support and Advisory Services</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#conclusion-excellence-in-executive-security-leadership">Conclusion: Excellence in Executive Security Leadership</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#administrators">Administrators</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/administrators.html">Administrators Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#dashboard-overview">Dashboard Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#getting-started">Getting Started</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#user-and-access-management">User and Access Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#system-configuration-and-management">System Configuration and Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#monitoring-and-maintenance">Monitoring and Maintenance</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#security-and-compliance">Security and Compliance</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#platform-updates-and-maintenance">Platform Updates and Maintenance</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#troubleshooting-and-support">Troubleshooting and Support</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#best-practices-for-platform-administration">Best Practices for Platform Administration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#feature-specific-guides">Feature-Specific Guides</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#attack-path-analysis">Attack Path Analysis</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/attack-path-analysis.html">Attack Path Analysis User Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#getting-started">Getting Started</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#core-concepts">Core Concepts</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#using-the-web-interface">Using the Web Interface</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#using-the-api">Using the API</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#use-cases-and-workflows">Use Cases and Workflows</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#best-practices">Best Practices</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#threat-modeling">Threat Modeling</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#asset-discovery-and-management">Asset Discovery and Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#threat-intelligence-integration">Threat Intelligence Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#common-workflows">Common Workflows</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#security-assessment-workflow">Security Assessment Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#incident-response-workflow">Incident Response Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#red-team-exercise-workflow">Red Team Exercise Workflow</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#general-guidelines">General Guidelines</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#role-specific-tips">Role-Specific Tips</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#getting-help">Getting Help</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#support-resources">Support Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#training-and-certification">Training and Certification</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#feedback-and-contributions">Feedback and Contributions</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#base-url">Base URL</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#api-endpoints">API Endpoints</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#authentication-user-management">Authentication &amp; User Management</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/authentication.html">Authentication API</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#authentication-flow">Authentication Flow</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#security-and-authorization">Security and Authorization</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#integration-examples">Integration Examples</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#best-practices">Best Practices</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#asset-management">Asset Management</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/asset-management.html">Asset Management API</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-crud-operations">Asset CRUD Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-discovery">Asset Discovery</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#bulk-operations">Bulk Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-relationships">Asset Relationships</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-tags-and-metadata">Asset Tags and Metadata</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#error-handling">Error Handling</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#sdk-examples">SDK Examples</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#attack-path-analysis">Attack Path Analysis</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/attack-path-analysis.html">Attack Path Analysis API</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#endpoints">Endpoints</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#error-responses">Error Responses</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#performance-considerations">Performance Considerations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#best-practices">Best Practices</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#examples">Examples</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#threat-modeling-risk-assessment">Threat Modeling &amp; Risk Assessment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/threat-modeling.html">Threat Modeling API Reference</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#threat-actor-simulation">Threat Actor Simulation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#risk-assessment">Risk Assessment</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#compliance-impact-analysis">Compliance Impact Analysis</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#mitigation-strategies">Mitigation Strategies</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#continuous-monitoring">Continuous Monitoring</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#error-handling">Error Handling</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#rate-limiting">Rate Limiting</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/mitre-attack-integration.html">MITRE ATT&amp;CK Integration API Reference</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#att-ck-data-management">ATT&amp;CK Data Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#technique-correlation">Technique Correlation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#threat-actor-attribution">Threat Actor Attribution</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#attack-pattern-analysis">Attack Pattern Analysis</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#att-ck-navigator-integration">ATT&amp;CK Navigator Integration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#threat-intelligence-enrichment">Threat Intelligence Enrichment</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#error-handling">Error Handling</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#rate-limiting">Rate Limiting</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#threat-intelligence">Threat Intelligence</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#monitoring-dashboards">Monitoring &amp; Dashboards</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#integrations">Integrations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#common-response-formats">Common Response Formats</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#success-response">Success Response</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#error-response">Error Response</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#http-status-codes">HTTP Status Codes</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#pagination">Pagination</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#filtering-and-sorting">Filtering and Sorting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#field-selection">Field Selection</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#webhooks">Webhooks</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#sdk-and-libraries">SDK and Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#testing-and-development">Testing and Development</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#support-and-resources">Support and Resources</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#examples">Examples</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Development</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#getting-started">Getting Started</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#ways-to-contribute">Ways to Contribute</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#before-you-start">Before You Start</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#contribution-workflow">Contribution Workflow</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-1-find-or-create-an-issue">Step 1: Find or Create an Issue</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-2-fork-and-clone">Step 2: Fork and Clone</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-3-create-a-branch">Step 3: Create a Branch</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-4-make-changes">Step 4: Make Changes</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-5-test-your-changes">Step 5: Test Your Changes</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-6-submit-pull-request">Step 6: Submit Pull Request</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#code-standards">Code Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#python-code-style">Python Code Style</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#javascript-typescript-style">JavaScript/TypeScript Style</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#testing-guidelines">Testing Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#test-structure">Test Structure</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#documentation-standards">Documentation Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#documentation-types">Documentation Types</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#writing-guidelines">Writing Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#review-process">Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#code-review-checklist">Code Review Checklist</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#review-guidelines">Review Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#community-guidelines">Community Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#code-of-conduct">Code of Conduct</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#communication">Communication</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#recognition">Recognition</a></li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#getting-help">Getting Help</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Use Cases</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="asset-discovery.html">Asset Discovery Use Cases</a><ul>
<li class="toctree-l2"><a class="reference internal" href="asset-discovery.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="asset-discovery.html#discovery-methods">Discovery Methods</a><ul>
<li class="toctree-l3"><a class="reference internal" href="asset-discovery.html#cloud-provider-discovery">Cloud Provider Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="asset-discovery.html#network-based-discovery">Network-Based Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="asset-discovery.html#api-discovery">API Discovery</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="asset-discovery.html#use-case-scenarios">Use Case Scenarios</a><ul>
<li class="toctree-l3"><a class="reference internal" href="asset-discovery.html#scenario-1-multi-cloud-asset-inventory">Scenario 1: Multi-Cloud Asset Inventory</a></li>
<li class="toctree-l3"><a class="reference internal" href="asset-discovery.html#scenario-2-continuous-asset-monitoring">Scenario 2: Continuous Asset Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="asset-discovery.html#scenario-3-compliance-asset-reporting">Scenario 3: Compliance Asset Reporting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="asset-discovery.html#advanced-discovery-features">Advanced Discovery Features</a><ul>
<li class="toctree-l3"><a class="reference internal" href="asset-discovery.html#agent-based-discovery">Agent-Based Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="asset-discovery.html#container-discovery">Container Discovery</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="asset-discovery.html#discovery-automation">Discovery Automation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="asset-discovery.html#scheduled-discovery">Scheduled Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="asset-discovery.html#integration-with-external-tools">Integration with External Tools</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="asset-discovery.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="asset-discovery.html#discovery-strategy">Discovery Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="asset-discovery.html#performance-optimization">Performance Optimization</a></li>
<li class="toctree-l3"><a class="reference internal" href="asset-discovery.html#security-considerations">Security Considerations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="asset-discovery.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="asset-discovery.html#common-issues">Common Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="asset-discovery.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Attack Path Analysis Use Cases</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="#key-capabilities">Key Capabilities</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#analysis-types">Analysis Types</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#use-case-scenarios">Use Case Scenarios</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#scenario-1-red-team-attack-simulation">Scenario 1: Red Team Attack Simulation</a></li>
<li class="toctree-l3"><a class="reference internal" href="#scenario-2-purple-team-validation-exercise">Scenario 2: Purple Team Validation Exercise</a></li>
<li class="toctree-l3"><a class="reference internal" href="#scenario-3-compliance-risk-assessment">Scenario 3: Compliance Risk Assessment</a></li>
<li class="toctree-l3"><a class="reference internal" href="#scenario-4-incident-response-planning">Scenario 4: Incident Response Planning</a></li>
<li class="toctree-l3"><a class="reference internal" href="#scenario-5-zero-trust-architecture-planning">Scenario 5: Zero Trust Architecture Planning</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#advanced-analysis-features">Advanced Analysis Features</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#risk-scoring-and-prioritization">Risk Scoring and Prioritization</a></li>
<li class="toctree-l3"><a class="reference internal" href="#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#integration-and-automation">Integration and Automation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#siem-integration">SIEM Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="#soar-integration">SOAR Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#analysis-guidelines">Analysis Guidelines</a></li>
<li class="toctree-l3"><a class="reference internal" href="#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Architecture</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../architecture/zero-trust-architecture.html">Zero-Trust Architecture Implementation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#identity-verification-service">Identity Verification Service</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#service-account-management">Service Account Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#tls-1-3-enforcement">TLS 1.3+ Enforcement</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#real-time-security-event-correlation">Real-Time Security Event Correlation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#network-security">Network Security</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#kubernetes-network-policies">Kubernetes Network Policies</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#rbac-implementation">RBAC Implementation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#data-protection">Data Protection</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#encryption-implementation">Encryption Implementation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#data-classification">Data Classification</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#monitoring-and-alerting">Monitoring and Alerting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#security-monitoring">Security Monitoring</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#implementation-guidelines">Implementation Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#deployment-checklist">Deployment Checklist</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#security-validation">Security Validation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#security-principles">Security Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#implementation-tips">Implementation Tips</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../deployment/production-architecture.html">Production Architecture Overview</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#kubernetes-infrastructure">Kubernetes Infrastructure</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#application-architecture">Application Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#data-architecture">Data Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#network-architecture">Network Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#security-architecture">Security Architecture</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#zero-trust-security-implementation">Zero-Trust Security Implementation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#monitoring-architecture">Monitoring Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#deployment-strategy">Deployment Strategy</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#blue-green-deployment">Blue-Green Deployment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#infrastructure-as-code">Infrastructure as Code</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#performance-specifications">Performance Specifications</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#scalability-metrics">Scalability Metrics</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#resource-requirements">Resource Requirements</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#disaster-recovery">Disaster Recovery</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#backup-and-recovery-strategy">Backup and Recovery Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#high-availability-design">High Availability Design</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#operational-excellence">Operational Excellence</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#security-best-practices">Security Best Practices</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Security &amp; Compliance</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../security/enhanced-audit-logging.html">Enhanced Audit Logging System</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-event-structure">Audit Event Structure</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#tamper-proof-audit-chain">Tamper-Proof Audit Chain</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#event-enrichment-pipeline">Event Enrichment Pipeline</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#real-time-monitoring">Real-Time Monitoring</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#compliance-integration">Compliance Integration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#framework-mapping">Framework Mapping</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#compliance-reporting">Compliance Reporting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-service-architecture">Audit Service Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#database-schema">Database Schema</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#configuration">Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#monitoring-and-alerting">Monitoring and Alerting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-monitoring-dashboard">Audit Monitoring Dashboard</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#alert-configuration">Alert Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-strategy">Audit Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#operational-guidelines">Operational Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#performance-tuning">Performance Tuning</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../security/incident-response-procedures.html">Incident Response Procedures - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#incident-classification">Incident Classification</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#severity-levels">Severity Levels</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#critical-p0">Critical (P0)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#high-p1">High (P1)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#medium-p2">Medium (P2)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#low-p3">Low (P3)</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#incident-response-team">Incident Response Team</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#roles-and-responsibilities">Roles and Responsibilities</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#incident-commander-ic">Incident Commander (IC)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#technical-lead">Technical Lead</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#security-lead">Security Lead</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#communications-lead">Communications Lead</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#incident-response-process">Incident Response Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-1-detection-and-initial-response-0-15-minutes">Phase 1: Detection and Initial Response (0-15 minutes)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#incident-detection">1.1 Incident Detection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#initial-assessment">1.2 Initial Assessment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#team-notification">1.3 Team Notification</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-2-investigation-and-containment-15-minutes-2-hours">Phase 2: Investigation and Containment (15 minutes - 2 hours)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#incident-war-room-setup">2.1 Incident War Room Setup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#technical-investigation">2.2 Technical Investigation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#security-assessment-if-applicable">2.3 Security Assessment (if applicable)</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-3-resolution-and-recovery-2-8-hours">Phase 3: Resolution and Recovery (2-8 hours)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#fix-implementation">3.1 Fix Implementation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#system-recovery">3.2 System Recovery</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-4-communication-and-documentation-ongoing">Phase 4: Communication and Documentation (Ongoing)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#status-updates">4.1 Status Updates</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#resolution-communication">4.2 Resolution Communication</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-5-post-incident-review-24-72-hours-after-resolution">Phase 5: Post-Incident Review (24-72 hours after resolution)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#post-mortem-meeting">5.1 Post-Mortem Meeting</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#documentation">5.2 Documentation</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#security-specific-procedures">Security-Specific Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#data-breach-response">Data Breach Response</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#immediate-actions-0-1-hour">Immediate Actions (0-1 hour)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#notification-requirements">Notification Requirements</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#vulnerability-response">Vulnerability Response</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#critical-vulnerabilities-cvss-9-0">Critical Vulnerabilities (CVSS 9.0+)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#high-vulnerabilities-cvss-7-0-8-9">High Vulnerabilities (CVSS 7.0-8.9)</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#emergency-contacts">Emergency Contacts</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#internal-team">Internal Team</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#external-contacts">External Contacts</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#tools-and-resources">Tools and Resources</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#monitoring-and-alerting">Monitoring and Alerting</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#communication">Communication</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#id1">Documentation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#training-and-drills">Training and Drills</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#regular-training">Regular Training</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#documentation-updates">Documentation Updates</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../security/security-review-processes.html">Security Review Processes - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#security-review-framework">Security Review Framework</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-types">Review Types</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#code-security-review">1. Code Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#infrastructure-security-review">2. Infrastructure Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#architecture-security-review">3. Architecture Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#compliance-review">4. Compliance Review</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#code-security-review-process">Code Security Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#pre-review-checklist">Pre-Review Checklist</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#developer-responsibilities">Developer Responsibilities</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-criteria">Review Criteria</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#input-validation-and-sanitization">1. Input Validation and Sanitization</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#authentication-and-authorization">2. Authentication and Authorization</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#data-protection">3. Data Protection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#error-handling-and-logging">4. Error Handling and Logging</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#cryptography-and-secrets">5. Cryptography and Secrets</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-process-workflow">Review Process Workflow</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#step-1-automated-security-checks">Step 1: Automated Security Checks</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#step-2-manual-security-review">Step 2: Manual Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#step-3-security-testing">Step 3: Security Testing</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#id1">Infrastructure Security Review</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#infrastructure-as-code-iac-review">Infrastructure as Code (IaC) Review</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#terraform-security-checklist">Terraform Security Checklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#example-review-process">Example Review Process</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#kubernetes-security-review">Kubernetes Security Review</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#manifest-security-checklist">Manifest Security Checklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#example-security-policies">Example Security Policies</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#id2">Architecture Security Review</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#design-review-process">Design Review Process</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#threat-modeling">1. Threat Modeling</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#security-architecture-patterns">2. Security Architecture Patterns</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#integration-security">3. Integration Security</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-documentation-template">Review Documentation Template</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#compliance-review-process">Compliance Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#regulatory-requirements">Regulatory Requirements</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#gdpr-compliance-checklist">GDPR Compliance Checklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#soc-2-compliance-checklist">SOC 2 Compliance Checklist</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#compliance-review-workflow">Compliance Review Workflow</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#change-impact-assessment">1. Change Impact Assessment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#documentation-requirements">2. Documentation Requirements</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#security-review-tools-and-automation">Security Review Tools and Automation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#automated-security-scanning">Automated Security Scanning</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#security-metrics-and-kpis">Security Metrics and KPIs</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#training-and-awareness">Training and Awareness</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#security-review-training">Security Review Training</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#documentation-and-resources">Documentation and Resources</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html">GDPR Compliance Framework</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-subject-rights-management">Data Subject Rights Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#consent-management-system">Consent Management System</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-processing-records">Data Processing Records</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-breach-management">Data Breach Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#privacy-impact-assessment">Privacy Impact Assessment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#gdpr-service-architecture">GDPR Service Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-processing-workflows">Data Processing Workflows</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#compliance-monitoring">Compliance Monitoring</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#automated-compliance-checks">Automated Compliance Checks</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#compliance-reporting">Compliance Reporting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#privacy-by-design-implementation">Privacy by Design Implementation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#operational-guidelines">Operational Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#technical-implementation">Technical Implementation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#database-schema">Database Schema</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#configuration-example">Configuration Example</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../access-control/least-privilege-framework.html">Least Privilege Access Control Framework</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-request-management">Access Request Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#just-in-time-access-provisioning">Just-in-Time Access Provisioning</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#privilege-escalation-management">Privilege Escalation Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-review-and-certification">Access Review and Certification</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-control-architecture">Access Control Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#role-based-access-templates">Role-Based Access Templates</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#risk-assessment-engine">Risk Assessment Engine</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#monitoring-and-analytics">Monitoring and Analytics</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-usage-monitoring">Access Usage Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-analytics-dashboard">Access Analytics Dashboard</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#configuration-management">Configuration Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#policy-configuration">Policy Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#role-template-configuration">Role Template Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-management-principles">Access Management Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#operational-guidelines">Operational Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../security/index.html">Security Documentation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/security-summary.html">Security Documentation Summary</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#executive-overview">Executive Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#security-documentation-structure">Security Documentation Structure</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#recent-security-achievements">Recent Security Achievements</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#june-2025-security-review-results">June 2025 Security Review Results</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#key-security-fixes-implemented">Key Security Fixes Implemented</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#security-architecture-excellence">Security Architecture Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#multi-layered-security-approach">Multi-Layered Security Approach</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#security-testing-excellence">Security Testing Excellence</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#compliance-and-regulatory-excellence">Compliance and Regulatory Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#regulatory-compliance-status">Regulatory Compliance Status</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#industry-standards-compliance">Industry Standards Compliance</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#security-operations-excellence">Security Operations Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#vulnerability-management-program">Vulnerability Management Program</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#security-monitoring-and-incident-response">Security Monitoring and Incident Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#risk-management-excellence">Risk Management Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#enterprise-risk-assessment">Enterprise Risk Assessment</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#business-impact-and-value">Business Impact and Value</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#security-investment-roi">Security Investment ROI</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#future-security-roadmap">Future Security Roadmap</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#short-term-objectives-q3-q4-2025">Short-Term Objectives (Q3-Q4 2025)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#long-term-strategic-goals-2026">Long-Term Strategic Goals (2026)</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#overview">Overview</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/index.html#security-principles">Security Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/index.html#security-framework">Security Framework</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/index.html#security-architecture">Security Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#security-documentation-sections">Security Documentation Sections</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#security-certifications-and-compliance">Security Certifications and Compliance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#security-contact-information">Security Contact Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#quick-security-reference">Quick Security Reference</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Deployment &amp; Operations</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../deployment/production-deployment-guide.html">Production Deployment Guide - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#prerequisites">Prerequisites</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#infrastructure-requirements">Infrastructure Requirements</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#minimum-system-requirements">Minimum System Requirements</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#network-requirements">Network Requirements</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#external-dependencies">External Dependencies</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#required-tools-and-access">Required Tools and Access</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#development-tools">Development Tools</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#aws-access-requirements">AWS Access Requirements</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#infrastructure-deployment">Infrastructure Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-1-environment-configuration">Step 1: Environment Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-aws-cli">1.1 Configure AWS CLI</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#set-environment-variables">1.2 Set Environment Variables</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-2-infrastructure-provisioning">Step 2: Infrastructure Provisioning</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#deploy-core-infrastructure">2.1 Deploy Core Infrastructure</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-kubectl-access">2.2 Configure kubectl Access</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#install-cluster-add-ons">2.3 Install Cluster Add-ons</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-3-database-setup">Step 3: Database Setup</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-initialization">3.1 Database Initialization</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-migration">3.2 Database Migration</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#application-deployment">Application Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-4-container-image-preparation">Step 4: Container Image Preparation</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#build-and-push-images">4.1 Build and Push Images</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-5-kubernetes-deployment">Step 5: Kubernetes Deployment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#create-namespace-and-secrets">5.1 Create Namespace and Secrets</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#deploy-application-components">5.2 Deploy Application Components</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-ingress-and-load-balancer">5.3 Configure Ingress and Load Balancer</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-6-ssl-tls-configuration">Step 6: SSL/TLS Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#request-ssl-certificate">6.1 Request SSL Certificate</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-https-redirect">6.2 Configure HTTPS Redirect</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#post-deployment-configuration">Post-Deployment Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-7-application-configuration">Step 7: Application Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#initialize-application-data">7.1 Initialize Application Data</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-monitoring-and-alerting">7.2 Configure Monitoring and Alerting</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-8-security-hardening">Step 8: Security Hardening</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#network-policies">8.1 Network Policies</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#pod-security-policies">8.2 Pod Security Policies</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#verification-and-testing">Verification and Testing</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-9-deployment-verification">Step 9: Deployment Verification</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#health-checks">9.1 Health Checks</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#functional-testing">9.2 Functional Testing</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#security-testing">9.3 Security Testing</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#backup-and-recovery-setup">Backup and Recovery Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-10-backup-configuration">Step 10: Backup Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-backup">10.1 Database Backup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#application-data-backup">10.2 Application Data Backup</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#monitoring-and-maintenance">Monitoring and Maintenance</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-11-ongoing-monitoring">Step 11: Ongoing Monitoring</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#set-up-alerts">11.1 Set Up Alerts</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#performance-monitoring">11.2 Performance Monitoring</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#troubleshooting-common-issues">Troubleshooting Common Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-connection-issues">Database Connection Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#pod-startup-issues">Pod Startup Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#load-balancer-issues">Load Balancer Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#rollback-procedures">Rollback Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#emergency-rollback">Emergency Rollback</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-rollback">Database Rollback</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../deployment/environment-setup.html">Environment Setup Documentation - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#environment-types">Environment Types</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#development-environment">Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#staging-environment">Staging Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#local-development-setup">Local Development Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#prerequisites">Prerequisites</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#system-requirements">System Requirements</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#required-software">Required Software</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#development-environment-setup">Development Environment Setup</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#clone-repository">1. Clone Repository</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#environment-configuration">2. Environment Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#backend-setup">3. Backend Setup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#frontend-setup">4. Frontend Setup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#start-development-services">5. Start Development Services</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#verify-development-setup">6. Verify Development Setup</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#staging-environment-setup">Staging Environment Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#infrastructure-provisioning">Infrastructure Provisioning</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#aws-configuration">1. AWS Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#terraform-deployment">2. Terraform Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#kubernetes-configuration">3. Kubernetes Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#application-deployment">Application Deployment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#build-and-push-images">1. Build and Push Images</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#deploy-to-staging">2. Deploy to Staging</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#configure-staging-data">3. Configure Staging Data</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#production-environment-setup">Production Environment Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#pre-production-checklist">Pre-Production Checklist</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#security-review">Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#performance-review">Performance Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#operational-readiness">Operational Readiness</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#production-deployment-process">Production Deployment Process</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#final-infrastructure-review">1. Final Infrastructure Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#production-image-preparation">2. Production Image Preparation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#blue-green-deployment">3. Blue-Green Deployment</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#environment-specific-configurations">Environment-Specific Configurations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#development-configuration">Development Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#staging-configuration">Staging Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#production-configuration">Production Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#ci-cd-pipeline-configuration">CI/CD Pipeline Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#github-actions-workflow">GitHub Actions Workflow</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#environment-management-scripts">Environment Management Scripts</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#deployment-script">Deployment Script</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#environment-cleanup-script">Environment Cleanup Script</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../deployment/troubleshooting-guide.html">Troubleshooting Guide - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#general-troubleshooting-approach">General Troubleshooting Approach</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#information-gathering">1. Information Gathering</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#log-analysis">2. Log Analysis</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#network-connectivity">3. Network Connectivity</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#common-issues-and-solutions">Common Issues and Solutions</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#application-startup-issues">Application Startup Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-pod-stuck-in-pending-state">Issue: Pod Stuck in Pending State</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-pod-stuck-in-crashloopbackoff">Issue: Pod Stuck in CrashLoopBackOff</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#database-issues">Database Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-database-connection-timeout">Issue: Database Connection Timeout</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-database-migration-failures">Issue: Database Migration Failures</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#network-and-connectivity-issues">Network and Connectivity Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-service-not-accessible">Issue: Service Not Accessible</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-ssl-tls-certificate-problems">Issue: SSL/TLS Certificate Problems</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#performance-issues">Performance Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-high-response-times">Issue: High Response Times</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-memory-leaks">Issue: Memory Leaks</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#storage-issues">Storage Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-persistent-volume-problems">Issue: Persistent Volume Problems</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#monitoring-and-alerting-issues">Monitoring and Alerting Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-missing-metrics">Issue: Missing Metrics</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#emergency-procedures">Emergency Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#complete-system-recovery">Complete System Recovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#rollback-procedure">Rollback Procedure</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#getting-help">Getting Help</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#internal-resources">Internal Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#external-support">External Support</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#escalation-procedures">Escalation Procedures</a></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content style-external-links">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Attack Path Analysis Use Cases</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/use-cases/attack-path-analysis.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="attack-path-analysis-use-cases">
<h1>Attack Path Analysis Use Cases<a class="headerlink" href="#attack-path-analysis-use-cases" title="Link to this heading"></a></h1>
<p>Comprehensive guide to attack path analysis capabilities in the Blast-Radius Security Tool, covering threat modeling, risk assessment, and security validation scenarios.</p>
<nav class="contents local" id="table-of-contents">
<p class="topic-title">Table of Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#overview" id="id7">Overview</a></p></li>
<li><p><a class="reference internal" href="#key-capabilities" id="id8">Key Capabilities</a></p>
<ul>
<li><p><a class="reference internal" href="#analysis-types" id="id9">Analysis Types</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#use-case-scenarios" id="id10">Use Case Scenarios</a></p>
<ul>
<li><p><a class="reference internal" href="#scenario-1-red-team-attack-simulation" id="id11">Scenario 1: Red Team Attack Simulation</a></p></li>
<li><p><a class="reference internal" href="#scenario-2-purple-team-validation-exercise" id="id12">Scenario 2: Purple Team Validation Exercise</a></p></li>
<li><p><a class="reference internal" href="#scenario-3-compliance-risk-assessment" id="id13">Scenario 3: Compliance Risk Assessment</a></p></li>
<li><p><a class="reference internal" href="#scenario-4-incident-response-planning" id="id14">Scenario 4: Incident Response Planning</a></p></li>
<li><p><a class="reference internal" href="#scenario-5-zero-trust-architecture-planning" id="id15">Scenario 5: Zero Trust Architecture Planning</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#advanced-analysis-features" id="id16">Advanced Analysis Features</a></p>
<ul>
<li><p><a class="reference internal" href="#risk-scoring-and-prioritization" id="id17">Risk Scoring and Prioritization</a></p></li>
<li><p><a class="reference internal" href="#mitre-att-ck-integration" id="id18">MITRE ATT&amp;CK Integration</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#integration-and-automation" id="id19">Integration and Automation</a></p>
<ul>
<li><p><a class="reference internal" href="#siem-integration" id="id20">SIEM Integration</a></p></li>
<li><p><a class="reference internal" href="#soar-integration" id="id21">SOAR Integration</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#best-practices" id="id22">Best Practices</a></p>
<ul>
<li><p><a class="reference internal" href="#analysis-guidelines" id="id23">Analysis Guidelines</a></p></li>
<li><p><a class="reference internal" href="#performance-optimization" id="id24">Performance Optimization</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#next-steps" id="id25">Next Steps</a></p></li>
</ul>
</nav>
<section id="overview">
<h2><a class="toc-backref" href="#id7" role="doc-backlink">Overview</a><a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>Attack path analysis is the core capability of the Blast-Radius Security Tool, providing:</p>
<ul class="simple">
<li><p><strong>Graph-based Analysis</strong> - Visualize complex attack scenarios across your infrastructure</p></li>
<li><p><strong>MITRE ATT&amp;CK Integration</strong> - Map attack paths to real-world threat techniques</p></li>
<li><p><strong>Risk Quantification</strong> - Calculate blast radius and impact assessment</p></li>
<li><p><strong>Threat Modeling</strong> - Proactive security analysis and validation</p></li>
<li><p><strong>Compliance Support</strong> - Evidence for security frameworks and audits</p></li>
</ul>
</section>
<section id="key-capabilities">
<h2><a class="toc-backref" href="#id8" role="doc-backlink">Key Capabilities</a><a class="headerlink" href="#key-capabilities" title="Link to this heading"></a></h2>
<section id="analysis-types">
<h3><a class="toc-backref" href="#id9" role="doc-backlink">Analysis Types</a><a class="headerlink" href="#analysis-types" title="Link to this heading"></a></h3>
<p><strong>1. Point-to-Point Analysis</strong>
- Analyze attack paths between specific assets
- Identify shortest and highest-risk paths
- Calculate blast radius from compromised assets</p>
<p><strong>2. Multi-Target Analysis</strong>
- Analyze paths from one source to multiple targets
- Identify critical chokepoints and vulnerabilities
- Prioritize security controls and mitigations</p>
<p><strong>3. Lateral Movement Analysis</strong>
- Map potential lateral movement scenarios
- Identify privilege escalation opportunities
- Analyze network segmentation effectiveness</p>
<p><strong>4. Crown Jewel Protection</strong>
- Analyze paths to critical business assets
- Validate security controls around sensitive data
- Assess impact of potential breaches</p>
</section>
</section>
<section id="use-case-scenarios">
<h2><a class="toc-backref" href="#id10" role="doc-backlink">Use Case Scenarios</a><a class="headerlink" href="#use-case-scenarios" title="Link to this heading"></a></h2>
<section id="scenario-1-red-team-attack-simulation">
<h3><a class="toc-backref" href="#id11" role="doc-backlink">Scenario 1: Red Team Attack Simulation</a><a class="headerlink" href="#scenario-1-red-team-attack-simulation" title="Link to this heading"></a></h3>
<p><strong>Business Context:</strong>
Red team needs to identify realistic attack paths for penetration testing and security validation exercises.</p>
<p><strong>Implementation:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Red team attack path discovery</span>
<span class="kn">from</span> <span class="nn">app.services.attack_path_service</span> <span class="kn">import</span> <span class="n">AttackPathService</span>
<span class="kn">from</span> <span class="nn">app.models.asset</span> <span class="kn">import</span> <span class="n">Asset</span>

<span class="c1"># Initialize attack path service</span>
<span class="n">attack_service</span> <span class="o">=</span> <span class="n">AttackPathService</span><span class="p">()</span>

<span class="c1"># Define attack scenario</span>
<span class="n">external_asset</span> <span class="o">=</span> <span class="n">Asset</span><span class="o">.</span><span class="n">get_by_name</span><span class="p">(</span><span class="s2">&quot;external-web-server&quot;</span><span class="p">)</span>
<span class="n">crown_jewel</span> <span class="o">=</span> <span class="n">Asset</span><span class="o">.</span><span class="n">get_by_name</span><span class="p">(</span><span class="s2">&quot;customer-database&quot;</span><span class="p">)</span>

<span class="c1"># Analyze attack paths</span>
<span class="n">attack_paths</span> <span class="o">=</span> <span class="n">attack_service</span><span class="o">.</span><span class="n">analyze_attack_paths</span><span class="p">(</span>
    <span class="n">source</span><span class="o">=</span><span class="n">external_asset</span><span class="p">,</span>
    <span class="n">target</span><span class="o">=</span><span class="n">crown_jewel</span><span class="p">,</span>
    <span class="n">max_depth</span><span class="o">=</span><span class="mi">8</span><span class="p">,</span>
    <span class="n">include_mitre</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="n">attack_types</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;lateral_movement&quot;</span><span class="p">,</span> <span class="s2">&quot;privilege_escalation&quot;</span><span class="p">]</span>
<span class="p">)</span>

<span class="c1"># Generate red team report</span>
<span class="n">red_team_report</span> <span class="o">=</span> <span class="n">attack_service</span><span class="o">.</span><span class="n">generate_red_team_report</span><span class="p">(</span>
    <span class="n">attack_paths</span><span class="o">=</span><span class="n">attack_paths</span><span class="p">,</span>
    <span class="n">include_techniques</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="n">include_tools</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="nb">format</span><span class="o">=</span><span class="s2">&quot;detailed&quot;</span>
<span class="p">)</span>
</pre></div>
</div>
<p><strong>Expected Results:</strong></p>
<table class="docutils align-default" id="id1">
<caption><span class="caption-text">Red Team Analysis Results</span><a class="headerlink" href="#id1" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 30.0%" />
<col style="width: 25.0%" />
<col style="width: 25.0%" />
<col style="width: 20.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Attack Path</p></th>
<th class="head"><p>Risk Score</p></th>
<th class="head"><p>MITRE Techniques</p></th>
<th class="head"><p>Estimated Time</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>Web → App → DB</strong></p></td>
<td><p>8.5/10</p></td>
<td><p>T1190, T1068, T1003</p></td>
<td><p>2-4 hours</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Web → File → Domain → DB</strong></p></td>
<td><p>7.2/10</p></td>
<td><p>T1190, T1021, T1078</p></td>
<td><p>4-8 hours</p></td>
</tr>
<tr class="row-even"><td><p><strong>Web → Email → Workstation → DB</strong></p></td>
<td><p>6.8/10</p></td>
<td><p>T1566, T1204, T1055</p></td>
<td><p>1-2 days</p></td>
</tr>
</tbody>
</table>
<p><strong>Red Team Deliverables:</strong>
- Detailed attack path documentation
- MITRE ATT&amp;CK technique mapping
- Tool and exploit recommendations
- Timeline and complexity estimates
- Defensive recommendations</p>
</section>
<section id="scenario-2-purple-team-validation-exercise">
<h3><a class="toc-backref" href="#id12" role="doc-backlink">Scenario 2: Purple Team Validation Exercise</a><a class="headerlink" href="#scenario-2-purple-team-validation-exercise" title="Link to this heading"></a></h3>
<p><strong>Business Context:</strong>
Purple team collaboration to validate detection capabilities and improve security controls through realistic attack simulation.</p>
<p><strong>Implementation:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Purple team validation workflow</span>
<span class="kn">from</span> <span class="nn">app.services.purple_team_service</span> <span class="kn">import</span> <span class="n">PurpleTeamService</span>
<span class="kn">from</span> <span class="nn">app.integrations.siem</span> <span class="kn">import</span> <span class="n">SIEMIntegration</span>

<span class="n">purple_service</span> <span class="o">=</span> <span class="n">PurpleTeamService</span><span class="p">()</span>
<span class="n">siem</span> <span class="o">=</span> <span class="n">SIEMIntegration</span><span class="p">()</span>

<span class="c1"># Define validation scenario</span>
<span class="n">validation_scenario</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="s2">&quot;Lateral Movement Detection&quot;</span><span class="p">,</span>
    <span class="s2">&quot;source_asset&quot;</span><span class="p">:</span> <span class="s2">&quot;compromised-workstation&quot;</span><span class="p">,</span>
    <span class="s2">&quot;target_assets&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;file-server&quot;</span><span class="p">,</span> <span class="s2">&quot;domain-controller&quot;</span><span class="p">],</span>
    <span class="s2">&quot;techniques&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;T1021.001&quot;</span><span class="p">,</span> <span class="s2">&quot;T1078.002&quot;</span><span class="p">,</span> <span class="s2">&quot;T1003.001&quot;</span><span class="p">],</span>
    <span class="s2">&quot;detection_rules&quot;</span><span class="p">:</span> <span class="p">[</span><span class="s2">&quot;rule_001&quot;</span><span class="p">,</span> <span class="s2">&quot;rule_002&quot;</span><span class="p">,</span> <span class="s2">&quot;rule_003&quot;</span><span class="p">]</span>
<span class="p">}</span>

<span class="c1"># Execute purple team exercise</span>
<span class="n">exercise_results</span> <span class="o">=</span> <span class="n">purple_service</span><span class="o">.</span><span class="n">execute_validation</span><span class="p">(</span>
    <span class="n">scenario</span><span class="o">=</span><span class="n">validation_scenario</span><span class="p">,</span>
    <span class="n">simulate_attacks</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="n">monitor_detections</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="n">duration_hours</span><span class="o">=</span><span class="mi">4</span>
<span class="p">)</span>

<span class="c1"># Analyze detection effectiveness</span>
<span class="n">detection_analysis</span> <span class="o">=</span> <span class="n">purple_service</span><span class="o">.</span><span class="n">analyze_detection_coverage</span><span class="p">(</span>
    <span class="n">attack_paths</span><span class="o">=</span><span class="n">exercise_results</span><span class="o">.</span><span class="n">attack_paths</span><span class="p">,</span>
    <span class="n">siem_alerts</span><span class="o">=</span><span class="n">siem</span><span class="o">.</span><span class="n">get_alerts</span><span class="p">(</span><span class="n">timeframe</span><span class="o">=</span><span class="s2">&quot;4h&quot;</span><span class="p">),</span>
    <span class="n">detection_rules</span><span class="o">=</span><span class="n">validation_scenario</span><span class="p">[</span><span class="s2">&quot;detection_rules&quot;</span><span class="p">]</span>
<span class="p">)</span>
</pre></div>
</div>
<p><strong>Purple Team Metrics:</strong></p>
<table class="docutils align-default" id="id2">
<caption><span class="caption-text">Detection Effectiveness Analysis</span><a class="headerlink" href="#id2" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 25.0%" />
<col style="width: 20.0%" />
<col style="width: 20.0%" />
<col style="width: 35.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>MITRE Technique</p></th>
<th class="head"><p>Simulated</p></th>
<th class="head"><p>Detected</p></th>
<th class="head"><p>Detection Quality</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>T1021.001 (RDP)</strong></p></td>
<td><p>✅ Yes</p></td>
<td><p>✅ Yes</p></td>
<td><p>High confidence, low false positives</p></td>
</tr>
<tr class="row-odd"><td><p><strong>T1078.002 (Domain Accounts)</strong></p></td>
<td><p>✅ Yes</p></td>
<td><p>⚠️ Partial</p></td>
<td><p>Medium confidence, needs tuning</p></td>
</tr>
<tr class="row-even"><td><p><strong>T1003.001 (LSASS)</strong></p></td>
<td><p>✅ Yes</p></td>
<td><p>❌ No</p></td>
<td><p>Missing detection rule</p></td>
</tr>
</tbody>
</table>
<p><strong>Collaboration Workflow:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Purple team collaboration platform</span>
<span class="n">collaboration_results</span> <span class="o">=</span> <span class="n">purple_service</span><span class="o">.</span><span class="n">generate_collaboration_report</span><span class="p">(</span>
    <span class="n">red_team_findings</span><span class="o">=</span><span class="n">exercise_results</span><span class="o">.</span><span class="n">attack_paths</span><span class="p">,</span>
    <span class="n">blue_team_detections</span><span class="o">=</span><span class="n">detection_analysis</span><span class="p">,</span>
    <span class="n">recommendations</span><span class="o">=</span><span class="p">{</span>
        <span class="s2">&quot;immediate&quot;</span><span class="p">:</span> <span class="p">[</span>
            <span class="s2">&quot;Deploy LSASS monitoring rule&quot;</span><span class="p">,</span>
            <span class="s2">&quot;Tune domain account detection thresholds&quot;</span>
        <span class="p">],</span>
        <span class="s2">&quot;short_term&quot;</span><span class="p">:</span> <span class="p">[</span>
            <span class="s2">&quot;Implement network segmentation&quot;</span><span class="p">,</span>
            <span class="s2">&quot;Deploy additional endpoint monitoring&quot;</span>
        <span class="p">],</span>
        <span class="s2">&quot;long_term&quot;</span><span class="p">:</span> <span class="p">[</span>
            <span class="s2">&quot;Zero trust architecture implementation&quot;</span><span class="p">,</span>
            <span class="s2">&quot;Advanced behavioral analytics&quot;</span>
        <span class="p">]</span>
    <span class="p">}</span>
<span class="p">)</span>
</pre></div>
</div>
</section>
<section id="scenario-3-compliance-risk-assessment">
<h3><a class="toc-backref" href="#id13" role="doc-backlink">Scenario 3: Compliance Risk Assessment</a><a class="headerlink" href="#scenario-3-compliance-risk-assessment" title="Link to this heading"></a></h3>
<p><strong>Business Context:</strong>
Organization needs to demonstrate security controls effectiveness for SOC 2, PCI DSS, and ISO 27001 compliance audits.</p>
<p><strong>Implementation:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Compliance-focused attack path analysis</span>
<span class="kn">from</span> <span class="nn">app.services.compliance_service</span> <span class="kn">import</span> <span class="n">ComplianceService</span>
<span class="kn">from</span> <span class="nn">app.models.compliance_framework</span> <span class="kn">import</span> <span class="n">ComplianceFramework</span>

<span class="n">compliance_service</span> <span class="o">=</span> <span class="n">ComplianceService</span><span class="p">()</span>

<span class="c1"># Define compliance scope</span>
<span class="n">pci_scope</span> <span class="o">=</span> <span class="n">compliance_service</span><span class="o">.</span><span class="n">get_compliance_scope</span><span class="p">(</span><span class="s2">&quot;PCI_DSS&quot;</span><span class="p">)</span>
<span class="n">cardholder_data_assets</span> <span class="o">=</span> <span class="n">pci_scope</span><span class="o">.</span><span class="n">get_assets_by_classification</span><span class="p">(</span><span class="s2">&quot;cardholder_data&quot;</span><span class="p">)</span>

<span class="c1"># Analyze attack paths to sensitive data</span>
<span class="n">compliance_analysis</span> <span class="o">=</span> <span class="n">compliance_service</span><span class="o">.</span><span class="n">analyze_compliance_risk</span><span class="p">(</span>
    <span class="n">framework</span><span class="o">=</span><span class="s2">&quot;PCI_DSS&quot;</span><span class="p">,</span>
    <span class="n">sensitive_assets</span><span class="o">=</span><span class="n">cardholder_data_assets</span><span class="p">,</span>
    <span class="n">include_network_segmentation</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="n">include_access_controls</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="n">include_monitoring</span><span class="o">=</span><span class="kc">True</span>
<span class="p">)</span>

<span class="c1"># Generate compliance report</span>
<span class="n">pci_report</span> <span class="o">=</span> <span class="n">compliance_service</span><span class="o">.</span><span class="n">generate_compliance_report</span><span class="p">(</span>
    <span class="n">framework</span><span class="o">=</span><span class="s2">&quot;PCI_DSS&quot;</span><span class="p">,</span>
    <span class="n">analysis_results</span><span class="o">=</span><span class="n">compliance_analysis</span><span class="p">,</span>
    <span class="n">include_evidence</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="n">include_recommendations</span><span class="o">=</span><span class="kc">True</span>
<span class="p">)</span>
</pre></div>
</div>
<p><strong>Compliance Analysis Results:</strong></p>
<table class="docutils align-default" id="id3">
<caption><span class="caption-text">PCI DSS Compliance Assessment</span><a class="headerlink" href="#id3" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 30.0%" />
<col style="width: 25.0%" />
<col style="width: 25.0%" />
<col style="width: 20.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Control Area</p></th>
<th class="head"><p>Risk Level</p></th>
<th class="head"><p>Attack Paths</p></th>
<th class="head"><p>Compliance Status</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>Network Segmentation</strong></p></td>
<td><p>Low</p></td>
<td><p>2 paths found</p></td>
<td><p>✅ Compliant</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Access Controls</strong></p></td>
<td><p>Medium</p></td>
<td><p>5 paths found</p></td>
<td><p>⚠️ Needs improvement</p></td>
</tr>
<tr class="row-even"><td><p><strong>Monitoring &amp; Logging</strong></p></td>
<td><p>High</p></td>
<td><p>8 paths found</p></td>
<td><p>❌ Non-compliant</p></td>
</tr>
</tbody>
</table>
<p><strong>Compliance Deliverables:</strong>
- Executive summary with risk ratings
- Detailed technical findings
- Control effectiveness assessment
- Remediation roadmap with priorities
- Evidence package for auditors</p>
</section>
<section id="scenario-4-incident-response-planning">
<h3><a class="toc-backref" href="#id14" role="doc-backlink">Scenario 4: Incident Response Planning</a><a class="headerlink" href="#scenario-4-incident-response-planning" title="Link to this heading"></a></h3>
<p><strong>Business Context:</strong>
Security team needs to prepare incident response playbooks based on realistic attack scenarios and potential blast radius.</p>
<p><strong>Implementation:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Incident response scenario planning</span>
<span class="kn">from</span> <span class="nn">app.services.incident_response_service</span> <span class="kn">import</span> <span class="n">IncidentResponseService</span>
<span class="kn">from</span> <span class="nn">app.models.incident_scenario</span> <span class="kn">import</span> <span class="n">IncidentScenario</span>

<span class="n">ir_service</span> <span class="o">=</span> <span class="n">IncidentResponseService</span><span class="p">()</span>

<span class="c1"># Define incident scenarios</span>
<span class="n">ransomware_scenario</span> <span class="o">=</span> <span class="n">IncidentScenario</span><span class="p">(</span>
    <span class="n">name</span><span class="o">=</span><span class="s2">&quot;Ransomware Attack&quot;</span><span class="p">,</span>
    <span class="n">initial_compromise</span><span class="o">=</span><span class="s2">&quot;email-server&quot;</span><span class="p">,</span>
    <span class="n">attack_objectives</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;data_encryption&quot;</span><span class="p">,</span> <span class="s2">&quot;data_exfiltration&quot;</span><span class="p">],</span>
    <span class="n">threat_actor_profile</span><span class="o">=</span><span class="s2">&quot;financially_motivated&quot;</span>
<span class="p">)</span>

<span class="c1"># Analyze potential blast radius</span>
<span class="n">blast_radius_analysis</span> <span class="o">=</span> <span class="n">ir_service</span><span class="o">.</span><span class="n">analyze_blast_radius</span><span class="p">(</span>
    <span class="n">scenario</span><span class="o">=</span><span class="n">ransomware_scenario</span><span class="p">,</span>
    <span class="n">include_business_impact</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="n">include_recovery_time</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="n">include_financial_impact</span><span class="o">=</span><span class="kc">True</span>
<span class="p">)</span>

<span class="c1"># Generate incident response playbook</span>
<span class="n">ir_playbook</span> <span class="o">=</span> <span class="n">ir_service</span><span class="o">.</span><span class="n">generate_playbook</span><span class="p">(</span>
    <span class="n">scenario</span><span class="o">=</span><span class="n">ransomware_scenario</span><span class="p">,</span>
    <span class="n">blast_radius</span><span class="o">=</span><span class="n">blast_radius_analysis</span><span class="p">,</span>
    <span class="n">include_containment_steps</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="n">include_communication_plan</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="n">include_recovery_procedures</span><span class="o">=</span><span class="kc">True</span>
<span class="p">)</span>
</pre></div>
</div>
<p><strong>Incident Response Analysis:</strong></p>
<table class="docutils align-default" id="id4">
<caption><span class="caption-text">Blast Radius Assessment</span><a class="headerlink" href="#id4" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 25.0%" />
<col style="width: 25.0%" />
<col style="width: 25.0%" />
<col style="width: 25.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Asset Category</p></th>
<th class="head"><p>Assets at Risk</p></th>
<th class="head"><p>Business Impact</p></th>
<th class="head"><p>Recovery Time</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>Critical Systems</strong></p></td>
<td><p>12 assets</p></td>
<td><p>High</p></td>
<td><p>24-48 hours</p></td>
</tr>
<tr class="row-odd"><td><p><strong>User Workstations</strong></p></td>
<td><p>450 assets</p></td>
<td><p>Medium</p></td>
<td><p>4-8 hours</p></td>
</tr>
<tr class="row-even"><td><p><strong>File Servers</strong></p></td>
<td><p>8 assets</p></td>
<td><p>High</p></td>
<td><p>12-24 hours</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Backup Systems</strong></p></td>
<td><p>3 assets</p></td>
<td><p>Critical</p></td>
<td><p>1-2 hours</p></td>
</tr>
</tbody>
</table>
<p><strong>Playbook Components:</strong>
- Threat detection indicators
- Containment procedures by asset type
- Communication templates
- Recovery prioritization matrix
- Lessons learned framework</p>
</section>
<section id="scenario-5-zero-trust-architecture-planning">
<h3><a class="toc-backref" href="#id15" role="doc-backlink">Scenario 5: Zero Trust Architecture Planning</a><a class="headerlink" href="#scenario-5-zero-trust-architecture-planning" title="Link to this heading"></a></h3>
<p><strong>Business Context:</strong>
Organization is implementing zero trust architecture and needs to understand current trust boundaries and attack paths.</p>
<p><strong>Implementation:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Zero trust analysis</span>
<span class="kn">from</span> <span class="nn">app.services.zero_trust_service</span> <span class="kn">import</span> <span class="n">ZeroTrustService</span>
<span class="kn">from</span> <span class="nn">app.models.trust_boundary</span> <span class="kn">import</span> <span class="n">TrustBoundary</span>

<span class="n">zt_service</span> <span class="o">=</span> <span class="n">ZeroTrustService</span><span class="p">()</span>

<span class="c1"># Analyze current trust boundaries</span>
<span class="n">current_boundaries</span> <span class="o">=</span> <span class="n">zt_service</span><span class="o">.</span><span class="n">identify_trust_boundaries</span><span class="p">(</span>
    <span class="n">include_network_segments</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="n">include_identity_boundaries</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="n">include_application_boundaries</span><span class="o">=</span><span class="kc">True</span>
<span class="p">)</span>

<span class="c1"># Analyze attack paths across trust boundaries</span>
<span class="n">boundary_analysis</span> <span class="o">=</span> <span class="n">zt_service</span><span class="o">.</span><span class="n">analyze_boundary_crossings</span><span class="p">(</span>
    <span class="n">trust_boundaries</span><span class="o">=</span><span class="n">current_boundaries</span><span class="p">,</span>
    <span class="n">include_privilege_escalation</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
    <span class="n">include_lateral_movement</span><span class="o">=</span><span class="kc">True</span>
<span class="p">)</span>

<span class="c1"># Generate zero trust roadmap</span>
<span class="n">zt_roadmap</span> <span class="o">=</span> <span class="n">zt_service</span><span class="o">.</span><span class="n">generate_zero_trust_roadmap</span><span class="p">(</span>
    <span class="n">current_state</span><span class="o">=</span><span class="n">boundary_analysis</span><span class="p">,</span>
    <span class="n">target_maturity</span><span class="o">=</span><span class="s2">&quot;advanced&quot;</span><span class="p">,</span>
    <span class="n">implementation_phases</span><span class="o">=</span><span class="mi">4</span><span class="p">,</span>
    <span class="n">include_technology_recommendations</span><span class="o">=</span><span class="kc">True</span>
<span class="p">)</span>
</pre></div>
</div>
<p><strong>Zero Trust Analysis Results:</strong></p>
<table class="docutils align-default" id="id5">
<caption><span class="caption-text">Trust Boundary Analysis</span><a class="headerlink" href="#id5" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 30.0%" />
<col style="width: 25.0%" />
<col style="width: 25.0%" />
<col style="width: 20.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Trust Boundary</p></th>
<th class="head"><p>Current State</p></th>
<th class="head"><p>Attack Paths</p></th>
<th class="head"><p>ZT Priority</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>Network Perimeter</strong></p></td>
<td><p>Traditional firewall</p></td>
<td><p>15 paths</p></td>
<td><p>High</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Identity Systems</strong></p></td>
<td><p>AD-based</p></td>
<td><p>8 paths</p></td>
<td><p>Critical</p></td>
</tr>
<tr class="row-even"><td><p><strong>Application Access</strong></p></td>
<td><p>VPN-based</p></td>
<td><p>12 paths</p></td>
<td><p>High</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Data Access</strong></p></td>
<td><p>Role-based</p></td>
<td><p>6 paths</p></td>
<td><p>Medium</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="advanced-analysis-features">
<h2><a class="toc-backref" href="#id16" role="doc-backlink">Advanced Analysis Features</a><a class="headerlink" href="#advanced-analysis-features" title="Link to this heading"></a></h2>
<section id="risk-scoring-and-prioritization">
<h3><a class="toc-backref" href="#id17" role="doc-backlink">Risk Scoring and Prioritization</a><a class="headerlink" href="#risk-scoring-and-prioritization" title="Link to this heading"></a></h3>
<p><strong>Multi-Factor Risk Assessment:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Advanced risk scoring</span>
<span class="n">risk_factors</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s2">&quot;asset_criticality&quot;</span><span class="p">:</span> <span class="mf">0.3</span><span class="p">,</span>      <span class="c1"># Business importance</span>
    <span class="s2">&quot;vulnerability_severity&quot;</span><span class="p">:</span> <span class="mf">0.25</span><span class="p">,</span> <span class="c1"># Technical risk</span>
    <span class="s2">&quot;exposure_level&quot;</span><span class="p">:</span> <span class="mf">0.2</span><span class="p">,</span>          <span class="c1"># Attack surface</span>
    <span class="s2">&quot;control_effectiveness&quot;</span><span class="p">:</span> <span class="mf">0.15</span><span class="p">,</span>   <span class="c1"># Security controls</span>
    <span class="s2">&quot;threat_likelihood&quot;</span><span class="p">:</span> <span class="mf">0.1</span>         <span class="c1"># Threat intelligence</span>
<span class="p">}</span>

<span class="k">def</span> <span class="nf">calculate_path_risk</span><span class="p">(</span><span class="n">attack_path</span><span class="p">,</span> <span class="n">risk_factors</span><span class="p">):</span>
    <span class="n">total_risk</span> <span class="o">=</span> <span class="mf">0.0</span>

    <span class="k">for</span> <span class="n">factor</span><span class="p">,</span> <span class="n">weight</span> <span class="ow">in</span> <span class="n">risk_factors</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
        <span class="n">factor_score</span> <span class="o">=</span> <span class="n">attack_path</span><span class="o">.</span><span class="n">get_factor_score</span><span class="p">(</span><span class="n">factor</span><span class="p">)</span>
        <span class="n">total_risk</span> <span class="o">+=</span> <span class="n">factor_score</span> <span class="o">*</span> <span class="n">weight</span>

    <span class="k">return</span> <span class="nb">min</span><span class="p">(</span><span class="n">total_risk</span><span class="p">,</span> <span class="mf">10.0</span><span class="p">)</span>  <span class="c1"># Cap at 10.0</span>
</pre></div>
</div>
<p><strong>Risk Prioritization Matrix:</strong></p>
<table class="docutils align-default" id="id6">
<caption><span class="caption-text">Risk Prioritization Framework</span><a class="headerlink" href="#id6" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 20.0%" />
<col style="width: 20.0%" />
<col style="width: 20.0%" />
<col style="width: 20.0%" />
<col style="width: 20.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Risk Score</p></th>
<th class="head"><p>Priority</p></th>
<th class="head"><p>Response Time</p></th>
<th class="head"><p>Resources</p></th>
<th class="head"><p>Escalation</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>9.0-10.0</strong></p></td>
<td><p>Critical</p></td>
<td><p>Immediate</p></td>
<td><p>All available</p></td>
<td><p>C-level</p></td>
</tr>
<tr class="row-odd"><td><p><strong>7.0-8.9</strong></p></td>
<td><p>High</p></td>
<td><p>24 hours</p></td>
<td><p>Dedicated team</p></td>
<td><p>Security leadership</p></td>
</tr>
<tr class="row-even"><td><p><strong>5.0-6.9</strong></p></td>
<td><p>Medium</p></td>
<td><p>1 week</p></td>
<td><p>Regular team</p></td>
<td><p>Team lead</p></td>
</tr>
<tr class="row-odd"><td><p><strong>3.0-4.9</strong></p></td>
<td><p>Low</p></td>
<td><p>1 month</p></td>
<td><p>As available</p></td>
<td><p>None</p></td>
</tr>
<tr class="row-even"><td><p><strong>0.0-2.9</strong></p></td>
<td><p>Informational</p></td>
<td><p>Next cycle</p></td>
<td><p>Background</p></td>
<td><p>None</p></td>
</tr>
</tbody>
</table>
</section>
<section id="mitre-att-ck-integration">
<h3><a class="toc-backref" href="#id18" role="doc-backlink">MITRE ATT&amp;CK Integration</a><a class="headerlink" href="#mitre-att-ck-integration" title="Link to this heading"></a></h3>
<p><strong>Technique Mapping:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># MITRE ATT&amp;CK technique correlation</span>
<span class="k">def</span> <span class="nf">map_attack_path_to_mitre</span><span class="p">(</span><span class="n">attack_path</span><span class="p">):</span>
    <span class="n">technique_mapping</span> <span class="o">=</span> <span class="p">[]</span>

    <span class="k">for</span> <span class="n">step</span> <span class="ow">in</span> <span class="n">attack_path</span><span class="o">.</span><span class="n">steps</span><span class="p">:</span>
        <span class="n">techniques</span> <span class="o">=</span> <span class="n">mitre_service</span><span class="o">.</span><span class="n">get_techniques_for_step</span><span class="p">(</span>
            <span class="n">source_asset_type</span><span class="o">=</span><span class="n">step</span><span class="o">.</span><span class="n">source</span><span class="o">.</span><span class="n">asset_type</span><span class="p">,</span>
            <span class="n">target_asset_type</span><span class="o">=</span><span class="n">step</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">asset_type</span><span class="p">,</span>
            <span class="n">attack_vector</span><span class="o">=</span><span class="n">step</span><span class="o">.</span><span class="n">attack_vector</span><span class="p">,</span>
            <span class="n">privileges_required</span><span class="o">=</span><span class="n">step</span><span class="o">.</span><span class="n">privileges_required</span>
        <span class="p">)</span>

        <span class="n">technique_mapping</span><span class="o">.</span><span class="n">append</span><span class="p">({</span>
            <span class="s2">&quot;step&quot;</span><span class="p">:</span> <span class="n">step</span><span class="o">.</span><span class="n">id</span><span class="p">,</span>
            <span class="s2">&quot;techniques&quot;</span><span class="p">:</span> <span class="n">techniques</span><span class="p">,</span>
            <span class="s2">&quot;tactic&quot;</span><span class="p">:</span> <span class="n">techniques</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">tactic</span> <span class="k">if</span> <span class="n">techniques</span> <span class="k">else</span> <span class="kc">None</span><span class="p">,</span>
            <span class="s2">&quot;confidence&quot;</span><span class="p">:</span> <span class="n">step</span><span class="o">.</span><span class="n">confidence_score</span>
        <span class="p">})</span>

    <span class="k">return</span> <span class="n">technique_mapping</span>
</pre></div>
</div>
<p><strong>Threat Actor Profiling:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Threat actor analysis</span>
<span class="k">def</span> <span class="nf">analyze_threat_actor_fit</span><span class="p">(</span><span class="n">attack_path</span><span class="p">,</span> <span class="n">threat_actors</span><span class="p">):</span>
    <span class="n">actor_scores</span> <span class="o">=</span> <span class="p">{}</span>

    <span class="k">for</span> <span class="n">actor</span> <span class="ow">in</span> <span class="n">threat_actors</span><span class="p">:</span>
        <span class="n">score</span> <span class="o">=</span> <span class="mf">0.0</span>

        <span class="c1"># Check technique overlap</span>
        <span class="n">actor_techniques</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">actor</span><span class="o">.</span><span class="n">techniques</span><span class="p">)</span>
        <span class="n">path_techniques</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">attack_path</span><span class="o">.</span><span class="n">mitre_techniques</span><span class="p">)</span>
        <span class="n">technique_overlap</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="n">actor_techniques</span> <span class="o">&amp;</span> <span class="n">path_techniques</span><span class="p">)</span>

        <span class="c1"># Check targeting preferences</span>
        <span class="k">if</span> <span class="n">attack_path</span><span class="o">.</span><span class="n">target_sector</span> <span class="ow">in</span> <span class="n">actor</span><span class="o">.</span><span class="n">target_sectors</span><span class="p">:</span>
            <span class="n">score</span> <span class="o">+=</span> <span class="mf">2.0</span>

        <span class="c1"># Check sophistication level</span>
        <span class="k">if</span> <span class="n">attack_path</span><span class="o">.</span><span class="n">complexity_score</span> <span class="o">&lt;=</span> <span class="n">actor</span><span class="o">.</span><span class="n">sophistication_level</span><span class="p">:</span>
            <span class="n">score</span> <span class="o">+=</span> <span class="mf">1.0</span>

        <span class="n">actor_scores</span><span class="p">[</span><span class="n">actor</span><span class="o">.</span><span class="n">name</span><span class="p">]</span> <span class="o">=</span> <span class="n">score</span> <span class="o">+</span> <span class="n">technique_overlap</span>

    <span class="k">return</span> <span class="nb">sorted</span><span class="p">(</span><span class="n">actor_scores</span><span class="o">.</span><span class="n">items</span><span class="p">(),</span> <span class="n">key</span><span class="o">=</span><span class="k">lambda</span> <span class="n">x</span><span class="p">:</span> <span class="n">x</span><span class="p">[</span><span class="mi">1</span><span class="p">],</span> <span class="n">reverse</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="integration-and-automation">
<h2><a class="toc-backref" href="#id19" role="doc-backlink">Integration and Automation</a><a class="headerlink" href="#integration-and-automation" title="Link to this heading"></a></h2>
<section id="siem-integration">
<h3><a class="toc-backref" href="#id20" role="doc-backlink">SIEM Integration</a><a class="headerlink" href="#siem-integration" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Automated SIEM rule generation</span>
<span class="k">def</span> <span class="nf">generate_detection_rules</span><span class="p">(</span><span class="n">attack_paths</span><span class="p">):</span>
    <span class="n">detection_rules</span> <span class="o">=</span> <span class="p">[]</span>

    <span class="k">for</span> <span class="n">path</span> <span class="ow">in</span> <span class="n">attack_paths</span><span class="p">:</span>
        <span class="k">for</span> <span class="n">technique</span> <span class="ow">in</span> <span class="n">path</span><span class="o">.</span><span class="n">mitre_techniques</span><span class="p">:</span>
            <span class="n">rule</span> <span class="o">=</span> <span class="p">{</span>
                <span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;Detect </span><span class="si">{</span><span class="n">technique</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
                <span class="s2">&quot;technique_id&quot;</span><span class="p">:</span> <span class="n">technique</span><span class="o">.</span><span class="n">id</span><span class="p">,</span>
                <span class="s2">&quot;severity&quot;</span><span class="p">:</span> <span class="n">path</span><span class="o">.</span><span class="n">risk_score</span><span class="p">,</span>
                <span class="s2">&quot;query&quot;</span><span class="p">:</span> <span class="n">technique</span><span class="o">.</span><span class="n">detection_query</span><span class="p">,</span>
                <span class="s2">&quot;false_positive_rate&quot;</span><span class="p">:</span> <span class="n">technique</span><span class="o">.</span><span class="n">fp_rate</span><span class="p">,</span>
                <span class="s2">&quot;data_sources&quot;</span><span class="p">:</span> <span class="n">technique</span><span class="o">.</span><span class="n">data_sources</span>
            <span class="p">}</span>
            <span class="n">detection_rules</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">rule</span><span class="p">)</span>

    <span class="k">return</span> <span class="n">detection_rules</span>
</pre></div>
</div>
</section>
<section id="soar-integration">
<h3><a class="toc-backref" href="#id21" role="doc-backlink">SOAR Integration</a><a class="headerlink" href="#soar-integration" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Automated response workflows</span>
<span class="k">def</span> <span class="nf">create_response_playbook</span><span class="p">(</span><span class="n">attack_path</span><span class="p">):</span>
    <span class="n">playbook_steps</span> <span class="o">=</span> <span class="p">[]</span>

    <span class="k">for</span> <span class="n">step</span> <span class="ow">in</span> <span class="n">attack_path</span><span class="o">.</span><span class="n">steps</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">step</span><span class="o">.</span><span class="n">risk_score</span> <span class="o">&gt;</span> <span class="mf">7.0</span><span class="p">:</span>
            <span class="n">playbook_steps</span><span class="o">.</span><span class="n">extend</span><span class="p">([</span>
                <span class="sa">f</span><span class="s2">&quot;Isolate </span><span class="si">{</span><span class="n">step</span><span class="o">.</span><span class="n">source</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
                <span class="sa">f</span><span class="s2">&quot;Monitor </span><span class="si">{</span><span class="n">step</span><span class="o">.</span><span class="n">target</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
                <span class="sa">f</span><span class="s2">&quot;Alert security team&quot;</span><span class="p">,</span>
                <span class="sa">f</span><span class="s2">&quot;Collect forensic evidence&quot;</span>
            <span class="p">])</span>

    <span class="k">return</span> <span class="p">{</span>
        <span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;Response to </span><span class="si">{</span><span class="n">attack_path</span><span class="o">.</span><span class="n">name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
        <span class="s2">&quot;trigger&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;Detection of </span><span class="si">{</span><span class="n">attack_path</span><span class="o">.</span><span class="n">initial_technique</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
        <span class="s2">&quot;steps&quot;</span><span class="p">:</span> <span class="n">playbook_steps</span><span class="p">,</span>
        <span class="s2">&quot;escalation&quot;</span><span class="p">:</span> <span class="s2">&quot;<EMAIL>&quot;</span>
    <span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="best-practices">
<h2><a class="toc-backref" href="#id22" role="doc-backlink">Best Practices</a><a class="headerlink" href="#best-practices" title="Link to this heading"></a></h2>
<section id="analysis-guidelines">
<h3><a class="toc-backref" href="#id23" role="doc-backlink">Analysis Guidelines</a><a class="headerlink" href="#analysis-guidelines" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Start with Crown Jewels</strong> - Focus on protecting most critical assets</p></li>
<li><p><strong>Regular Analysis</strong> - Run analysis after infrastructure changes</p></li>
<li><p><strong>Validate Results</strong> - Cross-check with penetration testing</p></li>
<li><p><strong>Update Models</strong> - Keep asset relationships current</p></li>
<li><p><strong>Collaborate</strong> - Share results with relevant teams</p></li>
</ol>
</section>
<section id="performance-optimization">
<h3><a class="toc-backref" href="#id24" role="doc-backlink">Performance Optimization</a><a class="headerlink" href="#performance-optimization" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Limit Depth</strong> - Use appropriate max_depth for analysis scope</p></li>
<li><p><strong>Filter Assets</strong> - Focus on active and relevant assets</p></li>
<li><p><strong>Cache Results</strong> - Cache frequently accessed analysis results</p></li>
<li><p><strong>Parallel Processing</strong> - Use async analysis for multiple targets</p></li>
<li><p><strong>Monitor Resources</strong> - Track analysis performance and resource usage</p></li>
</ol>
</section>
</section>
<section id="next-steps">
<h2><a class="toc-backref" href="#id25" role="doc-backlink">Next Steps</a><a class="headerlink" href="#next-steps" title="Link to this heading"></a></h2>
<p>For comprehensive attack path analysis:</p>
<ol class="arabic simple">
<li><p><strong>Define Use Cases</strong> - Identify specific analysis scenarios for your organization</p></li>
<li><p><strong>Configure Assets</strong> - Ensure complete and accurate asset inventory</p></li>
<li><p><strong>Validate Relationships</strong> - Verify asset connections and dependencies</p></li>
<li><p><strong>Integrate Tools</strong> - Connect with SIEM, SOAR, and other security tools</p></li>
<li><p><strong>Train Teams</strong> - Educate security teams on analysis capabilities and interpretation</p></li>
</ol>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Attack path analysis is most effective when combined with threat intelligence,
vulnerability management, and continuous monitoring. Regular analysis helps
maintain an accurate understanding of your security posture.</p>
</div>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="asset-discovery.html" class="btn btn-neutral float-left" title="Asset Discovery Use Cases" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="../architecture/zero-trust-architecture.html" class="btn btn-neutral float-right" title="Zero-Trust Architecture Implementation" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, Blast-Radius Security Tool Team.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>