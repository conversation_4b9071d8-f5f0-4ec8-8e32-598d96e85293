<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Machine Learning Threat Prediction - Technical Specification &mdash; Blast-Radius Security Tool v1.0.0 - Enterprise Security Platform Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ac1674a3" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ac1674a3" />

  
    <link rel="canonical" href="https://blast-radius.readthedocs.io/technical-specifications/ml-threat-prediction.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=528dc5d9"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Blast-Radius Security Tool v1.0.0 - Enterprise Security Platform Documentation"
          href="../_static/opensearch.xml"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🎉 Latest Release</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../latest-implementations-summary.html">Latest Implementations Summary - Production Ready Release</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#implementation-achievements">Implementation Achievements</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#infrastructure-as-code-iac-100-complete">Infrastructure as Code (IaC) - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#zero-trust-architecture-100-complete">Zero-Trust Architecture - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#gdpr-compliance-framework-100-complete">GDPR Compliance Framework - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#enhanced-audit-logging-100-complete">Enhanced Audit Logging - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#least-privilege-access-control-100-complete">Least Privilege Access Control - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#production-deployment-infrastructure-100-complete">Production Deployment Infrastructure - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#documentation-operations-100-complete">Documentation &amp; Operations - 100% Complete ✅</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#technical-specifications">Technical Specifications</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#performance-characteristics">Performance Characteristics</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#security-specifications">Security Specifications</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#compliance-coverage">Compliance Coverage</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#deployment-architecture">Deployment Architecture</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#next-steps-and-recommendations">Next Steps and Recommendations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#immediate-actions">Immediate Actions</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#long-term-roadmap">Long-term Roadmap</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🚀 Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#prerequisites">Prerequisites</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#system-requirements">System Requirements</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#software-dependencies">Software Dependencies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#installation-methods">Installation Methods</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#method-1-quick-start-with-docker-recommended">Method 1: Quick Start with Docker (Recommended)</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#method-2-development-installation">Method 2: Development Installation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#method-3-production-installation">Method 3: Production Installation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#configuration">Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#environment-variables">Environment Variables</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#initial-setup">Initial Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#creating-admin-user">Creating Admin User</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#configuring-integrations">Configuring Integrations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#verification">Verification</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#health-checks">Health Checks</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#performance-testing">Performance Testing</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#core-configuration">Core Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#application-settings">Application Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#database-configuration">Database Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#security-configuration">Security Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#authentication-settings">Authentication Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#encryption-settings">Encryption Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#access-control-settings">Access Control Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#integration-configuration">Integration Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#cloud-provider-settings">Cloud Provider Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#servicenow-integration">ServiceNow Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#threat-intelligence-settings">Threat Intelligence Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#monitoring-and-logging">Monitoring and Logging</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#logging-configuration">Logging Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#monitoring-settings">Monitoring Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#environment-specific-configuration">Environment-Specific Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#development-environment">Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#staging-environment">Staging Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#configuration-validation">Configuration Validation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#security-best-practices">Security Best Practices</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#performance-best-practices">Performance Best Practices</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#troubleshooting">Troubleshooting</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../quick-start-guide.html">Quick Start Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#quick-installation">Quick Installation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-1-get-the-code">Step 1: Get the Code</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-2-start-the-platform">Step 2: Start the Platform</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-3-access-the-platform">Step 3: Access the Platform</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#first-login-and-setup">First Login and Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#initial-login">Initial Login</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#password-change">Password Change</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#dashboard-overview">Dashboard Overview</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#basic-configuration">Basic Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#user-profile-setup">User Profile Setup</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#multi-factor-authentication-recommended">Multi-Factor Authentication (Recommended)</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#your-first-attack-path-analysis">Your First Attack Path Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-1-add-sample-assets">Step 1: Add Sample Assets</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-2-run-attack-path-analysis">Step 2: Run Attack Path Analysis</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-3-view-results">Step 3: View Results</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#understanding-the-visualization">Understanding the Visualization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#essential-features-tour">Essential Features Tour</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#real-time-monitoring">Real-Time Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#asset-management">Asset Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#user-and-role-management">User and Role Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#threat-intelligence">Threat Intelligence</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#integration-setup">Integration Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#cloud-provider-integration">Cloud Provider Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#servicenow-integration">ServiceNow Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#next-steps">Next Steps</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#immediate-actions">Immediate Actions</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#learning-resources">Learning Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#advanced-configuration">Advanced Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#common-quick-start-issues">Common Quick Start Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#platform-won-t-start">Platform Won’t Start</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#can-t-access-web-interface">Can’t Access Web Interface</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#login-issues">Login Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#performance-issues">Performance Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#getting-help">Getting Help</a></li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#congratulations">Congratulations!</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📊 Documentation Excellence</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../documentation-overview.html">Documentation Overview and Achievements</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#documentation-excellence-achievements">🏆 Documentation Excellence Achievements</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#comprehensive-user-guide-ecosystem">📚 Comprehensive User Guide Ecosystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#key-documentation-features">🎯 Key Documentation Features</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#measurable-impact-and-value">📈 Measurable Impact and Value</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#industry-leadership-and-recognition">🌟 Industry Leadership and Recognition</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#future-enhancements-and-roadmap">🚀 Future Enhancements and Roadmap</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📚 Enterprise User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guides/index.html">User Guides</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#role-based-guides">Role-Based Guides</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#soc-operators">SOC Operators</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/soc-operators.html">SOC Operators Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#role-definition-and-responsibilities">Role Definition and Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#core-platform-capabilities-for-soc-operations">Core Platform Capabilities for SOC Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#getting-started-soc-operator-onboarding">Getting Started: SOC Operator Onboarding</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#advanced-real-time-monitoring-and-threat-detection">Advanced Real-time Monitoring and Threat Detection</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#comprehensive-incident-response-framework">Comprehensive Incident Response Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#advanced-reporting-and-documentation-framework">Advanced Reporting and Documentation Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#soc-operations-best-practices-and-excellence-framework">SOC Operations Best Practices and Excellence Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#comprehensive-scenario-based-training-and-response-procedures">Comprehensive Scenario-Based Training and Response Procedures</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#comprehensive-support-resources-and-professional-development">Comprehensive Support Resources and Professional Development</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#conclusion-excellence-in-soc-operations">Conclusion: Excellence in SOC Operations</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#security-architects">Security Architects</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/security-architects.html">Security Architects Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#executive-summary-for-security-architects">Executive Summary for Security Architects</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-platform-capabilities-for-security-architecture">Advanced Platform Capabilities for Security Architecture</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#enterprise-security-architecture-methodology">Enterprise Security Architecture Methodology</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-risk-assessment-and-quantitative-analysis-framework">Advanced Risk Assessment and Quantitative Analysis Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-security-architecture-design-and-implementation">Advanced Security Architecture Design and Implementation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#comprehensive-compliance-and-governance-framework">Comprehensive Compliance and Governance Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-analytics-and-executive-communication-framework">Advanced Analytics and Executive Communication Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#enterprise-security-architecture-best-practices-and-excellence">Enterprise Security Architecture Best Practices and Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#professional-development-and-support-resources">Professional Development and Support Resources</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#conclusion-excellence-in-security-architecture-leadership">Conclusion: Excellence in Security Architecture Leadership</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#red-team-members">Red Team Members</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/red-team-members.html">Red Team Members Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#executive-summary-for-red-team-operations">Executive Summary for Red Team Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#role-definition-and-offensive-security-responsibilities">Role Definition and Offensive Security Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-red-team-platform-capabilities">Advanced Red Team Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#comprehensive-red-team-methodology-and-campaign-framework">Comprehensive Red Team Methodology and Campaign Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-attack-path-discovery-and-exploitation-framework">Advanced Attack Path Discovery and Exploitation Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-attack-simulation-and-specialized-testing-scenarios">Advanced Attack Simulation and Specialized Testing Scenarios</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-reporting-and-performance-metrics-framework">Advanced Reporting and Performance Metrics Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#red-team-best-practices-and-operational-excellence">Red Team Best Practices and Operational Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-troubleshooting-and-technical-support">Advanced Troubleshooting and Technical Support</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#conclusion-excellence-in-red-team-operations">Conclusion: Excellence in Red Team Operations</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#purple-team-members">Purple Team Members</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/purple-team-members.html">Purple Team Members Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#executive-summary-for-purple-team-excellence">Executive Summary for Purple Team Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#advanced-purple-team-platform-capabilities">Advanced Purple Team Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#comprehensive-purple-team-methodology-and-framework">Comprehensive Purple Team Methodology and Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#advanced-detection-engineering-and-security-control-optimization">Advanced Detection Engineering and Security Control Optimization</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#comprehensive-purple-team-scenarios-and-implementation-framework">Comprehensive Purple Team Scenarios and Implementation Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#advanced-troubleshooting-and-technical-support-framework">Advanced Troubleshooting and Technical Support Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#conclusion-excellence-in-purple-team-operations-and-collaborative-security">Conclusion: Excellence in Purple Team Operations and Collaborative Security</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#compliance-officers">Compliance Officers</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/compliance-officers.html">Compliance Officers Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#executive-summary-for-compliance-excellence">Executive Summary for Compliance Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-compliance-platform-capabilities">Advanced Compliance Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-regulatory-framework-management">Advanced Regulatory Framework Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#automated-compliance-monitoring-and-management">Automated Compliance Monitoring and Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#comprehensive-audit-management-and-preparation">Comprehensive Audit Management and Preparation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-risk-assessment-and-management-framework">Advanced Risk Assessment and Management Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#cross-functional-collaboration-and-integration">Cross-Functional Collaboration and Integration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#professional-development-and-excellence-framework">Professional Development and Excellence Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-support-and-troubleshooting-framework">Advanced Support and Troubleshooting Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#conclusion-excellence-in-compliance-and-governance">Conclusion: Excellence in Compliance and Governance</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#executive-leadership">Executive Leadership</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/executive-leadership.html">Executive Leadership Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#executive-summary-for-strategic-security-leadership">Executive Summary for Strategic Security Leadership</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#advanced-executive-security-platform-capabilities">Advanced Executive Security Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#strategic-security-risk-management-framework">Strategic Security Risk Management Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#business-aligned-security-performance-management">Business-Aligned Security Performance Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#strategic-security-investment-and-resource-management">Strategic Security Investment and Resource Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#crisis-management-and-business-continuity">Crisis Management and Business Continuity</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#regulatory-compliance-and-governance-excellence">Regulatory Compliance and Governance Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#strategic-security-communication-and-stakeholder-management">Strategic Security Communication and Stakeholder Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#digital-transformation-and-innovation-enablement">Digital Transformation and Innovation Enablement</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#professional-development-and-executive-excellence">Professional Development and Executive Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#advanced-support-and-advisory-services">Advanced Support and Advisory Services</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#conclusion-excellence-in-executive-security-leadership">Conclusion: Excellence in Executive Security Leadership</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#administrators">Administrators</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/administrators.html">Administrators Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#dashboard-overview">Dashboard Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#getting-started">Getting Started</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#user-and-access-management">User and Access Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#system-configuration-and-management">System Configuration and Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#monitoring-and-maintenance">Monitoring and Maintenance</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#security-and-compliance">Security and Compliance</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#platform-updates-and-maintenance">Platform Updates and Maintenance</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#troubleshooting-and-support">Troubleshooting and Support</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#best-practices-for-platform-administration">Best Practices for Platform Administration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#feature-specific-guides">Feature-Specific Guides</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#attack-path-analysis">Attack Path Analysis</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/attack-path-analysis.html">Attack Path Analysis User Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#getting-started">Getting Started</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#core-concepts">Core Concepts</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#using-the-web-interface">Using the Web Interface</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#using-the-api">Using the API</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#use-cases-and-workflows">Use Cases and Workflows</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#best-practices">Best Practices</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#threat-modeling">Threat Modeling</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#asset-discovery-and-management">Asset Discovery and Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#threat-intelligence-integration">Threat Intelligence Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#common-workflows">Common Workflows</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#security-assessment-workflow">Security Assessment Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#incident-response-workflow">Incident Response Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#red-team-exercise-workflow">Red Team Exercise Workflow</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#general-guidelines">General Guidelines</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#role-specific-tips">Role-Specific Tips</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#getting-help">Getting Help</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#support-resources">Support Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#training-and-certification">Training and Certification</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#feedback-and-contributions">Feedback and Contributions</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#base-url">Base URL</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#api-endpoints">API Endpoints</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#authentication-user-management">Authentication &amp; User Management</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/authentication.html">Authentication API</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#authentication-flow">Authentication Flow</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#security-and-authorization">Security and Authorization</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#integration-examples">Integration Examples</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#best-practices">Best Practices</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#asset-management">Asset Management</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/asset-management.html">Asset Management API</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-crud-operations">Asset CRUD Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-discovery">Asset Discovery</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#bulk-operations">Bulk Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-relationships">Asset Relationships</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-tags-and-metadata">Asset Tags and Metadata</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#error-handling">Error Handling</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#sdk-examples">SDK Examples</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#attack-path-analysis">Attack Path Analysis</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/attack-path-analysis.html">Attack Path Analysis API</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#endpoints">Endpoints</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#error-responses">Error Responses</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#performance-considerations">Performance Considerations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#best-practices">Best Practices</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#examples">Examples</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#threat-modeling-risk-assessment">Threat Modeling &amp; Risk Assessment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/threat-modeling.html">Threat Modeling API Reference</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#threat-actor-simulation">Threat Actor Simulation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#risk-assessment">Risk Assessment</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#compliance-impact-analysis">Compliance Impact Analysis</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#mitigation-strategies">Mitigation Strategies</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#continuous-monitoring">Continuous Monitoring</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#error-handling">Error Handling</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#rate-limiting">Rate Limiting</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/mitre-attack-integration.html">MITRE ATT&amp;CK Integration API Reference</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#att-ck-data-management">ATT&amp;CK Data Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#technique-correlation">Technique Correlation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#threat-actor-attribution">Threat Actor Attribution</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#attack-pattern-analysis">Attack Pattern Analysis</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#att-ck-navigator-integration">ATT&amp;CK Navigator Integration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#threat-intelligence-enrichment">Threat Intelligence Enrichment</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#error-handling">Error Handling</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#rate-limiting">Rate Limiting</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#threat-intelligence">Threat Intelligence</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#monitoring-dashboards">Monitoring &amp; Dashboards</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#integrations">Integrations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#common-response-formats">Common Response Formats</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#success-response">Success Response</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#error-response">Error Response</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#http-status-codes">HTTP Status Codes</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#pagination">Pagination</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#filtering-and-sorting">Filtering and Sorting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#field-selection">Field Selection</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#webhooks">Webhooks</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#sdk-and-libraries">SDK and Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#testing-and-development">Testing and Development</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#support-and-resources">Support and Resources</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#examples">Examples</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Development</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#getting-started">Getting Started</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#ways-to-contribute">Ways to Contribute</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#before-you-start">Before You Start</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#contribution-workflow">Contribution Workflow</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-1-find-or-create-an-issue">Step 1: Find or Create an Issue</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-2-fork-and-clone">Step 2: Fork and Clone</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-3-create-a-branch">Step 3: Create a Branch</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-4-make-changes">Step 4: Make Changes</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-5-test-your-changes">Step 5: Test Your Changes</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-6-submit-pull-request">Step 6: Submit Pull Request</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#code-standards">Code Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#python-code-style">Python Code Style</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#javascript-typescript-style">JavaScript/TypeScript Style</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#testing-guidelines">Testing Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#test-structure">Test Structure</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#documentation-standards">Documentation Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#documentation-types">Documentation Types</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#writing-guidelines">Writing Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#review-process">Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#code-review-checklist">Code Review Checklist</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#review-guidelines">Review Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#community-guidelines">Community Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#code-of-conduct">Code of Conduct</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#communication">Communication</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#recognition">Recognition</a></li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#getting-help">Getting Help</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Use Cases</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../use-cases/asset-discovery.html">Asset Discovery Use Cases</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#discovery-methods">Discovery Methods</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#cloud-provider-discovery">Cloud Provider Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#network-based-discovery">Network-Based Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#api-discovery">API Discovery</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#use-case-scenarios">Use Case Scenarios</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#scenario-1-multi-cloud-asset-inventory">Scenario 1: Multi-Cloud Asset Inventory</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#scenario-2-continuous-asset-monitoring">Scenario 2: Continuous Asset Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#scenario-3-compliance-asset-reporting">Scenario 3: Compliance Asset Reporting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#advanced-discovery-features">Advanced Discovery Features</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#agent-based-discovery">Agent-Based Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#container-discovery">Container Discovery</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#discovery-automation">Discovery Automation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#scheduled-discovery">Scheduled Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#integration-with-external-tools">Integration with External Tools</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#discovery-strategy">Discovery Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#performance-optimization">Performance Optimization</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#security-considerations">Security Considerations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#common-issues">Common Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../use-cases/attack-path-analysis.html">Attack Path Analysis Use Cases</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#key-capabilities">Key Capabilities</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#analysis-types">Analysis Types</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#use-case-scenarios">Use Case Scenarios</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-1-red-team-attack-simulation">Scenario 1: Red Team Attack Simulation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-2-purple-team-validation-exercise">Scenario 2: Purple Team Validation Exercise</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-3-compliance-risk-assessment">Scenario 3: Compliance Risk Assessment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-4-incident-response-planning">Scenario 4: Incident Response Planning</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-5-zero-trust-architecture-planning">Scenario 5: Zero Trust Architecture Planning</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#advanced-analysis-features">Advanced Analysis Features</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#risk-scoring-and-prioritization">Risk Scoring and Prioritization</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#integration-and-automation">Integration and Automation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#siem-integration">SIEM Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#soar-integration">SOAR Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#analysis-guidelines">Analysis Guidelines</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Architecture</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../architecture/zero-trust-architecture.html">Zero-Trust Architecture Implementation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#identity-verification-service">Identity Verification Service</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#service-account-management">Service Account Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#tls-1-3-enforcement">TLS 1.3+ Enforcement</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#real-time-security-event-correlation">Real-Time Security Event Correlation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#network-security">Network Security</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#kubernetes-network-policies">Kubernetes Network Policies</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#rbac-implementation">RBAC Implementation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#data-protection">Data Protection</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#encryption-implementation">Encryption Implementation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#data-classification">Data Classification</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#monitoring-and-alerting">Monitoring and Alerting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#security-monitoring">Security Monitoring</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#implementation-guidelines">Implementation Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#deployment-checklist">Deployment Checklist</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#security-validation">Security Validation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#security-principles">Security Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#implementation-tips">Implementation Tips</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../deployment/production-architecture.html">Production Architecture Overview</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#kubernetes-infrastructure">Kubernetes Infrastructure</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#application-architecture">Application Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#data-architecture">Data Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#network-architecture">Network Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#security-architecture">Security Architecture</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#zero-trust-security-implementation">Zero-Trust Security Implementation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#monitoring-architecture">Monitoring Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#deployment-strategy">Deployment Strategy</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#blue-green-deployment">Blue-Green Deployment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#infrastructure-as-code">Infrastructure as Code</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#performance-specifications">Performance Specifications</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#scalability-metrics">Scalability Metrics</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#resource-requirements">Resource Requirements</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#disaster-recovery">Disaster Recovery</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#backup-and-recovery-strategy">Backup and Recovery Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#high-availability-design">High Availability Design</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#operational-excellence">Operational Excellence</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#security-best-practices">Security Best Practices</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Security &amp; Compliance</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../security/enhanced-audit-logging.html">Enhanced Audit Logging System</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-event-structure">Audit Event Structure</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#tamper-proof-audit-chain">Tamper-Proof Audit Chain</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#event-enrichment-pipeline">Event Enrichment Pipeline</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#real-time-monitoring">Real-Time Monitoring</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#compliance-integration">Compliance Integration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#framework-mapping">Framework Mapping</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#compliance-reporting">Compliance Reporting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-service-architecture">Audit Service Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#database-schema">Database Schema</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#configuration">Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#monitoring-and-alerting">Monitoring and Alerting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-monitoring-dashboard">Audit Monitoring Dashboard</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#alert-configuration">Alert Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-strategy">Audit Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#operational-guidelines">Operational Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#performance-tuning">Performance Tuning</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../security/incident-response-procedures.html">Incident Response Procedures - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#incident-classification">Incident Classification</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#severity-levels">Severity Levels</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#critical-p0">Critical (P0)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#high-p1">High (P1)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#medium-p2">Medium (P2)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#low-p3">Low (P3)</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#incident-response-team">Incident Response Team</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#roles-and-responsibilities">Roles and Responsibilities</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#incident-commander-ic">Incident Commander (IC)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#technical-lead">Technical Lead</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#security-lead">Security Lead</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#communications-lead">Communications Lead</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#incident-response-process">Incident Response Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-1-detection-and-initial-response-0-15-minutes">Phase 1: Detection and Initial Response (0-15 minutes)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#incident-detection">1.1 Incident Detection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#initial-assessment">1.2 Initial Assessment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#team-notification">1.3 Team Notification</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-2-investigation-and-containment-15-minutes-2-hours">Phase 2: Investigation and Containment (15 minutes - 2 hours)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#incident-war-room-setup">2.1 Incident War Room Setup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#technical-investigation">2.2 Technical Investigation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#security-assessment-if-applicable">2.3 Security Assessment (if applicable)</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-3-resolution-and-recovery-2-8-hours">Phase 3: Resolution and Recovery (2-8 hours)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#fix-implementation">3.1 Fix Implementation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#system-recovery">3.2 System Recovery</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-4-communication-and-documentation-ongoing">Phase 4: Communication and Documentation (Ongoing)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#status-updates">4.1 Status Updates</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#resolution-communication">4.2 Resolution Communication</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-5-post-incident-review-24-72-hours-after-resolution">Phase 5: Post-Incident Review (24-72 hours after resolution)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#post-mortem-meeting">5.1 Post-Mortem Meeting</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#documentation">5.2 Documentation</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#security-specific-procedures">Security-Specific Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#data-breach-response">Data Breach Response</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#immediate-actions-0-1-hour">Immediate Actions (0-1 hour)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#notification-requirements">Notification Requirements</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#vulnerability-response">Vulnerability Response</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#critical-vulnerabilities-cvss-9-0">Critical Vulnerabilities (CVSS 9.0+)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#high-vulnerabilities-cvss-7-0-8-9">High Vulnerabilities (CVSS 7.0-8.9)</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#emergency-contacts">Emergency Contacts</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#internal-team">Internal Team</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#external-contacts">External Contacts</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#tools-and-resources">Tools and Resources</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#monitoring-and-alerting">Monitoring and Alerting</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#communication">Communication</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#id1">Documentation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#training-and-drills">Training and Drills</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#regular-training">Regular Training</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#documentation-updates">Documentation Updates</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../security/security-review-processes.html">Security Review Processes - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#security-review-framework">Security Review Framework</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-types">Review Types</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#code-security-review">1. Code Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#infrastructure-security-review">2. Infrastructure Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#architecture-security-review">3. Architecture Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#compliance-review">4. Compliance Review</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#code-security-review-process">Code Security Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#pre-review-checklist">Pre-Review Checklist</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#developer-responsibilities">Developer Responsibilities</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-criteria">Review Criteria</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#input-validation-and-sanitization">1. Input Validation and Sanitization</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#authentication-and-authorization">2. Authentication and Authorization</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#data-protection">3. Data Protection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#error-handling-and-logging">4. Error Handling and Logging</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#cryptography-and-secrets">5. Cryptography and Secrets</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-process-workflow">Review Process Workflow</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#step-1-automated-security-checks">Step 1: Automated Security Checks</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#step-2-manual-security-review">Step 2: Manual Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#step-3-security-testing">Step 3: Security Testing</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#id1">Infrastructure Security Review</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#infrastructure-as-code-iac-review">Infrastructure as Code (IaC) Review</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#terraform-security-checklist">Terraform Security Checklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#example-review-process">Example Review Process</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#kubernetes-security-review">Kubernetes Security Review</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#manifest-security-checklist">Manifest Security Checklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#example-security-policies">Example Security Policies</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#id2">Architecture Security Review</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#design-review-process">Design Review Process</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#threat-modeling">1. Threat Modeling</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#security-architecture-patterns">2. Security Architecture Patterns</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#integration-security">3. Integration Security</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-documentation-template">Review Documentation Template</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#compliance-review-process">Compliance Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#regulatory-requirements">Regulatory Requirements</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#gdpr-compliance-checklist">GDPR Compliance Checklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#soc-2-compliance-checklist">SOC 2 Compliance Checklist</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#compliance-review-workflow">Compliance Review Workflow</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#change-impact-assessment">1. Change Impact Assessment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#documentation-requirements">2. Documentation Requirements</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#security-review-tools-and-automation">Security Review Tools and Automation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#automated-security-scanning">Automated Security Scanning</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#security-metrics-and-kpis">Security Metrics and KPIs</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#training-and-awareness">Training and Awareness</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#security-review-training">Security Review Training</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#documentation-and-resources">Documentation and Resources</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html">GDPR Compliance Framework</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-subject-rights-management">Data Subject Rights Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#consent-management-system">Consent Management System</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-processing-records">Data Processing Records</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-breach-management">Data Breach Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#privacy-impact-assessment">Privacy Impact Assessment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#gdpr-service-architecture">GDPR Service Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-processing-workflows">Data Processing Workflows</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#compliance-monitoring">Compliance Monitoring</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#automated-compliance-checks">Automated Compliance Checks</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#compliance-reporting">Compliance Reporting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#privacy-by-design-implementation">Privacy by Design Implementation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#operational-guidelines">Operational Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#technical-implementation">Technical Implementation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#database-schema">Database Schema</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#configuration-example">Configuration Example</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../access-control/least-privilege-framework.html">Least Privilege Access Control Framework</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-request-management">Access Request Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#just-in-time-access-provisioning">Just-in-Time Access Provisioning</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#privilege-escalation-management">Privilege Escalation Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-review-and-certification">Access Review and Certification</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-control-architecture">Access Control Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#role-based-access-templates">Role-Based Access Templates</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#risk-assessment-engine">Risk Assessment Engine</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#monitoring-and-analytics">Monitoring and Analytics</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-usage-monitoring">Access Usage Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-analytics-dashboard">Access Analytics Dashboard</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#configuration-management">Configuration Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#policy-configuration">Policy Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#role-template-configuration">Role Template Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-management-principles">Access Management Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#operational-guidelines">Operational Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../security/index.html">Security Documentation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/security-summary.html">Security Documentation Summary</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#executive-overview">Executive Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#security-documentation-structure">Security Documentation Structure</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#recent-security-achievements">Recent Security Achievements</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#june-2025-security-review-results">June 2025 Security Review Results</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#key-security-fixes-implemented">Key Security Fixes Implemented</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#security-architecture-excellence">Security Architecture Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#multi-layered-security-approach">Multi-Layered Security Approach</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#security-testing-excellence">Security Testing Excellence</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#compliance-and-regulatory-excellence">Compliance and Regulatory Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#regulatory-compliance-status">Regulatory Compliance Status</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#industry-standards-compliance">Industry Standards Compliance</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#security-operations-excellence">Security Operations Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#vulnerability-management-program">Vulnerability Management Program</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#security-monitoring-and-incident-response">Security Monitoring and Incident Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#risk-management-excellence">Risk Management Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#enterprise-risk-assessment">Enterprise Risk Assessment</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#business-impact-and-value">Business Impact and Value</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#security-investment-roi">Security Investment ROI</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#future-security-roadmap">Future Security Roadmap</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#short-term-objectives-q3-q4-2025">Short-Term Objectives (Q3-Q4 2025)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#long-term-strategic-goals-2026">Long-Term Strategic Goals (2026)</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#overview">Overview</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/index.html#security-principles">Security Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/index.html#security-framework">Security Framework</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/index.html#security-architecture">Security Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#security-documentation-sections">Security Documentation Sections</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#security-certifications-and-compliance">Security Certifications and Compliance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#security-contact-information">Security Contact Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#quick-security-reference">Quick Security Reference</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Deployment &amp; Operations</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../deployment/production-deployment-guide.html">Production Deployment Guide - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#prerequisites">Prerequisites</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#infrastructure-requirements">Infrastructure Requirements</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#minimum-system-requirements">Minimum System Requirements</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#network-requirements">Network Requirements</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#external-dependencies">External Dependencies</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#required-tools-and-access">Required Tools and Access</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#development-tools">Development Tools</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#aws-access-requirements">AWS Access Requirements</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#infrastructure-deployment">Infrastructure Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-1-environment-configuration">Step 1: Environment Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-aws-cli">1.1 Configure AWS CLI</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#set-environment-variables">1.2 Set Environment Variables</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-2-infrastructure-provisioning">Step 2: Infrastructure Provisioning</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#deploy-core-infrastructure">2.1 Deploy Core Infrastructure</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-kubectl-access">2.2 Configure kubectl Access</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#install-cluster-add-ons">2.3 Install Cluster Add-ons</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-3-database-setup">Step 3: Database Setup</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-initialization">3.1 Database Initialization</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-migration">3.2 Database Migration</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#application-deployment">Application Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-4-container-image-preparation">Step 4: Container Image Preparation</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#build-and-push-images">4.1 Build and Push Images</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-5-kubernetes-deployment">Step 5: Kubernetes Deployment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#create-namespace-and-secrets">5.1 Create Namespace and Secrets</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#deploy-application-components">5.2 Deploy Application Components</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-ingress-and-load-balancer">5.3 Configure Ingress and Load Balancer</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-6-ssl-tls-configuration">Step 6: SSL/TLS Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#request-ssl-certificate">6.1 Request SSL Certificate</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-https-redirect">6.2 Configure HTTPS Redirect</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#post-deployment-configuration">Post-Deployment Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-7-application-configuration">Step 7: Application Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#initialize-application-data">7.1 Initialize Application Data</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-monitoring-and-alerting">7.2 Configure Monitoring and Alerting</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-8-security-hardening">Step 8: Security Hardening</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#network-policies">8.1 Network Policies</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#pod-security-policies">8.2 Pod Security Policies</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#verification-and-testing">Verification and Testing</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-9-deployment-verification">Step 9: Deployment Verification</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#health-checks">9.1 Health Checks</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#functional-testing">9.2 Functional Testing</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#security-testing">9.3 Security Testing</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#backup-and-recovery-setup">Backup and Recovery Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-10-backup-configuration">Step 10: Backup Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-backup">10.1 Database Backup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#application-data-backup">10.2 Application Data Backup</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#monitoring-and-maintenance">Monitoring and Maintenance</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-11-ongoing-monitoring">Step 11: Ongoing Monitoring</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#set-up-alerts">11.1 Set Up Alerts</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#performance-monitoring">11.2 Performance Monitoring</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#troubleshooting-common-issues">Troubleshooting Common Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-connection-issues">Database Connection Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#pod-startup-issues">Pod Startup Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#load-balancer-issues">Load Balancer Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#rollback-procedures">Rollback Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#emergency-rollback">Emergency Rollback</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-rollback">Database Rollback</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../deployment/environment-setup.html">Environment Setup Documentation - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#environment-types">Environment Types</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#development-environment">Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#staging-environment">Staging Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#local-development-setup">Local Development Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#prerequisites">Prerequisites</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#system-requirements">System Requirements</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#required-software">Required Software</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#development-environment-setup">Development Environment Setup</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#clone-repository">1. Clone Repository</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#environment-configuration">2. Environment Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#backend-setup">3. Backend Setup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#frontend-setup">4. Frontend Setup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#start-development-services">5. Start Development Services</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#verify-development-setup">6. Verify Development Setup</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#staging-environment-setup">Staging Environment Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#infrastructure-provisioning">Infrastructure Provisioning</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#aws-configuration">1. AWS Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#terraform-deployment">2. Terraform Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#kubernetes-configuration">3. Kubernetes Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#application-deployment">Application Deployment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#build-and-push-images">1. Build and Push Images</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#deploy-to-staging">2. Deploy to Staging</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#configure-staging-data">3. Configure Staging Data</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#production-environment-setup">Production Environment Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#pre-production-checklist">Pre-Production Checklist</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#security-review">Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#performance-review">Performance Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#operational-readiness">Operational Readiness</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#production-deployment-process">Production Deployment Process</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#final-infrastructure-review">1. Final Infrastructure Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#production-image-preparation">2. Production Image Preparation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#blue-green-deployment">3. Blue-Green Deployment</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#environment-specific-configurations">Environment-Specific Configurations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#development-configuration">Development Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#staging-configuration">Staging Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#production-configuration">Production Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#ci-cd-pipeline-configuration">CI/CD Pipeline Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#github-actions-workflow">GitHub Actions Workflow</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#environment-management-scripts">Environment Management Scripts</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#deployment-script">Deployment Script</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#environment-cleanup-script">Environment Cleanup Script</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../deployment/troubleshooting-guide.html">Troubleshooting Guide - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#general-troubleshooting-approach">General Troubleshooting Approach</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#information-gathering">1. Information Gathering</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#log-analysis">2. Log Analysis</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#network-connectivity">3. Network Connectivity</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#common-issues-and-solutions">Common Issues and Solutions</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#application-startup-issues">Application Startup Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-pod-stuck-in-pending-state">Issue: Pod Stuck in Pending State</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-pod-stuck-in-crashloopbackoff">Issue: Pod Stuck in CrashLoopBackOff</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#database-issues">Database Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-database-connection-timeout">Issue: Database Connection Timeout</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-database-migration-failures">Issue: Database Migration Failures</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#network-and-connectivity-issues">Network and Connectivity Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-service-not-accessible">Issue: Service Not Accessible</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-ssl-tls-certificate-problems">Issue: SSL/TLS Certificate Problems</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#performance-issues">Performance Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-high-response-times">Issue: High Response Times</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-memory-leaks">Issue: Memory Leaks</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#storage-issues">Storage Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-persistent-volume-problems">Issue: Persistent Volume Problems</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#monitoring-and-alerting-issues">Monitoring and Alerting Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-missing-metrics">Issue: Missing Metrics</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#emergency-procedures">Emergency Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#complete-system-recovery">Complete System Recovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#rollback-procedure">Rollback Procedure</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#getting-help">Getting Help</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#internal-resources">Internal Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#external-support">External Support</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#escalation-procedures">Escalation Procedures</a></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content style-external-links">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Machine Learning Threat Prediction - Technical Specification</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/technical-specifications/ml-threat-prediction.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="machine-learning-threat-prediction-technical-specification">
<h1>Machine Learning Threat Prediction - Technical Specification<a class="headerlink" href="#machine-learning-threat-prediction-technical-specification" title="Link to this heading"></a></h1>
<section id="overview">
<h2>Overview<a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>This document defines the machine learning infrastructure for threat prediction and anomaly detection in the Blast-Radius Security Tool. The implementation focuses on batch processing for scalability and uses multiple ML algorithms for comprehensive threat analysis.</p>
</section>
<section id="ml-architecture-design">
<h2>🧠 ML Architecture Design<a class="headerlink" href="#ml-architecture-design" title="Link to this heading"></a></h2>
<section id="core-components">
<h3>Core Components<a class="headerlink" href="#core-components" title="Link to this heading"></a></h3>
<section id="feature-engineering-pipeline">
<h4>1. Feature Engineering Pipeline<a class="headerlink" href="#feature-engineering-pipeline" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">dataclasses</span> <span class="kn">import</span> <span class="n">dataclass</span>
<span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">List</span><span class="p">,</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">Any</span><span class="p">,</span> <span class="n">Optional</span>
<span class="kn">import</span> <span class="nn">pandas</span> <span class="k">as</span> <span class="nn">pd</span>
<span class="kn">import</span> <span class="nn">numpy</span> <span class="k">as</span> <span class="nn">np</span>
<span class="kn">from</span> <span class="nn">sklearn.preprocessing</span> <span class="kn">import</span> <span class="n">StandardScaler</span><span class="p">,</span> <span class="n">LabelEncoder</span>

<span class="nd">@dataclass</span>
<span class="k">class</span> <span class="nc">AssetFeatures</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Feature set for asset threat prediction&quot;&quot;&quot;</span>
    
    <span class="c1"># Basic asset features</span>
    <span class="n">asset_type_encoded</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">environment_encoded</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">criticality_level_encoded</span><span class="p">:</span> <span class="nb">int</span>
    
    <span class="c1"># Network features</span>
    <span class="n">connection_count</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">inbound_connections</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">outbound_connections</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">network_centrality</span><span class="p">:</span> <span class="nb">float</span>
    
    <span class="c1"># Vulnerability features</span>
    <span class="n">critical_vulns_count</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">high_vulns_count</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">medium_vulns_count</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">avg_vuln_age_days</span><span class="p">:</span> <span class="nb">float</span>
    <span class="n">cvss_score_avg</span><span class="p">:</span> <span class="nb">float</span>
    
    <span class="c1"># Risk features</span>
    <span class="n">current_risk_score</span><span class="p">:</span> <span class="nb">float</span>
    <span class="n">risk_trend_7d</span><span class="p">:</span> <span class="nb">float</span>
    <span class="n">risk_trend_30d</span><span class="p">:</span> <span class="nb">float</span>
    
    <span class="c1"># Behavioral features</span>
    <span class="n">access_pattern_anomaly</span><span class="p">:</span> <span class="nb">float</span>
    <span class="n">traffic_volume_anomaly</span><span class="p">:</span> <span class="nb">float</span>
    <span class="n">configuration_change_frequency</span><span class="p">:</span> <span class="nb">float</span>
    
    <span class="c1"># Temporal features</span>
    <span class="n">days_since_last_scan</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">days_since_last_update</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">hour_of_day</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">day_of_week</span><span class="p">:</span> <span class="nb">int</span>

<span class="k">class</span> <span class="nc">FeatureEngineer</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Feature engineering for ML models&quot;&quot;&quot;</span>
    
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">db_session</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">db</span> <span class="o">=</span> <span class="n">db_session</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">scalers</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">encoders</span> <span class="o">=</span> <span class="p">{}</span>
    
    <span class="k">async</span> <span class="k">def</span> <span class="nf">extract_asset_features</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">asset_id</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AssetFeatures</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Extract features for a single asset&quot;&quot;&quot;</span>
        
        <span class="c1"># Get asset data</span>
        <span class="n">asset</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_asset_data</span><span class="p">(</span><span class="n">asset_id</span><span class="p">)</span>
        
        <span class="c1"># Calculate network features</span>
        <span class="n">network_features</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_calculate_network_features</span><span class="p">(</span><span class="n">asset_id</span><span class="p">)</span>
        
        <span class="c1"># Calculate vulnerability features</span>
        <span class="n">vuln_features</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_calculate_vulnerability_features</span><span class="p">(</span><span class="n">asset_id</span><span class="p">)</span>
        
        <span class="c1"># Calculate behavioral features</span>
        <span class="n">behavioral_features</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_calculate_behavioral_features</span><span class="p">(</span><span class="n">asset_id</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">AssetFeatures</span><span class="p">(</span>
            <span class="n">asset_type_encoded</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">_encode_categorical</span><span class="p">(</span><span class="n">asset</span><span class="o">.</span><span class="n">asset_type</span><span class="p">,</span> <span class="s1">&#39;asset_type&#39;</span><span class="p">),</span>
            <span class="n">environment_encoded</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">_encode_categorical</span><span class="p">(</span><span class="n">asset</span><span class="o">.</span><span class="n">environment</span><span class="p">,</span> <span class="s1">&#39;environment&#39;</span><span class="p">),</span>
            <span class="n">criticality_level_encoded</span><span class="o">=</span><span class="bp">self</span><span class="o">.</span><span class="n">_encode_categorical</span><span class="p">(</span><span class="n">asset</span><span class="o">.</span><span class="n">criticality_level</span><span class="p">,</span> <span class="s1">&#39;criticality&#39;</span><span class="p">),</span>
            <span class="o">**</span><span class="n">network_features</span><span class="p">,</span>
            <span class="o">**</span><span class="n">vuln_features</span><span class="p">,</span>
            <span class="o">**</span><span class="n">behavioral_features</span>
        <span class="p">)</span>
    
    <span class="k">async</span> <span class="k">def</span> <span class="nf">extract_batch_features</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">asset_ids</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="n">pd</span><span class="o">.</span><span class="n">DataFrame</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Extract features for multiple assets&quot;&quot;&quot;</span>
        <span class="n">features_list</span> <span class="o">=</span> <span class="p">[]</span>
        
        <span class="k">for</span> <span class="n">asset_id</span> <span class="ow">in</span> <span class="n">asset_ids</span><span class="p">:</span>
            <span class="n">features</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">extract_asset_features</span><span class="p">(</span><span class="n">asset_id</span><span class="p">)</span>
            <span class="n">feature_dict</span> <span class="o">=</span> <span class="n">asdict</span><span class="p">(</span><span class="n">features</span><span class="p">)</span>
            <span class="n">feature_dict</span><span class="p">[</span><span class="s1">&#39;asset_id&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">asset_id</span>
            <span class="n">features_list</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">feature_dict</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="n">pd</span><span class="o">.</span><span class="n">DataFrame</span><span class="p">(</span><span class="n">features_list</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="ml-model-implementation">
<h4>2. ML Model Implementation<a class="headerlink" href="#ml-model-implementation" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">sklearn.ensemble</span> <span class="kn">import</span> <span class="n">RandomForestClassifier</span><span class="p">,</span> <span class="n">IsolationForest</span>
<span class="kn">from</span> <span class="nn">sklearn.neural_network</span> <span class="kn">import</span> <span class="n">MLPClassifier</span>
<span class="kn">from</span> <span class="nn">sklearn.model_selection</span> <span class="kn">import</span> <span class="n">train_test_split</span><span class="p">,</span> <span class="n">cross_val_score</span>
<span class="kn">from</span> <span class="nn">sklearn.metrics</span> <span class="kn">import</span> <span class="n">classification_report</span><span class="p">,</span> <span class="n">roc_auc_score</span>
<span class="kn">import</span> <span class="nn">joblib</span>
<span class="kn">import</span> <span class="nn">asyncio</span>

<span class="k">class</span> <span class="nc">ThreatPredictionModels</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;ML models for threat prediction&quot;&quot;&quot;</span>
    
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">models</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s1">&#39;random_forest&#39;</span><span class="p">:</span> <span class="n">RandomForestClassifier</span><span class="p">(</span>
                <span class="n">n_estimators</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span>
                <span class="n">max_depth</span><span class="o">=</span><span class="mi">10</span><span class="p">,</span>
                <span class="n">random_state</span><span class="o">=</span><span class="mi">42</span><span class="p">,</span>
                <span class="n">class_weight</span><span class="o">=</span><span class="s1">&#39;balanced&#39;</span>
            <span class="p">),</span>
            <span class="s1">&#39;neural_network&#39;</span><span class="p">:</span> <span class="n">MLPClassifier</span><span class="p">(</span>
                <span class="n">hidden_layer_sizes</span><span class="o">=</span><span class="p">(</span><span class="mi">100</span><span class="p">,</span> <span class="mi">50</span><span class="p">),</span>
                <span class="n">max_iter</span><span class="o">=</span><span class="mi">1000</span><span class="p">,</span>
                <span class="n">random_state</span><span class="o">=</span><span class="mi">42</span><span class="p">,</span>
                <span class="n">early_stopping</span><span class="o">=</span><span class="kc">True</span>
            <span class="p">),</span>
            <span class="s1">&#39;isolation_forest&#39;</span><span class="p">:</span> <span class="n">IsolationForest</span><span class="p">(</span>
                <span class="n">contamination</span><span class="o">=</span><span class="mf">0.1</span><span class="p">,</span>
                <span class="n">random_state</span><span class="o">=</span><span class="mi">42</span>
            <span class="p">)</span>
        <span class="p">}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">trained_models</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">feature_scaler</span> <span class="o">=</span> <span class="n">StandardScaler</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">is_trained</span> <span class="o">=</span> <span class="kc">False</span>
    
    <span class="k">async</span> <span class="k">def</span> <span class="nf">train_models</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">training_data</span><span class="p">:</span> <span class="n">pd</span><span class="o">.</span><span class="n">DataFrame</span><span class="p">,</span> <span class="n">target_column</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s1">&#39;is_compromised&#39;</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Train all ML models&quot;&quot;&quot;</span>
        
        <span class="c1"># Prepare features and target</span>
        <span class="n">feature_columns</span> <span class="o">=</span> <span class="p">[</span><span class="n">col</span> <span class="k">for</span> <span class="n">col</span> <span class="ow">in</span> <span class="n">training_data</span><span class="o">.</span><span class="n">columns</span> 
                          <span class="k">if</span> <span class="n">col</span> <span class="ow">not</span> <span class="ow">in</span> <span class="p">[</span><span class="s1">&#39;asset_id&#39;</span><span class="p">,</span> <span class="n">target_column</span><span class="p">]]</span>
        <span class="n">X</span> <span class="o">=</span> <span class="n">training_data</span><span class="p">[</span><span class="n">feature_columns</span><span class="p">]</span>
        <span class="n">y</span> <span class="o">=</span> <span class="n">training_data</span><span class="p">[</span><span class="n">target_column</span><span class="p">]</span>
        
        <span class="c1"># Scale features</span>
        <span class="n">X_scaled</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">feature_scaler</span><span class="o">.</span><span class="n">fit_transform</span><span class="p">(</span><span class="n">X</span><span class="p">)</span>
        
        <span class="c1"># Split data</span>
        <span class="n">X_train</span><span class="p">,</span> <span class="n">X_test</span><span class="p">,</span> <span class="n">y_train</span><span class="p">,</span> <span class="n">y_test</span> <span class="o">=</span> <span class="n">train_test_split</span><span class="p">(</span>
            <span class="n">X_scaled</span><span class="p">,</span> <span class="n">y</span><span class="p">,</span> <span class="n">test_size</span><span class="o">=</span><span class="mf">0.2</span><span class="p">,</span> <span class="n">random_state</span><span class="o">=</span><span class="mi">42</span><span class="p">,</span> <span class="n">stratify</span><span class="o">=</span><span class="n">y</span>
        <span class="p">)</span>
        
        <span class="c1"># Train models</span>
        <span class="k">for</span> <span class="n">model_name</span><span class="p">,</span> <span class="n">model</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">models</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
            <span class="k">if</span> <span class="n">model_name</span> <span class="o">==</span> <span class="s1">&#39;isolation_forest&#39;</span><span class="p">:</span>
                <span class="c1"># Unsupervised model - train on normal data only</span>
                <span class="n">normal_data</span> <span class="o">=</span> <span class="n">X_train</span><span class="p">[</span><span class="n">y_train</span> <span class="o">==</span> <span class="mi">0</span><span class="p">]</span>
                <span class="n">model</span><span class="o">.</span><span class="n">fit</span><span class="p">(</span><span class="n">normal_data</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="c1"># Supervised models</span>
                <span class="n">model</span><span class="o">.</span><span class="n">fit</span><span class="p">(</span><span class="n">X_train</span><span class="p">,</span> <span class="n">y_train</span><span class="p">)</span>
            
            <span class="bp">self</span><span class="o">.</span><span class="n">trained_models</span><span class="p">[</span><span class="n">model_name</span><span class="p">]</span> <span class="o">=</span> <span class="n">model</span>
            
            <span class="c1"># Evaluate model</span>
            <span class="k">if</span> <span class="n">model_name</span> <span class="o">!=</span> <span class="s1">&#39;isolation_forest&#39;</span><span class="p">:</span>
                <span class="n">y_pred</span> <span class="o">=</span> <span class="n">model</span><span class="o">.</span><span class="n">predict</span><span class="p">(</span><span class="n">X_test</span><span class="p">)</span>
                <span class="n">y_pred_proba</span> <span class="o">=</span> <span class="n">model</span><span class="o">.</span><span class="n">predict_proba</span><span class="p">(</span><span class="n">X_test</span><span class="p">)[:,</span> <span class="mi">1</span><span class="p">]</span>
                
                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="se">\n</span><span class="si">{</span><span class="n">model_name</span><span class="o">.</span><span class="n">upper</span><span class="p">()</span><span class="si">}</span><span class="s2"> Performance:&quot;</span><span class="p">)</span>
                <span class="nb">print</span><span class="p">(</span><span class="n">classification_report</span><span class="p">(</span><span class="n">y_test</span><span class="p">,</span> <span class="n">y_pred</span><span class="p">))</span>
                <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;ROC AUC: </span><span class="si">{</span><span class="n">roc_auc_score</span><span class="p">(</span><span class="n">y_test</span><span class="p">,</span><span class="w"> </span><span class="n">y_pred_proba</span><span class="p">)</span><span class="si">:</span><span class="s2">.3f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        
        <span class="bp">self</span><span class="o">.</span><span class="n">is_trained</span> <span class="o">=</span> <span class="kc">True</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_save_models</span><span class="p">()</span>
    
    <span class="k">async</span> <span class="k">def</span> <span class="nf">predict_threat_probability</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">features</span><span class="p">:</span> <span class="n">pd</span><span class="o">.</span><span class="n">DataFrame</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">np</span><span class="o">.</span><span class="n">ndarray</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Predict threat probability for assets&quot;&quot;&quot;</span>
        
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">is_trained</span><span class="p">:</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_load_models</span><span class="p">()</span>
        
        <span class="c1"># Scale features</span>
        <span class="n">feature_columns</span> <span class="o">=</span> <span class="p">[</span><span class="n">col</span> <span class="k">for</span> <span class="n">col</span> <span class="ow">in</span> <span class="n">features</span><span class="o">.</span><span class="n">columns</span> <span class="k">if</span> <span class="n">col</span> <span class="o">!=</span> <span class="s1">&#39;asset_id&#39;</span><span class="p">]</span>
        <span class="n">X_scaled</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">feature_scaler</span><span class="o">.</span><span class="n">transform</span><span class="p">(</span><span class="n">features</span><span class="p">[</span><span class="n">feature_columns</span><span class="p">])</span>
        
        <span class="n">predictions</span> <span class="o">=</span> <span class="p">{}</span>
        
        <span class="c1"># Random Forest predictions</span>
        <span class="n">rf_proba</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">trained_models</span><span class="p">[</span><span class="s1">&#39;random_forest&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">predict_proba</span><span class="p">(</span><span class="n">X_scaled</span><span class="p">)[:,</span> <span class="mi">1</span><span class="p">]</span>
        <span class="n">predictions</span><span class="p">[</span><span class="s1">&#39;random_forest&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">rf_proba</span>
        
        <span class="c1"># Neural Network predictions</span>
        <span class="n">nn_proba</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">trained_models</span><span class="p">[</span><span class="s1">&#39;neural_network&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">predict_proba</span><span class="p">(</span><span class="n">X_scaled</span><span class="p">)[:,</span> <span class="mi">1</span><span class="p">]</span>
        <span class="n">predictions</span><span class="p">[</span><span class="s1">&#39;neural_network&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">nn_proba</span>
        
        <span class="c1"># Isolation Forest anomaly scores</span>
        <span class="n">iso_scores</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">trained_models</span><span class="p">[</span><span class="s1">&#39;isolation_forest&#39;</span><span class="p">]</span><span class="o">.</span><span class="n">decision_function</span><span class="p">(</span><span class="n">X_scaled</span><span class="p">)</span>
        <span class="c1"># Convert to probability (higher score = less anomalous)</span>
        <span class="n">iso_proba</span> <span class="o">=</span> <span class="mi">1</span> <span class="o">/</span> <span class="p">(</span><span class="mi">1</span> <span class="o">+</span> <span class="n">np</span><span class="o">.</span><span class="n">exp</span><span class="p">(</span><span class="o">-</span><span class="n">iso_scores</span><span class="p">))</span>
        <span class="n">predictions</span><span class="p">[</span><span class="s1">&#39;isolation_forest&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="mi">1</span> <span class="o">-</span> <span class="n">iso_proba</span>  <span class="c1"># Invert for threat probability</span>
        
        <span class="c1"># Ensemble prediction (weighted average)</span>
        <span class="n">ensemble_proba</span> <span class="o">=</span> <span class="p">(</span>
            <span class="mf">0.4</span> <span class="o">*</span> <span class="n">rf_proba</span> <span class="o">+</span> 
            <span class="mf">0.4</span> <span class="o">*</span> <span class="n">nn_proba</span> <span class="o">+</span> 
            <span class="mf">0.2</span> <span class="o">*</span> <span class="n">predictions</span><span class="p">[</span><span class="s1">&#39;isolation_forest&#39;</span><span class="p">]</span>
        <span class="p">)</span>
        <span class="n">predictions</span><span class="p">[</span><span class="s1">&#39;ensemble&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="n">ensemble_proba</span>
        
        <span class="k">return</span> <span class="n">predictions</span>
    
    <span class="k">async</span> <span class="k">def</span> <span class="nf">detect_anomalies</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">features</span><span class="p">:</span> <span class="n">pd</span><span class="o">.</span><span class="n">DataFrame</span><span class="p">,</span> <span class="n">threshold</span><span class="p">:</span> <span class="nb">float</span> <span class="o">=</span> <span class="mf">0.7</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Dict</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Detect anomalous assets&quot;&quot;&quot;</span>
        
        <span class="n">predictions</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">predict_threat_probability</span><span class="p">(</span><span class="n">features</span><span class="p">)</span>
        <span class="n">anomalies</span> <span class="o">=</span> <span class="p">[]</span>
        
        <span class="k">for</span> <span class="n">idx</span><span class="p">,</span> <span class="n">asset_id</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">features</span><span class="p">[</span><span class="s1">&#39;asset_id&#39;</span><span class="p">]):</span>
            <span class="n">ensemble_score</span> <span class="o">=</span> <span class="n">predictions</span><span class="p">[</span><span class="s1">&#39;ensemble&#39;</span><span class="p">][</span><span class="n">idx</span><span class="p">]</span>
            
            <span class="k">if</span> <span class="n">ensemble_score</span> <span class="o">&gt;</span> <span class="n">threshold</span><span class="p">:</span>
                <span class="n">anomaly</span> <span class="o">=</span> <span class="p">{</span>
                    <span class="s1">&#39;asset_id&#39;</span><span class="p">:</span> <span class="n">asset_id</span><span class="p">,</span>
                    <span class="s1">&#39;anomaly_score&#39;</span><span class="p">:</span> <span class="nb">float</span><span class="p">(</span><span class="n">ensemble_score</span><span class="p">),</span>
                    <span class="s1">&#39;model_scores&#39;</span><span class="p">:</span> <span class="p">{</span>
                        <span class="s1">&#39;random_forest&#39;</span><span class="p">:</span> <span class="nb">float</span><span class="p">(</span><span class="n">predictions</span><span class="p">[</span><span class="s1">&#39;random_forest&#39;</span><span class="p">][</span><span class="n">idx</span><span class="p">]),</span>
                        <span class="s1">&#39;neural_network&#39;</span><span class="p">:</span> <span class="nb">float</span><span class="p">(</span><span class="n">predictions</span><span class="p">[</span><span class="s1">&#39;neural_network&#39;</span><span class="p">][</span><span class="n">idx</span><span class="p">]),</span>
                        <span class="s1">&#39;isolation_forest&#39;</span><span class="p">:</span> <span class="nb">float</span><span class="p">(</span><span class="n">predictions</span><span class="p">[</span><span class="s1">&#39;isolation_forest&#39;</span><span class="p">][</span><span class="n">idx</span><span class="p">])</span>
                    <span class="p">},</span>
                    <span class="s1">&#39;risk_level&#39;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">_categorize_risk</span><span class="p">(</span><span class="n">ensemble_score</span><span class="p">)</span>
                <span class="p">}</span>
                <span class="n">anomalies</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">anomaly</span><span class="p">)</span>
        
        <span class="k">return</span> <span class="nb">sorted</span><span class="p">(</span><span class="n">anomalies</span><span class="p">,</span> <span class="n">key</span><span class="o">=</span><span class="k">lambda</span> <span class="n">x</span><span class="p">:</span> <span class="n">x</span><span class="p">[</span><span class="s1">&#39;anomaly_score&#39;</span><span class="p">],</span> <span class="n">reverse</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    
    <span class="k">def</span> <span class="nf">_categorize_risk</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">score</span><span class="p">:</span> <span class="nb">float</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Categorize risk level based on score&quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">score</span> <span class="o">&gt;=</span> <span class="mf">0.9</span><span class="p">:</span>
            <span class="k">return</span> <span class="s1">&#39;critical&#39;</span>
        <span class="k">elif</span> <span class="n">score</span> <span class="o">&gt;=</span> <span class="mf">0.7</span><span class="p">:</span>
            <span class="k">return</span> <span class="s1">&#39;high&#39;</span>
        <span class="k">elif</span> <span class="n">score</span> <span class="o">&gt;=</span> <span class="mf">0.5</span><span class="p">:</span>
            <span class="k">return</span> <span class="s1">&#39;medium&#39;</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="k">return</span> <span class="s1">&#39;low&#39;</span>
</pre></div>
</div>
</section>
<section id="batch-processing-service">
<h4>3. Batch Processing Service<a class="headerlink" href="#batch-processing-service" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">celery</span> <span class="kn">import</span> <span class="n">Celery</span>
<span class="kn">from</span> <span class="nn">datetime</span> <span class="kn">import</span> <span class="n">datetime</span><span class="p">,</span> <span class="n">timedelta</span>
<span class="kn">import</span> <span class="nn">logging</span>

<span class="k">class</span> <span class="nc">ThreatPredictionService</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Service for batch threat prediction processing&quot;&quot;&quot;</span>
    
    <span class="k">def</span> <span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">db_session</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">db</span> <span class="o">=</span> <span class="n">db_session</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">feature_engineer</span> <span class="o">=</span> <span class="n">FeatureEngineer</span><span class="p">(</span><span class="n">db_session</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">ml_models</span> <span class="o">=</span> <span class="n">ThreatPredictionModels</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>
    
    <span class="k">async</span> <span class="k">def</span> <span class="nf">run_batch_prediction</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">asset_ids</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Run batch threat prediction for assets&quot;&quot;&quot;</span>
        
        <span class="n">start_time</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">utcnow</span><span class="p">()</span>
        
        <span class="c1"># Get asset IDs if not provided</span>
        <span class="k">if</span> <span class="n">asset_ids</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">asset_ids</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_active_asset_ids</span><span class="p">()</span>
        
        <span class="bp">self</span><span class="o">.</span><span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Starting batch prediction for </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">asset_ids</span><span class="p">)</span><span class="si">}</span><span class="s2"> assets&quot;</span><span class="p">)</span>
        
        <span class="k">try</span><span class="p">:</span>
            <span class="c1"># Extract features</span>
            <span class="n">features_df</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">feature_engineer</span><span class="o">.</span><span class="n">extract_batch_features</span><span class="p">(</span><span class="n">asset_ids</span><span class="p">)</span>
            
            <span class="c1"># Run predictions</span>
            <span class="n">predictions</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">ml_models</span><span class="o">.</span><span class="n">predict_threat_probability</span><span class="p">(</span><span class="n">features_df</span><span class="p">)</span>
            
            <span class="c1"># Store results</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_store_prediction_results</span><span class="p">(</span><span class="n">features_df</span><span class="p">,</span> <span class="n">predictions</span><span class="p">)</span>
            
            <span class="c1"># Detect high-risk assets</span>
            <span class="n">anomalies</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">ml_models</span><span class="o">.</span><span class="n">detect_anomalies</span><span class="p">(</span><span class="n">features_df</span><span class="p">)</span>
            
            <span class="c1"># Generate alerts for critical threats</span>
            <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_generate_threat_alerts</span><span class="p">(</span><span class="n">anomalies</span><span class="p">)</span>
            
            <span class="n">end_time</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">utcnow</span><span class="p">()</span>
            <span class="n">processing_time</span> <span class="o">=</span> <span class="p">(</span><span class="n">end_time</span> <span class="o">-</span> <span class="n">start_time</span><span class="p">)</span><span class="o">.</span><span class="n">total_seconds</span><span class="p">()</span>
            
            <span class="n">result</span> <span class="o">=</span> <span class="p">{</span>
                <span class="s1">&#39;status&#39;</span><span class="p">:</span> <span class="s1">&#39;completed&#39;</span><span class="p">,</span>
                <span class="s1">&#39;assets_processed&#39;</span><span class="p">:</span> <span class="nb">len</span><span class="p">(</span><span class="n">asset_ids</span><span class="p">),</span>
                <span class="s1">&#39;anomalies_detected&#39;</span><span class="p">:</span> <span class="nb">len</span><span class="p">(</span><span class="n">anomalies</span><span class="p">),</span>
                <span class="s1">&#39;processing_time_seconds&#39;</span><span class="p">:</span> <span class="n">processing_time</span><span class="p">,</span>
                <span class="s1">&#39;high_risk_assets&#39;</span><span class="p">:</span> <span class="p">[</span><span class="n">a</span> <span class="k">for</span> <span class="n">a</span> <span class="ow">in</span> <span class="n">anomalies</span> <span class="k">if</span> <span class="n">a</span><span class="p">[</span><span class="s1">&#39;risk_level&#39;</span><span class="p">]</span> <span class="ow">in</span> <span class="p">[</span><span class="s1">&#39;critical&#39;</span><span class="p">,</span> <span class="s1">&#39;high&#39;</span><span class="p">]],</span>
                <span class="s1">&#39;timestamp&#39;</span><span class="p">:</span> <span class="n">end_time</span><span class="o">.</span><span class="n">isoformat</span><span class="p">()</span>
            <span class="p">}</span>
            
            <span class="bp">self</span><span class="o">.</span><span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Batch prediction completed: </span><span class="si">{</span><span class="n">result</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
            <span class="k">return</span> <span class="n">result</span>
            
        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Batch prediction failed: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
            <span class="k">raise</span>
    
    <span class="k">async</span> <span class="k">def</span> <span class="nf">retrain_models</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">lookback_days</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">90</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Retrain ML models with recent data&quot;&quot;&quot;</span>
        
        <span class="c1"># Get training data</span>
        <span class="n">training_data</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_prepare_training_data</span><span class="p">(</span><span class="n">lookback_days</span><span class="p">)</span>
        
        <span class="c1"># Train models</span>
        <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">ml_models</span><span class="o">.</span><span class="n">train_models</span><span class="p">(</span><span class="n">training_data</span><span class="p">)</span>
        
        <span class="c1"># Evaluate model performance</span>
        <span class="n">performance_metrics</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_evaluate_model_performance</span><span class="p">()</span>
        
        <span class="k">return</span> <span class="p">{</span>
            <span class="s1">&#39;status&#39;</span><span class="p">:</span> <span class="s1">&#39;completed&#39;</span><span class="p">,</span>
            <span class="s1">&#39;training_samples&#39;</span><span class="p">:</span> <span class="nb">len</span><span class="p">(</span><span class="n">training_data</span><span class="p">),</span>
            <span class="s1">&#39;performance_metrics&#39;</span><span class="p">:</span> <span class="n">performance_metrics</span><span class="p">,</span>
            <span class="s1">&#39;retrained_at&#39;</span><span class="p">:</span> <span class="n">datetime</span><span class="o">.</span><span class="n">utcnow</span><span class="p">()</span><span class="o">.</span><span class="n">isoformat</span><span class="p">()</span>
        <span class="p">}</span>
</pre></div>
</div>
</section>
<section id="database-models-for-ml-results">
<h4>4. Database Models for ML Results<a class="headerlink" href="#database-models-for-ml-results" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">class</span> <span class="nc">ThreatPrediction</span><span class="p">(</span><span class="n">Base</span><span class="p">):</span>
    <span class="n">__tablename__</span> <span class="o">=</span> <span class="s2">&quot;threat_predictions&quot;</span>
    
    <span class="nb">id</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">UUID</span><span class="p">(</span><span class="n">as_uuid</span><span class="o">=</span><span class="kc">True</span><span class="p">),</span> <span class="n">primary_key</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="n">uuid</span><span class="o">.</span><span class="n">uuid4</span><span class="p">)</span>
    <span class="n">asset_id</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">UUID</span><span class="p">(</span><span class="n">as_uuid</span><span class="o">=</span><span class="kc">True</span><span class="p">),</span> <span class="n">ForeignKey</span><span class="p">(</span><span class="s2">&quot;assets.id&quot;</span><span class="p">),</span> <span class="n">nullable</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
    <span class="n">prediction_date</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">DateTime</span><span class="p">(</span><span class="n">timezone</span><span class="o">=</span><span class="kc">True</span><span class="p">),</span> <span class="n">default</span><span class="o">=</span><span class="n">func</span><span class="o">.</span><span class="n">now</span><span class="p">())</span>
    
    <span class="c1"># Model predictions</span>
    <span class="n">random_forest_score</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">DECIMAL</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">4</span><span class="p">))</span>
    <span class="n">neural_network_score</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">DECIMAL</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">4</span><span class="p">))</span>
    <span class="n">isolation_forest_score</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">DECIMAL</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">4</span><span class="p">))</span>
    <span class="n">ensemble_score</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">DECIMAL</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">4</span><span class="p">))</span>
    
    <span class="c1"># Risk categorization</span>
    <span class="n">risk_level</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">String</span><span class="p">(</span><span class="mi">20</span><span class="p">))</span>  <span class="c1"># critical, high, medium, low</span>
    <span class="n">confidence_score</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">DECIMAL</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">4</span><span class="p">))</span>
    
    <span class="c1"># Feature importance</span>
    <span class="n">top_risk_factors</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">JSONB</span><span class="p">)</span>  <span class="c1"># Top contributing features</span>
    
    <span class="c1"># Model metadata</span>
    <span class="n">model_version</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">String</span><span class="p">(</span><span class="mi">50</span><span class="p">))</span>
    <span class="n">feature_vector</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">JSONB</span><span class="p">)</span>  <span class="c1"># Store feature values for analysis</span>
    
    <span class="c1"># Relationships</span>
    <span class="n">asset</span> <span class="o">=</span> <span class="n">relationship</span><span class="p">(</span><span class="s2">&quot;Asset&quot;</span><span class="p">,</span> <span class="n">back_populates</span><span class="o">=</span><span class="s2">&quot;threat_predictions&quot;</span><span class="p">)</span>

<span class="k">class</span> <span class="nc">MLModelMetadata</span><span class="p">(</span><span class="n">Base</span><span class="p">):</span>
    <span class="n">__tablename__</span> <span class="o">=</span> <span class="s2">&quot;ml_model_metadata&quot;</span>
    
    <span class="nb">id</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">UUID</span><span class="p">(</span><span class="n">as_uuid</span><span class="o">=</span><span class="kc">True</span><span class="p">),</span> <span class="n">primary_key</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="n">uuid</span><span class="o">.</span><span class="n">uuid4</span><span class="p">)</span>
    <span class="n">model_name</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">String</span><span class="p">(</span><span class="mi">100</span><span class="p">),</span> <span class="n">nullable</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
    <span class="n">model_version</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">String</span><span class="p">(</span><span class="mi">50</span><span class="p">),</span> <span class="n">nullable</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
    <span class="n">model_type</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">String</span><span class="p">(</span><span class="mi">50</span><span class="p">))</span>  <span class="c1"># classification, anomaly_detection</span>
    
    <span class="c1"># Training metadata</span>
    <span class="n">training_date</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">DateTime</span><span class="p">(</span><span class="n">timezone</span><span class="o">=</span><span class="kc">True</span><span class="p">),</span> <span class="n">default</span><span class="o">=</span><span class="n">func</span><span class="o">.</span><span class="n">now</span><span class="p">())</span>
    <span class="n">training_samples</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">Integer</span><span class="p">)</span>
    <span class="n">feature_count</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">Integer</span><span class="p">)</span>
    
    <span class="c1"># Performance metrics</span>
    <span class="n">accuracy</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">DECIMAL</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">4</span><span class="p">))</span>
    <span class="n">precision</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">DECIMAL</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">4</span><span class="p">))</span>
    <span class="n">recall</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">DECIMAL</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">4</span><span class="p">))</span>
    <span class="n">f1_score</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">DECIMAL</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">4</span><span class="p">))</span>
    <span class="n">roc_auc</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">DECIMAL</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">4</span><span class="p">))</span>
    
    <span class="c1"># Model configuration</span>
    <span class="n">hyperparameters</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">JSONB</span><span class="p">)</span>
    <span class="n">feature_importance</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">JSONB</span><span class="p">)</span>
    
    <span class="c1"># Status</span>
    <span class="n">is_active</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">Boolean</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    <span class="n">deployment_date</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">DateTime</span><span class="p">(</span><span class="n">timezone</span><span class="o">=</span><span class="kc">True</span><span class="p">))</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="celery-tasks-for-batch-processing">
<h2>🔄 Celery Tasks for Batch Processing<a class="headerlink" href="#celery-tasks-for-batch-processing" title="Link to this heading"></a></h2>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">celery</span> <span class="kn">import</span> <span class="n">Celery</span>
<span class="kn">from</span> <span class="nn">celery.schedules</span> <span class="kn">import</span> <span class="n">crontab</span>

<span class="nd">@celery_app</span><span class="o">.</span><span class="n">task</span>
<span class="k">def</span> <span class="nf">run_daily_threat_prediction</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Daily batch threat prediction task&quot;&quot;&quot;</span>
    
    <span class="n">db</span> <span class="o">=</span> <span class="n">SessionLocal</span><span class="p">()</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">service</span> <span class="o">=</span> <span class="n">ThreatPredictionService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">result</span> <span class="o">=</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">run</span><span class="p">(</span><span class="n">service</span><span class="o">.</span><span class="n">run_batch_prediction</span><span class="p">())</span>
        <span class="k">return</span> <span class="n">result</span>
    <span class="k">finally</span><span class="p">:</span>
        <span class="n">db</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>

<span class="nd">@celery_app</span><span class="o">.</span><span class="n">task</span>
<span class="k">def</span> <span class="nf">retrain_ml_models</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Weekly model retraining task&quot;&quot;&quot;</span>
    
    <span class="n">db</span> <span class="o">=</span> <span class="n">SessionLocal</span><span class="p">()</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">service</span> <span class="o">=</span> <span class="n">ThreatPredictionService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
        <span class="n">result</span> <span class="o">=</span> <span class="n">asyncio</span><span class="o">.</span><span class="n">run</span><span class="p">(</span><span class="n">service</span><span class="o">.</span><span class="n">retrain_models</span><span class="p">())</span>
        <span class="k">return</span> <span class="n">result</span>
    <span class="k">finally</span><span class="p">:</span>
        <span class="n">db</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>

<span class="c1"># Schedule tasks</span>
<span class="n">celery_app</span><span class="o">.</span><span class="n">conf</span><span class="o">.</span><span class="n">beat_schedule</span> <span class="o">=</span> <span class="p">{</span>
    <span class="s1">&#39;daily-threat-prediction&#39;</span><span class="p">:</span> <span class="p">{</span>
        <span class="s1">&#39;task&#39;</span><span class="p">:</span> <span class="s1">&#39;run_daily_threat_prediction&#39;</span><span class="p">,</span>
        <span class="s1">&#39;schedule&#39;</span><span class="p">:</span> <span class="n">crontab</span><span class="p">(</span><span class="n">hour</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">minute</span><span class="o">=</span><span class="mi">0</span><span class="p">),</span>  <span class="c1"># Run at 2 AM daily</span>
    <span class="p">},</span>
    <span class="s1">&#39;weekly-model-retraining&#39;</span><span class="p">:</span> <span class="p">{</span>
        <span class="s1">&#39;task&#39;</span><span class="p">:</span> <span class="s1">&#39;retrain_ml_models&#39;</span><span class="p">,</span>
        <span class="s1">&#39;schedule&#39;</span><span class="p">:</span> <span class="n">crontab</span><span class="p">(</span><span class="n">hour</span><span class="o">=</span><span class="mi">3</span><span class="p">,</span> <span class="n">minute</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">day_of_week</span><span class="o">=</span><span class="mi">0</span><span class="p">),</span>  <span class="c1"># Sunday 3 AM</span>
    <span class="p">},</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
<section id="api-endpoints">
<h2>📊 API Endpoints<a class="headerlink" href="#api-endpoints" title="Link to this heading"></a></h2>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="nd">@app</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/ml/predict/batch&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">])</span>
<span class="k">async</span> <span class="k">def</span> <span class="nf">run_batch_threat_prediction</span><span class="p">(</span>
    <span class="n">asset_ids</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">List</span><span class="p">[</span><span class="nb">str</span><span class="p">]]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Trigger batch threat prediction&quot;&quot;&quot;</span>
    
    <span class="n">service</span> <span class="o">=</span> <span class="n">ThreatPredictionService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
    <span class="n">result</span> <span class="o">=</span> <span class="k">await</span> <span class="n">service</span><span class="o">.</span><span class="n">run_batch_prediction</span><span class="p">(</span><span class="n">asset_ids</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">result</span>

<span class="nd">@app</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;/ml/predictions/</span><span class="si">{asset_id}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">response_model</span><span class="o">=</span><span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">])</span>
<span class="k">async</span> <span class="k">def</span> <span class="nf">get_asset_threat_prediction</span><span class="p">(</span>
    <span class="n">asset_id</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get latest threat prediction for an asset&quot;&quot;&quot;</span>
    
    <span class="n">prediction</span> <span class="o">=</span> <span class="n">db</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">ThreatPrediction</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
        <span class="n">ThreatPrediction</span><span class="o">.</span><span class="n">asset_id</span> <span class="o">==</span> <span class="n">asset_id</span>
    <span class="p">)</span><span class="o">.</span><span class="n">order_by</span><span class="p">(</span><span class="n">ThreatPrediction</span><span class="o">.</span><span class="n">prediction_date</span><span class="o">.</span><span class="n">desc</span><span class="p">())</span><span class="o">.</span><span class="n">first</span><span class="p">()</span>
    
    <span class="k">if</span> <span class="ow">not</span> <span class="n">prediction</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="n">status_code</span><span class="o">=</span><span class="mi">404</span><span class="p">,</span> <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;No predictions found&quot;</span><span class="p">)</span>
    
    <span class="k">return</span> <span class="p">{</span>
        <span class="s2">&quot;asset_id&quot;</span><span class="p">:</span> <span class="nb">str</span><span class="p">(</span><span class="n">prediction</span><span class="o">.</span><span class="n">asset_id</span><span class="p">),</span>
        <span class="s2">&quot;prediction_date&quot;</span><span class="p">:</span> <span class="n">prediction</span><span class="o">.</span><span class="n">prediction_date</span><span class="o">.</span><span class="n">isoformat</span><span class="p">(),</span>
        <span class="s2">&quot;ensemble_score&quot;</span><span class="p">:</span> <span class="nb">float</span><span class="p">(</span><span class="n">prediction</span><span class="o">.</span><span class="n">ensemble_score</span><span class="p">),</span>
        <span class="s2">&quot;risk_level&quot;</span><span class="p">:</span> <span class="n">prediction</span><span class="o">.</span><span class="n">risk_level</span><span class="p">,</span>
        <span class="s2">&quot;confidence_score&quot;</span><span class="p">:</span> <span class="nb">float</span><span class="p">(</span><span class="n">prediction</span><span class="o">.</span><span class="n">confidence_score</span><span class="p">),</span>
        <span class="s2">&quot;top_risk_factors&quot;</span><span class="p">:</span> <span class="n">prediction</span><span class="o">.</span><span class="n">top_risk_factors</span>
    <span class="p">}</span>

<span class="nd">@app</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/ml/models/retrain&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span> <span class="nf">retrain_models</span><span class="p">(</span>
    <span class="n">lookback_days</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">90</span><span class="p">,</span>
    <span class="n">db</span><span class="p">:</span> <span class="n">Session</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_db</span><span class="p">)</span>
<span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Trigger model retraining&quot;&quot;&quot;</span>
    
    <span class="n">service</span> <span class="o">=</span> <span class="n">ThreatPredictionService</span><span class="p">(</span><span class="n">db</span><span class="p">)</span>
    <span class="n">result</span> <span class="o">=</span> <span class="k">await</span> <span class="n">service</span><span class="o">.</span><span class="n">retrain_models</span><span class="p">(</span><span class="n">lookback_days</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">result</span>
</pre></div>
</div>
<p>This ML infrastructure provides a robust foundation for threat prediction with batch processing, multiple algorithms, and comprehensive result storage.</p>
</section>
</section>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, Blast-Radius Security Tool Team.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>