# Environment Setup Documentation - Blast-Radius Security Tool

## Overview

This document provides detailed instructions for setting up different environments (development, staging, production) for the Blast-Radius Security Tool, including local development setup, CI/CD pipeline configuration, and environment-specific configurations.

## Environment Types

### Development Environment
- **Purpose**: Local development and testing
- **Infrastructure**: Docker Compose or local Kubernetes
- **Database**: PostgreSQL container or local instance
- **Cache**: Redis container
- **Monitoring**: Basic logging and metrics

### Staging Environment
- **Purpose**: Pre-production testing and validation
- **Infrastructure**: Kubernetes cluster (smaller scale)
- **Database**: RDS instance (smaller instance type)
- **Cache**: ElastiCache (single node)
- **Monitoring**: Full monitoring stack

### Production Environment
- **Purpose**: Live application serving users
- **Infrastructure**: Kubernetes cluster (full scale)
- **Database**: RDS Multi-AZ with read replicas
- **Cache**: ElastiCache cluster
- **Monitoring**: Comprehensive monitoring and alerting

## Local Development Setup

### Prerequisites

#### System Requirements
- **Operating System**: Linux, macOS, or Windows with WSL2
- **Memory**: Minimum 8GB RAM (16GB recommended)
- **Storage**: 50GB available disk space
- **CPU**: 4+ cores recommended

#### Required Software
```bash
# Install Docker and Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install Python 3.11+
sudo apt update
sudo apt install python3.11 python3.11-venv python3.11-dev

# Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Git
sudo apt install git
```

### Development Environment Setup

#### 1. Clone Repository
```bash
# Clone the repository
git clone https://github.com/your-org/blast-radius.git
cd blast-radius

# Create development branch
git checkout -b feature/your-feature-name
```

#### 2. Environment Configuration
```bash
# Copy environment template
cp .env.example .env.development

# Edit environment variables
cat > .env.development << 'EOF'
# Application Configuration
DEBUG=true
ENVIRONMENT=development
SECRET_KEY=dev-secret-key-change-in-production
JWT_SECRET_KEY=dev-jwt-secret-change-in-production

# Database Configuration
DATABASE_URL=postgresql://blast_radius:password@localhost:5432/blast_radius_dev
REDIS_URL=redis://localhost:6379/0

# External Services (Development)
ENABLE_EXTERNAL_APIS=false
LOG_LEVEL=DEBUG

# Security Settings (Relaxed for Development)
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
CSRF_TRUSTED_ORIGINS=["http://localhost:3000"]

# Monitoring (Optional for Development)
PROMETHEUS_ENABLED=false
GRAFANA_ENABLED=false
EOF
```

#### 3. Backend Setup
```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python3.11 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements/development.txt

# Install pre-commit hooks
pre-commit install

# Run database migrations
alembic upgrade head

# Create development superuser
python manage.py create-superuser --email admin@localhost --password admin123

# Load sample data
python manage.py load-sample-data
```

#### 4. Frontend Setup
```bash
# Navigate to frontend directory
cd ../frontend

# Install dependencies
npm install

# Install development tools
npm install -g @angular/cli

# Build for development
npm run build:dev
```

#### 5. Start Development Services
```bash
# Start infrastructure services
docker-compose -f docker-compose.dev.yml up -d postgres redis

# Start backend (in backend directory)
cd backend
source venv/bin/activate
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Start frontend (in frontend directory)
cd ../frontend
npm run serve

# Start Celery worker (in backend directory)
cd ../backend
celery -A app.celery_app worker --loglevel=info

# Start Celery beat scheduler
celery -A app.celery_app beat --loglevel=info
```

#### 6. Verify Development Setup
```bash
# Test backend API
curl http://localhost:8000/health
curl http://localhost:8000/api/v1/assets

# Test frontend
curl http://localhost:3000

# Test database connection
python -c "from app.db.session import SessionLocal; db = SessionLocal(); print('Database connected successfully')"

# Test Redis connection
redis-cli ping
```

## Staging Environment Setup

### Infrastructure Provisioning

#### 1. AWS Configuration
```bash
# Configure AWS CLI for staging
aws configure --profile staging
# Use staging-specific credentials

# Set environment variables
export AWS_PROFILE=staging
export AWS_REGION=us-west-2
export ENVIRONMENT=staging
```

#### 2. Terraform Deployment
```bash
# Initialize Terraform for staging
cd infrastructure/terraform
terraform init
terraform workspace new staging
terraform workspace select staging

# Deploy staging infrastructure
terraform plan -var-file="environments/staging.tfvars" -out=staging.tfplan
terraform apply staging.tfplan
```

#### 3. Kubernetes Configuration
```bash
# Update kubeconfig for staging cluster
aws eks update-kubeconfig --region us-west-2 --name blast-radius-staging-cluster

# Verify cluster access
kubectl cluster-info
kubectl get nodes

# Create staging namespace
kubectl create namespace blast-radius-staging
```

### Application Deployment

#### 1. Build and Push Images
```bash
# Build staging images
docker build -t blast-radius-backend:staging -f backend/Dockerfile backend/
docker build -t blast-radius-frontend:staging -f frontend/Dockerfile frontend/

# Tag and push to ECR
aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin $ACCOUNT_ID.dkr.ecr.us-west-2.amazonaws.com

docker tag blast-radius-backend:staging $ACCOUNT_ID.dkr.ecr.us-west-2.amazonaws.com/blast-radius-backend:staging
docker push $ACCOUNT_ID.dkr.ecr.us-west-2.amazonaws.com/blast-radius-backend:staging

docker tag blast-radius-frontend:staging $ACCOUNT_ID.dkr.ecr.us-west-2.amazonaws.com/blast-radius-frontend:staging
docker push $ACCOUNT_ID.dkr.ecr.us-west-2.amazonaws.com/blast-radius-frontend:staging
```

#### 2. Deploy to Staging
```bash
# Deploy application
kubectl apply -f k8s/staging/ -n blast-radius-staging

# Wait for deployment
kubectl rollout status deployment/blast-radius-backend -n blast-radius-staging
kubectl rollout status deployment/blast-radius-frontend -n blast-radius-staging

# Verify deployment
kubectl get pods -n blast-radius-staging
kubectl get services -n blast-radius-staging
```

#### 3. Configure Staging Data
```bash
# Run database migrations
kubectl exec -it deployment/blast-radius-backend -n blast-radius-staging -- alembic upgrade head

# Load staging data
kubectl exec -it deployment/blast-radius-backend -n blast-radius-staging -- python manage.py load-staging-data

# Create staging admin user
kubectl exec -it deployment/blast-radius-backend -n blast-radius-staging -- python manage.py create-superuser --email <EMAIL>
```

## Production Environment Setup

### Pre-Production Checklist

#### Security Review
- [ ] Security scan completed and vulnerabilities addressed
- [ ] Penetration testing completed
- [ ] SSL certificates configured and validated
- [ ] Network security groups reviewed and approved
- [ ] Secrets management implemented and tested
- [ ] Backup and recovery procedures tested

#### Performance Review
- [ ] Load testing completed with acceptable results
- [ ] Database performance optimized
- [ ] Caching strategy implemented and tested
- [ ] CDN configuration optimized
- [ ] Auto-scaling policies configured and tested

#### Operational Readiness
- [ ] Monitoring and alerting configured
- [ ] Log aggregation and analysis setup
- [ ] Incident response procedures documented
- [ ] Runbooks created and validated
- [ ] Team training completed

### Production Deployment Process

#### 1. Final Infrastructure Review
```bash
# Review production Terraform plan
cd infrastructure/terraform
terraform workspace select production
terraform plan -var-file="environments/production.tfvars" -out=production.tfplan

# Review plan with team before applying
terraform show production.tfplan

# Apply with approval
terraform apply production.tfplan
```

#### 2. Production Image Preparation
```bash
# Build production images with security scanning
docker build -t blast-radius-backend:production -f backend/Dockerfile.production backend/
docker build -t blast-radius-frontend:production -f frontend/Dockerfile.production frontend/

# Security scan images
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
  aquasec/trivy image blast-radius-backend:production

# Push to production ECR
docker tag blast-radius-backend:production $ACCOUNT_ID.dkr.ecr.us-west-2.amazonaws.com/blast-radius-backend:production
docker push $ACCOUNT_ID.dkr.ecr.us-west-2.amazonaws.com/blast-radius-backend:production
```

#### 3. Blue-Green Deployment
```bash
# Deploy to green environment
kubectl apply -f k8s/production/green/ -n blast-radius

# Verify green deployment
kubectl get pods -l version=green -n blast-radius

# Run smoke tests
kubectl exec -it deployment/blast-radius-backend-green -n blast-radius -- python manage.py smoke-test

# Switch traffic to green
kubectl patch service blast-radius-backend -n blast-radius -p '{"spec":{"selector":{"version":"green"}}}'

# Monitor for issues
kubectl logs -f deployment/blast-radius-backend-green -n blast-radius

# Clean up blue environment after verification
kubectl delete -f k8s/production/blue/ -n blast-radius
```

## Environment-Specific Configurations

### Development Configuration
```yaml
# k8s/development/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: blast-radius-config
  namespace: blast-radius-dev
data:
  DEBUG: "true"
  LOG_LEVEL: "DEBUG"
  ENVIRONMENT: "development"
  ENABLE_PROFILING: "true"
  CORS_ORIGINS: '["http://localhost:3000"]'
```

### Staging Configuration
```yaml
# k8s/staging/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: blast-radius-config
  namespace: blast-radius-staging
data:
  DEBUG: "false"
  LOG_LEVEL: "INFO"
  ENVIRONMENT: "staging"
  ENABLE_PROFILING: "false"
  CORS_ORIGINS: '["https://staging.blast-radius.com"]'
```

### Production Configuration
```yaml
# k8s/production/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: blast-radius-config
  namespace: blast-radius
data:
  DEBUG: "false"
  LOG_LEVEL: "WARNING"
  ENVIRONMENT: "production"
  ENABLE_PROFILING: "false"
  CORS_ORIGINS: '["https://blast-radius.com"]'
```

## CI/CD Pipeline Configuration

### GitHub Actions Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy to Environment

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run tests
        run: |
          docker-compose -f docker-compose.test.yml up --abort-on-container-exit
          docker-compose -f docker-compose.test.yml down

  deploy-staging:
    if: github.ref == 'refs/heads/develop'
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to staging
        run: |
          ./scripts/deploy.sh staging

  deploy-production:
    if: github.ref == 'refs/heads/main'
    needs: test
    runs-on: ubuntu-latest
    environment: production
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to production
        run: |
          ./scripts/deploy.sh production
```

## Environment Management Scripts

### Deployment Script
```bash
#!/bin/bash
# scripts/deploy.sh

set -e

ENVIRONMENT=$1
if [ -z "$ENVIRONMENT" ]; then
    echo "Usage: $0 <environment>"
    exit 1
fi

echo "Deploying to $ENVIRONMENT environment..."

# Load environment-specific configuration
source .env.$ENVIRONMENT

# Build and push images
./scripts/build-images.sh $ENVIRONMENT

# Deploy infrastructure if needed
if [ "$ENVIRONMENT" != "development" ]; then
    ./scripts/deploy-infrastructure.sh $ENVIRONMENT
fi

# Deploy application
./scripts/deploy-application.sh $ENVIRONMENT

# Run post-deployment tests
./scripts/post-deployment-tests.sh $ENVIRONMENT

echo "Deployment to $ENVIRONMENT completed successfully!"
```

### Environment Cleanup Script
```bash
#!/bin/bash
# scripts/cleanup-environment.sh

ENVIRONMENT=$1
if [ -z "$ENVIRONMENT" ]; then
    echo "Usage: $0 <environment>"
    exit 1
fi

echo "Cleaning up $ENVIRONMENT environment..."

# Delete Kubernetes resources
kubectl delete namespace blast-radius-$ENVIRONMENT --ignore-not-found

# Clean up infrastructure (for non-production environments)
if [ "$ENVIRONMENT" != "production" ]; then
    cd infrastructure/terraform
    terraform workspace select $ENVIRONMENT
    terraform destroy -var-file="environments/$ENVIRONMENT.tfvars" -auto-approve
fi

echo "Environment $ENVIRONMENT cleaned up successfully!"
```

This environment setup documentation provides comprehensive guidance for setting up and managing different environments for the Blast-Radius Security Tool, from local development to production deployment.
