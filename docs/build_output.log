Running Sphinx v7.4.7
Warning: sphinxcontrib.mermaid not available. Mermaid diagrams will be shown as code blocks.
loading translations [en]... done
Converting `source_suffix = ['.rst', '.md']` to `source_suffix = {'.rst': 'restructuredtext', '.md': 'restructuredtext'}`.
loading pickled environment... done
[autosummary] generating autosummary for: DOCUMENTATION_EXPANSION_SUMMARY.md, DOCUMENTATION_SUMMARY.md, LOCAL-DEVELOPMENT.md, README.md, access-control/least-privilege-framework.rst, api/asset-management.rst, api/attack-path-analysis.rst, api/authentication.rst, api/index.rst, api/mitre-attack-integration.rst, ..., user-guides/attack-path-analysis.rst, user-guides/compliance-officers.rst, user-guides/executive-leadership.rst, user-guides/index.rst, user-guides/mitre-attack-integration.rst, user-guides/purple-team-members.rst, user-guides/red-team-members.rst, user-guides/security-architects.rst, user-guides/soc-operators.rst, user-guides/threat-modeling.rst
myst v4.0.0: MdParserConfig(commonmark_only=False, gfm_only=False, enable_extensions={'colon_fence', 'deflist', 'smartquotes', 'substitution', 'tasklist', 'dollarmath', 'html_image', 'html_admonition', 'replacements', 'fieldlist', 'strikethrough'}, disable_syntax=[], all_links_external=False, links_external_new_tab=False, url_schemes=('http', 'https', 'mailto', 'ftp'), ref_domains=None, fence_as_directive=set(), number_code_blocks=[], title_to_header=False, heading_anchors=0, heading_slug_func=None, html_meta={}, footnote_sort=True, footnote_transition=True, words_per_minute=200, substitutions={}, linkify_fuzzy_links=True, dmath_allow_labels=True, dmath_allow_space=True, dmath_allow_digits=True, dmath_double_inline=False, update_mathjax=True, mathjax_classes='tex2jax_process|mathjax_process|math|output_area', enable_checkboxes=False, suppress_warnings=[], highlight_code_blocks=True)
building [mo]: targets for 0 po files that are out of date
writing output... 
building [html]: targets for 1 source files that are out of date
updating environment: 1 added, 1 changed, 0 removed
reading sources... [ 50%] business/index
reading sources... [100%] business/strategic-next-phases

looking for now-outdated files... none found
pickling environment... done
checking consistency... /home/<USER>/dev/work/blast-radius/docs/DOCUMENTATION_EXPANSION_SUMMARY.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/DOCUMENTATION_SUMMARY.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/LOCAL-DEVELOPMENT.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/README.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/development/code-standards.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/development/setup.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/development/workflow.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/enhanced-features-summary.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/enhanced-prd-v2.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/implementation-gap-analysis.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/multi-cloud-integration.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/operations/runbooks/backup-recovery-runbooks.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/operations/runbooks/maintenance-procedures.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/operations/runbooks/monitoring-runbooks.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/performance/index.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/performance/optimization.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/phase-integration-plan.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/architecture/overview.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/compliance-documentation.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/framework.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/operations/incident-response.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/procedures/security-review-process.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/procedures/vulnerability-disclosure.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/reviews/security-assessment.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/reviews/security-review-2025-06-14.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/reviews/vulnerability-management.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/testing/dynamic-testing.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/testing/security-automation.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/security/testing/static-analysis.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/technical-specifications/compliance-framework-schema.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/technical-specifications/ml-threat-prediction.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/technical-specifications/thehive-integration.md: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/technical/index.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/technical/product-requirements.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/testing/index.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/testing/unit-tests.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/common-issues.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/troubleshooting/faq.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/user-guides/mitre-attack-integration.rst: WARNING: document isn't included in any toctree
/home/<USER>/dev/work/blast-radius/docs/user-guides/threat-modeling.rst: WARNING: document isn't included in any toctree
done
preparing documents... WARNING: unsupported theme option 'flyout_display' given
WARNING: unsupported theme option 'version_selector' given
WARNING: unsupported theme option 'language_selector' given
done
copying assets... 
copying static files... done
copying extra files... done
copying assets: done
writing output... [ 33%] business/index
writing output... [ 67%] business/strategic-next-phases
writing output... [100%] index

generating indices... genindex done
highlighting module code... 
writing additional pages... search opensearch done
dumping search index in English (code: en)... done
dumping object inventory... done
build succeeded, 43 warnings.

The HTML pages are in _build/html.
