"""
Real-time Security Event Correlation System for Zero-Trust Architecture.

This module provides comprehensive security event correlation, analysis,
and response capabilities for detecting and responding to security threats
in real-time.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set, Tuple
from enum import Enum
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import hashlib
import ipaddress

from sqlalchemy.orm import Session
from redis import Redis

from app.core.config import settings
from app.core.logging import get_logger
from app.db.models.security_event import SecurityEvent, SecurityIncident, ThreatIndicator
from app.services.notification_service import NotificationService

logger = get_logger(__name__)


class EventSeverity(Enum):
    """Security event severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class EventCategory(Enum):
    """Security event categories."""
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    DATA_ACCESS = "data_access"
    NETWORK_ACTIVITY = "network_activity"
    SYSTEM_ACTIVITY = "system_activity"
    MALWARE = "malware"
    INTRUSION = "intrusion"
    DATA_EXFILTRATION = "data_exfiltration"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    LATERAL_MOVEMENT = "lateral_movement"


class ThreatLevel(Enum):
    """Threat level classifications."""
    BENIGN = "benign"
    SUSPICIOUS = "suspicious"
    MALICIOUS = "malicious"
    CRITICAL = "critical"


@dataclass
class SecurityEventData:
    """Security event data structure."""
    event_id: str
    timestamp: datetime
    source_ip: str
    user_id: Optional[str]
    service_account_id: Optional[str]
    event_type: str
    category: EventCategory
    severity: EventSeverity
    description: str
    raw_data: Dict[str, Any]
    indicators: List[str]
    metadata: Dict[str, Any]


@dataclass
class CorrelationRule:
    """Security event correlation rule."""
    rule_id: str
    name: str
    description: str
    event_types: List[str]
    time_window_minutes: int
    threshold_count: int
    severity: EventSeverity
    conditions: Dict[str, Any]
    actions: List[str]
    enabled: bool = True


@dataclass
class SecurityIncidentData:
    """Security incident data structure."""
    incident_id: str
    title: str
    description: str
    severity: EventSeverity
    threat_level: ThreatLevel
    affected_assets: List[str]
    indicators_of_compromise: List[str]
    timeline: List[Dict[str, Any]]
    response_actions: List[str]
    status: str
    created_at: datetime
    updated_at: datetime


class SecurityEventCorrelator:
    """Real-time security event correlation engine."""
    
    def __init__(self, db: Session, redis_client: Redis, notification_service: NotificationService):
        self.db = db
        self.redis = redis_client
        self.notification_service = notification_service
        
        # Event buffers for correlation
        self.event_buffer = deque(maxlen=10000)
        self.correlation_cache = defaultdict(list)
        
        # Correlation rules
        self.correlation_rules = self._load_correlation_rules()
        
        # Threat intelligence
        self.threat_indicators = self._load_threat_indicators()
        
        # Rate limiting for events
        self.rate_limits = defaultdict(lambda: deque(maxlen=100))
        
        # Active incidents
        self.active_incidents = {}
        
        # Event processing queue
        self.event_queue = asyncio.Queue()
        
        # Start background tasks
        self._start_background_tasks()
    
    async def process_security_event(self, event_data: SecurityEventData) -> Optional[SecurityIncidentData]:
        """
        Process a security event and perform correlation analysis.
        
        Args:
            event_data: Security event to process
            
        Returns:
            SecurityIncidentData if incident created, None otherwise
        """
        try:
            # Enrich event with threat intelligence
            enriched_event = await self._enrich_event(event_data)
            
            # Store event in buffer
            self.event_buffer.append(enriched_event)
            
            # Store in database
            await self._store_event(enriched_event)
            
            # Perform correlation analysis
            incident = await self._correlate_event(enriched_event)
            
            # Check for rate limiting violations
            await self._check_rate_limits(enriched_event)
            
            # Update threat indicators
            await self._update_threat_indicators(enriched_event)
            
            return incident
            
        except Exception as e:
            logger.error(f"Error processing security event: {str(e)}")
            return None
    
    async def _enrich_event(self, event_data: SecurityEventData) -> SecurityEventData:
        """
        Enrich security event with additional context and threat intelligence.
        
        Args:
            event_data: Original event data
            
        Returns:
            Enriched event data
        """
        # Add geolocation data for IP addresses
        if event_data.source_ip:
            geo_data = await self._get_ip_geolocation(event_data.source_ip)
            event_data.metadata.update(geo_data)
        
        # Check against threat intelligence feeds
        threat_indicators = await self._check_threat_indicators(event_data)
        if threat_indicators:
            event_data.indicators.extend(threat_indicators)
            event_data.metadata["threat_indicators"] = threat_indicators
        
        # Add user context
        if event_data.user_id:
            user_context = await self._get_user_context(event_data.user_id)
            event_data.metadata["user_context"] = user_context
        
        # Add service account context
        if event_data.service_account_id:
            service_context = await self._get_service_account_context(event_data.service_account_id)
            event_data.metadata["service_context"] = service_context
        
        # Calculate risk score
        risk_score = await self._calculate_risk_score(event_data)
        event_data.metadata["risk_score"] = risk_score
        
        return event_data
    
    async def _correlate_event(self, event_data: SecurityEventData) -> Optional[SecurityIncidentData]:
        """
        Correlate event with existing events to detect patterns and create incidents.
        
        Args:
            event_data: Event to correlate
            
        Returns:
            SecurityIncidentData if incident created
        """
        for rule in self.correlation_rules:
            if not rule.enabled:
                continue
            
            if event_data.event_type not in rule.event_types:
                continue
            
            # Check if event matches rule conditions
            if not await self._matches_rule_conditions(event_data, rule):
                continue
            
            # Get related events within time window
            related_events = await self._get_related_events(event_data, rule)
            
            # Check if threshold is met
            if len(related_events) >= rule.threshold_count:
                # Create or update incident
                incident = await self._create_or_update_incident(event_data, related_events, rule)
                
                # Execute response actions
                await self._execute_response_actions(incident, rule.actions)
                
                return incident
        
        return None
    
    async def _matches_rule_conditions(self, event_data: SecurityEventData, rule: CorrelationRule) -> bool:
        """
        Check if event matches rule conditions.
        
        Args:
            event_data: Event to check
            rule: Correlation rule
            
        Returns:
            True if event matches conditions
        """
        conditions = rule.conditions
        
        # Check IP address conditions
        if "source_ip_ranges" in conditions:
            ip_ranges = conditions["source_ip_ranges"]
            if not any(ipaddress.ip_address(event_data.source_ip) in ipaddress.ip_network(range_) 
                      for range_ in ip_ranges):
                return False
        
        # Check user conditions
        if "user_patterns" in conditions:
            user_patterns = conditions["user_patterns"]
            if event_data.user_id and not any(pattern in event_data.user_id for pattern in user_patterns):
                return False
        
        # Check severity conditions
        if "min_severity" in conditions:
            min_severity = EventSeverity(conditions["min_severity"])
            severity_order = [EventSeverity.LOW, EventSeverity.MEDIUM, EventSeverity.HIGH, EventSeverity.CRITICAL]
            if severity_order.index(event_data.severity) < severity_order.index(min_severity):
                return False
        
        # Check metadata conditions
        if "metadata_conditions" in conditions:
            metadata_conditions = conditions["metadata_conditions"]
            for key, expected_value in metadata_conditions.items():
                if key not in event_data.metadata or event_data.metadata[key] != expected_value:
                    return False
        
        return True
    
    async def _get_related_events(self, event_data: SecurityEventData, rule: CorrelationRule) -> List[SecurityEventData]:
        """
        Get events related to the current event within the rule's time window.
        
        Args:
            event_data: Current event
            rule: Correlation rule
            
        Returns:
            List of related events
        """
        time_window_start = event_data.timestamp - timedelta(minutes=rule.time_window_minutes)
        
        related_events = []
        
        # Search in event buffer
        for buffered_event in self.event_buffer:
            if buffered_event.timestamp < time_window_start:
                continue
            
            if buffered_event.event_type not in rule.event_types:
                continue
            
            # Check if events are related (same source IP, user, etc.)
            if await self._are_events_related(event_data, buffered_event):
                related_events.append(buffered_event)
        
        return related_events
    
    async def _are_events_related(self, event1: SecurityEventData, event2: SecurityEventData) -> bool:
        """
        Determine if two events are related.
        
        Args:
            event1: First event
            event2: Second event
            
        Returns:
            True if events are related
        """
        # Same source IP
        if event1.source_ip == event2.source_ip:
            return True
        
        # Same user
        if event1.user_id and event1.user_id == event2.user_id:
            return True
        
        # Same service account
        if event1.service_account_id and event1.service_account_id == event2.service_account_id:
            return True
        
        # Similar indicators
        common_indicators = set(event1.indicators) & set(event2.indicators)
        if len(common_indicators) > 0:
            return True
        
        return False
    
    async def _create_or_update_incident(
        self,
        trigger_event: SecurityEventData,
        related_events: List[SecurityEventData],
        rule: CorrelationRule
    ) -> SecurityIncidentData:
        """
        Create a new incident or update an existing one.
        
        Args:
            trigger_event: Event that triggered the incident
            related_events: Related events
            rule: Correlation rule that was triggered
            
        Returns:
            SecurityIncidentData
        """
        # Generate incident ID
        incident_id = self._generate_incident_id(trigger_event, rule)
        
        # Check if incident already exists
        if incident_id in self.active_incidents:
            incident = self.active_incidents[incident_id]
            # Update existing incident
            incident.timeline.append({
                "timestamp": trigger_event.timestamp.isoformat(),
                "event_type": trigger_event.event_type,
                "description": trigger_event.description
            })
            incident.updated_at = datetime.utcnow()
        else:
            # Create new incident
            incident = SecurityIncidentData(
                incident_id=incident_id,
                title=f"Security Incident: {rule.name}",
                description=f"Correlation rule '{rule.name}' triggered by {len(related_events)} related events",
                severity=rule.severity,
                threat_level=self._determine_threat_level(trigger_event, related_events),
                affected_assets=self._extract_affected_assets(trigger_event, related_events),
                indicators_of_compromise=self._extract_iocs(trigger_event, related_events),
                timeline=[{
                    "timestamp": event.timestamp.isoformat(),
                    "event_type": event.event_type,
                    "description": event.description
                } for event in related_events],
                response_actions=[],
                status="open",
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            self.active_incidents[incident_id] = incident
        
        # Store incident in database
        await self._store_incident(incident)
        
        return incident
    
    async def _execute_response_actions(self, incident: SecurityIncidentData, actions: List[str]) -> None:
        """
        Execute automated response actions for an incident.
        
        Args:
            incident: Security incident
            actions: List of actions to execute
        """
        for action in actions:
            try:
                if action == "notify_security_team":
                    await self._notify_security_team(incident)
                elif action == "block_ip":
                    await self._block_suspicious_ips(incident)
                elif action == "disable_user":
                    await self._disable_suspicious_users(incident)
                elif action == "quarantine_asset":
                    await self._quarantine_affected_assets(incident)
                elif action == "collect_forensics":
                    await self._collect_forensic_data(incident)
                else:
                    logger.warning(f"Unknown response action: {action}")
            except Exception as e:
                logger.error(f"Error executing response action {action}: {str(e)}")
    
    async def _notify_security_team(self, incident: SecurityIncidentData) -> None:
        """Send notification to security team about the incident."""
        message = {
            "incident_id": incident.incident_id,
            "title": incident.title,
            "severity": incident.severity.value,
            "threat_level": incident.threat_level.value,
            "description": incident.description,
            "affected_assets": incident.affected_assets,
            "created_at": incident.created_at.isoformat()
        }
        
        await self.notification_service.send_security_alert(
            "security_incident",
            message,
            severity=incident.severity.value
        )
    
    async def _check_rate_limits(self, event_data: SecurityEventData) -> None:
        """
        Check for rate limiting violations that might indicate attacks.
        
        Args:
            event_data: Event to check
        """
        # Check rate limits by source IP
        ip_key = f"rate_limit:ip:{event_data.source_ip}"
        self.rate_limits[ip_key].append(event_data.timestamp)
        
        # Remove old entries
        cutoff_time = event_data.timestamp - timedelta(minutes=5)
        while self.rate_limits[ip_key] and self.rate_limits[ip_key][0] < cutoff_time:
            self.rate_limits[ip_key].popleft()
        
        # Check if rate limit exceeded
        if len(self.rate_limits[ip_key]) > 100:  # 100 events in 5 minutes
            await self._handle_rate_limit_violation(event_data, "ip", event_data.source_ip)
    
    async def _handle_rate_limit_violation(self, event_data: SecurityEventData, limit_type: str, identifier: str) -> None:
        """Handle rate limit violations."""
        logger.warning(f"Rate limit violation detected: {limit_type}={identifier}")
        
        # Create high-severity event
        rate_limit_event = SecurityEventData(
            event_id=f"rate_limit_{limit_type}_{identifier}_{int(event_data.timestamp.timestamp())}",
            timestamp=event_data.timestamp,
            source_ip=event_data.source_ip,
            user_id=event_data.user_id,
            service_account_id=event_data.service_account_id,
            event_type="rate_limit_violation",
            category=EventCategory.NETWORK_ACTIVITY,
            severity=EventSeverity.HIGH,
            description=f"Rate limit violation: {limit_type}={identifier}",
            raw_data={"original_event": asdict(event_data)},
            indicators=[f"rate_limit_violation_{limit_type}"],
            metadata={"limit_type": limit_type, "identifier": identifier}
        )
        
        await self.process_security_event(rate_limit_event)
    
    def _load_correlation_rules(self) -> List[CorrelationRule]:
        """Load correlation rules from configuration."""
        # Default correlation rules for zero-trust architecture
        return [
            CorrelationRule(
                rule_id="failed_auth_attempts",
                name="Multiple Failed Authentication Attempts",
                description="Detect multiple failed authentication attempts from same source",
                event_types=["authentication_failed", "login_failed"],
                time_window_minutes=5,
                threshold_count=5,
                severity=EventSeverity.HIGH,
                conditions={"min_severity": "medium"},
                actions=["notify_security_team", "block_ip"]
            ),
            CorrelationRule(
                rule_id="privilege_escalation",
                name="Privilege Escalation Attempt",
                description="Detect attempts to escalate privileges",
                event_types=["privilege_escalation", "unauthorized_access"],
                time_window_minutes=10,
                threshold_count=3,
                severity=EventSeverity.CRITICAL,
                conditions={"min_severity": "high"},
                actions=["notify_security_team", "disable_user", "collect_forensics"]
            ),
            CorrelationRule(
                rule_id="data_exfiltration",
                name="Potential Data Exfiltration",
                description="Detect patterns indicating data exfiltration",
                event_types=["large_data_transfer", "unusual_download"],
                time_window_minutes=30,
                threshold_count=2,
                severity=EventSeverity.CRITICAL,
                conditions={"min_severity": "medium"},
                actions=["notify_security_team", "quarantine_asset", "collect_forensics"]
            ),
            CorrelationRule(
                rule_id="lateral_movement",
                name="Lateral Movement Detection",
                description="Detect lateral movement within the network",
                event_types=["network_scan", "service_enumeration", "credential_access"],
                time_window_minutes=60,
                threshold_count=4,
                severity=EventSeverity.HIGH,
                conditions={"min_severity": "medium"},
                actions=["notify_security_team", "quarantine_asset"]
            )
        ]
    
    def _load_threat_indicators(self) -> Set[str]:
        """Load threat indicators from threat intelligence feeds."""
        # This would typically load from external threat intelligence feeds
        # For now, return a basic set of indicators
        return {
            "malicious_ip_ranges",
            "known_malware_hashes",
            "suspicious_domains",
            "attack_signatures"
        }
    
    def _generate_incident_id(self, event: SecurityEventData, rule: CorrelationRule) -> str:
        """Generate a unique incident ID."""
        data = f"{rule.rule_id}:{event.source_ip}:{event.user_id or 'unknown'}"
        return hashlib.md5(data.encode()).hexdigest()[:16]
    
    def _determine_threat_level(self, trigger_event: SecurityEventData, related_events: List[SecurityEventData]) -> ThreatLevel:
        """Determine threat level based on events."""
        if trigger_event.severity == EventSeverity.CRITICAL:
            return ThreatLevel.CRITICAL
        elif len(related_events) > 10:
            return ThreatLevel.MALICIOUS
        elif trigger_event.severity == EventSeverity.HIGH:
            return ThreatLevel.SUSPICIOUS
        else:
            return ThreatLevel.BENIGN
    
    def _extract_affected_assets(self, trigger_event: SecurityEventData, related_events: List[SecurityEventData]) -> List[str]:
        """Extract affected assets from events."""
        assets = set()
        
        for event in [trigger_event] + related_events:
            if event.source_ip:
                assets.add(f"ip:{event.source_ip}")
            if event.user_id:
                assets.add(f"user:{event.user_id}")
            if event.service_account_id:
                assets.add(f"service_account:{event.service_account_id}")
        
        return list(assets)
    
    def _extract_iocs(self, trigger_event: SecurityEventData, related_events: List[SecurityEventData]) -> List[str]:
        """Extract indicators of compromise from events."""
        iocs = set()
        
        for event in [trigger_event] + related_events:
            iocs.update(event.indicators)
        
        return list(iocs)
    
    async def _store_event(self, event_data: SecurityEventData) -> None:
        """Store security event in database."""
        # Implementation would store in SecurityEvent model
        pass
    
    async def _store_incident(self, incident: SecurityIncidentData) -> None:
        """Store security incident in database."""
        # Implementation would store in SecurityIncident model
        pass
    
    def _start_background_tasks(self) -> None:
        """Start background tasks for event processing."""
        # This would start async tasks for continuous processing
        pass
    
    # Additional helper methods would be implemented here...
