# Production Deployment Guide - Blast-Radius Security Tool

## Overview

This guide provides comprehensive instructions for deploying the Blast-Radius Security Tool in a production environment, including infrastructure setup, application deployment, and post-deployment verification.

## Prerequisites

### Infrastructure Requirements

#### Minimum System Requirements
- **Kubernetes Cluster**: EKS 1.28+ with 3+ nodes
- **Node Specifications**: 
  - CPU: 4 vCPUs per node
  - Memory: 16 GB RAM per node
  - Storage: 100 GB SSD per node
- **Database**: PostgreSQL 15+ (RDS recommended)
- **Cache**: Redis 7+ (ElastiCache recommended)
- **Load Balancer**: Application Load Balancer (ALB)

#### Network Requirements
- **VPC**: Dedicated VPC with public and private subnets
- **Security Groups**: Properly configured for web, app, and database tiers
- **DNS**: Route 53 hosted zone for domain management
- **SSL/TLS**: Valid SSL certificates (ACM recommended)

#### External Dependencies
- **Container Registry**: ECR for Docker images
- **Secrets Management**: AWS Secrets Manager
- **Monitoring**: CloudWatch, Prometheus, Grafana
- **Backup**: S3 for database backups and file storage

### Required Tools and Access

#### Development Tools
```bash
# Install required CLI tools
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
curl -o aws-iam-authenticator https://amazon-eks.s3.us-west-2.amazonaws.com/1.21.2/2021-07-05/bin/linux/amd64/aws-iam-authenticator
curl -LO https://get.helm.sh/helm-v3.12.0-linux-amd64.tar.gz

# Install Terraform
wget https://releases.hashicorp.com/terraform/1.5.0/terraform_1.5.0_linux_amd64.zip
unzip terraform_1.5.0_linux_amd64.zip
sudo mv terraform /usr/local/bin/
```

#### AWS Access Requirements
- **IAM Permissions**: Administrator access or specific permissions for:
  - EKS cluster management
  - RDS instance management
  - VPC and networking
  - IAM role creation
  - Secrets Manager access
  - ECR repository access

## Infrastructure Deployment

### Step 1: Environment Configuration

#### 1.1 Configure AWS CLI
```bash
# Configure AWS credentials
aws configure
# AWS Access Key ID: [Your Access Key]
# AWS Secret Access Key: [Your Secret Key]
# Default region name: us-west-2
# Default output format: json

# Verify access
aws sts get-caller-identity
```

#### 1.2 Set Environment Variables
```bash
# Create environment configuration
cat > .env.production << 'EOF'
# AWS Configuration
AWS_REGION=us-west-2
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)

# Project Configuration
PROJECT_NAME=blast-radius
ENVIRONMENT=production
DOMAIN_NAME=blast-radius.com

# Database Configuration
DB_INSTANCE_CLASS=db.r6g.xlarge
DB_ALLOCATED_STORAGE=500
DB_MAX_ALLOCATED_STORAGE=1000

# EKS Configuration
EKS_CLUSTER_VERSION=1.28
EKS_NODE_INSTANCE_TYPE=t3.large
EKS_DESIRED_CAPACITY=3
EKS_MAX_CAPACITY=10

# Application Configuration
APP_REPLICAS=3
WORKER_REPLICAS=2
EOF

source .env.production
```

### Step 2: Infrastructure Provisioning

#### 2.1 Deploy Core Infrastructure
```bash
# Initialize Terraform
cd infrastructure/terraform
terraform init

# Create workspace for production
terraform workspace new production
terraform workspace select production

# Plan infrastructure deployment
terraform plan -var-file="environments/production.tfvars" -out=production.tfplan

# Review the plan carefully
terraform show production.tfplan

# Apply infrastructure changes
terraform apply production.tfplan
```

#### 2.2 Configure kubectl Access
```bash
# Update kubeconfig for EKS cluster
aws eks update-kubeconfig --region $AWS_REGION --name $PROJECT_NAME-cluster

# Verify cluster access
kubectl cluster-info
kubectl get nodes
```

#### 2.3 Install Cluster Add-ons
```bash
# Install AWS Load Balancer Controller
helm repo add eks https://aws.github.io/eks-charts
helm repo update

helm install aws-load-balancer-controller eks/aws-load-balancer-controller \
  -n kube-system \
  --set clusterName=$PROJECT_NAME-cluster \
  --set serviceAccount.create=false \
  --set serviceAccount.name=aws-load-balancer-controller

# Install Cluster Autoscaler
helm install cluster-autoscaler autoscaler/cluster-autoscaler \
  --namespace kube-system \
  --set autoDiscovery.clusterName=$PROJECT_NAME-cluster \
  --set awsRegion=$AWS_REGION

# Install Metrics Server
kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml
```

### Step 3: Database Setup

#### 3.1 Database Initialization
```bash
# Get database endpoint from Terraform output
DB_ENDPOINT=$(terraform output -raw database_endpoint)
DB_USERNAME=$(terraform output -raw database_username)

# Get database password from Secrets Manager
DB_PASSWORD=$(aws secretsmanager get-secret-value \
  --secret-id $PROJECT_NAME-database-credentials \
  --query SecretString --output text | jq -r .password)

# Connect to database and create initial schema
PGPASSWORD=$DB_PASSWORD psql -h $DB_ENDPOINT -U $DB_USERNAME -d blast_radius << 'EOF'
-- Verify database connection
SELECT version();

-- Create application user
CREATE USER blast_radius_app WITH PASSWORD 'secure_app_password';

-- Grant necessary permissions
GRANT CONNECT ON DATABASE blast_radius TO blast_radius_app;
GRANT USAGE ON SCHEMA public TO blast_radius_app;
GRANT CREATE ON SCHEMA public TO blast_radius_app;

-- Create initial tables (will be managed by Alembic migrations)
\q
EOF
```

#### 3.2 Database Migration
```bash
# Run database migrations
kubectl create job --from=cronjob/database-migration migration-$(date +%s) -n blast-radius

# Monitor migration progress
kubectl logs -f job/migration-$(date +%s) -n blast-radius

# Verify migration completion
kubectl get jobs -n blast-radius
```

## Application Deployment

### Step 4: Container Image Preparation

#### 4.1 Build and Push Images
```bash
# Login to ECR
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com

# Build backend image
docker build -t blast-radius-backend:latest -f backend/Dockerfile backend/
docker tag blast-radius-backend:latest $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/blast-radius-backend:latest
docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/blast-radius-backend:latest

# Build frontend image
docker build -t blast-radius-frontend:latest -f frontend/Dockerfile frontend/
docker tag blast-radius-frontend:latest $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/blast-radius-frontend:latest
docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/blast-radius-frontend:latest

# Build worker image
docker build -t blast-radius-worker:latest -f backend/Dockerfile.worker backend/
docker tag blast-radius-worker:latest $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/blast-radius-worker:latest
docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/blast-radius-worker:latest
```

### Step 5: Kubernetes Deployment

#### 5.1 Create Namespace and Secrets
```bash
# Create application namespace
kubectl create namespace blast-radius

# Create database secret
kubectl create secret generic database-credentials \
  --from-literal=host=$DB_ENDPOINT \
  --from-literal=username=blast_radius_app \
  --from-literal=password=$DB_PASSWORD \
  --from-literal=database=blast_radius \
  -n blast-radius

# Create Redis secret
REDIS_ENDPOINT=$(terraform output -raw redis_endpoint)
kubectl create secret generic redis-credentials \
  --from-literal=host=$REDIS_ENDPOINT \
  --from-literal=port=6379 \
  -n blast-radius

# Create application secrets
kubectl create secret generic app-secrets \
  --from-literal=secret-key=$(openssl rand -base64 32) \
  --from-literal=jwt-secret=$(openssl rand -base64 32) \
  -n blast-radius
```

#### 5.2 Deploy Application Components
```bash
# Deploy backend API
kubectl apply -f k8s/backend/ -n blast-radius

# Deploy frontend
kubectl apply -f k8s/frontend/ -n blast-radius

# Deploy worker
kubectl apply -f k8s/worker/ -n blast-radius

# Deploy monitoring stack
kubectl apply -f k8s/monitoring/ -n blast-radius
```

#### 5.3 Configure Ingress and Load Balancer
```bash
# Deploy ingress controller
kubectl apply -f k8s/ingress/ -n blast-radius

# Get load balancer DNS name
kubectl get ingress blast-radius-ingress -n blast-radius -o jsonpath='{.status.loadBalancer.ingress[0].hostname}'

# Update DNS records in Route 53
aws route53 change-resource-record-sets \
  --hosted-zone-id $HOSTED_ZONE_ID \
  --change-batch file://dns-update.json
```

### Step 6: SSL/TLS Configuration

#### 6.1 Request SSL Certificate
```bash
# Request certificate from ACM
CERT_ARN=$(aws acm request-certificate \
  --domain-name $DOMAIN_NAME \
  --subject-alternative-names "*.$DOMAIN_NAME" \
  --validation-method DNS \
  --query CertificateArn --output text)

# Get validation records
aws acm describe-certificate --certificate-arn $CERT_ARN

# Add DNS validation records to Route 53
# (Follow the validation instructions from ACM)
```

#### 6.2 Configure HTTPS Redirect
```bash
# Update ingress with SSL certificate
kubectl patch ingress blast-radius-ingress -n blast-radius -p '{
  "metadata": {
    "annotations": {
      "alb.ingress.kubernetes.io/certificate-arn": "'$CERT_ARN'",
      "alb.ingress.kubernetes.io/ssl-redirect": "443"
    }
  }
}'
```

## Post-Deployment Configuration

### Step 7: Application Configuration

#### 7.1 Initialize Application Data
```bash
# Create initial admin user
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- python manage.py create-admin \
  --email <EMAIL> \
  --password $(openssl rand -base64 16)

# Load initial data
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- python manage.py load-fixtures

# Run initial asset discovery
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- python manage.py discover-assets
```

#### 7.2 Configure Monitoring and Alerting
```bash
# Import Grafana dashboards
kubectl exec -it deployment/grafana -n blast-radius -- \
  grafana-cli admin import-dashboard /var/lib/grafana/dashboards/blast-radius-overview.json

# Configure Prometheus targets
kubectl apply -f monitoring/prometheus/servicemonitors/ -n blast-radius

# Test alerting
kubectl exec -it deployment/alertmanager -n blast-radius -- \
  amtool alert add alertname=TestAlert severity=warning
```

### Step 8: Security Hardening

#### 8.1 Network Policies
```bash
# Apply network policies
kubectl apply -f k8s/security/network-policies/ -n blast-radius

# Verify network isolation
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  nc -zv blast-radius-database 5432
```

#### 8.2 Pod Security Policies
```bash
# Apply pod security policies
kubectl apply -f k8s/security/pod-security-policies/

# Verify security contexts
kubectl get pods -n blast-radius -o jsonpath='{.items[*].spec.securityContext}'
```

## Verification and Testing

### Step 9: Deployment Verification

#### 9.1 Health Checks
```bash
# Check pod status
kubectl get pods -n blast-radius

# Check service endpoints
kubectl get endpoints -n blast-radius

# Test application health
curl -f https://$DOMAIN_NAME/health
curl -f https://$DOMAIN_NAME/api/health
```

#### 9.2 Functional Testing
```bash
# Run integration tests
kubectl create job integration-tests --image=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/blast-radius-tests:latest -n blast-radius

# Monitor test execution
kubectl logs -f job/integration-tests -n blast-radius

# Run performance tests
kubectl apply -f k8s/tests/performance-test.yaml -n blast-radius
```

#### 9.3 Security Testing
```bash
# Run security scans
kubectl create job security-scan --image=owasp/zap2docker-stable:latest -n blast-radius

# Check SSL configuration
curl -I https://$DOMAIN_NAME
openssl s_client -connect $DOMAIN_NAME:443 -servername $DOMAIN_NAME
```

## Backup and Recovery Setup

### Step 10: Backup Configuration

#### 10.1 Database Backup
```bash
# Configure automated database backups
aws rds modify-db-instance \
  --db-instance-identifier $PROJECT_NAME-database \
  --backup-retention-period 30 \
  --preferred-backup-window "03:00-04:00" \
  --preferred-maintenance-window "sun:04:00-sun:05:00"

# Create manual backup
aws rds create-db-snapshot \
  --db-instance-identifier $PROJECT_NAME-database \
  --db-snapshot-identifier $PROJECT_NAME-manual-snapshot-$(date +%Y%m%d)
```

#### 10.2 Application Data Backup
```bash
# Configure persistent volume backups
kubectl apply -f k8s/backup/volume-snapshots.yaml -n blast-radius

# Schedule regular backups
kubectl apply -f k8s/backup/backup-cronjob.yaml -n blast-radius
```

## Monitoring and Maintenance

### Step 11: Ongoing Monitoring

#### 11.1 Set Up Alerts
```bash
# Configure critical alerts
kubectl apply -f monitoring/alertmanager/critical-alerts.yaml -n blast-radius

# Test alert delivery
kubectl exec -it deployment/alertmanager -n blast-radius -- \
  amtool alert add alertname=DeploymentTest severity=critical
```

#### 11.2 Performance Monitoring
```bash
# Enable detailed monitoring
kubectl patch deployment blast-radius-backend -n blast-radius -p '{
  "spec": {
    "template": {
      "metadata": {
        "annotations": {
          "prometheus.io/scrape": "true",
          "prometheus.io/port": "8000",
          "prometheus.io/path": "/metrics"
        }
      }
    }
  }
}'
```

## Troubleshooting Common Issues

### Database Connection Issues
```bash
# Check database connectivity
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  pg_isready -h $DB_ENDPOINT -p 5432

# Check security group rules
aws ec2 describe-security-groups --group-ids $DB_SECURITY_GROUP_ID
```

### Pod Startup Issues
```bash
# Check pod logs
kubectl logs -f deployment/blast-radius-backend -n blast-radius

# Check resource constraints
kubectl describe pod -l app=blast-radius-backend -n blast-radius

# Check image pull issues
kubectl get events -n blast-radius --sort-by='.lastTimestamp'
```

### Load Balancer Issues
```bash
# Check ALB status
aws elbv2 describe-load-balancers --names $PROJECT_NAME-alb

# Check target group health
aws elbv2 describe-target-health --target-group-arn $TARGET_GROUP_ARN

# Check ingress configuration
kubectl describe ingress blast-radius-ingress -n blast-radius
```

## Rollback Procedures

### Emergency Rollback
```bash
# Rollback to previous deployment
kubectl rollout undo deployment/blast-radius-backend -n blast-radius

# Check rollback status
kubectl rollout status deployment/blast-radius-backend -n blast-radius

# Verify application functionality
curl -f https://$DOMAIN_NAME/health
```

### Database Rollback
```bash
# Restore from snapshot (if needed)
aws rds restore-db-instance-from-db-snapshot \
  --db-instance-identifier $PROJECT_NAME-database-restored \
  --db-snapshot-identifier $SNAPSHOT_IDENTIFIER
```

This production deployment guide provides comprehensive instructions for deploying the Blast-Radius Security Tool in a production environment with proper security, monitoring, and backup configurations.

## Next Steps

After successful deployment:
1. **Review Security Configuration**: Conduct security review and penetration testing
2. **Performance Tuning**: Monitor and optimize application performance
3. **Backup Testing**: Verify backup and recovery procedures
4. **Documentation Updates**: Update operational runbooks and procedures
5. **Team Training**: Train operations team on monitoring and maintenance procedures

For additional deployment scenarios, see:
- [Environment Setup Documentation](environment-setup.md)
- [Troubleshooting Guide](troubleshooting-guide.md)
- [Operational Runbooks](../operations/)
