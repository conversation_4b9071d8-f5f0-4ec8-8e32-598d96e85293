# Zero-Trust Network Policies for Blast-Radius Security Tool
# These policies implement microsegmentation and default-deny networking
# following zero-trust principles

# Default Deny All Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
  namespace: blast-radius
  labels:
    security.blast-radius.com/policy: "zero-trust"
    security.blast-radius.com/type: "default-deny"
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress

---
# Frontend Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: blast-radius-frontend-policy
  namespace: blast-radius
  labels:
    security.blast-radius.com/policy: "zero-trust"
    security.blast-radius.com/component: "frontend"
spec:
  podSelector:
    matchLabels:
      app: blast-radius-frontend
  policyTypes:
  - Ingress
  - Egress
  
  ingress:
  # Allow ingress from load balancer/ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: TCP
      port: 3000
  
  # Allow ingress from monitoring
  - from:
    - podSelector:
        matchLabels:
          app: prometheus
    - podSelector:
        matchLabels:
          app: grafana
    ports:
    - protocol: TCP
      port: 3000
  
  egress:
  # Allow egress to backend API
  - to:
    - podSelector:
        matchLabels:
          app: blast-radius-backend
    ports:
    - protocol: TCP
      port: 8000
  
  # Allow DNS resolution
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Allow HTTPS to external CDNs and APIs (if needed)
  - to: []
    ports:
    - protocol: TCP
      port: 443

---
# Backend Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: blast-radius-backend-policy
  namespace: blast-radius
  labels:
    security.blast-radius.com/policy: "zero-trust"
    security.blast-radius.com/component: "backend"
spec:
  podSelector:
    matchLabels:
      app: blast-radius-backend
  policyTypes:
  - Ingress
  - Egress
  
  ingress:
  # Allow ingress from frontend
  - from:
    - podSelector:
        matchLabels:
          app: blast-radius-frontend
    ports:
    - protocol: TCP
      port: 8000
  
  # Allow ingress from workers
  - from:
    - podSelector:
        matchLabels:
          app: blast-radius-worker
    ports:
    - protocol: TCP
      port: 8000
  
  # Allow ingress from load balancer for direct API access
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: TCP
      port: 8000
  
  # Allow ingress from monitoring
  - from:
    - podSelector:
        matchLabels:
          app: prometheus
    - podSelector:
        matchLabels:
          app: grafana
    ports:
    - protocol: TCP
      port: 8000
  
  egress:
  # Allow egress to database
  - to: []
    ports:
    - protocol: TCP
      port: 5432
  
  # Allow egress to Redis
  - to: []
    ports:
    - protocol: TCP
      port: 6379
  
  # Allow DNS resolution
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Allow HTTPS to external APIs
  - to: []
    ports:
    - protocol: TCP
      port: 443
  
  # Allow HTTP for external integrations (if needed)
  - to: []
    ports:
    - protocol: TCP
      port: 80

---
# Worker Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: blast-radius-worker-policy
  namespace: blast-radius
  labels:
    security.blast-radius.com/policy: "zero-trust"
    security.blast-radius.com/component: "worker"
spec:
  podSelector:
    matchLabels:
      app: blast-radius-worker
  policyTypes:
  - Ingress
  - Egress
  
  ingress:
  # Allow ingress from monitoring for metrics
  - from:
    - podSelector:
        matchLabels:
          app: prometheus
    ports:
    - protocol: TCP
      port: 9540  # Celery metrics port
  
  egress:
  # Allow egress to backend API
  - to:
    - podSelector:
        matchLabels:
          app: blast-radius-backend
    ports:
    - protocol: TCP
      port: 8000
  
  # Allow egress to database
  - to: []
    ports:
    - protocol: TCP
      port: 5432
  
  # Allow egress to Redis
  - to: []
    ports:
    - protocol: TCP
      port: 6379
  
  # Allow DNS resolution
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Allow HTTPS to external APIs for integrations
  - to: []
    ports:
    - protocol: TCP
      port: 443
  
  # Allow HTTP for external integrations (if needed)
  - to: []
    ports:
    - protocol: TCP
      port: 80

---
# Monitoring Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: blast-radius-monitoring-policy
  namespace: blast-radius
  labels:
    security.blast-radius.com/policy: "zero-trust"
    security.blast-radius.com/component: "monitoring"
spec:
  podSelector:
    matchLabels:
      app: prometheus
  policyTypes:
  - Ingress
  - Egress
  
  ingress:
  # Allow ingress from Grafana
  - from:
    - podSelector:
        matchLabels:
          app: grafana
    ports:
    - protocol: TCP
      port: 9090
  
  # Allow ingress from ingress controller for external access
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 9090
  
  egress:
  # Allow egress to scrape all application metrics
  - to:
    - podSelector:
        matchLabels:
          app: blast-radius-backend
    ports:
    - protocol: TCP
      port: 8000
  
  - to:
    - podSelector:
        matchLabels:
          app: blast-radius-frontend
    ports:
    - protocol: TCP
      port: 3000
  
  - to:
    - podSelector:
        matchLabels:
          app: blast-radius-worker
    ports:
    - protocol: TCP
      port: 9540
  
  # Allow DNS resolution
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Allow access to Kubernetes API for service discovery
  - to: []
    ports:
    - protocol: TCP
      port: 443

---
# Grafana Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: blast-radius-grafana-policy
  namespace: blast-radius
  labels:
    security.blast-radius.com/policy: "zero-trust"
    security.blast-radius.com/component: "monitoring"
spec:
  podSelector:
    matchLabels:
      app: grafana
  policyTypes:
  - Ingress
  - Egress
  
  ingress:
  # Allow ingress from ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 3000
  
  egress:
  # Allow egress to Prometheus
  - to:
    - podSelector:
        matchLabels:
          app: prometheus
    ports:
    - protocol: TCP
      port: 9090
  
  # Allow DNS resolution
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53

---
# Database Access Policy (for external database)
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: blast-radius-database-access-policy
  namespace: blast-radius
  labels:
    security.blast-radius.com/policy: "zero-trust"
    security.blast-radius.com/component: "database"
spec:
  podSelector:
    matchLabels:
      security.blast-radius.com/database-access: "allowed"
  policyTypes:
  - Egress
  
  egress:
  # Allow egress to external database
  - to: []
    ports:
    - protocol: TCP
      port: 5432

---
# Redis Access Policy (for external Redis)
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: blast-radius-redis-access-policy
  namespace: blast-radius
  labels:
    security.blast-radius.com/policy: "zero-trust"
    security.blast-radius.com/component: "redis"
spec:
  podSelector:
    matchLabels:
      security.blast-radius.com/redis-access: "allowed"
  policyTypes:
  - Egress
  
  egress:
  # Allow egress to external Redis
  - to: []
    ports:
    - protocol: TCP
      port: 6379

---
# Backup Job Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: blast-radius-backup-policy
  namespace: blast-radius
  labels:
    security.blast-radius.com/policy: "zero-trust"
    security.blast-radius.com/component: "backup"
spec:
  podSelector:
    matchLabels:
      app: blast-radius-backup
  policyTypes:
  - Egress
  
  egress:
  # Allow egress to database for backup
  - to: []
    ports:
    - protocol: TCP
      port: 5432
  
  # Allow DNS resolution
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Allow HTTPS to S3 for backup storage
  - to: []
    ports:
    - protocol: TCP
      port: 443

---
# Emergency Access Policy (for troubleshooting)
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: blast-radius-emergency-access-policy
  namespace: blast-radius
  labels:
    security.blast-radius.com/policy: "zero-trust"
    security.blast-radius.com/component: "emergency"
  annotations:
    security.blast-radius.com/emergency: "true"
    security.blast-radius.com/description: "Emergency access policy for troubleshooting - should be removed after use"
spec:
  podSelector:
    matchLabels:
      security.blast-radius.com/emergency-access: "enabled"
  policyTypes:
  - Ingress
  - Egress
  
  ingress:
  # Allow ingress from specific troubleshooting pods
  - from:
    - podSelector:
        matchLabels:
          security.blast-radius.com/role: "troubleshooting"
    ports:
    - protocol: TCP
      port: 22  # SSH
    - protocol: TCP
      port: 8080  # Debug port
  
  egress:
  # Allow all egress for troubleshooting (temporary)
  - to: []
