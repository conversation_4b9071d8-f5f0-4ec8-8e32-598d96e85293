"""
Least Privilege Access Control Service for Compliance Framework.

This service implements comprehensive least privilege access controls including:
- Dynamic permission assignment based on job functions
- Time-based access controls
- Just-in-time (JIT) access provisioning
- Access reviews and certification
- Privilege escalation monitoring
- Automated access deprovisioning
"""

import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Set
from enum import Enum
from dataclasses import dataclass, asdict
from uuid import UUID, uuid4

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.core.config import settings
from app.core.logging import get_logger
from app.core.permissions import Permission, UserRole
from app.db.models.user import User
from app.db.models.asset import Asset
from app.services.enhanced_audit_service import EnhancedAuditService, AuditEventType, AuditSeverity

logger = get_logger(__name__)


class AccessType(Enum):
    """Types of access that can be granted."""
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    EXECUTE = "execute"
    ADMIN = "admin"
    AUDIT = "audit"


class AccessJustification(Enum):
    """Justifications for access requests."""
    JOB_FUNCTION = "job_function"
    PROJECT_ASSIGNMENT = "project_assignment"
    TEMPORARY_ASSIGNMENT = "temporary_assignment"
    INCIDENT_RESPONSE = "incident_response"
    AUDIT_REQUIREMENT = "audit_requirement"
    COMPLIANCE_REQUIREMENT = "compliance_requirement"
    EMERGENCY_ACCESS = "emergency_access"


class AccessRequestStatus(Enum):
    """Status of access requests."""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    EXPIRED = "expired"
    REVOKED = "revoked"


@dataclass
class AccessRequest:
    """Access request for least privilege implementation."""
    request_id: str
    user_id: str
    resource_type: str
    resource_id: Optional[str]
    access_types: List[AccessType]
    justification: AccessJustification
    business_justification: str
    requested_by: str
    approved_by: Optional[str]
    status: AccessRequestStatus
    requested_at: datetime
    approved_at: Optional[datetime]
    expires_at: Optional[datetime]
    revoked_at: Optional[datetime]
    auto_revoke: bool
    review_required: bool
    emergency_access: bool
    metadata: Dict[str, Any]


@dataclass
class AccessReview:
    """Access review for periodic certification."""
    review_id: str
    user_id: str
    reviewer_id: str
    review_type: str  # periodic, role_change, termination
    status: str  # pending, completed, overdue
    scheduled_date: datetime
    completed_date: Optional[datetime]
    access_items: List[Dict[str, Any]]
    decisions: Dict[str, str]  # access_item_id -> decision (keep, revoke, modify)
    comments: str
    metadata: Dict[str, Any]


@dataclass
class PrivilegeEscalation:
    """Privilege escalation event for monitoring."""
    escalation_id: str
    user_id: str
    from_role: str
    to_role: str
    escalation_type: str  # temporary, permanent, emergency
    justification: str
    approved_by: str
    escalated_at: datetime
    expires_at: Optional[datetime]
    reverted_at: Optional[datetime]
    metadata: Dict[str, Any]


class LeastPrivilegeService:
    """Service for implementing least privilege access controls."""
    
    def __init__(self, db: Session, audit_service: EnhancedAuditService):
        self.db = db
        self.audit_service = audit_service
        
        # In-memory storage for demonstration (would use database in production)
        self.access_requests: Dict[str, AccessRequest] = {}
        self.access_reviews: Dict[str, AccessReview] = {}
        self.privilege_escalations: Dict[str, PrivilegeEscalation] = {}
        
        # Role-based access templates
        self.role_templates = self._initialize_role_templates()
        
        # Access policies
        self.access_policies = self._initialize_access_policies()
    
    async def request_access(
        self,
        user_id: str,
        resource_type: str,
        access_types: List[AccessType],
        justification: AccessJustification,
        business_justification: str,
        requested_by: str,
        resource_id: Optional[str] = None,
        duration_hours: Optional[int] = None,
        emergency: bool = False
    ) -> AccessRequest:
        """
        Request access to a resource following least privilege principles.
        
        Args:
            user_id: ID of user requesting access
            resource_type: Type of resource
            access_types: Types of access requested
            justification: Justification category
            business_justification: Business justification text
            requested_by: ID of user making the request
            resource_id: Specific resource ID (optional)
            duration_hours: Duration in hours (optional)
            emergency: Emergency access flag
            
        Returns:
            AccessRequest object
        """
        request_id = str(uuid4())
        now = datetime.utcnow()
        
        # Calculate expiration time
        expires_at = None
        if duration_hours:
            expires_at = now + timedelta(hours=duration_hours)
        elif emergency:
            expires_at = now + timedelta(hours=4)  # Emergency access expires in 4 hours
        
        # Determine if approval is required
        approval_required = self._requires_approval(
            user_id, resource_type, access_types, justification, emergency
        )
        
        # Create access request
        access_request = AccessRequest(
            request_id=request_id,
            user_id=user_id,
            resource_type=resource_type,
            resource_id=resource_id,
            access_types=access_types,
            justification=justification,
            business_justification=business_justification,
            requested_by=requested_by,
            approved_by=None,
            status=AccessRequestStatus.PENDING if approval_required else AccessRequestStatus.APPROVED,
            requested_at=now,
            approved_at=None if approval_required else now,
            expires_at=expires_at,
            revoked_at=None,
            auto_revoke=expires_at is not None,
            review_required=not emergency,
            emergency_access=emergency,
            metadata={}
        )
        
        self.access_requests[request_id] = access_request
        
        # Auto-approve if no approval required
        if not approval_required:
            await self._grant_access(access_request)
        
        # Log audit event
        await self.audit_service.log_audit_event(
            event_type=AuditEventType.PERMISSION_GRANTED if not approval_required else AuditEventType.API_CALL,
            action="access_requested",
            description=f"Access requested for {resource_type}",
            severity=AuditSeverity.INFO,
            user_id=user_id,
            resource_type=resource_type,
            resource_id=resource_id,
            metadata={
                "request_id": request_id,
                "access_types": [at.value for at in access_types],
                "justification": justification.value,
                "emergency": emergency
            }
        )
        
        logger.info(f"Access request {request_id} created for user {user_id}")
        
        return access_request
    
    async def approve_access_request(
        self,
        request_id: str,
        approved_by: str,
        comments: Optional[str] = None
    ) -> bool:
        """
        Approve an access request.
        
        Args:
            request_id: ID of the access request
            approved_by: ID of approver
            comments: Optional approval comments
            
        Returns:
            True if approved successfully
        """
        if request_id not in self.access_requests:
            raise ValueError(f"Access request {request_id} not found")
        
        access_request = self.access_requests[request_id]
        
        if access_request.status != AccessRequestStatus.PENDING:
            raise ValueError(f"Access request {request_id} is not pending")
        
        # Update request
        access_request.status = AccessRequestStatus.APPROVED
        access_request.approved_by = approved_by
        access_request.approved_at = datetime.utcnow()
        
        if comments:
            access_request.metadata["approval_comments"] = comments
        
        # Grant access
        await self._grant_access(access_request)
        
        # Log audit event
        await self.audit_service.log_audit_event(
            event_type=AuditEventType.PERMISSION_GRANTED,
            action="access_approved",
            description=f"Access approved for {access_request.resource_type}",
            severity=AuditSeverity.INFO,
            user_id=access_request.user_id,
            resource_type=access_request.resource_type,
            resource_id=access_request.resource_id,
            metadata={
                "request_id": request_id,
                "approved_by": approved_by,
                "comments": comments
            }
        )
        
        logger.info(f"Access request {request_id} approved by {approved_by}")
        
        return True
    
    async def revoke_access(
        self,
        user_id: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        revoked_by: Optional[str] = None,
        reason: str = "Manual revocation"
    ) -> bool:
        """
        Revoke access to a resource.
        
        Args:
            user_id: ID of user to revoke access from
            resource_type: Type of resource
            resource_id: Specific resource ID (optional)
            revoked_by: ID of user revoking access
            reason: Reason for revocation
            
        Returns:
            True if access was revoked
        """
        revoked_count = 0
        
        # Find and revoke matching access requests
        for access_request in self.access_requests.values():
            if (access_request.user_id == user_id and
                access_request.resource_type == resource_type and
                (resource_id is None or access_request.resource_id == resource_id) and
                access_request.status == AccessRequestStatus.APPROVED):
                
                access_request.status = AccessRequestStatus.REVOKED
                access_request.revoked_at = datetime.utcnow()
                access_request.metadata["revocation_reason"] = reason
                access_request.metadata["revoked_by"] = revoked_by
                
                revoked_count += 1
        
        # Actually remove permissions (implementation would depend on permission system)
        await self._remove_permissions(user_id, resource_type, resource_id)
        
        # Log audit event
        await self.audit_service.log_audit_event(
            event_type=AuditEventType.PERMISSION_REVOKED,
            action="access_revoked",
            description=f"Access revoked for {resource_type}",
            severity=AuditSeverity.WARNING,
            user_id=user_id,
            resource_type=resource_type,
            resource_id=resource_id,
            metadata={
                "revoked_by": revoked_by,
                "reason": reason,
                "revoked_count": revoked_count
            }
        )
        
        logger.info(f"Revoked {revoked_count} access grants for user {user_id}")
        
        return revoked_count > 0
    
    async def escalate_privileges(
        self,
        user_id: str,
        target_role: str,
        justification: str,
        approved_by: str,
        duration_hours: Optional[int] = None,
        emergency: bool = False
    ) -> PrivilegeEscalation:
        """
        Escalate user privileges temporarily.
        
        Args:
            user_id: ID of user to escalate
            target_role: Target role to escalate to
            justification: Justification for escalation
            approved_by: ID of approver
            duration_hours: Duration in hours
            emergency: Emergency escalation flag
            
        Returns:
            PrivilegeEscalation object
        """
        escalation_id = str(uuid4())
        now = datetime.utcnow()
        
        # Get current user role
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            raise ValueError(f"User {user_id} not found")
        
        current_role = user.roles[0].name if user.roles else "none"
        
        # Calculate expiration
        expires_at = None
        if duration_hours:
            expires_at = now + timedelta(hours=duration_hours)
        elif emergency:
            expires_at = now + timedelta(hours=2)  # Emergency escalation expires in 2 hours
        
        # Create escalation record
        escalation = PrivilegeEscalation(
            escalation_id=escalation_id,
            user_id=user_id,
            from_role=current_role,
            to_role=target_role,
            escalation_type="emergency" if emergency else "temporary",
            justification=justification,
            approved_by=approved_by,
            escalated_at=now,
            expires_at=expires_at,
            reverted_at=None,
            metadata={}
        )
        
        self.privilege_escalations[escalation_id] = escalation
        
        # Actually escalate privileges (implementation would depend on role system)
        await self._apply_privilege_escalation(user_id, target_role)
        
        # Log audit event
        await self.audit_service.log_audit_event(
            event_type=AuditEventType.ROLE_ASSIGNED,
            action="privilege_escalated",
            description=f"Privileges escalated from {current_role} to {target_role}",
            severity=AuditSeverity.WARNING,
            user_id=user_id,
            metadata={
                "escalation_id": escalation_id,
                "from_role": current_role,
                "to_role": target_role,
                "justification": justification,
                "approved_by": approved_by,
                "emergency": emergency
            }
        )
        
        logger.warning(f"Privileges escalated for user {user_id}: {current_role} -> {target_role}")
        
        return escalation
    
    async def schedule_access_review(
        self,
        user_id: str,
        reviewer_id: str,
        review_type: str = "periodic",
        scheduled_date: Optional[datetime] = None
    ) -> AccessReview:
        """
        Schedule an access review for a user.
        
        Args:
            user_id: ID of user to review
            reviewer_id: ID of reviewer
            review_type: Type of review
            scheduled_date: When to conduct review
            
        Returns:
            AccessReview object
        """
        review_id = str(uuid4())
        
        if not scheduled_date:
            scheduled_date = datetime.utcnow() + timedelta(days=30)
        
        # Collect current access items
        access_items = await self._collect_user_access_items(user_id)
        
        review = AccessReview(
            review_id=review_id,
            user_id=user_id,
            reviewer_id=reviewer_id,
            review_type=review_type,
            status="pending",
            scheduled_date=scheduled_date,
            completed_date=None,
            access_items=access_items,
            decisions={},
            comments="",
            metadata={}
        )
        
        self.access_reviews[review_id] = review
        
        # Log audit event
        await self.audit_service.log_audit_event(
            event_type=AuditEventType.AUDIT_LOG_ACCESSED,
            action="access_review_scheduled",
            description=f"Access review scheduled for user",
            severity=AuditSeverity.INFO,
            user_id=user_id,
            metadata={
                "review_id": review_id,
                "reviewer_id": reviewer_id,
                "review_type": review_type,
                "scheduled_date": scheduled_date.isoformat()
            }
        )
        
        logger.info(f"Access review {review_id} scheduled for user {user_id}")
        
        return review
    
    async def cleanup_expired_access(self) -> Dict[str, int]:
        """
        Clean up expired access grants and escalations.
        
        Returns:
            Cleanup statistics
        """
        now = datetime.utcnow()
        cleanup_stats = {
            "expired_access_requests": 0,
            "expired_escalations": 0,
            "auto_revoked_access": 0
        }
        
        # Clean up expired access requests
        for access_request in self.access_requests.values():
            if (access_request.expires_at and 
                access_request.expires_at <= now and 
                access_request.status == AccessRequestStatus.APPROVED):
                
                access_request.status = AccessRequestStatus.EXPIRED
                access_request.revoked_at = now
                access_request.metadata["auto_expired"] = True
                
                # Remove actual permissions
                await self._remove_permissions(
                    access_request.user_id,
                    access_request.resource_type,
                    access_request.resource_id
                )
                
                cleanup_stats["expired_access_requests"] += 1
        
        # Clean up expired privilege escalations
        for escalation in self.privilege_escalations.values():
            if (escalation.expires_at and 
                escalation.expires_at <= now and 
                not escalation.reverted_at):
                
                escalation.reverted_at = now
                
                # Revert privileges
                await self._revert_privilege_escalation(escalation.user_id, escalation.from_role)
                
                cleanup_stats["expired_escalations"] += 1
        
        # Log cleanup activity
        await self.audit_service.log_audit_event(
            event_type=AuditEventType.SYSTEM_CONFIG_CHANGED,
            action="access_cleanup",
            description="Automated cleanup of expired access",
            severity=AuditSeverity.INFO,
            metadata=cleanup_stats
        )
        
        logger.info(f"Access cleanup completed: {cleanup_stats}")
        
        return cleanup_stats
    
    def _requires_approval(
        self,
        user_id: str,
        resource_type: str,
        access_types: List[AccessType],
        justification: AccessJustification,
        emergency: bool
    ) -> bool:
        """Determine if access request requires approval."""
        # Emergency access may bypass approval for certain scenarios
        if emergency and justification == AccessJustification.INCIDENT_RESPONSE:
            return False
        
        # High-privilege access always requires approval
        if AccessType.ADMIN in access_types or AccessType.DELETE in access_types:
            return True
        
        # Check against access policies
        policy_key = f"{resource_type}:{':'.join([at.value for at in access_types])}"
        if policy_key in self.access_policies:
            return self.access_policies[policy_key].get("requires_approval", True)
        
        # Default to requiring approval
        return True
    
    def _initialize_role_templates(self) -> Dict[str, Dict[str, Any]]:
        """Initialize role-based access templates."""
        return {
            "security_analyst": {
                "permissions": ["read_assets", "read_threats", "create_incidents"],
                "resource_types": ["assets", "threats", "incidents"],
                "max_access_duration_hours": 8
            },
            "security_admin": {
                "permissions": ["read_assets", "write_assets", "admin_users", "admin_system"],
                "resource_types": ["assets", "users", "system"],
                "max_access_duration_hours": 24
            },
            "auditor": {
                "permissions": ["read_assets", "read_audit_logs", "export_data"],
                "resource_types": ["assets", "audit_logs"],
                "max_access_duration_hours": 168  # 1 week
            }
        }
    
    def _initialize_access_policies(self) -> Dict[str, Dict[str, Any]]:
        """Initialize access policies."""
        return {
            "assets:read": {"requires_approval": False, "max_duration_hours": 24},
            "assets:write": {"requires_approval": True, "max_duration_hours": 8},
            "assets:delete": {"requires_approval": True, "max_duration_hours": 4},
            "users:admin": {"requires_approval": True, "max_duration_hours": 2},
            "system:admin": {"requires_approval": True, "max_duration_hours": 1}
        }
    
    # Additional helper methods would be implemented here...
    async def _grant_access(self, access_request: AccessRequest) -> None:
        """Grant access based on approved request."""
        pass
    
    async def _remove_permissions(self, user_id: str, resource_type: str, resource_id: Optional[str]) -> None:
        """Remove permissions from user."""
        pass
    
    async def _apply_privilege_escalation(self, user_id: str, target_role: str) -> None:
        """Apply privilege escalation."""
        pass
    
    async def _revert_privilege_escalation(self, user_id: str, original_role: str) -> None:
        """Revert privilege escalation."""
        pass
    
    async def _collect_user_access_items(self, user_id: str) -> List[Dict[str, Any]]:
        """Collect all access items for a user."""
        return []
