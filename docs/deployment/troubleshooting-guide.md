# Troubleshooting Guide - Blast-Radius Security Tool

## Overview

This guide provides comprehensive troubleshooting procedures for common issues encountered during deployment, operation, and maintenance of the Blast-Radius Security Tool.

## General Troubleshooting Approach

### 1. Information Gathering
```bash
# Check system status
kubectl get pods -n blast-radius
kubectl get services -n blast-radius
kubectl get ingress -n blast-radius

# Check recent events
kubectl get events -n blast-radius --sort-by='.lastTimestamp' | tail -20

# Check resource usage
kubectl top nodes
kubectl top pods -n blast-radius
```

### 2. Log Analysis
```bash
# Application logs
kubectl logs -f deployment/blast-radius-backend -n blast-radius --tail=100

# System logs
journalctl -u kubelet -f

# Container logs
docker logs container_name --tail=100 -f
```

### 3. Network Connectivity
```bash
# Test internal connectivity
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- nc -zv database-service 5432

# Test external connectivity
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- curl -I https://api.external-service.com

# DNS resolution
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- nslookup database-service
```

## Common Issues and Solutions

### Application Startup Issues

#### Issue: Pod Stuck in Pending State
**Symptoms:**
- Pods remain in "Pending" status
- No containers are created

**Diagnosis:**
```bash
# Check pod details
kubectl describe pod <pod-name> -n blast-radius

# Check node resources
kubectl describe nodes

# Check persistent volume claims
kubectl get pvc -n blast-radius
```

**Common Causes and Solutions:**

1. **Insufficient Resources**
   ```bash
   # Check resource requests vs available
   kubectl describe nodes | grep -A 5 "Allocated resources"
   
   # Solution: Scale cluster or reduce resource requests
   kubectl scale deployment blast-radius-backend --replicas=2 -n blast-radius
   ```

2. **PVC Binding Issues**
   ```bash
   # Check PVC status
   kubectl get pvc -n blast-radius
   
   # Check storage class
   kubectl get storageclass
   
   # Solution: Create or fix storage class
   kubectl apply -f k8s/storage/storageclass.yaml
   ```

3. **Node Selector Issues**
   ```bash
   # Check node labels
   kubectl get nodes --show-labels
   
   # Solution: Remove or update node selector
   kubectl patch deployment blast-radius-backend -n blast-radius -p '{"spec":{"template":{"spec":{"nodeSelector":null}}}}'
   ```

#### Issue: Pod Stuck in CrashLoopBackOff
**Symptoms:**
- Pod repeatedly crashes and restarts
- Exit code indicates application failure

**Diagnosis:**
```bash
# Check pod logs
kubectl logs <pod-name> -n blast-radius --previous

# Check pod events
kubectl describe pod <pod-name> -n blast-radius

# Check liveness/readiness probes
kubectl get pod <pod-name> -n blast-radius -o yaml | grep -A 10 "livenessProbe\|readinessProbe"
```

**Common Solutions:**

1. **Database Connection Issues**
   ```bash
   # Test database connectivity
   kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
     pg_isready -h $DATABASE_HOST -p 5432
   
   # Check database credentials
   kubectl get secret database-credentials -n blast-radius -o yaml
   
   # Solution: Update database configuration
   kubectl patch secret database-credentials -n blast-radius -p '{"data":{"host":"<base64-encoded-host>"}}'
   ```

2. **Missing Environment Variables**
   ```bash
   # Check environment variables
   kubectl exec -it deployment/blast-radius-backend -n blast-radius -- env | grep -i blast
   
   # Solution: Update configmap or secret
   kubectl patch configmap blast-radius-config -n blast-radius -p '{"data":{"MISSING_VAR":"value"}}'
   ```

3. **Application Configuration Errors**
   ```bash
   # Check application configuration
   kubectl exec -it deployment/blast-radius-backend -n blast-radius -- python -c "from app.core.config import settings; print(settings)"
   
   # Solution: Fix configuration and restart
   kubectl rollout restart deployment/blast-radius-backend -n blast-radius
   ```

### Database Issues

#### Issue: Database Connection Timeout
**Symptoms:**
- Application cannot connect to database
- Connection timeout errors in logs

**Diagnosis:**
```bash
# Test database connectivity from pod
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME -c "SELECT 1;"

# Check database status (for RDS)
aws rds describe-db-instances --db-instance-identifier blast-radius-database

# Check security groups
aws ec2 describe-security-groups --group-ids $DATABASE_SECURITY_GROUP_ID
```

**Solutions:**

1. **Security Group Configuration**
   ```bash
   # Add rule to allow database access
   aws ec2 authorize-security-group-ingress \
     --group-id $DATABASE_SECURITY_GROUP_ID \
     --protocol tcp \
     --port 5432 \
     --source-group $APPLICATION_SECURITY_GROUP_ID
   ```

2. **Database Instance Issues**
   ```bash
   # Check database status
   aws rds describe-db-instances --db-instance-identifier blast-radius-database \
     --query 'DBInstances[0].DBInstanceStatus'
   
   # Restart database if needed
   aws rds reboot-db-instance --db-instance-identifier blast-radius-database
   ```

#### Issue: Database Migration Failures
**Symptoms:**
- Migration job fails
- Database schema inconsistencies

**Diagnosis:**
```bash
# Check migration job logs
kubectl logs job/database-migration -n blast-radius

# Check current migration status
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  alembic current

# Check migration history
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  alembic history
```

**Solutions:**

1. **Manual Migration**
   ```bash
   # Run specific migration
   kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
     alembic upgrade head
   
   # Downgrade if needed
   kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
     alembic downgrade -1
   ```

2. **Schema Repair**
   ```bash
   # Mark migration as complete
   kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
     alembic stamp head
   
   # Repair schema manually
   kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
     psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME -f repair.sql
   ```

### Network and Connectivity Issues

#### Issue: Service Not Accessible
**Symptoms:**
- External requests fail to reach the application
- Load balancer health checks fail

**Diagnosis:**
```bash
# Check service configuration
kubectl get service blast-radius-backend -n blast-radius -o yaml

# Check endpoints
kubectl get endpoints blast-radius-backend -n blast-radius

# Check ingress configuration
kubectl describe ingress blast-radius-ingress -n blast-radius

# Check load balancer status
aws elbv2 describe-load-balancers --names blast-radius-alb
```

**Solutions:**

1. **Service Configuration Issues**
   ```bash
   # Check service selector
   kubectl get service blast-radius-backend -n blast-radius -o jsonpath='{.spec.selector}'
   
   # Check pod labels
   kubectl get pods -n blast-radius --show-labels
   
   # Fix service selector
   kubectl patch service blast-radius-backend -n blast-radius -p '{"spec":{"selector":{"app":"blast-radius-backend"}}}'
   ```

2. **Ingress Configuration Issues**
   ```bash
   # Check ingress controller logs
   kubectl logs -f deployment/aws-load-balancer-controller -n kube-system
   
   # Check ingress annotations
   kubectl get ingress blast-radius-ingress -n blast-radius -o yaml
   
   # Update ingress configuration
   kubectl apply -f k8s/ingress/blast-radius-ingress.yaml -n blast-radius
   ```

#### Issue: SSL/TLS Certificate Problems
**Symptoms:**
- SSL certificate errors
- HTTPS connections fail

**Diagnosis:**
```bash
# Check certificate status
aws acm describe-certificate --certificate-arn $CERTIFICATE_ARN

# Test SSL connection
openssl s_client -connect blast-radius.com:443 -servername blast-radius.com

# Check certificate in ingress
kubectl get ingress blast-radius-ingress -n blast-radius -o yaml | grep certificate
```

**Solutions:**

1. **Certificate Validation**
   ```bash
   # Check DNS validation records
   aws route53 list-resource-record-sets --hosted-zone-id $HOSTED_ZONE_ID
   
   # Add missing validation records
   aws route53 change-resource-record-sets \
     --hosted-zone-id $HOSTED_ZONE_ID \
     --change-batch file://certificate-validation.json
   ```

2. **Certificate Renewal**
   ```bash
   # Request new certificate
   aws acm request-certificate \
     --domain-name blast-radius.com \
     --subject-alternative-names "*.blast-radius.com" \
     --validation-method DNS
   
   # Update ingress with new certificate
   kubectl patch ingress blast-radius-ingress -n blast-radius -p \
     '{"metadata":{"annotations":{"alb.ingress.kubernetes.io/certificate-arn":"'$NEW_CERTIFICATE_ARN'"}}}'
   ```

### Performance Issues

#### Issue: High Response Times
**Symptoms:**
- API responses are slow
- Database queries take too long

**Diagnosis:**
```bash
# Check application metrics
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  curl http://localhost:8000/metrics

# Check database performance
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"

# Check resource usage
kubectl top pods -n blast-radius
```

**Solutions:**

1. **Database Optimization**
   ```bash
   # Add database indexes
   kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
     psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
     -c "CREATE INDEX CONCURRENTLY idx_assets_type ON assets(asset_type);"
   
   # Update database statistics
   kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
     psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
     -c "ANALYZE;"
   ```

2. **Application Scaling**
   ```bash
   # Scale application horizontally
   kubectl scale deployment blast-radius-backend --replicas=5 -n blast-radius
   
   # Scale vertically (increase resources)
   kubectl patch deployment blast-radius-backend -n blast-radius -p \
     '{"spec":{"template":{"spec":{"containers":[{"name":"blast-radius-backend","resources":{"requests":{"cpu":"500m","memory":"1Gi"},"limits":{"cpu":"1000m","memory":"2Gi"}}}]}}}}'
   ```

#### Issue: Memory Leaks
**Symptoms:**
- Memory usage continuously increases
- Pods get OOMKilled

**Diagnosis:**
```bash
# Check memory usage over time
kubectl top pods -n blast-radius --sort-by=memory

# Check memory limits
kubectl describe pod <pod-name> -n blast-radius | grep -A 5 "Limits\|Requests"

# Check for memory leaks in application
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  python -c "import psutil; print(f'Memory: {psutil.virtual_memory().percent}%')"
```

**Solutions:**

1. **Adjust Memory Limits**
   ```bash
   # Increase memory limits
   kubectl patch deployment blast-radius-backend -n blast-radius -p \
     '{"spec":{"template":{"spec":{"containers":[{"name":"blast-radius-backend","resources":{"limits":{"memory":"4Gi"}}}]}}}}'
   ```

2. **Application Optimization**
   ```bash
   # Enable memory profiling
   kubectl patch deployment blast-radius-backend -n blast-radius -p \
     '{"spec":{"template":{"spec":{"containers":[{"name":"blast-radius-backend","env":[{"name":"MEMORY_PROFILING","value":"true"}]}]}}}}'
   
   # Restart pods to clear memory
   kubectl rollout restart deployment/blast-radius-backend -n blast-radius
   ```

### Storage Issues

#### Issue: Persistent Volume Problems
**Symptoms:**
- Pods cannot mount volumes
- Data persistence issues

**Diagnosis:**
```bash
# Check PVC status
kubectl get pvc -n blast-radius

# Check PV status
kubectl get pv

# Check storage class
kubectl get storageclass

# Check volume mount in pod
kubectl describe pod <pod-name> -n blast-radius | grep -A 10 "Mounts\|Volumes"
```

**Solutions:**

1. **PVC Binding Issues**
   ```bash
   # Delete and recreate PVC
   kubectl delete pvc data-pvc -n blast-radius
   kubectl apply -f k8s/storage/pvc.yaml -n blast-radius
   
   # Check storage class availability
   kubectl get storageclass
   ```

2. **Volume Mount Permissions**
   ```bash
   # Fix volume permissions
   kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
     chown -R 1000:1000 /app/data
   
   # Update security context
   kubectl patch deployment blast-radius-backend -n blast-radius -p \
     '{"spec":{"template":{"spec":{"securityContext":{"fsGroup":1000}}}}}'
   ```

## Monitoring and Alerting Issues

### Issue: Missing Metrics
**Symptoms:**
- Grafana dashboards show no data
- Prometheus targets are down

**Diagnosis:**
```bash
# Check Prometheus targets
kubectl port-forward service/prometheus 9090:9090 -n blast-radius
# Visit http://localhost:9090/targets

# Check service monitor configuration
kubectl get servicemonitor -n blast-radius

# Check metrics endpoint
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  curl http://localhost:8000/metrics
```

**Solutions:**

1. **Fix Service Monitor**
   ```bash
   # Apply service monitor
   kubectl apply -f monitoring/prometheus/servicemonitors/ -n blast-radius
   
   # Check Prometheus configuration
   kubectl get configmap prometheus-config -n blast-radius -o yaml
   ```

2. **Enable Metrics in Application**
   ```bash
   # Enable metrics endpoint
   kubectl patch deployment blast-radius-backend -n blast-radius -p \
     '{"spec":{"template":{"metadata":{"annotations":{"prometheus.io/scrape":"true","prometheus.io/port":"8000","prometheus.io/path":"/metrics"}}}}}'
   ```

## Emergency Procedures

### Complete System Recovery
```bash
# 1. Check cluster status
kubectl cluster-info
kubectl get nodes

# 2. Restore from backup if needed
./scripts/restore-from-backup.sh production

# 3. Redeploy application
kubectl apply -f k8s/production/ -n blast-radius

# 4. Verify system health
./scripts/health-check.sh production

# 5. Notify stakeholders
./scripts/send-notification.sh "System restored and operational"
```

### Rollback Procedure
```bash
# 1. Identify last known good version
kubectl rollout history deployment/blast-radius-backend -n blast-radius

# 2. Rollback to previous version
kubectl rollout undo deployment/blast-radius-backend -n blast-radius

# 3. Verify rollback
kubectl rollout status deployment/blast-radius-backend -n blast-radius

# 4. Test functionality
curl -f https://blast-radius.com/health
```

## Getting Help

### Internal Resources
- **Documentation**: `/docs/` directory
- **Runbooks**: `/docs/operations/runbooks/`
- **Architecture Diagrams**: `/docs/architecture/`

### External Support
- **Kubernetes Issues**: https://kubernetes.io/docs/tasks/debug-application-cluster/
- **AWS Support**: AWS Support Center
- **Application Logs**: Check CloudWatch Logs

### Escalation Procedures
1. **Level 1**: Development team member
2. **Level 2**: Senior engineer or team lead
3. **Level 3**: Architecture team or external consultant
4. **Emergency**: On-call engineer via PagerDuty

This troubleshooting guide provides systematic approaches to diagnosing and resolving common issues in the Blast-Radius Security Tool deployment and operation.
