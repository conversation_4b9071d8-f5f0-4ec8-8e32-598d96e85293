# Alertmanager Configuration for Blast-Radius Security Tool

global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'password'
  smtp_require_tls: true

# Templates for alert notifications
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# Route tree for alert routing
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default-receiver'
  routes:
    # Critical alerts go to on-call team immediately
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      repeat_interval: 5m
      routes:
        # Security-specific critical alerts
        - match:
            team: security
          receiver: 'security-critical'
        # Platform critical alerts
        - match:
            team: platform
          receiver: 'platform-critical'

    # Warning alerts go to team channels
    - match:
        severity: warning
      receiver: 'warning-alerts'
      group_interval: 5m
      repeat_interval: 30m
      routes:
        # Security warnings
        - match:
            team: security
          receiver: 'security-warnings'
        # Platform warnings
        - match:
            team: platform
          receiver: 'platform-warnings'

    # Info alerts for monitoring
    - match:
        severity: info
      receiver: 'info-alerts'
      group_interval: 10m
      repeat_interval: 2h

    # Database-specific alerts
    - match:
        service: database
      receiver: 'database-alerts'
      group_interval: 5m

    # API-specific alerts
    - match:
        service: api
      receiver: 'api-alerts'
      group_interval: 2m

# Inhibition rules to prevent alert spam
inhibit_rules:
  # Inhibit warning alerts if critical alert is firing
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance']

  # Inhibit info alerts if warning or critical is firing
  - source_match:
      severity: 'warning'
    target_match:
      severity: 'info'
    equal: ['alertname', 'instance']

  # Inhibit API alerts if the service is down
  - source_match:
      alertname: 'BlastRadiusAPIDown'
    target_match_re:
      alertname: 'BlastRadiusAPI.*'
    equal: ['instance']

# Alert receivers configuration
receivers:
  # Default receiver for unmatched alerts
  - name: 'default-receiver'
    email_configs:
      - to: '<EMAIL>'
        subject: '[Blast-Radius] {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          {{ end }}

  # Critical alerts - immediate notification
  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[CRITICAL] Blast-Radius Alert: {{ .GroupLabels.alertname }}'
        body: |
          🚨 CRITICAL ALERT 🚨
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Severity: {{ .Labels.severity }}
          Service: {{ .Labels.service }}
          Team: {{ .Labels.team }}
          
          Runbook: {{ .Annotations.runbook_url }}
          
          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          {{ end }}
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#alerts-critical'
        title: '🚨 Critical Alert: {{ .GroupLabels.alertname }}'
        text: |
          {{ range .Alerts }}
          *{{ .Annotations.summary }}*
          {{ .Annotations.description }}
          
          *Service:* {{ .Labels.service }}
          *Team:* {{ .Labels.team }}
          {{ if .Annotations.runbook_url }}*Runbook:* {{ .Annotations.runbook_url }}{{ end }}
          {{ end }}
        send_resolved: true

  # Security critical alerts
  - name: 'security-critical'
    email_configs:
      - to: '<EMAIL>'
        subject: '[SECURITY CRITICAL] {{ .GroupLabels.alertname }}'
        body: |
          🔒 SECURITY CRITICAL ALERT 🔒
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          
          This is a security-related critical alert that requires immediate attention.
          
          Labels: {{ range .Labels.SortedPairs }}{{ .Name }}={{ .Value }} {{ end }}
          {{ end }}

  # Platform critical alerts
  - name: 'platform-critical'
    email_configs:
      - to: '<EMAIL>'
        subject: '[PLATFORM CRITICAL] {{ .GroupLabels.alertname }}'

  # Warning alerts
  - name: 'warning-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[WARNING] Blast-Radius: {{ .GroupLabels.alertname }}'

  # Security warnings
  - name: 'security-warnings'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#security-alerts'
        title: '⚠️ Security Warning: {{ .GroupLabels.alertname }}'

  # Platform warnings
  - name: 'platform-warnings'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#platform-alerts'
        title: '⚠️ Platform Warning: {{ .GroupLabels.alertname }}'

  # Info alerts
  - name: 'info-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[INFO] Blast-Radius: {{ .GroupLabels.alertname }}'

  # Database alerts
  - name: 'database-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[DATABASE] {{ .GroupLabels.alertname }}'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#database-alerts'
        title: '🗄️ Database Alert: {{ .GroupLabels.alertname }}'

  # API alerts
  - name: 'api-alerts'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#api-alerts'
        title: '🔌 API Alert: {{ .GroupLabels.alertname }}'
        send_resolved: true

# Silence configuration
silences:
  # Example: Silence maintenance window alerts
  # - matchers:
  #     - name: alertname
  #       value: MaintenanceMode
  #   startsAt: "2024-01-01T00:00:00Z"
  #   endsAt: "2024-01-01T06:00:00Z"
  #   createdBy: "<EMAIL>"
  #   comment: "Scheduled maintenance window"
