# Backup and Recovery Runbooks - Blast-Radius Security Tool

## Overview

This document provides comprehensive procedures for backup and recovery operations for the Blast-Radius Security Tool, including database backups, application data backups, and disaster recovery procedures.

## Backup Procedures

### Database Backup Procedures

#### Automated Daily Backup
**Schedule**: Daily at 2:00 AM UTC
**Retention**: 30 days for daily, 12 weeks for weekly, 12 months for monthly

```bash
#!/bin/bash
# scripts/automated-db-backup.sh

set -e

# Configuration
BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_BUCKET="blast-radius-backups"
DATABASE_IDENTIFIER="blast-radius-database"

echo "Starting automated database backup - $BACKUP_DATE"

# 1. Create RDS snapshot
SNAPSHOT_ID="blast-radius-auto-backup-$BACKUP_DATE"
aws rds create-db-snapshot \
  --db-instance-identifier $DATABASE_IDENTIFIER \
  --db-snapshot-identifier $SNAPSHOT_ID

# 2. Wait for snapshot completion
echo "Waiting for snapshot to complete..."
aws rds wait db-snapshot-completed \
  --db-snapshot-identifier $SNAPSHOT_ID

# 3. Create logical backup
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  pg_dump -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  --verbose --no-password --format=custom \
  > /tmp/blast-radius-backup-$BACKUP_DATE.dump

# 4. Upload to S3
aws s3 cp /tmp/blast-radius-backup-$BACKUP_DATE.dump \
  s3://$BACKUP_BUCKET/database/daily/blast-radius-backup-$BACKUP_DATE.dump

# 5. Cleanup local backup
rm /tmp/blast-radius-backup-$BACKUP_DATE.dump

# 6. Verify backup integrity
aws s3 ls s3://$BACKUP_BUCKET/database/daily/blast-radius-backup-$BACKUP_DATE.dump

echo "Database backup completed successfully - $SNAPSHOT_ID"
```

#### Manual Database Backup
```bash
#!/bin/bash
# scripts/manual-db-backup.sh

BACKUP_TYPE=${1:-manual}
BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="blast-radius-$BACKUP_TYPE-backup-$BACKUP_DATE"

echo "Creating manual database backup: $BACKUP_NAME"

# 1. Create RDS snapshot
aws rds create-db-snapshot \
  --db-instance-identifier blast-radius-database \
  --db-snapshot-identifier $BACKUP_NAME

# 2. Create logical backup with compression
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  pg_dump -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME \
  --verbose --no-password --format=custom --compress=9 \
  > /tmp/$BACKUP_NAME.dump

# 3. Upload to S3 with metadata
aws s3 cp /tmp/$BACKUP_NAME.dump \
  s3://blast-radius-backups/database/manual/$BACKUP_NAME.dump \
  --metadata "backup-type=$BACKUP_TYPE,created-by=$(whoami),created-date=$BACKUP_DATE"

# 4. Create backup manifest
cat > /tmp/$BACKUP_NAME.manifest << EOF
{
  "backup_name": "$BACKUP_NAME",
  "backup_type": "$BACKUP_TYPE",
  "created_date": "$BACKUP_DATE",
  "created_by": "$(whoami)",
  "database_version": "$(kubectl exec -it deployment/blast-radius-backend -n blast-radius -- psql -h $DATABASE_HOST -U $DATABASE_USER -d $DATABASE_NAME -t -c 'SELECT version();')",
  "backup_size": "$(stat -c%s /tmp/$BACKUP_NAME.dump)",
  "rds_snapshot": "$BACKUP_NAME"
}
EOF

# 5. Upload manifest
aws s3 cp /tmp/$BACKUP_NAME.manifest \
  s3://blast-radius-backups/database/manual/$BACKUP_NAME.manifest

# 6. Cleanup
rm /tmp/$BACKUP_NAME.dump /tmp/$BACKUP_NAME.manifest

echo "Manual backup completed: $BACKUP_NAME"
```

#### Pre-Deployment Backup
```bash
#!/bin/bash
# scripts/pre-deployment-backup.sh

DEPLOYMENT_VERSION=${1:-$(git rev-parse --short HEAD)}
BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="blast-radius-pre-deploy-$DEPLOYMENT_VERSION-$BACKUP_DATE"

echo "Creating pre-deployment backup: $BACKUP_NAME"

# 1. Create RDS snapshot
aws rds create-db-snapshot \
  --db-instance-identifier blast-radius-database \
  --db-snapshot-identifier $BACKUP_NAME \
  --tags Key=Purpose,Value=PreDeployment Key=Version,Value=$DEPLOYMENT_VERSION

# 2. Backup application configuration
kubectl get configmaps -n blast-radius -o yaml > /tmp/configmaps-$BACKUP_DATE.yaml
kubectl get secrets -n blast-radius -o yaml > /tmp/secrets-$BACKUP_DATE.yaml

# 3. Backup persistent volumes
kubectl get pv -o yaml > /tmp/persistent-volumes-$BACKUP_DATE.yaml
kubectl get pvc -n blast-radius -o yaml > /tmp/persistent-volume-claims-$BACKUP_DATE.yaml

# 4. Create deployment state backup
kubectl get deployments -n blast-radius -o yaml > /tmp/deployments-$BACKUP_DATE.yaml
kubectl get services -n blast-radius -o yaml > /tmp/services-$BACKUP_DATE.yaml
kubectl get ingress -n blast-radius -o yaml > /tmp/ingress-$BACKUP_DATE.yaml

# 5. Package and upload
tar -czf /tmp/pre-deployment-$BACKUP_NAME.tar.gz \
  /tmp/*-$BACKUP_DATE.yaml

aws s3 cp /tmp/pre-deployment-$BACKUP_NAME.tar.gz \
  s3://blast-radius-backups/deployments/pre-deployment-$BACKUP_NAME.tar.gz

# 6. Cleanup
rm /tmp/*-$BACKUP_DATE.yaml /tmp/pre-deployment-$BACKUP_NAME.tar.gz

echo "Pre-deployment backup completed: $BACKUP_NAME"
```

### Application Data Backup

#### File System Backup
```bash
#!/bin/bash
# scripts/filesystem-backup.sh

BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="blast-radius-filesystem-$BACKUP_DATE"

echo "Starting filesystem backup: $BACKUP_NAME"

# 1. Create volume snapshots
kubectl apply -f - << EOF
apiVersion: snapshot.storage.k8s.io/v1
kind: VolumeSnapshot
metadata:
  name: blast-radius-data-snapshot-$BACKUP_DATE
  namespace: blast-radius
spec:
  source:
    persistentVolumeClaimName: blast-radius-data-pvc
  volumeSnapshotClassName: ebs-csi-snapshot-class
EOF

# 2. Wait for snapshot completion
kubectl wait --for=condition=ReadyToUse volumesnapshot/blast-radius-data-snapshot-$BACKUP_DATE -n blast-radius --timeout=300s

# 3. Backup uploaded files
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  tar -czf /tmp/uploaded-files-$BACKUP_DATE.tar.gz /app/uploads/

# 4. Upload to S3
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  aws s3 cp /tmp/uploaded-files-$BACKUP_DATE.tar.gz \
  s3://blast-radius-backups/filesystem/uploaded-files-$BACKUP_DATE.tar.gz

# 5. Backup logs
kubectl logs deployment/blast-radius-backend -n blast-radius --since=24h > /tmp/app-logs-$BACKUP_DATE.log
aws s3 cp /tmp/app-logs-$BACKUP_DATE.log \
  s3://blast-radius-backups/logs/app-logs-$BACKUP_DATE.log

# 6. Cleanup
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  rm /tmp/uploaded-files-$BACKUP_DATE.tar.gz
rm /tmp/app-logs-$BACKUP_DATE.log

echo "Filesystem backup completed: $BACKUP_NAME"
```

#### Configuration Backup
```bash
#!/bin/bash
# scripts/config-backup.sh

BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="blast-radius-config-$BACKUP_DATE"

echo "Starting configuration backup: $BACKUP_NAME"

# 1. Backup Kubernetes configurations
mkdir -p /tmp/config-backup-$BACKUP_DATE/{k8s,terraform,monitoring}

# Kubernetes resources
kubectl get all -n blast-radius -o yaml > /tmp/config-backup-$BACKUP_DATE/k8s/all-resources.yaml
kubectl get configmaps -n blast-radius -o yaml > /tmp/config-backup-$BACKUP_DATE/k8s/configmaps.yaml
kubectl get secrets -n blast-radius -o yaml > /tmp/config-backup-$BACKUP_DATE/k8s/secrets.yaml
kubectl get networkpolicies -n blast-radius -o yaml > /tmp/config-backup-$BACKUP_DATE/k8s/networkpolicies.yaml

# 2. Backup Terraform state
aws s3 cp s3://blast-radius-terraform-state/terraform.tfstate \
  /tmp/config-backup-$BACKUP_DATE/terraform/terraform.tfstate

# 3. Backup monitoring configurations
kubectl get configmaps -n blast-radius prometheus-config -o yaml > \
  /tmp/config-backup-$BACKUP_DATE/monitoring/prometheus-config.yaml
kubectl get configmaps -n blast-radius grafana-dashboards -o yaml > \
  /tmp/config-backup-$BACKUP_DATE/monitoring/grafana-dashboards.yaml

# 4. Package and upload
tar -czf /tmp/$BACKUP_NAME.tar.gz -C /tmp config-backup-$BACKUP_DATE/

aws s3 cp /tmp/$BACKUP_NAME.tar.gz \
  s3://blast-radius-backups/configuration/$BACKUP_NAME.tar.gz

# 5. Cleanup
rm -rf /tmp/config-backup-$BACKUP_DATE /tmp/$BACKUP_NAME.tar.gz

echo "Configuration backup completed: $BACKUP_NAME"
```

## Recovery Procedures

### Database Recovery

#### Point-in-Time Recovery
```bash
#!/bin/bash
# scripts/database-point-in-time-recovery.sh

RESTORE_TIME=${1:-$(date -d '1 hour ago' --iso-8601=seconds)}
NEW_INSTANCE_ID="blast-radius-database-restored-$(date +%Y%m%d%H%M%S)"

echo "Starting point-in-time recovery to: $RESTORE_TIME"

# 1. Create new RDS instance from point-in-time
aws rds restore-db-instance-to-point-in-time \
  --source-db-instance-identifier blast-radius-database \
  --target-db-instance-identifier $NEW_INSTANCE_ID \
  --restore-time $RESTORE_TIME \
  --db-subnet-group-name blast-radius-db-subnet-group \
  --vpc-security-group-ids $DATABASE_SECURITY_GROUP_ID

# 2. Wait for instance to be available
echo "Waiting for restored instance to be available..."
aws rds wait db-instance-available \
  --db-instance-identifier $NEW_INSTANCE_ID

# 3. Get new endpoint
NEW_ENDPOINT=$(aws rds describe-db-instances \
  --db-instance-identifier $NEW_INSTANCE_ID \
  --query 'DBInstances[0].Endpoint.Address' --output text)

echo "Restored database endpoint: $NEW_ENDPOINT"

# 4. Update application configuration (manual step)
echo "Manual step required: Update DATABASE_HOST to $NEW_ENDPOINT"
echo "kubectl patch secret database-credentials -n blast-radius -p '{\"data\":{\"host\":\"$(echo -n $NEW_ENDPOINT | base64)\"}}'"

# 5. Restart application
echo "Restart application after updating configuration:"
echo "kubectl rollout restart deployment/blast-radius-backend -n blast-radius"

echo "Point-in-time recovery completed. New instance: $NEW_INSTANCE_ID"
```

#### Snapshot Recovery
```bash
#!/bin/bash
# scripts/database-snapshot-recovery.sh

SNAPSHOT_ID=${1}
NEW_INSTANCE_ID="blast-radius-database-restored-$(date +%Y%m%d%H%M%S)"

if [ -z "$SNAPSHOT_ID" ]; then
    echo "Usage: $0 <snapshot-id>"
    echo "Available snapshots:"
    aws rds describe-db-snapshots \
      --db-instance-identifier blast-radius-database \
      --query 'DBSnapshots[*].[DBSnapshotIdentifier,SnapshotCreateTime]' \
      --output table
    exit 1
fi

echo "Starting snapshot recovery from: $SNAPSHOT_ID"

# 1. Restore from snapshot
aws rds restore-db-instance-from-db-snapshot \
  --db-instance-identifier $NEW_INSTANCE_ID \
  --db-snapshot-identifier $SNAPSHOT_ID \
  --db-subnet-group-name blast-radius-db-subnet-group \
  --vpc-security-group-ids $DATABASE_SECURITY_GROUP_ID

# 2. Wait for completion
aws rds wait db-instance-available \
  --db-instance-identifier $NEW_INSTANCE_ID

# 3. Get endpoint
NEW_ENDPOINT=$(aws rds describe-db-instances \
  --db-instance-identifier $NEW_INSTANCE_ID \
  --query 'DBInstances[0].Endpoint.Address' --output text)

echo "Database restored successfully. Endpoint: $NEW_ENDPOINT"
echo "Update application configuration and restart services."
```

#### Logical Backup Recovery
```bash
#!/bin/bash
# scripts/logical-backup-recovery.sh

BACKUP_FILE=${1}
TARGET_DATABASE=${2:-blast_radius_restored}

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup-file> [target-database]"
    echo "Available backups:"
    aws s3 ls s3://blast-radius-backups/database/daily/ | tail -10
    exit 1
fi

echo "Starting logical backup recovery from: $BACKUP_FILE"

# 1. Download backup file
aws s3 cp s3://blast-radius-backups/database/daily/$BACKUP_FILE /tmp/$BACKUP_FILE

# 2. Create target database
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d postgres \
  -c "CREATE DATABASE $TARGET_DATABASE;"

# 3. Restore from backup
kubectl exec -i deployment/blast-radius-backend -n blast-radius -- \
  pg_restore -h $DATABASE_HOST -U $DATABASE_USER -d $TARGET_DATABASE \
  --verbose --no-password < /tmp/$BACKUP_FILE

# 4. Verify restoration
kubectl exec -it deployment/blast-radius-backend -n blast-radius -- \
  psql -h $DATABASE_HOST -U $DATABASE_USER -d $TARGET_DATABASE \
  -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';"

# 5. Cleanup
rm /tmp/$BACKUP_FILE

echo "Logical backup recovery completed. Database: $TARGET_DATABASE"
```

### Application Recovery

#### Full Application Recovery
```bash
#!/bin/bash
# scripts/full-application-recovery.sh

BACKUP_DATE=${1}
RECOVERY_NAMESPACE=${2:-blast-radius-recovery}

if [ -z "$BACKUP_DATE" ]; then
    echo "Usage: $0 <backup-date> [recovery-namespace]"
    echo "Available backups:"
    aws s3 ls s3://blast-radius-backups/deployments/ | tail -10
    exit 1
fi

echo "Starting full application recovery for: $BACKUP_DATE"

# 1. Create recovery namespace
kubectl create namespace $RECOVERY_NAMESPACE --dry-run=client -o yaml | kubectl apply -f -

# 2. Download and extract backup
aws s3 cp s3://blast-radius-backups/deployments/pre-deployment-$BACKUP_DATE.tar.gz /tmp/
tar -xzf /tmp/pre-deployment-$BACKUP_DATE.tar.gz -C /tmp/

# 3. Restore configurations
kubectl apply -f /tmp/configmaps-*.yaml -n $RECOVERY_NAMESPACE
kubectl apply -f /tmp/secrets-*.yaml -n $RECOVERY_NAMESPACE

# 4. Restore persistent volumes
kubectl apply -f /tmp/persistent-volumes-*.yaml
kubectl apply -f /tmp/persistent-volume-claims-*.yaml -n $RECOVERY_NAMESPACE

# 5. Restore application deployments
kubectl apply -f /tmp/deployments-*.yaml -n $RECOVERY_NAMESPACE
kubectl apply -f /tmp/services-*.yaml -n $RECOVERY_NAMESPACE

# 6. Wait for pods to be ready
kubectl wait --for=condition=available deployment --all -n $RECOVERY_NAMESPACE --timeout=300s

# 7. Verify recovery
kubectl get pods -n $RECOVERY_NAMESPACE
kubectl exec -it deployment/blast-radius-backend -n $RECOVERY_NAMESPACE -- \
  curl -f http://localhost:8000/health

echo "Application recovery completed in namespace: $RECOVERY_NAMESPACE"
```

#### Configuration Recovery
```bash
#!/bin/bash
# scripts/config-recovery.sh

CONFIG_BACKUP=${1}

if [ -z "$CONFIG_BACKUP" ]; then
    echo "Usage: $0 <config-backup-name>"
    echo "Available config backups:"
    aws s3 ls s3://blast-radius-backups/configuration/ | tail -10
    exit 1
fi

echo "Starting configuration recovery from: $CONFIG_BACKUP"

# 1. Download configuration backup
aws s3 cp s3://blast-radius-backups/configuration/$CONFIG_BACKUP /tmp/
tar -xzf /tmp/$CONFIG_BACKUP -C /tmp/

# 2. Restore Kubernetes configurations
kubectl apply -f /tmp/config-backup-*/k8s/configmaps.yaml -n blast-radius
kubectl apply -f /tmp/config-backup-*/k8s/secrets.yaml -n blast-radius
kubectl apply -f /tmp/config-backup-*/k8s/networkpolicies.yaml -n blast-radius

# 3. Restore monitoring configurations
kubectl apply -f /tmp/config-backup-*/monitoring/ -n blast-radius

# 4. Restart affected services
kubectl rollout restart deployment/blast-radius-backend -n blast-radius
kubectl rollout restart deployment/prometheus -n blast-radius
kubectl rollout restart deployment/grafana -n blast-radius

# 5. Cleanup
rm -rf /tmp/config-backup-* /tmp/$CONFIG_BACKUP

echo "Configuration recovery completed"
```

## Disaster Recovery Procedures

### Complete System Recovery
```bash
#!/bin/bash
# scripts/disaster-recovery.sh

RECOVERY_REGION=${1:-us-west-2}
BACKUP_DATE=${2:-latest}

echo "Starting disaster recovery in region: $RECOVERY_REGION"

# 1. Deploy infrastructure in recovery region
cd infrastructure/terraform
terraform workspace new disaster-recovery-$(date +%Y%m%d)
terraform workspace select disaster-recovery-$(date +%Y%m%d)

terraform plan -var-file="environments/disaster-recovery.tfvars" \
  -var="aws_region=$RECOVERY_REGION" -out=dr.tfplan
terraform apply dr.tfplan

# 2. Restore database from cross-region backup
LATEST_SNAPSHOT=$(aws rds describe-db-snapshots \
  --region $RECOVERY_REGION \
  --query 'DBSnapshots[?starts_with(DBSnapshotIdentifier, `blast-radius`)]|sort_by(@, &SnapshotCreateTime)[-1].DBSnapshotIdentifier' \
  --output text)

aws rds restore-db-instance-from-db-snapshot \
  --region $RECOVERY_REGION \
  --db-instance-identifier blast-radius-database-dr \
  --db-snapshot-identifier $LATEST_SNAPSHOT

# 3. Deploy application
kubectl config use-context blast-radius-dr-cluster
kubectl apply -f k8s/production/ -n blast-radius

# 4. Update DNS to point to DR environment
aws route53 change-resource-record-sets \
  --hosted-zone-id $HOSTED_ZONE_ID \
  --change-batch file://dr-dns-update.json

echo "Disaster recovery completed in region: $RECOVERY_REGION"
```

### Recovery Testing
```bash
#!/bin/bash
# scripts/recovery-test.sh

TEST_TYPE=${1:-database}
TEST_DATE=$(date +%Y%m%d_%H%M%S)

echo "Starting recovery test: $TEST_TYPE - $TEST_DATE"

case $TEST_TYPE in
    "database")
        # Test database recovery
        ./scripts/database-snapshot-recovery.sh blast-radius-test-snapshot-$TEST_DATE
        ;;
    "application")
        # Test application recovery
        ./scripts/full-application-recovery.sh $TEST_DATE blast-radius-test
        ;;
    "disaster")
        # Test disaster recovery
        ./scripts/disaster-recovery.sh us-east-1 $TEST_DATE
        ;;
    *)
        echo "Unknown test type: $TEST_TYPE"
        echo "Available types: database, application, disaster"
        exit 1
        ;;
esac

echo "Recovery test completed: $TEST_TYPE - $TEST_DATE"
```

## Backup Monitoring and Validation

### Backup Verification
```bash
#!/bin/bash
# scripts/backup-verification.sh

echo "Starting backup verification - $(date)"

# 1. Verify RDS snapshots
echo "Checking RDS snapshots..."
aws rds describe-db-snapshots \
  --db-instance-identifier blast-radius-database \
  --query 'DBSnapshots[?SnapshotCreateTime>=`2024-01-01`].[DBSnapshotIdentifier,SnapshotCreateTime,Status]' \
  --output table

# 2. Verify S3 backups
echo "Checking S3 backups..."
aws s3 ls s3://blast-radius-backups/database/daily/ | tail -7

# 3. Test backup integrity
LATEST_BACKUP=$(aws s3 ls s3://blast-radius-backups/database/daily/ | tail -1 | awk '{print $4}')
aws s3 cp s3://blast-radius-backups/database/daily/$LATEST_BACKUP /tmp/test-backup.dump

# Verify backup file integrity
file /tmp/test-backup.dump
pg_restore --list /tmp/test-backup.dump | head -10

rm /tmp/test-backup.dump

echo "Backup verification completed"
```

This backup and recovery runbook provides comprehensive procedures for protecting and restoring the Blast-Radius Security Tool data and configurations.
