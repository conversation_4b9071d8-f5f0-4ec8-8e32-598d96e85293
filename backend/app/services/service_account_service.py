"""
Service Account Service for Zero-Trust Architecture.

This service provides comprehensive service account management including:
- Service account creation and lifecycle management
- Authentication and authorization
- Token management and rotation
- Audit logging and compliance
"""

import secrets
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.db.models.service_account import ServiceAccount, ServiceAccountToken, ServiceAccountAuditLog
from app.core.permissions import Permission
from app.core.security import create_access_token, verify_token
from app.core.exceptions import (
    ServiceAccountNotFoundError,
    ServiceAccountInactiveError,
    ServiceAccountExpiredError,
    ServiceAccountLockedError,
    InvalidCredentialsError,
    TokenExpiredError,
    TokenRevokedError,
    PermissionDeniedError
)


class ServiceAccountService:
    """Service for managing service accounts in a zero-trust environment."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_service_account(
        self,
        name: str,
        service_type: str,
        environment: str,
        description: Optional[str] = None,
        namespace: Optional[str] = None,
        permissions: Optional[List[Permission]] = None,
        scopes: Optional[List[str]] = None,
        expires_at: Optional[datetime] = None,
        created_by_user_id: Optional[UUID] = None,
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None
    ) -> Tuple[ServiceAccount, str]:
        """
        Create a new service account with credentials.
        
        Args:
            name: Unique name for the service account
            service_type: Type of service (api, worker, integration, etc.)
            environment: Environment (production, staging, development)
            description: Optional description
            namespace: Optional namespace/grouping
            permissions: List of permissions to grant
            scopes: List of OAuth-style scopes
            expires_at: Optional expiration date
            created_by_user_id: ID of user creating the account
            metadata: Additional metadata
            tags: Tags for organization
            
        Returns:
            Tuple of (ServiceAccount, client_secret)
            
        Raises:
            ValueError: If service account with name already exists
        """
        # Check if service account already exists
        existing = self.db.query(ServiceAccount).filter(ServiceAccount.name == name).first()
        if existing:
            raise ValueError(f"Service account with name '{name}' already exists")
        
        # Create service account
        service_account = ServiceAccount(
            name=name,
            service_type=service_type,
            environment=environment,
            description=description,
            namespace=namespace,
            expires_at=expires_at,
            created_by_user_id=created_by_user_id,
            metadata=metadata or {},
            tags=tags or []
        )
        
        # Generate credentials
        client_id, client_secret = service_account.generate_client_credentials()
        
        # Set permissions
        if permissions:
            service_account.permissions = [p.value for p in permissions]
        
        # Set scopes
        if scopes:
            service_account.scopes = scopes
        
        # Save to database
        self.db.add(service_account)
        self.db.commit()
        self.db.refresh(service_account)
        
        # Log creation
        self._log_event(
            service_account.id,
            "created",
            f"Service account '{name}' created",
            {"service_type": service_type, "environment": environment},
            actor_type="user",
            actor_id=str(created_by_user_id) if created_by_user_id else None,
            success=True
        )
        
        return service_account, client_secret
    
    def authenticate_service_account(
        self,
        client_id: str,
        client_secret: str,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        requested_scopes: Optional[List[str]] = None
    ) -> ServiceAccountToken:
        """
        Authenticate a service account and issue an access token.
        
        Args:
            client_id: Service account client ID
            client_secret: Service account client secret
            ip_address: Client IP address
            user_agent: Client user agent
            requested_scopes: Requested token scopes
            
        Returns:
            ServiceAccountToken
            
        Raises:
            InvalidCredentialsError: If credentials are invalid
            ServiceAccountInactiveError: If account is inactive
            ServiceAccountExpiredError: If account is expired
            ServiceAccountLockedError: If account is locked
        """
        # Find service account
        service_account = self.db.query(ServiceAccount).filter(
            ServiceAccount.client_id == client_id
        ).first()
        
        if not service_account:
            self._log_event(
                None,
                "authentication_failed",
                f"Authentication failed for unknown client_id: {client_id}",
                {"client_id": client_id, "ip_address": ip_address},
                actor_type="service_account",
                actor_id=client_id,
                success=False
            )
            raise InvalidCredentialsError("Invalid client credentials")
        
        # Check account status
        if not service_account.is_active:
            self._log_event(
                service_account.id,
                "authentication_failed",
                "Authentication failed: account inactive",
                {"reason": "inactive", "ip_address": ip_address},
                actor_type="service_account",
                actor_id=client_id,
                success=False
            )
            raise ServiceAccountInactiveError("Service account is inactive")
        
        if service_account.is_expired:
            self._log_event(
                service_account.id,
                "authentication_failed",
                "Authentication failed: account expired",
                {"reason": "expired", "ip_address": ip_address},
                actor_type="service_account",
                actor_id=client_id,
                success=False
            )
            raise ServiceAccountExpiredError("Service account is expired")
        
        if service_account.is_locked:
            self._log_event(
                service_account.id,
                "authentication_failed",
                "Authentication failed: account locked",
                {"reason": "locked", "ip_address": ip_address},
                actor_type="service_account",
                actor_id=client_id,
                success=False
            )
            raise ServiceAccountLockedError("Service account is locked")
        
        # Verify credentials
        if not service_account.verify_client_secret(client_secret):
            service_account.record_authentication_attempt(False, ip_address, user_agent)
            self.db.commit()
            
            self._log_event(
                service_account.id,
                "authentication_failed",
                "Authentication failed: invalid secret",
                {"reason": "invalid_secret", "ip_address": ip_address},
                actor_type="service_account",
                actor_id=client_id,
                success=False
            )
            raise InvalidCredentialsError("Invalid client credentials")
        
        # Check IP restrictions
        if not service_account.is_ip_allowed(ip_address or ""):
            self._log_event(
                service_account.id,
                "authentication_failed",
                "Authentication failed: IP not allowed",
                {"reason": "ip_restricted", "ip_address": ip_address},
                actor_type="service_account",
                actor_id=client_id,
                success=False
            )
            raise PermissionDeniedError("IP address not allowed")
        
        # Check user agent restrictions
        if not service_account.is_user_agent_allowed(user_agent or ""):
            self._log_event(
                service_account.id,
                "authentication_failed",
                "Authentication failed: User agent not allowed",
                {"reason": "user_agent_restricted", "user_agent": user_agent},
                actor_type="service_account",
                actor_id=client_id,
                success=False
            )
            raise PermissionDeniedError("User agent not allowed")
        
        # Validate requested scopes
        token_scopes = requested_scopes or service_account.scopes or []
        if requested_scopes:
            for scope in requested_scopes:
                if not service_account.has_scope(scope):
                    raise PermissionDeniedError(f"Scope '{scope}' not allowed")
        
        # Create access token
        token_lifetime = timedelta(minutes=service_account.max_token_lifetime_minutes)
        expires_at = datetime.utcnow() + token_lifetime
        
        # Generate token
        token_value = secrets.token_urlsafe(32)
        token_hash = ServiceAccount._hash_secret(token_value)
        
        # Create token record
        token = ServiceAccountToken(
            service_account_id=service_account.id,
            token_hash=token_hash,
            scopes=token_scopes,
            expires_at=expires_at,
            issued_ip=ip_address,
            issued_user_agent=user_agent
        )
        
        self.db.add(token)
        
        # Record successful authentication
        service_account.record_authentication_attempt(True, ip_address, user_agent)
        self.db.commit()
        self.db.refresh(token)
        
        # Log successful authentication
        self._log_event(
            service_account.id,
            "authenticated",
            "Service account authenticated successfully",
            {
                "token_id": str(token.id),
                "scopes": token_scopes,
                "ip_address": ip_address,
                "expires_at": expires_at.isoformat()
            },
            actor_type="service_account",
            actor_id=client_id,
            success=True
        )
        
        # Store the actual token value for return (not persisted)
        token.token_value = token_value
        
        return token
    
    def verify_token(self, token_value: str) -> Tuple[ServiceAccount, ServiceAccountToken]:
        """
        Verify an access token and return the associated service account.
        
        Args:
            token_value: The token to verify
            
        Returns:
            Tuple of (ServiceAccount, ServiceAccountToken)
            
        Raises:
            TokenExpiredError: If token is expired
            TokenRevokedError: If token is revoked
            InvalidCredentialsError: If token is invalid
        """
        # Hash the token to find it
        token_hash = ServiceAccount._hash_secret(token_value)
        
        # Find token
        token = self.db.query(ServiceAccountToken).filter(
            ServiceAccountToken.token_hash == token_hash
        ).first()
        
        if not token:
            raise InvalidCredentialsError("Invalid token")
        
        if token.is_expired:
            raise TokenExpiredError("Token has expired")
        
        if token.is_revoked:
            raise TokenRevokedError("Token has been revoked")
        
        # Record token usage
        if not token.record_usage():
            raise TokenExpiredError("Token usage limit exceeded")
        
        # Get service account
        service_account = self.db.query(ServiceAccount).filter(
            ServiceAccount.id == token.service_account_id
        ).first()
        
        if not service_account or not service_account.is_active:
            raise ServiceAccountInactiveError("Service account is inactive")
        
        self.db.commit()
        
        return service_account, token
    
    def rotate_service_account_secret(
        self,
        service_account_id: UUID,
        rotated_by_user_id: Optional[UUID] = None
    ) -> str:
        """
        Rotate the secret for a service account.
        
        Args:
            service_account_id: ID of the service account
            rotated_by_user_id: ID of user performing rotation
            
        Returns:
            New client secret
            
        Raises:
            ServiceAccountNotFoundError: If service account not found
        """
        service_account = self.db.query(ServiceAccount).filter(
            ServiceAccount.id == service_account_id
        ).first()
        
        if not service_account:
            raise ServiceAccountNotFoundError("Service account not found")
        
        # Rotate secret
        new_secret = service_account.rotate_secret()
        service_account.last_modified_by_user_id = rotated_by_user_id
        service_account.updated_at = datetime.utcnow()
        
        # Revoke all existing tokens
        self.db.query(ServiceAccountToken).filter(
            ServiceAccountToken.service_account_id == service_account_id,
            ServiceAccountToken.revoked_at.is_(None)
        ).update({"revoked_at": datetime.utcnow()})
        
        self.db.commit()
        
        # Log rotation
        self._log_event(
            service_account.id,
            "secret_rotated",
            "Service account secret rotated",
            {"rotated_by": str(rotated_by_user_id) if rotated_by_user_id else "system"},
            actor_type="user" if rotated_by_user_id else "system",
            actor_id=str(rotated_by_user_id) if rotated_by_user_id else "system",
            success=True
        )
        
        return new_secret
    
    def revoke_token(self, token_id: UUID) -> None:
        """
        Revoke a specific access token.
        
        Args:
            token_id: ID of the token to revoke
        """
        token = self.db.query(ServiceAccountToken).filter(
            ServiceAccountToken.id == token_id
        ).first()
        
        if token:
            token.revoke()
            self.db.commit()
            
            self._log_event(
                token.service_account_id,
                "token_revoked",
                f"Access token revoked: {token_id}",
                {"token_id": str(token_id)},
                actor_type="system",
                actor_id="system",
                success=True
            )
    
    def cleanup_expired_tokens(self) -> int:
        """
        Clean up expired tokens.
        
        Returns:
            Number of tokens cleaned up
        """
        expired_tokens = self.db.query(ServiceAccountToken).filter(
            ServiceAccountToken.expires_at < datetime.utcnow(),
            ServiceAccountToken.revoked_at.is_(None)
        ).all()
        
        count = len(expired_tokens)
        
        for token in expired_tokens:
            token.revoke()
        
        self.db.commit()
        
        return count
    
    def get_service_accounts(
        self,
        service_type: Optional[str] = None,
        environment: Optional[str] = None,
        namespace: Optional[str] = None,
        is_active: Optional[bool] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[ServiceAccount]:
        """
        Get service accounts with optional filtering.
        
        Args:
            service_type: Filter by service type
            environment: Filter by environment
            namespace: Filter by namespace
            is_active: Filter by active status
            limit: Maximum number of results
            offset: Offset for pagination
            
        Returns:
            List of ServiceAccount objects
        """
        query = self.db.query(ServiceAccount)
        
        if service_type:
            query = query.filter(ServiceAccount.service_type == service_type)
        
        if environment:
            query = query.filter(ServiceAccount.environment == environment)
        
        if namespace:
            query = query.filter(ServiceAccount.namespace == namespace)
        
        if is_active is not None:
            query = query.filter(ServiceAccount.is_active == is_active)
        
        return query.offset(offset).limit(limit).all()
    
    def _log_event(
        self,
        service_account_id: Optional[UUID],
        event_type: str,
        description: str,
        event_data: Optional[Dict[str, Any]] = None,
        actor_type: str = "system",
        actor_id: Optional[str] = None,
        success: bool = True,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        error_message: Optional[str] = None
    ) -> None:
        """Log an audit event for service account activity."""
        audit_log = ServiceAccountAuditLog(
            service_account_id=service_account_id,
            event_type=event_type,
            event_description=description,
            event_data=event_data or {},
            actor_type=actor_type,
            actor_id=actor_id,
            success=success,
            ip_address=ip_address,
            user_agent=user_agent,
            error_message=error_message
        )
        
        self.db.add(audit_log)
        # Note: Commit is handled by the calling method
