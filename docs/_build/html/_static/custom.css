/* Custom CSS for Blast-Radius Security Tool Documentation */

/* Brand colors */
:root {
    --blast-primary: #2980B9;
    --blast-secondary: #E74C3C;
    --blast-accent: #F39C12;
    --blast-success: #27AE60;
    --blast-warning: #F39C12;
    --blast-danger: #E74C3C;
    --blast-dark: #2C3E50;
    --blast-light: #ECF0F1;
    --blast-purple: #9B59B6;
    --blast-teal: #1ABC9C;
    --blast-orange: #E67E22;
}

/* Enhanced typography */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
}

h1 {
    color: var(--blast-primary);
    border-bottom: 3px solid var(--blast-accent);
    padding-bottom: 0.3em;
}

h2 {
    color: var(--blast-dark);
    border-bottom: 2px solid var(--blast-light);
    padding-bottom: 0.2em;
}

/* Enhanced content sections */
.section {
    margin-bottom: 2em;
}

/* Feature highlight boxes */
.feature-box {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-left: 4px solid var(--blast-primary);
    padding: 1.5em;
    margin: 1em 0;
    border-radius: 0 8px 8px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.feature-box h3 {
    color: var(--blast-primary);
    margin-top: 0;
}

/* Statistics and metrics styling */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1em;
    margin: 1.5em 0;
}

.stat-card {
    background: white;
    border: 1px solid #e1e4e5;
    border-radius: 8px;
    padding: 1.5em;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 2em;
    font-weight: bold;
    color: var(--blast-primary);
    display: block;
}

.stat-label {
    color: var(--blast-dark);
    font-weight: 500;
    margin-top: 0.5em;
}

/* Custom roles styling */
.user-role {
    background-color: var(--blast-primary);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: bold;
    font-size: 0.9em;
}

.api-endpoint {
    background-color: var(--blast-success);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 0.9em;
}

.permission {
    background-color: var(--blast-warning);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: bold;
    font-size: 0.9em;
}

/* Enhanced admonitions */
.admonition {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 1.5em 0;
}

.admonition.note {
    border-left: 4px solid var(--blast-primary);
    background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
}

.admonition.note .admonition-title {
    background-color: var(--blast-primary);
    color: white;
    border-radius: 4px 4px 0 0;
    padding: 0.8em 1em;
    margin: -1em -1em 1em -1em;
    font-weight: 600;
}

.admonition.warning {
    border-left: 4px solid var(--blast-warning);
    background: linear-gradient(135deg, #fff8e1 0%, #f8f9fa 100%);
}

.admonition.warning .admonition-title {
    background-color: var(--blast-warning);
    color: white;
    border-radius: 4px 4px 0 0;
    padding: 0.8em 1em;
    margin: -1em -1em 1em -1em;
    font-weight: 600;
}

.admonition.security {
    border-left: 4px solid var(--blast-danger);
    background: linear-gradient(135deg, #ffebee 0%, #f8f9fa 100%);
}

.admonition.security .admonition-title {
    background-color: var(--blast-danger);
    color: white;
    border-radius: 4px 4px 0 0;
    padding: 0.8em 1em;
    margin: -1em -1em 1em -1em;
    font-weight: 600;
}

.admonition.performance {
    border-left: 4px solid var(--blast-accent);
    background: linear-gradient(135deg, #fef9e7 0%, #f8f9fa 100%);
}

.admonition.performance .admonition-title {
    background-color: var(--blast-accent);
    color: white;
    border-radius: 4px 4px 0 0;
    padding: 0.8em 1em;
    margin: -1em -1em 1em -1em;
    font-weight: 600;
}

/* Role-specific styling */
.role-soc {
    border-left: 4px solid var(--blast-primary);
    background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
}

.role-architect {
    border-left: 4px solid var(--blast-success);
    background: linear-gradient(135deg, #e8f5e8 0%, #f8f9fa 100%);
}

.role-redteam {
    border-left: 4px solid var(--blast-danger);
    background: linear-gradient(135deg, #ffebee 0%, #f8f9fa 100%);
}

.role-purpleteam {
    border-left: 4px solid var(--blast-purple);
    background: linear-gradient(135deg, #f3e5f5 0%, #f8f9fa 100%);
}

.role-compliance {
    border-left: 4px solid var(--blast-teal);
    background: linear-gradient(135deg, #e0f2f1 0%, #f8f9fa 100%);
}

.role-executive {
    border-left: 4px solid var(--blast-orange);
    background: linear-gradient(135deg, #fff3e0 0%, #f8f9fa 100%);
}

/* Enhanced lists */
ul li {
    margin-bottom: 0.5em;
}

ul li::marker {
    color: var(--blast-primary);
}

/* Emoji enhancement */
.emoji {
    font-size: 1.2em;
    margin-right: 0.3em;
}

/* Progress indicators */
.progress-bar {
    background-color: #e9ecef;
    border-radius: 10px;
    height: 20px;
    overflow: hidden;
    margin: 0.5em 0;
}

.progress-fill {
    background: linear-gradient(90deg, var(--blast-primary) 0%, var(--blast-accent) 100%);
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

/* Highlight boxes */
.highlight-box {
    background: linear-gradient(135deg, var(--blast-primary) 0%, var(--blast-secondary) 100%);
    color: white;
    padding: 2em;
    border-radius: 12px;
    margin: 2em 0;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.highlight-box h3 {
    color: white;
    margin-top: 0;
    border-bottom: 2px solid rgba(255,255,255,0.3);
    padding-bottom: 0.5em;
}

/* Documentation metrics */
.doc-metrics {
    display: flex;
    flex-wrap: wrap;
    gap: 1em;
    margin: 1.5em 0;
}

.metric-item {
    background: white;
    border: 2px solid var(--blast-primary);
    border-radius: 8px;
    padding: 1em;
    text-align: center;
    min-width: 150px;
    flex: 1;
}

.metric-value {
    font-size: 1.8em;
    font-weight: bold;
    color: var(--blast-primary);
    display: block;
}

.metric-description {
    color: var(--blast-dark);
    font-size: 0.9em;
    margin-top: 0.3em;
}

/* Code blocks enhancement */
.highlight {
    border-radius: 5px;
    border: 1px solid #e1e4e5;
}

.highlight pre {
    padding: 12px;
    line-height: 1.4;
}

/* Table styling */
table.docutils {
    border-collapse: collapse;
    border: 1px solid #e1e4e5;
    margin: 1em 0;
}

table.docutils th {
    background-color: var(--blast-primary);
    color: white;
    padding: 8px 12px;
    text-align: left;
}

table.docutils td {
    padding: 8px 12px;
    border-bottom: 1px solid #e1e4e5;
}

table.docutils tr:nth-child(even) {
    background-color: #f8f9fa;
}

/* Navigation enhancements */
.wy-nav-side {
    background: linear-gradient(180deg, var(--blast-dark) 0%, #34495e 100%);
}

.wy-menu-vertical a {
    color: #bdc3c7;
}

.wy-menu-vertical a:hover {
    background-color: var(--blast-primary);
    color: white;
}

.wy-menu-vertical li.current > a {
    background-color: var(--blast-primary);
    border-right: 3px solid var(--blast-accent);
}

/* Header styling */
.wy-nav-top {
    background-color: var(--blast-primary);
}

/* Search box styling */
.wy-side-nav-search {
    background-color: var(--blast-dark);
}

.wy-side-nav-search input[type=text] {
    border: 1px solid var(--blast-primary);
    border-radius: 4px;
}

/* Content area enhancements */
.wy-nav-content {
    max-width: 1200px;
}

/* Custom badges */
.badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.badge-primary {
    color: #fff;
    background-color: var(--blast-primary);
}

.badge-secondary {
    color: #fff;
    background-color: var(--blast-secondary);
}

.badge-success {
    color: #fff;
    background-color: var(--blast-success);
}

.badge-warning {
    color: #212529;
    background-color: var(--blast-warning);
}

.badge-danger {
    color: #fff;
    background-color: var(--blast-danger);
}

/* Responsive design */
@media (max-width: 768px) {
    .wy-nav-content {
        margin-left: 0;
    }
    
    .wy-nav-side {
        left: -300px;
    }
}

/* Print styles */
@media print {
    .wy-nav-side,
    .wy-nav-top,
    .rst-footer-buttons {
        display: none !important;
    }
    
    .wy-nav-content {
        margin-left: 0 !important;
    }
}
