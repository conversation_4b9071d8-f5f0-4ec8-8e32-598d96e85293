<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Code Standards &amp; Style Guide &mdash; Blast-Radius Security Tool v1.0.0 - Enterprise Security Platform Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ac1674a3" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ac1674a3" />

  
    <link rel="canonical" href="https://blast-radius.readthedocs.io/development/code-standards.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=528dc5d9"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Blast-Radius Security Tool v1.0.0 - Enterprise Security Platform Documentation"
          href="../_static/opensearch.xml"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🎉 Latest Release</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../latest-implementations-summary.html">Latest Implementations Summary - Production Ready Release</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#implementation-achievements">Implementation Achievements</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#infrastructure-as-code-iac-100-complete">Infrastructure as Code (IaC) - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#zero-trust-architecture-100-complete">Zero-Trust Architecture - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#gdpr-compliance-framework-100-complete">GDPR Compliance Framework - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#enhanced-audit-logging-100-complete">Enhanced Audit Logging - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#least-privilege-access-control-100-complete">Least Privilege Access Control - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#production-deployment-infrastructure-100-complete">Production Deployment Infrastructure - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#documentation-operations-100-complete">Documentation &amp; Operations - 100% Complete ✅</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#technical-specifications">Technical Specifications</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#performance-characteristics">Performance Characteristics</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#security-specifications">Security Specifications</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#compliance-coverage">Compliance Coverage</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#deployment-architecture">Deployment Architecture</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#next-steps-and-recommendations">Next Steps and Recommendations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#immediate-actions">Immediate Actions</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#long-term-roadmap">Long-term Roadmap</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🚀 Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#prerequisites">Prerequisites</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#system-requirements">System Requirements</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#software-dependencies">Software Dependencies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#installation-methods">Installation Methods</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#method-1-quick-start-with-docker-recommended">Method 1: Quick Start with Docker (Recommended)</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#method-2-development-installation">Method 2: Development Installation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#method-3-production-installation">Method 3: Production Installation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#configuration">Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#environment-variables">Environment Variables</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#initial-setup">Initial Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#creating-admin-user">Creating Admin User</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#configuring-integrations">Configuring Integrations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#verification">Verification</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#health-checks">Health Checks</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#performance-testing">Performance Testing</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#core-configuration">Core Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#application-settings">Application Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#database-configuration">Database Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#security-configuration">Security Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#authentication-settings">Authentication Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#encryption-settings">Encryption Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#access-control-settings">Access Control Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#integration-configuration">Integration Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#cloud-provider-settings">Cloud Provider Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#servicenow-integration">ServiceNow Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#threat-intelligence-settings">Threat Intelligence Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#monitoring-and-logging">Monitoring and Logging</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#logging-configuration">Logging Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#monitoring-settings">Monitoring Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#environment-specific-configuration">Environment-Specific Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#development-environment">Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#staging-environment">Staging Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#configuration-validation">Configuration Validation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#security-best-practices">Security Best Practices</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#performance-best-practices">Performance Best Practices</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#troubleshooting">Troubleshooting</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../quick-start-guide.html">Quick Start Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#quick-installation">Quick Installation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-1-get-the-code">Step 1: Get the Code</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-2-start-the-platform">Step 2: Start the Platform</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-3-access-the-platform">Step 3: Access the Platform</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#first-login-and-setup">First Login and Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#initial-login">Initial Login</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#password-change">Password Change</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#dashboard-overview">Dashboard Overview</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#basic-configuration">Basic Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#user-profile-setup">User Profile Setup</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#multi-factor-authentication-recommended">Multi-Factor Authentication (Recommended)</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#your-first-attack-path-analysis">Your First Attack Path Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-1-add-sample-assets">Step 1: Add Sample Assets</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-2-run-attack-path-analysis">Step 2: Run Attack Path Analysis</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-3-view-results">Step 3: View Results</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#understanding-the-visualization">Understanding the Visualization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#essential-features-tour">Essential Features Tour</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#real-time-monitoring">Real-Time Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#asset-management">Asset Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#user-and-role-management">User and Role Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#threat-intelligence">Threat Intelligence</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#integration-setup">Integration Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#cloud-provider-integration">Cloud Provider Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#servicenow-integration">ServiceNow Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#next-steps">Next Steps</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#immediate-actions">Immediate Actions</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#learning-resources">Learning Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#advanced-configuration">Advanced Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#common-quick-start-issues">Common Quick Start Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#platform-won-t-start">Platform Won’t Start</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#can-t-access-web-interface">Can’t Access Web Interface</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#login-issues">Login Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#performance-issues">Performance Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#getting-help">Getting Help</a></li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#congratulations">Congratulations!</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📊 Documentation Excellence</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../documentation-overview.html">Documentation Overview and Achievements</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#documentation-excellence-achievements">🏆 Documentation Excellence Achievements</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#comprehensive-user-guide-ecosystem">📚 Comprehensive User Guide Ecosystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#key-documentation-features">🎯 Key Documentation Features</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#measurable-impact-and-value">📈 Measurable Impact and Value</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#industry-leadership-and-recognition">🌟 Industry Leadership and Recognition</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#future-enhancements-and-roadmap">🚀 Future Enhancements and Roadmap</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📚 Enterprise User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guides/index.html">User Guides</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#role-based-guides">Role-Based Guides</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#soc-operators">SOC Operators</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/soc-operators.html">SOC Operators Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#role-definition-and-responsibilities">Role Definition and Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#core-platform-capabilities-for-soc-operations">Core Platform Capabilities for SOC Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#getting-started-soc-operator-onboarding">Getting Started: SOC Operator Onboarding</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#advanced-real-time-monitoring-and-threat-detection">Advanced Real-time Monitoring and Threat Detection</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#comprehensive-incident-response-framework">Comprehensive Incident Response Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#advanced-reporting-and-documentation-framework">Advanced Reporting and Documentation Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#soc-operations-best-practices-and-excellence-framework">SOC Operations Best Practices and Excellence Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#comprehensive-scenario-based-training-and-response-procedures">Comprehensive Scenario-Based Training and Response Procedures</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#comprehensive-support-resources-and-professional-development">Comprehensive Support Resources and Professional Development</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#conclusion-excellence-in-soc-operations">Conclusion: Excellence in SOC Operations</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#security-architects">Security Architects</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/security-architects.html">Security Architects Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#executive-summary-for-security-architects">Executive Summary for Security Architects</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-platform-capabilities-for-security-architecture">Advanced Platform Capabilities for Security Architecture</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#enterprise-security-architecture-methodology">Enterprise Security Architecture Methodology</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-risk-assessment-and-quantitative-analysis-framework">Advanced Risk Assessment and Quantitative Analysis Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-security-architecture-design-and-implementation">Advanced Security Architecture Design and Implementation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#comprehensive-compliance-and-governance-framework">Comprehensive Compliance and Governance Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-analytics-and-executive-communication-framework">Advanced Analytics and Executive Communication Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#enterprise-security-architecture-best-practices-and-excellence">Enterprise Security Architecture Best Practices and Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#professional-development-and-support-resources">Professional Development and Support Resources</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#conclusion-excellence-in-security-architecture-leadership">Conclusion: Excellence in Security Architecture Leadership</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#red-team-members">Red Team Members</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/red-team-members.html">Red Team Members Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#executive-summary-for-red-team-operations">Executive Summary for Red Team Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#role-definition-and-offensive-security-responsibilities">Role Definition and Offensive Security Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-red-team-platform-capabilities">Advanced Red Team Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#comprehensive-red-team-methodology-and-campaign-framework">Comprehensive Red Team Methodology and Campaign Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-attack-path-discovery-and-exploitation-framework">Advanced Attack Path Discovery and Exploitation Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-attack-simulation-and-specialized-testing-scenarios">Advanced Attack Simulation and Specialized Testing Scenarios</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-reporting-and-performance-metrics-framework">Advanced Reporting and Performance Metrics Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#red-team-best-practices-and-operational-excellence">Red Team Best Practices and Operational Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-troubleshooting-and-technical-support">Advanced Troubleshooting and Technical Support</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#conclusion-excellence-in-red-team-operations">Conclusion: Excellence in Red Team Operations</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#purple-team-members">Purple Team Members</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/purple-team-members.html">Purple Team Members Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#executive-summary-for-purple-team-excellence">Executive Summary for Purple Team Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#advanced-purple-team-platform-capabilities">Advanced Purple Team Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#comprehensive-purple-team-methodology-and-framework">Comprehensive Purple Team Methodology and Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#advanced-detection-engineering-and-security-control-optimization">Advanced Detection Engineering and Security Control Optimization</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#comprehensive-purple-team-scenarios-and-implementation-framework">Comprehensive Purple Team Scenarios and Implementation Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#advanced-troubleshooting-and-technical-support-framework">Advanced Troubleshooting and Technical Support Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#conclusion-excellence-in-purple-team-operations-and-collaborative-security">Conclusion: Excellence in Purple Team Operations and Collaborative Security</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#compliance-officers">Compliance Officers</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/compliance-officers.html">Compliance Officers Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#executive-summary-for-compliance-excellence">Executive Summary for Compliance Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-compliance-platform-capabilities">Advanced Compliance Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-regulatory-framework-management">Advanced Regulatory Framework Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#automated-compliance-monitoring-and-management">Automated Compliance Monitoring and Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#comprehensive-audit-management-and-preparation">Comprehensive Audit Management and Preparation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-risk-assessment-and-management-framework">Advanced Risk Assessment and Management Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#cross-functional-collaboration-and-integration">Cross-Functional Collaboration and Integration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#professional-development-and-excellence-framework">Professional Development and Excellence Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-support-and-troubleshooting-framework">Advanced Support and Troubleshooting Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#conclusion-excellence-in-compliance-and-governance">Conclusion: Excellence in Compliance and Governance</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#executive-leadership">Executive Leadership</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/executive-leadership.html">Executive Leadership Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#executive-summary-for-strategic-security-leadership">Executive Summary for Strategic Security Leadership</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#advanced-executive-security-platform-capabilities">Advanced Executive Security Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#strategic-security-risk-management-framework">Strategic Security Risk Management Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#business-aligned-security-performance-management">Business-Aligned Security Performance Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#strategic-security-investment-and-resource-management">Strategic Security Investment and Resource Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#crisis-management-and-business-continuity">Crisis Management and Business Continuity</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#regulatory-compliance-and-governance-excellence">Regulatory Compliance and Governance Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#strategic-security-communication-and-stakeholder-management">Strategic Security Communication and Stakeholder Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#digital-transformation-and-innovation-enablement">Digital Transformation and Innovation Enablement</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#professional-development-and-executive-excellence">Professional Development and Executive Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#advanced-support-and-advisory-services">Advanced Support and Advisory Services</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#conclusion-excellence-in-executive-security-leadership">Conclusion: Excellence in Executive Security Leadership</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#administrators">Administrators</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/administrators.html">Administrators Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#dashboard-overview">Dashboard Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#getting-started">Getting Started</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#user-and-access-management">User and Access Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#system-configuration-and-management">System Configuration and Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#monitoring-and-maintenance">Monitoring and Maintenance</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#security-and-compliance">Security and Compliance</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#platform-updates-and-maintenance">Platform Updates and Maintenance</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#troubleshooting-and-support">Troubleshooting and Support</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#best-practices-for-platform-administration">Best Practices for Platform Administration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#feature-specific-guides">Feature-Specific Guides</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#attack-path-analysis">Attack Path Analysis</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/attack-path-analysis.html">Attack Path Analysis User Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#getting-started">Getting Started</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#core-concepts">Core Concepts</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#using-the-web-interface">Using the Web Interface</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#using-the-api">Using the API</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#use-cases-and-workflows">Use Cases and Workflows</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#best-practices">Best Practices</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#threat-modeling">Threat Modeling</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#asset-discovery-and-management">Asset Discovery and Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#threat-intelligence-integration">Threat Intelligence Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#common-workflows">Common Workflows</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#security-assessment-workflow">Security Assessment Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#incident-response-workflow">Incident Response Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#red-team-exercise-workflow">Red Team Exercise Workflow</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#general-guidelines">General Guidelines</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#role-specific-tips">Role-Specific Tips</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#getting-help">Getting Help</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#support-resources">Support Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#training-and-certification">Training and Certification</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#feedback-and-contributions">Feedback and Contributions</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#base-url">Base URL</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#api-endpoints">API Endpoints</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#authentication-user-management">Authentication &amp; User Management</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/authentication.html">Authentication API</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#authentication-flow">Authentication Flow</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#security-and-authorization">Security and Authorization</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#integration-examples">Integration Examples</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#best-practices">Best Practices</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#asset-management">Asset Management</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/asset-management.html">Asset Management API</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-crud-operations">Asset CRUD Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-discovery">Asset Discovery</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#bulk-operations">Bulk Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-relationships">Asset Relationships</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-tags-and-metadata">Asset Tags and Metadata</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#error-handling">Error Handling</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#sdk-examples">SDK Examples</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#attack-path-analysis">Attack Path Analysis</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/attack-path-analysis.html">Attack Path Analysis API</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#endpoints">Endpoints</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#error-responses">Error Responses</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#performance-considerations">Performance Considerations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#best-practices">Best Practices</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#examples">Examples</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#threat-modeling-risk-assessment">Threat Modeling &amp; Risk Assessment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/threat-modeling.html">Threat Modeling API Reference</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#threat-actor-simulation">Threat Actor Simulation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#risk-assessment">Risk Assessment</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#compliance-impact-analysis">Compliance Impact Analysis</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#mitigation-strategies">Mitigation Strategies</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#continuous-monitoring">Continuous Monitoring</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#error-handling">Error Handling</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#rate-limiting">Rate Limiting</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/mitre-attack-integration.html">MITRE ATT&amp;CK Integration API Reference</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#att-ck-data-management">ATT&amp;CK Data Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#technique-correlation">Technique Correlation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#threat-actor-attribution">Threat Actor Attribution</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#attack-pattern-analysis">Attack Pattern Analysis</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#att-ck-navigator-integration">ATT&amp;CK Navigator Integration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#threat-intelligence-enrichment">Threat Intelligence Enrichment</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#error-handling">Error Handling</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#rate-limiting">Rate Limiting</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#threat-intelligence">Threat Intelligence</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#monitoring-dashboards">Monitoring &amp; Dashboards</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#integrations">Integrations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#common-response-formats">Common Response Formats</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#success-response">Success Response</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#error-response">Error Response</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#http-status-codes">HTTP Status Codes</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#pagination">Pagination</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#filtering-and-sorting">Filtering and Sorting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#field-selection">Field Selection</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#webhooks">Webhooks</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#sdk-and-libraries">SDK and Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#testing-and-development">Testing and Development</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#support-and-resources">Support and Resources</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#examples">Examples</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Development</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="contributing.html">Contributing Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="contributing.html#getting-started">Getting Started</a><ul>
<li class="toctree-l3"><a class="reference internal" href="contributing.html#ways-to-contribute">Ways to Contribute</a></li>
<li class="toctree-l3"><a class="reference internal" href="contributing.html#before-you-start">Before You Start</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="contributing.html#contribution-workflow">Contribution Workflow</a><ul>
<li class="toctree-l3"><a class="reference internal" href="contributing.html#step-1-find-or-create-an-issue">Step 1: Find or Create an Issue</a></li>
<li class="toctree-l3"><a class="reference internal" href="contributing.html#step-2-fork-and-clone">Step 2: Fork and Clone</a></li>
<li class="toctree-l3"><a class="reference internal" href="contributing.html#step-3-create-a-branch">Step 3: Create a Branch</a></li>
<li class="toctree-l3"><a class="reference internal" href="contributing.html#step-4-make-changes">Step 4: Make Changes</a></li>
<li class="toctree-l3"><a class="reference internal" href="contributing.html#step-5-test-your-changes">Step 5: Test Your Changes</a></li>
<li class="toctree-l3"><a class="reference internal" href="contributing.html#step-6-submit-pull-request">Step 6: Submit Pull Request</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="contributing.html#code-standards">Code Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="contributing.html#python-code-style">Python Code Style</a></li>
<li class="toctree-l3"><a class="reference internal" href="contributing.html#javascript-typescript-style">JavaScript/TypeScript Style</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="contributing.html#testing-guidelines">Testing Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="contributing.html#test-structure">Test Structure</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="contributing.html#documentation-standards">Documentation Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="contributing.html#documentation-types">Documentation Types</a></li>
<li class="toctree-l3"><a class="reference internal" href="contributing.html#writing-guidelines">Writing Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="contributing.html#review-process">Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="contributing.html#code-review-checklist">Code Review Checklist</a></li>
<li class="toctree-l3"><a class="reference internal" href="contributing.html#review-guidelines">Review Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="contributing.html#community-guidelines">Community Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="contributing.html#code-of-conduct">Code of Conduct</a></li>
<li class="toctree-l3"><a class="reference internal" href="contributing.html#communication">Communication</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="contributing.html#recognition">Recognition</a></li>
<li class="toctree-l2"><a class="reference internal" href="contributing.html#getting-help">Getting Help</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Use Cases</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../use-cases/asset-discovery.html">Asset Discovery Use Cases</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#discovery-methods">Discovery Methods</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#cloud-provider-discovery">Cloud Provider Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#network-based-discovery">Network-Based Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#api-discovery">API Discovery</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#use-case-scenarios">Use Case Scenarios</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#scenario-1-multi-cloud-asset-inventory">Scenario 1: Multi-Cloud Asset Inventory</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#scenario-2-continuous-asset-monitoring">Scenario 2: Continuous Asset Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#scenario-3-compliance-asset-reporting">Scenario 3: Compliance Asset Reporting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#advanced-discovery-features">Advanced Discovery Features</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#agent-based-discovery">Agent-Based Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#container-discovery">Container Discovery</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#discovery-automation">Discovery Automation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#scheduled-discovery">Scheduled Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#integration-with-external-tools">Integration with External Tools</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#discovery-strategy">Discovery Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#performance-optimization">Performance Optimization</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#security-considerations">Security Considerations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#common-issues">Common Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../use-cases/attack-path-analysis.html">Attack Path Analysis Use Cases</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#key-capabilities">Key Capabilities</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#analysis-types">Analysis Types</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#use-case-scenarios">Use Case Scenarios</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-1-red-team-attack-simulation">Scenario 1: Red Team Attack Simulation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-2-purple-team-validation-exercise">Scenario 2: Purple Team Validation Exercise</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-3-compliance-risk-assessment">Scenario 3: Compliance Risk Assessment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-4-incident-response-planning">Scenario 4: Incident Response Planning</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-5-zero-trust-architecture-planning">Scenario 5: Zero Trust Architecture Planning</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#advanced-analysis-features">Advanced Analysis Features</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#risk-scoring-and-prioritization">Risk Scoring and Prioritization</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#integration-and-automation">Integration and Automation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#siem-integration">SIEM Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#soar-integration">SOAR Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#analysis-guidelines">Analysis Guidelines</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Architecture</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../architecture/zero-trust-architecture.html">Zero-Trust Architecture Implementation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#identity-verification-service">Identity Verification Service</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#service-account-management">Service Account Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#tls-1-3-enforcement">TLS 1.3+ Enforcement</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#real-time-security-event-correlation">Real-Time Security Event Correlation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#network-security">Network Security</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#kubernetes-network-policies">Kubernetes Network Policies</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#rbac-implementation">RBAC Implementation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#data-protection">Data Protection</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#encryption-implementation">Encryption Implementation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#data-classification">Data Classification</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#monitoring-and-alerting">Monitoring and Alerting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#security-monitoring">Security Monitoring</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#implementation-guidelines">Implementation Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#deployment-checklist">Deployment Checklist</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#security-validation">Security Validation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#security-principles">Security Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#implementation-tips">Implementation Tips</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../deployment/production-architecture.html">Production Architecture Overview</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#kubernetes-infrastructure">Kubernetes Infrastructure</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#application-architecture">Application Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#data-architecture">Data Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#network-architecture">Network Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#security-architecture">Security Architecture</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#zero-trust-security-implementation">Zero-Trust Security Implementation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#monitoring-architecture">Monitoring Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#deployment-strategy">Deployment Strategy</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#blue-green-deployment">Blue-Green Deployment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#infrastructure-as-code">Infrastructure as Code</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#performance-specifications">Performance Specifications</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#scalability-metrics">Scalability Metrics</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#resource-requirements">Resource Requirements</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#disaster-recovery">Disaster Recovery</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#backup-and-recovery-strategy">Backup and Recovery Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#high-availability-design">High Availability Design</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#operational-excellence">Operational Excellence</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#security-best-practices">Security Best Practices</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Security &amp; Compliance</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../security/enhanced-audit-logging.html">Enhanced Audit Logging System</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-event-structure">Audit Event Structure</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#tamper-proof-audit-chain">Tamper-Proof Audit Chain</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#event-enrichment-pipeline">Event Enrichment Pipeline</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#real-time-monitoring">Real-Time Monitoring</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#compliance-integration">Compliance Integration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#framework-mapping">Framework Mapping</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#compliance-reporting">Compliance Reporting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-service-architecture">Audit Service Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#database-schema">Database Schema</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#configuration">Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#monitoring-and-alerting">Monitoring and Alerting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-monitoring-dashboard">Audit Monitoring Dashboard</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#alert-configuration">Alert Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-strategy">Audit Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#operational-guidelines">Operational Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#performance-tuning">Performance Tuning</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../security/incident-response-procedures.html">Incident Response Procedures - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#incident-classification">Incident Classification</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#severity-levels">Severity Levels</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#critical-p0">Critical (P0)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#high-p1">High (P1)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#medium-p2">Medium (P2)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#low-p3">Low (P3)</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#incident-response-team">Incident Response Team</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#roles-and-responsibilities">Roles and Responsibilities</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#incident-commander-ic">Incident Commander (IC)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#technical-lead">Technical Lead</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#security-lead">Security Lead</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#communications-lead">Communications Lead</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#incident-response-process">Incident Response Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-1-detection-and-initial-response-0-15-minutes">Phase 1: Detection and Initial Response (0-15 minutes)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#incident-detection">1.1 Incident Detection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#initial-assessment">1.2 Initial Assessment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#team-notification">1.3 Team Notification</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-2-investigation-and-containment-15-minutes-2-hours">Phase 2: Investigation and Containment (15 minutes - 2 hours)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#incident-war-room-setup">2.1 Incident War Room Setup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#technical-investigation">2.2 Technical Investigation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#security-assessment-if-applicable">2.3 Security Assessment (if applicable)</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-3-resolution-and-recovery-2-8-hours">Phase 3: Resolution and Recovery (2-8 hours)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#fix-implementation">3.1 Fix Implementation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#system-recovery">3.2 System Recovery</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-4-communication-and-documentation-ongoing">Phase 4: Communication and Documentation (Ongoing)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#status-updates">4.1 Status Updates</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#resolution-communication">4.2 Resolution Communication</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-5-post-incident-review-24-72-hours-after-resolution">Phase 5: Post-Incident Review (24-72 hours after resolution)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#post-mortem-meeting">5.1 Post-Mortem Meeting</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#documentation">5.2 Documentation</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#security-specific-procedures">Security-Specific Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#data-breach-response">Data Breach Response</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#immediate-actions-0-1-hour">Immediate Actions (0-1 hour)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#notification-requirements">Notification Requirements</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#vulnerability-response">Vulnerability Response</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#critical-vulnerabilities-cvss-9-0">Critical Vulnerabilities (CVSS 9.0+)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#high-vulnerabilities-cvss-7-0-8-9">High Vulnerabilities (CVSS 7.0-8.9)</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#emergency-contacts">Emergency Contacts</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#internal-team">Internal Team</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#external-contacts">External Contacts</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#tools-and-resources">Tools and Resources</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#monitoring-and-alerting">Monitoring and Alerting</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#communication">Communication</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#id1">Documentation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#training-and-drills">Training and Drills</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#regular-training">Regular Training</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#documentation-updates">Documentation Updates</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../security/security-review-processes.html">Security Review Processes - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#security-review-framework">Security Review Framework</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-types">Review Types</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#code-security-review">1. Code Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#infrastructure-security-review">2. Infrastructure Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#architecture-security-review">3. Architecture Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#compliance-review">4. Compliance Review</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#code-security-review-process">Code Security Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#pre-review-checklist">Pre-Review Checklist</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#developer-responsibilities">Developer Responsibilities</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-criteria">Review Criteria</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#input-validation-and-sanitization">1. Input Validation and Sanitization</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#authentication-and-authorization">2. Authentication and Authorization</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#data-protection">3. Data Protection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#error-handling-and-logging">4. Error Handling and Logging</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#cryptography-and-secrets">5. Cryptography and Secrets</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-process-workflow">Review Process Workflow</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#step-1-automated-security-checks">Step 1: Automated Security Checks</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#step-2-manual-security-review">Step 2: Manual Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#step-3-security-testing">Step 3: Security Testing</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#id1">Infrastructure Security Review</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#infrastructure-as-code-iac-review">Infrastructure as Code (IaC) Review</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#terraform-security-checklist">Terraform Security Checklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#example-review-process">Example Review Process</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#kubernetes-security-review">Kubernetes Security Review</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#manifest-security-checklist">Manifest Security Checklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#example-security-policies">Example Security Policies</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#id2">Architecture Security Review</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#design-review-process">Design Review Process</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#threat-modeling">1. Threat Modeling</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#security-architecture-patterns">2. Security Architecture Patterns</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#integration-security">3. Integration Security</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-documentation-template">Review Documentation Template</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#compliance-review-process">Compliance Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#regulatory-requirements">Regulatory Requirements</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#gdpr-compliance-checklist">GDPR Compliance Checklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#soc-2-compliance-checklist">SOC 2 Compliance Checklist</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#compliance-review-workflow">Compliance Review Workflow</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#change-impact-assessment">1. Change Impact Assessment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#documentation-requirements">2. Documentation Requirements</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#security-review-tools-and-automation">Security Review Tools and Automation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#automated-security-scanning">Automated Security Scanning</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#security-metrics-and-kpis">Security Metrics and KPIs</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#training-and-awareness">Training and Awareness</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#security-review-training">Security Review Training</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#documentation-and-resources">Documentation and Resources</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html">GDPR Compliance Framework</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-subject-rights-management">Data Subject Rights Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#consent-management-system">Consent Management System</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-processing-records">Data Processing Records</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-breach-management">Data Breach Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#privacy-impact-assessment">Privacy Impact Assessment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#gdpr-service-architecture">GDPR Service Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-processing-workflows">Data Processing Workflows</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#compliance-monitoring">Compliance Monitoring</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#automated-compliance-checks">Automated Compliance Checks</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#compliance-reporting">Compliance Reporting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#privacy-by-design-implementation">Privacy by Design Implementation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#operational-guidelines">Operational Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#technical-implementation">Technical Implementation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#database-schema">Database Schema</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#configuration-example">Configuration Example</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../access-control/least-privilege-framework.html">Least Privilege Access Control Framework</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-request-management">Access Request Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#just-in-time-access-provisioning">Just-in-Time Access Provisioning</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#privilege-escalation-management">Privilege Escalation Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-review-and-certification">Access Review and Certification</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-control-architecture">Access Control Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#role-based-access-templates">Role-Based Access Templates</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#risk-assessment-engine">Risk Assessment Engine</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#monitoring-and-analytics">Monitoring and Analytics</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-usage-monitoring">Access Usage Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-analytics-dashboard">Access Analytics Dashboard</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#configuration-management">Configuration Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#policy-configuration">Policy Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#role-template-configuration">Role Template Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-management-principles">Access Management Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#operational-guidelines">Operational Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../security/index.html">Security Documentation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/security-summary.html">Security Documentation Summary</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#executive-overview">Executive Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#security-documentation-structure">Security Documentation Structure</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#recent-security-achievements">Recent Security Achievements</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#june-2025-security-review-results">June 2025 Security Review Results</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#key-security-fixes-implemented">Key Security Fixes Implemented</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#security-architecture-excellence">Security Architecture Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#multi-layered-security-approach">Multi-Layered Security Approach</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#security-testing-excellence">Security Testing Excellence</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#compliance-and-regulatory-excellence">Compliance and Regulatory Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#regulatory-compliance-status">Regulatory Compliance Status</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#industry-standards-compliance">Industry Standards Compliance</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#security-operations-excellence">Security Operations Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#vulnerability-management-program">Vulnerability Management Program</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#security-monitoring-and-incident-response">Security Monitoring and Incident Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#risk-management-excellence">Risk Management Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#enterprise-risk-assessment">Enterprise Risk Assessment</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#business-impact-and-value">Business Impact and Value</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#security-investment-roi">Security Investment ROI</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#future-security-roadmap">Future Security Roadmap</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#short-term-objectives-q3-q4-2025">Short-Term Objectives (Q3-Q4 2025)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#long-term-strategic-goals-2026">Long-Term Strategic Goals (2026)</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#overview">Overview</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/index.html#security-principles">Security Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/index.html#security-framework">Security Framework</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/index.html#security-architecture">Security Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#security-documentation-sections">Security Documentation Sections</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#security-certifications-and-compliance">Security Certifications and Compliance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#security-contact-information">Security Contact Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#quick-security-reference">Quick Security Reference</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Deployment &amp; Operations</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../deployment/production-deployment-guide.html">Production Deployment Guide - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#prerequisites">Prerequisites</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#infrastructure-requirements">Infrastructure Requirements</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#minimum-system-requirements">Minimum System Requirements</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#network-requirements">Network Requirements</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#external-dependencies">External Dependencies</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#required-tools-and-access">Required Tools and Access</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#development-tools">Development Tools</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#aws-access-requirements">AWS Access Requirements</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#infrastructure-deployment">Infrastructure Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-1-environment-configuration">Step 1: Environment Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-aws-cli">1.1 Configure AWS CLI</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#set-environment-variables">1.2 Set Environment Variables</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-2-infrastructure-provisioning">Step 2: Infrastructure Provisioning</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#deploy-core-infrastructure">2.1 Deploy Core Infrastructure</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-kubectl-access">2.2 Configure kubectl Access</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#install-cluster-add-ons">2.3 Install Cluster Add-ons</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-3-database-setup">Step 3: Database Setup</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-initialization">3.1 Database Initialization</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-migration">3.2 Database Migration</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#application-deployment">Application Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-4-container-image-preparation">Step 4: Container Image Preparation</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#build-and-push-images">4.1 Build and Push Images</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-5-kubernetes-deployment">Step 5: Kubernetes Deployment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#create-namespace-and-secrets">5.1 Create Namespace and Secrets</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#deploy-application-components">5.2 Deploy Application Components</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-ingress-and-load-balancer">5.3 Configure Ingress and Load Balancer</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-6-ssl-tls-configuration">Step 6: SSL/TLS Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#request-ssl-certificate">6.1 Request SSL Certificate</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-https-redirect">6.2 Configure HTTPS Redirect</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#post-deployment-configuration">Post-Deployment Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-7-application-configuration">Step 7: Application Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#initialize-application-data">7.1 Initialize Application Data</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-monitoring-and-alerting">7.2 Configure Monitoring and Alerting</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-8-security-hardening">Step 8: Security Hardening</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#network-policies">8.1 Network Policies</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#pod-security-policies">8.2 Pod Security Policies</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#verification-and-testing">Verification and Testing</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-9-deployment-verification">Step 9: Deployment Verification</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#health-checks">9.1 Health Checks</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#functional-testing">9.2 Functional Testing</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#security-testing">9.3 Security Testing</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#backup-and-recovery-setup">Backup and Recovery Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-10-backup-configuration">Step 10: Backup Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-backup">10.1 Database Backup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#application-data-backup">10.2 Application Data Backup</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#monitoring-and-maintenance">Monitoring and Maintenance</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-11-ongoing-monitoring">Step 11: Ongoing Monitoring</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#set-up-alerts">11.1 Set Up Alerts</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#performance-monitoring">11.2 Performance Monitoring</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#troubleshooting-common-issues">Troubleshooting Common Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-connection-issues">Database Connection Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#pod-startup-issues">Pod Startup Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#load-balancer-issues">Load Balancer Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#rollback-procedures">Rollback Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#emergency-rollback">Emergency Rollback</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-rollback">Database Rollback</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../deployment/environment-setup.html">Environment Setup Documentation - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#environment-types">Environment Types</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#development-environment">Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#staging-environment">Staging Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#local-development-setup">Local Development Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#prerequisites">Prerequisites</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#system-requirements">System Requirements</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#required-software">Required Software</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#development-environment-setup">Development Environment Setup</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#clone-repository">1. Clone Repository</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#environment-configuration">2. Environment Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#backend-setup">3. Backend Setup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#frontend-setup">4. Frontend Setup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#start-development-services">5. Start Development Services</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#verify-development-setup">6. Verify Development Setup</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#staging-environment-setup">Staging Environment Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#infrastructure-provisioning">Infrastructure Provisioning</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#aws-configuration">1. AWS Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#terraform-deployment">2. Terraform Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#kubernetes-configuration">3. Kubernetes Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#application-deployment">Application Deployment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#build-and-push-images">1. Build and Push Images</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#deploy-to-staging">2. Deploy to Staging</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#configure-staging-data">3. Configure Staging Data</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#production-environment-setup">Production Environment Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#pre-production-checklist">Pre-Production Checklist</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#security-review">Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#performance-review">Performance Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#operational-readiness">Operational Readiness</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#production-deployment-process">Production Deployment Process</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#final-infrastructure-review">1. Final Infrastructure Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#production-image-preparation">2. Production Image Preparation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#blue-green-deployment">3. Blue-Green Deployment</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#environment-specific-configurations">Environment-Specific Configurations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#development-configuration">Development Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#staging-configuration">Staging Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#production-configuration">Production Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#ci-cd-pipeline-configuration">CI/CD Pipeline Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#github-actions-workflow">GitHub Actions Workflow</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#environment-management-scripts">Environment Management Scripts</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#deployment-script">Deployment Script</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#environment-cleanup-script">Environment Cleanup Script</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../deployment/troubleshooting-guide.html">Troubleshooting Guide - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#general-troubleshooting-approach">General Troubleshooting Approach</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#information-gathering">1. Information Gathering</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#log-analysis">2. Log Analysis</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#network-connectivity">3. Network Connectivity</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#common-issues-and-solutions">Common Issues and Solutions</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#application-startup-issues">Application Startup Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-pod-stuck-in-pending-state">Issue: Pod Stuck in Pending State</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-pod-stuck-in-crashloopbackoff">Issue: Pod Stuck in CrashLoopBackOff</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#database-issues">Database Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-database-connection-timeout">Issue: Database Connection Timeout</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-database-migration-failures">Issue: Database Migration Failures</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#network-and-connectivity-issues">Network and Connectivity Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-service-not-accessible">Issue: Service Not Accessible</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-ssl-tls-certificate-problems">Issue: SSL/TLS Certificate Problems</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#performance-issues">Performance Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-high-response-times">Issue: High Response Times</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-memory-leaks">Issue: Memory Leaks</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#storage-issues">Storage Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-persistent-volume-problems">Issue: Persistent Volume Problems</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#monitoring-and-alerting-issues">Monitoring and Alerting Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-missing-metrics">Issue: Missing Metrics</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#emergency-procedures">Emergency Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#complete-system-recovery">Complete System Recovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#rollback-procedure">Rollback Procedure</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#getting-help">Getting Help</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#internal-resources">Internal Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#external-support">External Support</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#escalation-procedures">Escalation Procedures</a></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content style-external-links">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Code Standards &amp; Style Guide</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/development/code-standards.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="code-standards-style-guide">
<h1>Code Standards &amp; Style Guide<a class="headerlink" href="#code-standards-style-guide" title="Link to this heading"></a></h1>
<p>Comprehensive coding standards and style guidelines for the Blast-Radius Security Tool to ensure consistent, maintainable, and secure code across the entire project.</p>
<nav class="contents local" id="table-of-contents">
<p class="topic-title">Table of Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#overview" id="id4">Overview</a></p></li>
<li><p><a class="reference internal" href="#general-principles" id="id5">General Principles</a></p>
<ul>
<li><p><a class="reference internal" href="#core-principles" id="id6">Core Principles</a></p></li>
<li><p><a class="reference internal" href="#solid-principles" id="id7">SOLID Principles</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#python-standards" id="id8">Python Standards</a></p>
<ul>
<li><p><a class="reference internal" href="#code-style" id="id9">Code Style</a></p></li>
<li><p><a class="reference internal" href="#security-standards" id="id10">Security Standards</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#javascript-typescript-standards" id="id11">JavaScript/TypeScript Standards</a></p>
<ul>
<li><p><a class="reference internal" href="#id1" id="id12">Code Style</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#database-standards" id="id13">Database Standards</a></p>
<ul>
<li><p><a class="reference internal" href="#schema-design" id="id14">Schema Design</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#api-standards" id="id15">API Standards</a></p>
<ul>
<li><p><a class="reference internal" href="#rest-api-design" id="id16">REST API Design</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#documentation-standards" id="id17">Documentation Standards</a></p>
<ul>
<li><p><a class="reference internal" href="#code-documentation" id="id18">Code Documentation</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#testing-standards" id="id19">Testing Standards</a></p>
<ul>
<li><p><a class="reference internal" href="#test-organization" id="id20">Test Organization</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#quality-assurance" id="id21">Quality Assurance</a></p>
<ul>
<li><p><a class="reference internal" href="#automated-checks" id="id22">Automated Checks</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#tools-and-configuration" id="id23">Tools and Configuration</a></p>
<ul>
<li><p><a class="reference internal" href="#development-tools" id="id24">Development Tools</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#enforcement" id="id25">Enforcement</a></p></li>
</ul>
</nav>
<section id="overview">
<h2><a class="toc-backref" href="#id4" role="doc-backlink">Overview</a><a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>Code standards ensure:</p>
<ul class="simple">
<li><p><strong>Consistency</strong> - Uniform code style across the project</p></li>
<li><p><strong>Readability</strong> - Code that is easy to understand and maintain</p></li>
<li><p><strong>Security</strong> - Secure coding practices and vulnerability prevention</p></li>
<li><p><strong>Performance</strong> - Efficient and optimized code</p></li>
<li><p><strong>Maintainability</strong> - Code that is easy to modify and extend</p></li>
</ul>
</section>
<section id="general-principles">
<h2><a class="toc-backref" href="#id5" role="doc-backlink">General Principles</a><a class="headerlink" href="#general-principles" title="Link to this heading"></a></h2>
<section id="core-principles">
<h3><a class="toc-backref" href="#id6" role="doc-backlink">Core Principles</a><a class="headerlink" href="#core-principles" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Clarity over Cleverness</strong> - Write code that is easy to understand</p></li>
<li><p><strong>Consistency</strong> - Follow established patterns and conventions</p></li>
<li><p><strong>Security First</strong> - Consider security implications in all code</p></li>
<li><p><strong>Performance Awareness</strong> - Write efficient code without premature optimization</p></li>
<li><p><strong>Documentation</strong> - Document complex logic and public interfaces</p></li>
<li><p><strong>Testing</strong> - Write testable code with comprehensive test coverage</p></li>
</ol>
</section>
<section id="solid-principles">
<h3><a class="toc-backref" href="#id7" role="doc-backlink">SOLID Principles</a><a class="headerlink" href="#solid-principles" title="Link to this heading"></a></h3>
<table class="docutils align-default" id="id2">
<caption><span class="caption-text">SOLID Principles Application</span><a class="headerlink" href="#id2" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 20.0%" />
<col style="width: 80.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Principle</p></th>
<th class="head"><p>Application in Blast-Radius</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>Single Responsibility</strong></p></td>
<td><p>Each class/function has one reason to change</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Open/Closed</strong></p></td>
<td><p>Open for extension, closed for modification</p></td>
</tr>
<tr class="row-even"><td><p><strong>Liskov Substitution</strong></p></td>
<td><p>Derived classes must be substitutable for base classes</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Interface Segregation</strong></p></td>
<td><p>Many specific interfaces better than one general interface</p></td>
</tr>
<tr class="row-even"><td><p><strong>Dependency Inversion</strong></p></td>
<td><p>Depend on abstractions, not concretions</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="python-standards">
<h2><a class="toc-backref" href="#id8" role="doc-backlink">Python Standards</a><a class="headerlink" href="#python-standards" title="Link to this heading"></a></h2>
<section id="code-style">
<h3><a class="toc-backref" href="#id9" role="doc-backlink">Code Style</a><a class="headerlink" href="#code-style" title="Link to this heading"></a></h3>
<p>We follow <strong>PEP 8</strong> with these specific configurations:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># pyproject.toml</span>
<span class="p">[</span><span class="n">tool</span><span class="o">.</span><span class="n">black</span><span class="p">]</span>
<span class="n">line</span><span class="o">-</span><span class="n">length</span> <span class="o">=</span> <span class="mi">88</span>
<span class="n">target</span><span class="o">-</span><span class="n">version</span> <span class="o">=</span> <span class="p">[</span><span class="s1">&#39;py311&#39;</span><span class="p">]</span>
<span class="n">include</span> <span class="o">=</span> <span class="s1">&#39;\.pyi?$&#39;</span>

<span class="p">[</span><span class="n">tool</span><span class="o">.</span><span class="n">isort</span><span class="p">]</span>
<span class="n">profile</span> <span class="o">=</span> <span class="s2">&quot;black&quot;</span>
<span class="n">multi_line_output</span> <span class="o">=</span> <span class="mi">3</span>
<span class="n">line_length</span> <span class="o">=</span> <span class="mi">88</span>

<span class="p">[</span><span class="n">tool</span><span class="o">.</span><span class="n">flake8</span><span class="p">]</span>
<span class="nb">max</span><span class="o">-</span><span class="n">line</span><span class="o">-</span><span class="n">length</span> <span class="o">=</span> <span class="mi">88</span>
<span class="n">extend</span><span class="o">-</span><span class="n">ignore</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;E203&quot;</span><span class="p">,</span> <span class="s2">&quot;W503&quot;</span><span class="p">]</span>
<span class="nb">max</span><span class="o">-</span><span class="n">complexity</span> <span class="o">=</span> <span class="mi">10</span>
</pre></div>
</div>
<p><strong>Naming Conventions:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Classes - PascalCase</span>
<span class="k">class</span> <span class="nc">AttackPathAnalyzer</span><span class="p">:</span>
    <span class="k">pass</span>

<span class="c1"># Functions and variables - snake_case</span>
<span class="k">def</span> <span class="nf">analyze_attack_path</span><span class="p">():</span>
    <span class="n">source_asset_id</span> <span class="o">=</span> <span class="mi">123</span>
    <span class="k">return</span> <span class="n">attack_path_result</span>

<span class="c1"># Constants - UPPER_SNAKE_CASE</span>
<span class="n">MAX_ATTACK_PATH_DEPTH</span> <span class="o">=</span> <span class="mi">10</span>
<span class="n">DEFAULT_TIMEOUT_SECONDS</span> <span class="o">=</span> <span class="mi">30</span>

<span class="c1"># Private methods - leading underscore</span>
<span class="k">def</span> <span class="nf">_validate_input</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">data</span><span class="p">):</span>
    <span class="k">pass</span>

<span class="c1"># Protected methods - single underscore</span>
<span class="k">def</span> <span class="nf">_internal_method</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
    <span class="k">pass</span>
</pre></div>
</div>
<p><strong>Type Annotations:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">typing</span> <span class="kn">import</span> <span class="n">List</span><span class="p">,</span> <span class="n">Dict</span><span class="p">,</span> <span class="n">Optional</span><span class="p">,</span> <span class="n">Union</span>
<span class="kn">from</span> <span class="nn">app.models</span> <span class="kn">import</span> <span class="n">Asset</span><span class="p">,</span> <span class="n">AttackPath</span>

<span class="k">def</span> <span class="nf">analyze_attack_paths</span><span class="p">(</span>
    <span class="n">source_asset</span><span class="p">:</span> <span class="n">Asset</span><span class="p">,</span>
    <span class="n">target_assets</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">Asset</span><span class="p">],</span>
    <span class="n">max_depth</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">10</span><span class="p">,</span>
    <span class="n">include_mitre</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">True</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">AttackPath</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Analyze attack paths between source and target assets.</span>

<span class="sd">    Args:</span>
<span class="sd">        source_asset: Starting point for analysis</span>
<span class="sd">        target_assets: List of potential target assets</span>
<span class="sd">        max_depth: Maximum path depth to explore</span>
<span class="sd">        include_mitre: Whether to include MITRE ATT&amp;CK mapping</span>

<span class="sd">    Returns:</span>
<span class="sd">        List of discovered attack paths</span>

<span class="sd">    Raises:</span>
<span class="sd">        ValidationError: If input validation fails</span>
<span class="sd">        AnalysisError: If analysis cannot be completed</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">pass</span>
</pre></div>
</div>
<p><strong>Error Handling:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Good - Specific exceptions with context</span>
<span class="k">class</span> <span class="nc">AttackPathError</span><span class="p">(</span><span class="ne">Exception</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Base exception for attack path analysis.&quot;&quot;&quot;</span>
    <span class="k">pass</span>

<span class="k">class</span> <span class="nc">ValidationError</span><span class="p">(</span><span class="n">AttackPathError</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Raised when input validation fails.&quot;&quot;&quot;</span>
    <span class="k">pass</span>

<span class="k">class</span> <span class="nc">AnalysisError</span><span class="p">(</span><span class="n">AttackPathError</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Raised when analysis cannot be completed.&quot;&quot;&quot;</span>
    <span class="k">pass</span>

<span class="k">def</span> <span class="nf">analyze_path</span><span class="p">(</span><span class="n">source</span><span class="p">:</span> <span class="n">Asset</span><span class="p">,</span> <span class="n">target</span><span class="p">:</span> <span class="n">Asset</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttackPath</span><span class="p">:</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">source</span> <span class="ow">or</span> <span class="ow">not</span> <span class="n">target</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">ValidationError</span><span class="p">(</span><span class="s2">&quot;Source and target assets are required&quot;</span><span class="p">)</span>

    <span class="k">try</span><span class="p">:</span>
        <span class="n">result</span> <span class="o">=</span> <span class="n">perform_analysis</span><span class="p">(</span><span class="n">source</span><span class="p">,</span> <span class="n">target</span><span class="p">)</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">result</span><span class="p">:</span>
            <span class="k">raise</span> <span class="n">AnalysisError</span><span class="p">(</span><span class="s2">&quot;No attack paths found&quot;</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">result</span>
    <span class="k">except</span> <span class="n">DatabaseError</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Database error during analysis: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="k">raise</span> <span class="n">AnalysisError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Analysis failed due to database error: </span><span class="si">{</span><span class="n">e</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p><strong>Logging:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">logging</span>
<span class="kn">import</span> <span class="nn">structlog</span>

<span class="c1"># Use structured logging</span>
<span class="n">logger</span> <span class="o">=</span> <span class="n">structlog</span><span class="o">.</span><span class="n">get_logger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>

<span class="k">def</span> <span class="nf">process_asset_discovery</span><span class="p">(</span><span class="n">asset_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span>
        <span class="s2">&quot;Starting asset discovery&quot;</span><span class="p">,</span>
        <span class="n">asset_id</span><span class="o">=</span><span class="n">asset_id</span><span class="p">,</span>
        <span class="n">operation</span><span class="o">=</span><span class="s2">&quot;asset_discovery&quot;</span>
    <span class="p">)</span>

    <span class="k">try</span><span class="p">:</span>
        <span class="n">result</span> <span class="o">=</span> <span class="n">discover_asset</span><span class="p">(</span><span class="n">asset_id</span><span class="p">)</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span>
            <span class="s2">&quot;Asset discovery completed&quot;</span><span class="p">,</span>
            <span class="n">asset_id</span><span class="o">=</span><span class="n">asset_id</span><span class="p">,</span>
            <span class="n">assets_found</span><span class="o">=</span><span class="nb">len</span><span class="p">(</span><span class="n">result</span><span class="o">.</span><span class="n">assets</span><span class="p">),</span>
            <span class="n">duration_ms</span><span class="o">=</span><span class="n">result</span><span class="o">.</span><span class="n">duration</span>
        <span class="p">)</span>
    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span>
            <span class="s2">&quot;Asset discovery failed&quot;</span><span class="p">,</span>
            <span class="n">asset_id</span><span class="o">=</span><span class="n">asset_id</span><span class="p">,</span>
            <span class="n">error</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">),</span>
            <span class="n">error_type</span><span class="o">=</span><span class="nb">type</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="o">.</span><span class="vm">__name__</span>
        <span class="p">)</span>
        <span class="k">raise</span>
</pre></div>
</div>
</section>
<section id="security-standards">
<h3><a class="toc-backref" href="#id10" role="doc-backlink">Security Standards</a><a class="headerlink" href="#security-standards" title="Link to this heading"></a></h3>
<p><strong>Input Validation:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">pydantic</span> <span class="kn">import</span> <span class="n">BaseModel</span><span class="p">,</span> <span class="n">validator</span>
<span class="kn">import</span> <span class="nn">re</span>

<span class="k">class</span> <span class="nc">AssetCreateRequest</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
    <span class="n">name</span><span class="p">:</span> <span class="nb">str</span>
    <span class="n">ip_address</span><span class="p">:</span> <span class="nb">str</span>
    <span class="n">asset_type</span><span class="p">:</span> <span class="nb">str</span>

    <span class="nd">@validator</span><span class="p">(</span><span class="s1">&#39;name&#39;</span><span class="p">)</span>
    <span class="k">def</span> <span class="nf">validate_name</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">v</span><span class="p">):</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="n">v</span> <span class="ow">or</span> <span class="nb">len</span><span class="p">(</span><span class="n">v</span><span class="o">.</span><span class="n">strip</span><span class="p">())</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s1">&#39;Name cannot be empty&#39;</span><span class="p">)</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">v</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">255</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s1">&#39;Name too long&#39;</span><span class="p">)</span>
        <span class="c1"># Prevent XSS</span>
        <span class="k">if</span> <span class="n">re</span><span class="o">.</span><span class="n">search</span><span class="p">(</span><span class="sa">r</span><span class="s1">&#39;[&lt;&gt;&quot;</span><span class="se">\&#39;</span><span class="s1">]&#39;</span><span class="p">,</span> <span class="n">v</span><span class="p">):</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s1">&#39;Name contains invalid characters&#39;</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">v</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span>

    <span class="nd">@validator</span><span class="p">(</span><span class="s1">&#39;ip_address&#39;</span><span class="p">)</span>
    <span class="k">def</span> <span class="nf">validate_ip</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">v</span><span class="p">):</span>
        <span class="kn">import</span> <span class="nn">ipaddress</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="n">ipaddress</span><span class="o">.</span><span class="n">ip_address</span><span class="p">(</span><span class="n">v</span><span class="p">)</span>
        <span class="k">except</span> <span class="ne">ValueError</span><span class="p">:</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s1">&#39;Invalid IP address format&#39;</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">v</span>
</pre></div>
</div>
<p><strong>SQL Injection Prevention:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Good - Use parameterized queries</span>
<span class="k">def</span> <span class="nf">get_assets_by_type</span><span class="p">(</span><span class="n">asset_type</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Asset</span><span class="p">]:</span>
    <span class="n">query</span> <span class="o">=</span> <span class="s2">&quot;&quot;&quot;</span>
<span class="s2">        SELECT id, name, ip_address, asset_type</span>
<span class="s2">        FROM assets</span>
<span class="s2">        WHERE asset_type = </span><span class="si">%s</span><span class="s2"> AND is_active = true</span>
<span class="s2">    &quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="n">db</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="n">query</span><span class="p">,</span> <span class="p">(</span><span class="n">asset_type</span><span class="p">,))</span><span class="o">.</span><span class="n">fetchall</span><span class="p">()</span>

<span class="c1"># Good - Use ORM</span>
<span class="k">def</span> <span class="nf">get_assets_by_type_orm</span><span class="p">(</span><span class="n">asset_type</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">Asset</span><span class="p">]:</span>
    <span class="k">return</span> <span class="n">session</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">Asset</span><span class="p">)</span><span class="o">.</span><span class="n">filter</span><span class="p">(</span>
        <span class="n">Asset</span><span class="o">.</span><span class="n">asset_type</span> <span class="o">==</span> <span class="n">asset_type</span><span class="p">,</span>
        <span class="n">Asset</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span>
    <span class="p">)</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>
</pre></div>
</div>
<p><strong>Authentication &amp; Authorization:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span> <span class="nn">functools</span> <span class="kn">import</span> <span class="n">wraps</span>
<span class="kn">from</span> <span class="nn">app.auth</span> <span class="kn">import</span> <span class="n">get_current_user</span><span class="p">,</span> <span class="n">check_permission</span>

<span class="k">def</span> <span class="nf">require_permission</span><span class="p">(</span><span class="n">permission</span><span class="p">:</span> <span class="nb">str</span><span class="p">):</span>
    <span class="k">def</span> <span class="nf">decorator</span><span class="p">(</span><span class="n">func</span><span class="p">):</span>
        <span class="nd">@wraps</span><span class="p">(</span><span class="n">func</span><span class="p">)</span>
        <span class="k">def</span> <span class="nf">wrapper</span><span class="p">(</span><span class="o">*</span><span class="n">args</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">):</span>
            <span class="n">current_user</span> <span class="o">=</span> <span class="n">get_current_user</span><span class="p">()</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="n">check_permission</span><span class="p">(</span><span class="n">current_user</span><span class="p">,</span> <span class="n">permission</span><span class="p">):</span>
                <span class="k">raise</span> <span class="ne">PermissionError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Permission &#39;</span><span class="si">{</span><span class="n">permission</span><span class="si">}</span><span class="s2">&#39; required&quot;</span><span class="p">)</span>
            <span class="k">return</span> <span class="n">func</span><span class="p">(</span><span class="o">*</span><span class="n">args</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">wrapper</span>
    <span class="k">return</span> <span class="n">decorator</span>

<span class="nd">@require_permission</span><span class="p">(</span><span class="s1">&#39;asset:read&#39;</span><span class="p">)</span>
<span class="k">def</span> <span class="nf">get_asset</span><span class="p">(</span><span class="n">asset_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Asset</span><span class="p">:</span>
    <span class="k">return</span> <span class="n">Asset</span><span class="o">.</span><span class="n">get_by_id</span><span class="p">(</span><span class="n">asset_id</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="javascript-typescript-standards">
<h2><a class="toc-backref" href="#id11" role="doc-backlink">JavaScript/TypeScript Standards</a><a class="headerlink" href="#javascript-typescript-standards" title="Link to this heading"></a></h2>
<section id="id1">
<h3><a class="toc-backref" href="#id12" role="doc-backlink">Code Style</a><a class="headerlink" href="#id1" title="Link to this heading"></a></h3>
<p><strong>ESLint Configuration:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;extends&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="s2">&quot;@typescript-eslint/recommended&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;react-hooks/recommended&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="s2">&quot;prettier&quot;</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;rules&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;@typescript-eslint/no-unused-vars&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;error&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;@typescript-eslint/explicit-function-return-type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;warn&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;react-hooks/exhaustive-deps&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;error&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;prefer-const&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;error&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;no-var&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;error&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Naming Conventions:</strong></p>
<div class="highlight-typescript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Interfaces - PascalCase with &#39;I&#39; prefix (optional)</span>
<span class="kd">interface</span><span class="w"> </span><span class="nx">AttackPathResult</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">paths</span><span class="o">:</span><span class="w"> </span><span class="kt">AttackPath</span><span class="p">[];</span>
<span class="w">  </span><span class="nx">riskScore</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="p">}</span>

<span class="c1">// Types - PascalCase</span>
<span class="kr">type</span><span class="w"> </span><span class="nx">AssetType</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;server&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;database&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;workstation&#39;</span><span class="p">;</span>

<span class="c1">// Components - PascalCase</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">AttackPathVisualization</span><span class="o">:</span><span class="w"> </span><span class="kt">React.FC</span><span class="o">&lt;</span><span class="nx">Props</span><span class="o">&gt;</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">({</span><span class="w"> </span><span class="nx">data</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="o">&lt;</span><span class="nx">div</span><span class="o">&gt;</span><span class="p">{</span><span class="cm">/* Component content */</span><span class="p">}</span><span class="o">&lt;</span><span class="err">/div&gt;;</span>
<span class="p">};</span>

<span class="c1">// Functions and variables - camelCase</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">analyzeAttackPath</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">async</span><span class="w"> </span><span class="p">(</span><span class="nx">sourceId</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="nx">AttackPath</span><span class="p">[]</span><span class="o">&gt;</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">analysisResult</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">api</span><span class="p">.</span><span class="nx">analyzeAttackPath</span><span class="p">(</span><span class="nx">sourceId</span><span class="p">);</span>
<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="nx">analysisResult</span><span class="p">.</span><span class="nx">paths</span><span class="p">;</span>
<span class="p">};</span>

<span class="c1">// Constants - UPPER_SNAKE_CASE</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">MAX_RETRY_ATTEMPTS</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">3</span><span class="p">;</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">API_BASE_URL</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">process</span><span class="p">.</span><span class="nx">env</span><span class="p">.</span><span class="nx">REACT_APP_API_URL</span><span class="p">;</span>
</pre></div>
</div>
<p><strong>React Component Standards:</strong></p>
<div class="highlight-typescript notranslate"><div class="highlight"><pre><span></span><span class="k">import</span><span class="w"> </span><span class="nx">React</span><span class="p">,</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">useState</span><span class="p">,</span><span class="w"> </span><span class="nx">useCallback</span><span class="p">,</span><span class="w"> </span><span class="nx">useEffect</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;react&#39;</span><span class="p">;</span>
<span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">AttackPath</span><span class="p">,</span><span class="w"> </span><span class="nx">Asset</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;../types&#39;</span><span class="p">;</span>
<span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">attackPathService</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;../services&#39;</span><span class="p">;</span>

<span class="kd">interface</span><span class="w"> </span><span class="nx">AttackPathAnalyzerProps</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">sourceAsset</span><span class="o">:</span><span class="w"> </span><span class="kt">Asset</span><span class="p">;</span>
<span class="w">  </span><span class="nx">targetAsset</span><span class="o">:</span><span class="w"> </span><span class="kt">Asset</span><span class="p">;</span>
<span class="w">  </span><span class="nx">onAnalysisComplete</span><span class="o">:</span><span class="w"> </span><span class="p">(</span><span class="nx">paths</span><span class="o">:</span><span class="w"> </span><span class="kt">AttackPath</span><span class="p">[])</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="ow">void</span><span class="p">;</span>
<span class="w">  </span><span class="nx">className?</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="p">}</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">AttackPathAnalyzer</span><span class="o">:</span><span class="w"> </span><span class="kt">React.FC</span><span class="o">&lt;</span><span class="nx">AttackPathAnalyzerProps</span><span class="o">&gt;</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">({</span>
<span class="w">  </span><span class="nx">sourceAsset</span><span class="p">,</span>
<span class="w">  </span><span class="nx">targetAsset</span><span class="p">,</span>
<span class="w">  </span><span class="nx">onAnalysisComplete</span><span class="p">,</span>
<span class="w">  </span><span class="nx">className</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;&#39;</span>
<span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="p">[</span><span class="nx">isAnalyzing</span><span class="p">,</span><span class="w"> </span><span class="nx">setIsAnalyzing</span><span class="p">]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">useState</span><span class="p">(</span><span class="kc">false</span><span class="p">);</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="p">[</span><span class="nx">error</span><span class="p">,</span><span class="w"> </span><span class="nx">setError</span><span class="p">]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">useState</span><span class="o">&lt;</span><span class="kt">string</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="o">&gt;</span><span class="p">(</span><span class="kc">null</span><span class="p">);</span>

<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">handleAnalysis</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">useCallback</span><span class="p">(</span><span class="k">async</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">sourceAsset</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="o">!</span><span class="nx">targetAsset</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">setError</span><span class="p">(</span><span class="s1">&#39;Source and target assets are required&#39;</span><span class="p">);</span>
<span class="w">      </span><span class="k">return</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="nx">setIsAnalyzing</span><span class="p">(</span><span class="kc">true</span><span class="p">);</span>
<span class="w">    </span><span class="nx">setError</span><span class="p">(</span><span class="kc">null</span><span class="p">);</span>

<span class="w">    </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">paths</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">attackPathService</span><span class="p">.</span><span class="nx">analyze</span><span class="p">({</span>
<span class="w">        </span><span class="nx">sourceAssetId</span><span class="o">:</span><span class="w"> </span><span class="kt">sourceAsset.id</span><span class="p">,</span>
<span class="w">        </span><span class="nx">targetAssetId</span><span class="o">:</span><span class="w"> </span><span class="kt">targetAsset.id</span>
<span class="w">      </span><span class="p">});</span>
<span class="w">      </span><span class="nx">onAnalysisComplete</span><span class="p">(</span><span class="nx">paths</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="nx">err</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">errorMessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">err</span><span class="w"> </span><span class="ow">instanceof</span><span class="w"> </span><span class="ne">Error</span><span class="w"> </span><span class="o">?</span><span class="w"> </span><span class="nx">err</span><span class="p">.</span><span class="nx">message</span><span class="w"> </span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;Analysis failed&#39;</span><span class="p">;</span>
<span class="w">      </span><span class="nx">setError</span><span class="p">(</span><span class="nx">errorMessage</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">finally</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">setIsAnalyzing</span><span class="p">(</span><span class="kc">false</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span><span class="w"> </span><span class="p">[</span><span class="nx">sourceAsset</span><span class="p">,</span><span class="w"> </span><span class="nx">targetAsset</span><span class="p">,</span><span class="w"> </span><span class="nx">onAnalysisComplete</span><span class="p">]);</span>

<span class="w">  </span><span class="nx">useEffect</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Auto-analyze when assets change</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">sourceAsset</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="nx">targetAsset</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">handleAnalysis</span><span class="p">();</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span><span class="w"> </span><span class="p">[</span><span class="nx">sourceAsset</span><span class="o">?</span><span class="p">.</span><span class="nx">id</span><span class="p">,</span><span class="w"> </span><span class="nx">targetAsset</span><span class="o">?</span><span class="p">.</span><span class="nx">id</span><span class="p">,</span><span class="w"> </span><span class="nx">handleAnalysis</span><span class="p">]);</span>

<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="o">&lt;</span><span class="nx">div</span><span class="w"> </span><span class="nx">className</span><span class="o">=</span><span class="p">{</span><span class="sb">`attack-path-analyzer </span><span class="si">${</span><span class="nx">className</span><span class="si">}</span><span class="sb">`</span><span class="p">}</span><span class="o">&gt;</span>
<span class="w">      </span><span class="p">{</span><span class="cm">/* Component JSX */</span><span class="p">}</span>
<span class="w">    </span><span class="o">&lt;</span><span class="err">/div&gt;</span>
<span class="w">  </span><span class="p">);</span>
<span class="p">};</span>
</pre></div>
</div>
<p><strong>Error Handling:</strong></p>
<div class="highlight-typescript notranslate"><div class="highlight"><pre><span></span><span class="c1">// Custom error classes</span>
<span class="kd">class</span><span class="w"> </span><span class="nx">ApiError</span><span class="w"> </span><span class="k">extends</span><span class="w"> </span><span class="ne">Error</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span>
<span class="w">    </span><span class="nx">message</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">,</span>
<span class="w">    </span><span class="k">public</span><span class="w"> </span><span class="nx">statusCode</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">,</span>
<span class="w">    </span><span class="k">public</span><span class="w"> </span><span class="nx">response?</span><span class="o">:</span><span class="w"> </span><span class="kt">any</span>
<span class="w">  </span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">super</span><span class="p">(</span><span class="nx">message</span><span class="p">);</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;ApiError&#39;</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>

<span class="c1">// Error boundary component</span>
<span class="kd">class</span><span class="w"> </span><span class="nx">ErrorBoundary</span><span class="w"> </span><span class="k">extends</span><span class="w"> </span><span class="nx">React</span><span class="p">.</span><span class="nx">Component</span><span class="o">&lt;</span><span class="nx">Props</span><span class="p">,</span><span class="w"> </span><span class="nx">State</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kr">constructor</span><span class="p">(</span><span class="nx">props</span><span class="o">:</span><span class="w"> </span><span class="kt">Props</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">super</span><span class="p">(</span><span class="nx">props</span><span class="p">);</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">state</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">hasError</span><span class="o">:</span><span class="w"> </span><span class="kt">false</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">null</span><span class="w"> </span><span class="p">};</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">static</span><span class="w"> </span><span class="nx">getDerivedStateFromError</span><span class="p">(</span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">Error</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nx">State</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">hasError</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="w"> </span><span class="p">};</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">componentDidCatch</span><span class="p">(</span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">Error</span><span class="p">,</span><span class="w"> </span><span class="nx">errorInfo</span><span class="o">:</span><span class="w"> </span><span class="kt">React.ErrorInfo</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s1">&#39;Error caught by boundary:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">,</span><span class="w"> </span><span class="nx">errorInfo</span><span class="p">);</span>
<span class="w">    </span><span class="c1">// Send to error reporting service</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">render</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">state</span><span class="p">.</span><span class="nx">hasError</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="o">&lt;</span><span class="nx">ErrorFallback</span><span class="w"> </span><span class="nx">error</span><span class="o">=</span><span class="p">{</span><span class="k">this</span><span class="p">.</span><span class="nx">state</span><span class="p">.</span><span class="nx">error</span><span class="p">}</span><span class="w"> </span><span class="o">/&gt;</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">props</span><span class="p">.</span><span class="nx">children</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="database-standards">
<h2><a class="toc-backref" href="#id13" role="doc-backlink">Database Standards</a><a class="headerlink" href="#database-standards" title="Link to this heading"></a></h2>
<section id="schema-design">
<h3><a class="toc-backref" href="#id14" role="doc-backlink">Schema Design</a><a class="headerlink" href="#schema-design" title="Link to this heading"></a></h3>
<p><strong>Naming Conventions:</strong></p>
<div class="highlight-sql notranslate"><div class="highlight"><pre><span></span><span class="c1">-- Tables - plural, snake_case</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">attack_paths</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="n">id</span><span class="w"> </span><span class="n">BIGSERIAL</span><span class="w"> </span><span class="k">PRIMARY</span><span class="w"> </span><span class="k">KEY</span><span class="p">,</span>
<span class="w">    </span><span class="n">source_asset_id</span><span class="w"> </span><span class="nb">BIGINT</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">target_asset_id</span><span class="w"> </span><span class="nb">BIGINT</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">risk_score</span><span class="w"> </span><span class="nb">DECIMAL</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="k">NOT</span><span class="w"> </span><span class="k">NULL</span><span class="p">,</span>
<span class="w">    </span><span class="n">created_at</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">WITH</span><span class="w"> </span><span class="k">TIME</span><span class="w"> </span><span class="k">ZONE</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="n">NOW</span><span class="p">(),</span>
<span class="w">    </span><span class="n">updated_at</span><span class="w"> </span><span class="k">TIMESTAMP</span><span class="w"> </span><span class="k">WITH</span><span class="w"> </span><span class="k">TIME</span><span class="w"> </span><span class="k">ZONE</span><span class="w"> </span><span class="k">DEFAULT</span><span class="w"> </span><span class="n">NOW</span><span class="p">()</span>
<span class="p">);</span>

<span class="c1">-- Indexes - descriptive names</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_attack_paths_source_asset</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">attack_paths</span><span class="p">(</span><span class="n">source_asset_id</span><span class="p">);</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">idx_attack_paths_risk_score</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">attack_paths</span><span class="p">(</span><span class="n">risk_score</span><span class="w"> </span><span class="k">DESC</span><span class="p">);</span>

<span class="c1">-- Foreign keys - descriptive names</span>
<span class="k">ALTER</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">attack_paths</span>
<span class="k">ADD</span><span class="w"> </span><span class="k">CONSTRAINT</span><span class="w"> </span><span class="n">fk_attack_paths_source_asset</span>
<span class="k">FOREIGN</span><span class="w"> </span><span class="k">KEY</span><span class="w"> </span><span class="p">(</span><span class="n">source_asset_id</span><span class="p">)</span><span class="w"> </span><span class="k">REFERENCES</span><span class="w"> </span><span class="n">assets</span><span class="p">(</span><span class="n">id</span><span class="p">);</span>
</pre></div>
</div>
<p><strong>Migration Standards:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="sd">&quot;&quot;&quot;Add attack path analysis tables</span>

<span class="sd">Revision ID: 001_attack_paths</span>
<span class="sd">Revises: 000_initial</span>
<span class="sd">Create Date: 2024-01-15 10:30:00.000000</span>
<span class="sd">&quot;&quot;&quot;</span>

<span class="kn">from</span> <span class="nn">alembic</span> <span class="kn">import</span> <span class="n">op</span>
<span class="kn">import</span> <span class="nn">sqlalchemy</span> <span class="k">as</span> <span class="nn">sa</span>

<span class="k">def</span> <span class="nf">upgrade</span><span class="p">():</span>
    <span class="c1"># Create table with all constraints</span>
    <span class="n">op</span><span class="o">.</span><span class="n">create_table</span><span class="p">(</span>
        <span class="s1">&#39;attack_paths&#39;</span><span class="p">,</span>
        <span class="n">sa</span><span class="o">.</span><span class="n">Column</span><span class="p">(</span><span class="s1">&#39;id&#39;</span><span class="p">,</span> <span class="n">sa</span><span class="o">.</span><span class="n">BigInteger</span><span class="p">(),</span> <span class="n">nullable</span><span class="o">=</span><span class="kc">False</span><span class="p">),</span>
        <span class="n">sa</span><span class="o">.</span><span class="n">Column</span><span class="p">(</span><span class="s1">&#39;source_asset_id&#39;</span><span class="p">,</span> <span class="n">sa</span><span class="o">.</span><span class="n">BigInteger</span><span class="p">(),</span> <span class="n">nullable</span><span class="o">=</span><span class="kc">False</span><span class="p">),</span>
        <span class="n">sa</span><span class="o">.</span><span class="n">Column</span><span class="p">(</span><span class="s1">&#39;target_asset_id&#39;</span><span class="p">,</span> <span class="n">sa</span><span class="o">.</span><span class="n">BigInteger</span><span class="p">(),</span> <span class="n">nullable</span><span class="o">=</span><span class="kc">False</span><span class="p">),</span>
        <span class="n">sa</span><span class="o">.</span><span class="n">Column</span><span class="p">(</span><span class="s1">&#39;risk_score&#39;</span><span class="p">,</span> <span class="n">sa</span><span class="o">.</span><span class="n">Numeric</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">2</span><span class="p">),</span> <span class="n">nullable</span><span class="o">=</span><span class="kc">False</span><span class="p">),</span>
        <span class="n">sa</span><span class="o">.</span><span class="n">Column</span><span class="p">(</span><span class="s1">&#39;created_at&#39;</span><span class="p">,</span> <span class="n">sa</span><span class="o">.</span><span class="n">DateTime</span><span class="p">(</span><span class="n">timezone</span><span class="o">=</span><span class="kc">True</span><span class="p">),</span>
                 <span class="n">server_default</span><span class="o">=</span><span class="n">sa</span><span class="o">.</span><span class="n">text</span><span class="p">(</span><span class="s1">&#39;NOW()&#39;</span><span class="p">),</span> <span class="n">nullable</span><span class="o">=</span><span class="kc">False</span><span class="p">),</span>
        <span class="n">sa</span><span class="o">.</span><span class="n">Column</span><span class="p">(</span><span class="s1">&#39;updated_at&#39;</span><span class="p">,</span> <span class="n">sa</span><span class="o">.</span><span class="n">DateTime</span><span class="p">(</span><span class="n">timezone</span><span class="o">=</span><span class="kc">True</span><span class="p">),</span>
                 <span class="n">server_default</span><span class="o">=</span><span class="n">sa</span><span class="o">.</span><span class="n">text</span><span class="p">(</span><span class="s1">&#39;NOW()&#39;</span><span class="p">),</span> <span class="n">nullable</span><span class="o">=</span><span class="kc">False</span><span class="p">),</span>
        <span class="n">sa</span><span class="o">.</span><span class="n">PrimaryKeyConstraint</span><span class="p">(</span><span class="s1">&#39;id&#39;</span><span class="p">),</span>
        <span class="n">sa</span><span class="o">.</span><span class="n">ForeignKeyConstraint</span><span class="p">([</span><span class="s1">&#39;source_asset_id&#39;</span><span class="p">],</span> <span class="p">[</span><span class="s1">&#39;assets.id&#39;</span><span class="p">]),</span>
        <span class="n">sa</span><span class="o">.</span><span class="n">ForeignKeyConstraint</span><span class="p">([</span><span class="s1">&#39;target_asset_id&#39;</span><span class="p">],</span> <span class="p">[</span><span class="s1">&#39;assets.id&#39;</span><span class="p">])</span>
    <span class="p">)</span>

    <span class="c1"># Create indexes</span>
    <span class="n">op</span><span class="o">.</span><span class="n">create_index</span><span class="p">(</span><span class="s1">&#39;idx_attack_paths_source_asset&#39;</span><span class="p">,</span> <span class="s1">&#39;attack_paths&#39;</span><span class="p">,</span> <span class="p">[</span><span class="s1">&#39;source_asset_id&#39;</span><span class="p">])</span>
    <span class="n">op</span><span class="o">.</span><span class="n">create_index</span><span class="p">(</span><span class="s1">&#39;idx_attack_paths_risk_score&#39;</span><span class="p">,</span> <span class="s1">&#39;attack_paths&#39;</span><span class="p">,</span> <span class="p">[</span><span class="s1">&#39;risk_score&#39;</span><span class="p">])</span>

<span class="k">def</span> <span class="nf">downgrade</span><span class="p">():</span>
    <span class="n">op</span><span class="o">.</span><span class="n">drop_table</span><span class="p">(</span><span class="s1">&#39;attack_paths&#39;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="api-standards">
<h2><a class="toc-backref" href="#id15" role="doc-backlink">API Standards</a><a class="headerlink" href="#api-standards" title="Link to this heading"></a></h2>
<section id="rest-api-design">
<h3><a class="toc-backref" href="#id16" role="doc-backlink">REST API Design</a><a class="headerlink" href="#rest-api-design" title="Link to this heading"></a></h3>
<p><strong>URL Structure:</strong></p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span># Good - RESTful URLs
GET    /api/v1/assets                    # List assets
POST   /api/v1/assets                    # Create asset
GET    /api/v1/assets/{id}               # Get specific asset
PUT    /api/v1/assets/{id}               # Update asset
DELETE /api/v1/assets/{id}               # Delete asset

# Nested resources
GET    /api/v1/assets/{id}/attack-paths  # Get attack paths for asset
POST   /api/v1/assets/{id}/scan          # Trigger scan for asset

# Bad - Non-RESTful URLs
GET    /api/v1/getAssets
POST   /api/v1/createAsset
GET    /api/v1/asset?action=get&amp;id=123
</pre></div>
</div>
<p><strong>Response Format:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;success&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;data&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">123</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;name&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;web-server-01&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;server&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;ip_address&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;*************&quot;</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;meta&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;timestamp&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;version&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;1.0.0&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>

<span class="c1">// Error response</span>
<span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;success&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;error&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;code&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;VALIDATION_ERROR&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Invalid asset type provided&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;details&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;field&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;type&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;allowed_values&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;server&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;database&quot;</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;workstation&quot;</span><span class="p">]</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;meta&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;timestamp&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-01-15T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;request_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;req_123456789&quot;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
<p><strong>Status Codes:</strong></p>
<table class="docutils align-default" id="id3">
<caption><span class="caption-text">HTTP Status Code Usage</span><a class="headerlink" href="#id3" title="Link to this table"></a></caption>
<colgroup>
<col style="width: 15.0%" />
<col style="width: 25.0%" />
<col style="width: 60.0%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Code</p></th>
<th class="head"><p>Usage</p></th>
<th class="head"><p>Example</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>200</strong></p></td>
<td><p>Successful GET, PUT</p></td>
<td><p>Asset retrieved/updated successfully</p></td>
</tr>
<tr class="row-odd"><td><p><strong>201</strong></p></td>
<td><p>Successful POST</p></td>
<td><p>Asset created successfully</p></td>
</tr>
<tr class="row-even"><td><p><strong>204</strong></p></td>
<td><p>Successful DELETE</p></td>
<td><p>Asset deleted successfully</p></td>
</tr>
<tr class="row-odd"><td><p><strong>400</strong></p></td>
<td><p>Bad Request</p></td>
<td><p>Invalid input data</p></td>
</tr>
<tr class="row-even"><td><p><strong>401</strong></p></td>
<td><p>Unauthorized</p></td>
<td><p>Missing or invalid authentication</p></td>
</tr>
<tr class="row-odd"><td><p><strong>403</strong></p></td>
<td><p>Forbidden</p></td>
<td><p>Insufficient permissions</p></td>
</tr>
<tr class="row-even"><td><p><strong>404</strong></p></td>
<td><p>Not Found</p></td>
<td><p>Asset does not exist</p></td>
</tr>
<tr class="row-odd"><td><p><strong>409</strong></p></td>
<td><p>Conflict</p></td>
<td><p>Asset already exists</p></td>
</tr>
<tr class="row-even"><td><p><strong>422</strong></p></td>
<td><p>Validation Error</p></td>
<td><p>Input validation failed</p></td>
</tr>
<tr class="row-odd"><td><p><strong>500</strong></p></td>
<td><p>Internal Error</p></td>
<td><p>Unexpected server error</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="documentation-standards">
<h2><a class="toc-backref" href="#id17" role="doc-backlink">Documentation Standards</a><a class="headerlink" href="#documentation-standards" title="Link to this heading"></a></h2>
<section id="code-documentation">
<h3><a class="toc-backref" href="#id18" role="doc-backlink">Code Documentation</a><a class="headerlink" href="#code-documentation" title="Link to this heading"></a></h3>
<p><strong>Python Docstrings:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span> <span class="nf">analyze_attack_path</span><span class="p">(</span>
    <span class="n">source_asset</span><span class="p">:</span> <span class="n">Asset</span><span class="p">,</span>
    <span class="n">target_asset</span><span class="p">:</span> <span class="n">Asset</span><span class="p">,</span>
    <span class="n">max_depth</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">10</span><span class="p">,</span>
    <span class="n">include_mitre</span><span class="p">:</span> <span class="nb">bool</span> <span class="o">=</span> <span class="kc">True</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">AttackPathResult</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Analyze attack paths between two assets.</span>

<span class="sd">    This function performs graph-based analysis to discover potential</span>
<span class="sd">    attack paths from a source asset to a target asset, optionally</span>
<span class="sd">    including MITRE ATT&amp;CK technique mapping.</span>

<span class="sd">    Args:</span>
<span class="sd">        source_asset: The starting point for attack path analysis.</span>
<span class="sd">            Must be a valid Asset instance with an ID.</span>
<span class="sd">        target_asset: The target asset to reach. Must be a valid</span>
<span class="sd">            Asset instance with an ID.</span>
<span class="sd">        max_depth: Maximum number of hops to explore in the attack</span>
<span class="sd">            path. Higher values increase analysis time but may find</span>
<span class="sd">            more complex paths. Defaults to 10.</span>
<span class="sd">        include_mitre: Whether to include MITRE ATT&amp;CK technique</span>
<span class="sd">            mapping in the results. Defaults to True.</span>

<span class="sd">    Returns:</span>
<span class="sd">        AttackPathResult containing:</span>
<span class="sd">            - paths: List of discovered attack paths</span>
<span class="sd">            - risk_scores: Risk assessment for each path</span>
<span class="sd">            - mitre_techniques: MITRE ATT&amp;CK techniques (if enabled)</span>
<span class="sd">            - analysis_metadata: Timing and performance data</span>

<span class="sd">    Raises:</span>
<span class="sd">        ValidationError: If source_asset or target_asset is invalid</span>
<span class="sd">        AnalysisError: If the analysis cannot be completed</span>
<span class="sd">        TimeoutError: If analysis exceeds maximum time limit</span>

<span class="sd">    Example:</span>
<span class="sd">        &gt;&gt;&gt; source = Asset.get_by_id(1)</span>
<span class="sd">        &gt;&gt;&gt; target = Asset.get_by_id(2)</span>
<span class="sd">        &gt;&gt;&gt; result = analyze_attack_path(source, target, max_depth=5)</span>
<span class="sd">        &gt;&gt;&gt; print(f&quot;Found {len(result.paths)} attack paths&quot;)</span>

<span class="sd">    Note:</span>
<span class="sd">        This function may take significant time for complex networks.</span>
<span class="sd">        Consider using async version for web applications.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">pass</span>
</pre></div>
</div>
<p><strong>TypeScript Documentation:</strong></p>
<div class="highlight-typescript notranslate"><div class="highlight"><pre><span></span><span class="cm">/**</span>
<span class="cm"> * Analyzes attack paths between assets using graph algorithms.</span>
<span class="cm"> *</span>
<span class="cm"> * @param sourceAssetId - ID of the source asset</span>
<span class="cm"> * @param targetAssetId - ID of the target asset</span>
<span class="cm"> * @param options - Analysis configuration options</span>
<span class="cm"> * @returns Promise resolving to attack path analysis results</span>
<span class="cm"> *</span>
<span class="cm"> * @throws {ValidationError} When asset IDs are invalid</span>
<span class="cm"> * @throws {ApiError} When API request fails</span>
<span class="cm"> *</span>
<span class="cm"> * @example</span>
<span class="cm"> * ```typescript</span>
<span class="cm"> * const result = await analyzeAttackPath(1, 2, {</span>
<span class="cm"> *   maxDepth: 5,</span>
<span class="cm"> *   includeMitre: true</span>
<span class="cm"> * });</span>
<span class="cm"> * console.log(`Found ${result.paths.length} paths`);</span>
<span class="cm"> * ```</span>
<span class="cm"> */</span>
<span class="k">export</span><span class="w"> </span><span class="k">async</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">analyzeAttackPath</span><span class="p">(</span>
<span class="w">  </span><span class="nx">sourceAssetId</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">targetAssetId</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">,</span>
<span class="w">  </span><span class="nx">options</span><span class="o">:</span><span class="w"> </span><span class="kt">AnalysisOptions</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{}</span>
<span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="nx">AttackPathResult</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// Implementation</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="testing-standards">
<h2><a class="toc-backref" href="#id19" role="doc-backlink">Testing Standards</a><a class="headerlink" href="#testing-standards" title="Link to this heading"></a></h2>
<section id="test-organization">
<h3><a class="toc-backref" href="#id20" role="doc-backlink">Test Organization</a><a class="headerlink" href="#test-organization" title="Link to this heading"></a></h3>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>tests/
├── unit/                    # Unit tests
│   ├── services/           # Service layer tests
│   ├── models/             # Model tests
│   └── utils/              # Utility function tests
├── integration/            # Integration tests
│   ├── api/                # API endpoint tests
│   ├── database/           # Database integration tests
│   └── external/           # External service tests
├── e2e/                    # End-to-end tests
│   ├── user_workflows/     # Complete user workflows
│   └── api_workflows/      # API workflow tests
├── performance/            # Performance tests
└── security/               # Security tests
</pre></div>
</div>
<p><strong>Test Naming:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Good test names - describe what they test</span>
<span class="k">def</span> <span class="nf">test_analyze_attack_path_returns_valid_paths_for_connected_assets</span><span class="p">():</span>
    <span class="k">pass</span>

<span class="k">def</span> <span class="nf">test_analyze_attack_path_raises_validation_error_for_invalid_assets</span><span class="p">():</span>
    <span class="k">pass</span>

<span class="k">def</span> <span class="nf">test_analyze_attack_path_respects_max_depth_parameter</span><span class="p">():</span>
    <span class="k">pass</span>

<span class="c1"># Bad test names - not descriptive</span>
<span class="k">def</span> <span class="nf">test_attack_path</span><span class="p">():</span>
    <span class="k">pass</span>

<span class="k">def</span> <span class="nf">test_analysis</span><span class="p">():</span>
    <span class="k">pass</span>
</pre></div>
</div>
</section>
</section>
<section id="quality-assurance">
<h2><a class="toc-backref" href="#id21" role="doc-backlink">Quality Assurance</a><a class="headerlink" href="#quality-assurance" title="Link to this heading"></a></h2>
<section id="automated-checks">
<h3><a class="toc-backref" href="#id22" role="doc-backlink">Automated Checks</a><a class="headerlink" href="#automated-checks" title="Link to this heading"></a></h3>
<p><strong>Pre-commit Hooks:</strong></p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="c1"># .pre-commit-config.yaml</span>
<span class="nt">repos</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">repo</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">https://github.com/psf/black</span>
<span class="w">    </span><span class="nt">rev</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">23.3.0</span>
<span class="w">    </span><span class="nt">hooks</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">black</span>
<span class="w">        </span><span class="nt">language_version</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">python3.11</span>

<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">repo</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">https://github.com/pycqa/isort</span>
<span class="w">    </span><span class="nt">rev</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5.12.0</span>
<span class="w">    </span><span class="nt">hooks</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">isort</span>
<span class="w">        </span><span class="nt">args</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&quot;--profile&quot;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&quot;black&quot;</span><span class="p p-Indicator">]</span>

<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">repo</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">https://github.com/pycqa/flake8</span>
<span class="w">    </span><span class="nt">rev</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">6.0.0</span>
<span class="w">    </span><span class="nt">hooks</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">flake8</span>
<span class="w">        </span><span class="nt">additional_dependencies</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">flake8-docstrings</span><span class="p p-Indicator">]</span>

<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">repo</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">https://github.com/pre-commit/mirrors-mypy</span>
<span class="w">    </span><span class="nt">rev</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">v1.3.0</span>
<span class="w">    </span><span class="nt">hooks</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">mypy</span>
<span class="w">        </span><span class="nt">additional_dependencies</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="nv">types-requests</span><span class="p p-Indicator">]</span>
</pre></div>
</div>
<p><strong>CI/CD Quality Gates:</strong></p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="c1"># Quality requirements for merge</span>
<span class="nt">quality_gates</span><span class="p">:</span>
<span class="w">  </span><span class="nt">code_coverage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">90%</span>
<span class="w">  </span><span class="nt">security_scan</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">pass</span>
<span class="w">  </span><span class="nt">performance_regression</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">none</span>
<span class="w">  </span><span class="nt">documentation_coverage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">95%</span>
<span class="w">  </span><span class="nt">type_coverage</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">85%</span>
</pre></div>
</div>
</section>
</section>
<section id="tools-and-configuration">
<h2><a class="toc-backref" href="#id23" role="doc-backlink">Tools and Configuration</a><a class="headerlink" href="#tools-and-configuration" title="Link to this heading"></a></h2>
<section id="development-tools">
<h3><a class="toc-backref" href="#id24" role="doc-backlink">Development Tools</a><a class="headerlink" href="#development-tools" title="Link to this heading"></a></h3>
<p><strong>Required Tools:</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Python tools</span>
pip<span class="w"> </span>install<span class="w"> </span>black<span class="w"> </span>isort<span class="w"> </span>flake8<span class="w"> </span>mypy<span class="w"> </span>bandit<span class="w"> </span>safety

<span class="c1"># JavaScript/TypeScript tools</span>
npm<span class="w"> </span>install<span class="w"> </span>-g<span class="w"> </span>eslint<span class="w"> </span>prettier<span class="w"> </span>typescript

<span class="c1"># Documentation tools</span>
pip<span class="w"> </span>install<span class="w"> </span>sphinx<span class="w"> </span>sphinx-rtd-theme

<span class="c1"># Testing tools</span>
pip<span class="w"> </span>install<span class="w"> </span>pytest<span class="w"> </span>pytest-cov<span class="w"> </span>pytest-mock
</pre></div>
</div>
<p><strong>IDE Configuration:</strong></p>
<div class="highlight-json notranslate"><div class="highlight"><pre><span></span><span class="c1">// VS Code settings.json</span>
<span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;python.defaultInterpreterPath&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;./venv/bin/python&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;python.linting.enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;python.linting.flake8Enabled&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;python.formatting.provider&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;black&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;editor.formatOnSave&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;editor.codeActionsOnSave&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;source.organizeImports&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">true</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</pre></div>
</div>
</section>
</section>
<section id="enforcement">
<h2><a class="toc-backref" href="#id25" role="doc-backlink">Enforcement</a><a class="headerlink" href="#enforcement" title="Link to this heading"></a></h2>
<p>These standards are enforced through:</p>
<ol class="arabic simple">
<li><p><strong>Automated tools</strong> - Linters, formatters, and type checkers</p></li>
<li><p><strong>CI/CD pipeline</strong> - Quality gates in continuous integration</p></li>
<li><p><strong>Code review</strong> - Manual review process for all changes</p></li>
<li><p><strong>Documentation</strong> - Clear guidelines and examples</p></li>
<li><p><strong>Training</strong> - Onboarding and ongoing education</p></li>
</ol>
<p>For questions about these standards or suggestions for improvements, please create an issue or start a discussion in the project repository.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>These standards are living documents that evolve with the project.
Regular reviews ensure they remain relevant and effective.</p>
</div>
</section>
</section>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, Blast-Radius Security Tool Team.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>