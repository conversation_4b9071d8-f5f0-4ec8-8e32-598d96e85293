# Grafana Datasource Configuration for Blast-Radius Security Tool

apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "15s"
      queryTimeout: "60s"
      httpMethod: "POST"
      manageAlerts: true
      alertmanagerUid: "alertmanager"
    secureJsonData: {}
    version: 1

  - name: Alertmanager
    type: alertmanager
    access: proxy
    url: http://alertmanager:9093
    uid: "alertmanager"
    editable: true
    jsonData:
      implementation: "prometheus"
      handleGrafanaManagedAlerts: false
    version: 1

  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    editable: true
    jsonData:
      maxLines: 1000
      derivedFields:
        - datasourceUid: "prometheus"
          matcherRegex: "trace_id=(\\w+)"
          name: "TraceID"
          url: "$${__value.raw}"
    version: 1
