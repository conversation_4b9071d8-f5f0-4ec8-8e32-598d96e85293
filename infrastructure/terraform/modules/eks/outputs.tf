# EKS Module Outputs for Blast-Radius Security Tool

# EKS Cluster Outputs
output "cluster_id" {
  description = "Name/ID of the EKS cluster"
  value       = aws_eks_cluster.main.name
}

output "cluster_arn" {
  description = "ARN of the EKS cluster"
  value       = aws_eks_cluster.main.arn
}

output "cluster_endpoint" {
  description = "Endpoint for EKS control plane"
  value       = aws_eks_cluster.main.endpoint
}

output "cluster_version" {
  description = "Kubernetes version of the EKS cluster"
  value       = aws_eks_cluster.main.version
}

output "cluster_platform_version" {
  description = "Platform version for the EKS cluster"
  value       = aws_eks_cluster.main.platform_version
}

output "cluster_status" {
  description = "Status of the EKS cluster"
  value       = aws_eks_cluster.main.status
}

# Cluster Authentication
output "cluster_certificate_authority_data" {
  description = "Base64 encoded certificate data required to communicate with the cluster"
  value       = aws_eks_cluster.main.certificate_authority[0].data
}

output "cluster_oidc_issuer_url" {
  description = "The URL on the EKS cluster OIDC Issuer"
  value       = aws_eks_cluster.main.identity[0].oidc[0].issuer
}

output "oidc_provider_arn" {
  description = "ARN of the OIDC Provider for IRSA"
  value       = aws_iam_openid_connect_provider.cluster.arn
}

# Node Group Outputs
output "node_group_arn" {
  description = "ARN of the EKS node group"
  value       = aws_eks_node_group.main.arn
}

output "node_group_status" {
  description = "Status of the EKS node group"
  value       = aws_eks_node_group.main.status
}

output "node_group_capacity_type" {
  description = "Type of capacity associated with the EKS Node Group"
  value       = aws_eks_node_group.main.capacity_type
}

output "node_group_instance_types" {
  description = "Set of instance types associated with the EKS Node Group"
  value       = aws_eks_node_group.main.instance_types
}

output "node_group_ami_type" {
  description = "Type of Amazon Machine Image (AMI) associated with the EKS Node Group"
  value       = aws_eks_node_group.main.ami_type
}

output "node_group_disk_size" {
  description = "Disk size in GiB for worker nodes"
  value       = aws_eks_node_group.main.disk_size
}

# Security Group Outputs
output "cluster_security_group_id" {
  description = "ID of the cluster security group"
  value       = aws_security_group.cluster.id
}

output "node_group_security_group_id" {
  description = "ID of the node group security group"
  value       = aws_security_group.node_group.id
}

output "cluster_primary_security_group_id" {
  description = "The cluster primary security group ID created by EKS"
  value       = aws_eks_cluster.main.vpc_config[0].cluster_security_group_id
}

# IAM Role Outputs
output "cluster_iam_role_arn" {
  description = "IAM role ARN of the EKS cluster"
  value       = aws_iam_role.cluster.arn
}

output "node_group_iam_role_arn" {
  description = "IAM role ARN of the EKS node group"
  value       = aws_iam_role.node_group.arn
}

output "vpc_cni_iam_role_arn" {
  description = "IAM role ARN of the VPC CNI"
  value       = aws_iam_role.vpc_cni.arn
}

# Add-on Outputs
output "vpc_cni_addon_arn" {
  description = "ARN of the VPC CNI EKS add-on"
  value       = aws_eks_addon.vpc_cni.arn
}

output "coredns_addon_arn" {
  description = "ARN of the CoreDNS EKS add-on"
  value       = aws_eks_addon.coredns.arn
}

output "kube_proxy_addon_arn" {
  description = "ARN of the kube-proxy EKS add-on"
  value       = aws_eks_addon.kube_proxy.arn
}

# CloudWatch Outputs
output "cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group for EKS cluster logs"
  value       = aws_cloudwatch_log_group.cluster.name
}

output "cloudwatch_log_group_arn" {
  description = "ARN of the CloudWatch log group for EKS cluster logs"
  value       = aws_cloudwatch_log_group.cluster.arn
}

# Launch Template Outputs
output "launch_template_id" {
  description = "ID of the launch template"
  value       = aws_launch_template.node_group.id
}

output "launch_template_latest_version" {
  description = "Latest version of the launch template"
  value       = aws_launch_template.node_group.latest_version
}

# Scaling Configuration
output "node_group_scaling_config" {
  description = "Scaling configuration of the node group"
  value = {
    desired_size = aws_eks_node_group.main.scaling_config[0].desired_size
    max_size     = aws_eks_node_group.main.scaling_config[0].max_size
    min_size     = aws_eks_node_group.main.scaling_config[0].min_size
  }
}

# Network Configuration
output "cluster_vpc_config" {
  description = "VPC configuration of the cluster"
  value = {
    vpc_id                   = aws_eks_cluster.main.vpc_config[0].vpc_id
    subnet_ids              = aws_eks_cluster.main.vpc_config[0].subnet_ids
    endpoint_private_access = aws_eks_cluster.main.vpc_config[0].endpoint_private_access
    endpoint_public_access  = aws_eks_cluster.main.vpc_config[0].endpoint_public_access
    public_access_cidrs     = aws_eks_cluster.main.vpc_config[0].public_access_cidrs
  }
}

# Kubernetes Configuration for kubectl
output "kubeconfig" {
  description = "kubectl configuration for connecting to the cluster"
  value = {
    cluster_name     = aws_eks_cluster.main.name
    endpoint         = aws_eks_cluster.main.endpoint
    ca_data          = aws_eks_cluster.main.certificate_authority[0].data
    region           = data.aws_region.current.name
  }
  sensitive = true
}

# EKS Cluster Configuration Summary
output "cluster_configuration" {
  description = "Summary of EKS cluster configuration"
  value = {
    cluster_name            = aws_eks_cluster.main.name
    kubernetes_version      = aws_eks_cluster.main.version
    platform_version        = aws_eks_cluster.main.platform_version
    endpoint_private_access = aws_eks_cluster.main.vpc_config[0].endpoint_private_access
    endpoint_public_access  = aws_eks_cluster.main.vpc_config[0].endpoint_public_access
    encryption_enabled      = length(aws_eks_cluster.main.encryption_config) > 0
    logging_enabled         = length(aws_eks_cluster.main.enabled_cluster_log_types) > 0
    node_group_capacity     = aws_eks_node_group.main.capacity_type
    node_group_ami_type     = aws_eks_node_group.main.ami_type
  }
}

# Security Configuration Summary
output "security_configuration" {
  description = "Summary of security configuration"
  value = {
    cluster_encryption_enabled = length(aws_eks_cluster.main.encryption_config) > 0
    oidc_provider_enabled      = true
    irsa_enabled              = var.enable_irsa
    network_policy_enabled    = var.enable_network_policy
    pod_security_policy       = var.enable_pod_security_policy
    private_networking        = var.enable_private_networking
    detailed_monitoring       = var.enable_detailed_monitoring
  }
}

# Monitoring Configuration Summary
output "monitoring_configuration" {
  description = "Summary of monitoring configuration"
  value = {
    container_insights_enabled = var.enable_container_insights
    prometheus_monitoring      = var.enable_prometheus_monitoring
    fluentd_logging           = var.enable_fluentd_logging
    cloudwatch_logs_enabled   = length(aws_eks_cluster.main.enabled_cluster_log_types) > 0
    log_retention_days        = var.log_retention_days
  }
}

# Compliance Information
output "compliance_information" {
  description = "Compliance and governance information"
  value = {
    compliance_framework = var.compliance_framework
    data_classification  = var.data_classification
    backup_enabled      = var.enable_backup
    backup_retention    = var.backup_retention_days
  }
}

# Cost Optimization Information
output "cost_optimization" {
  description = "Cost optimization configuration"
  value = {
    capacity_type           = aws_eks_node_group.main.capacity_type
    spot_instances_enabled  = var.enable_spot_instances
    spot_instance_percentage = var.spot_instance_percentage
  }
}

# Resource Tags
output "applied_tags" {
  description = "Tags applied to EKS resources"
  value       = var.common_tags
}

# Regional Information
output "aws_region" {
  description = "AWS region where the cluster is deployed"
  value       = data.aws_region.current.name
}

# Cluster Access Information
output "cluster_access_info" {
  description = "Information for accessing the cluster"
  value = {
    aws_cli_command = "aws eks update-kubeconfig --region ${data.aws_region.current.name} --name ${aws_eks_cluster.main.name}"
    kubectl_config  = "kubectl config current-context should show: arn:aws:eks:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:cluster/${aws_eks_cluster.main.name}"
  }
}
