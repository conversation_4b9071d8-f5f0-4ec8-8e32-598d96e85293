# requirements.txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
pydantic==2.5.0
python-multipart==0.0.6
httpx==0.25.2
networkx==3.2.1
python-dateutil==2.8.2
alembic==1.13.0
redis==5.0.1
celery==5.3.4
prometheus-client==0.19.0
structlog==23.2.0

# Dev dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

---

# docker-compose.yml
version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/security_graph_db
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=development
    depends_on:
      - db
      - redis
    volumes:
      - .:/app
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=security_graph_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  worker:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=**************************************/security_graph_db
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=development
    depends_on:
      - db
      - redis
    volumes:
      - .:/app
    command: celery -A tasks.celery_app worker --loglevel=info

  flower:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5555:5555"
    environment:
      - DATABASE_URL=**************************************/security_graph_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    command: celery -A tasks.celery_app flower

volumes:
  postgres_data:

---

# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd --create-home --shell /bin/bash app && \
    chown -R app:app /app
USER app

# Expose port
EXPOSE 8000

# Default command
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]

---

# alembic.ini
[alembic]
script_location = alembic
prepend_sys_path = .
version_path_separator = os
sqlalchemy.url = postgresql://postgres:password@localhost/security_graph_db

[post_write_hooks]

[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S

---

# .env.example
# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/security_graph_db

# Redis Configuration (for caching and background tasks)
REDIS_URL=redis://localhost:6379

# Environment
ENVIRONMENT=development

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4

# Security
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# External Services (for enrichment)
VIRUSTOTAL_API_KEY=your-virustotal-api-key
SHODAN_API_KEY=your-shodan-api-key
ABUSE_IPDB_API_KEY=your-abuseipdb-api-key

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_PERIOD=60

---

# config.py
import os
from typing import Optional
from pydantic import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # Database
    database_url: str = "postgresql://postgres:password@localhost:5432/security_graph_db"
    
    # Redis
    redis_url: str = "redis://localhost:6379"
    
    # Environment
    environment: str = "development"
    debug: bool = False
    
    # API
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    api_workers: int = 4
    
    # Security
    secret_key: str = "your-secret-key-here"
    access_token_expire_minutes: int = 30
    
    # External APIs
    virustotal_api_key: Optional[str] = None
    shodan_api_key: Optional[str] = None
    abuse_ipdb_api_key: Optional[str] = None
    
    # Logging
    log_level: str = "INFO"
    log_format: str = "json"
    
    # Rate Limiting
    rate_limit_requests: int = 100
    rate_limit_period: int = 60
    
    # Graph Analysis
    max_graph_depth: int = 10
    blast_radius_cache_ttl: int = 3600  # 1 hour
    enrichment_cache_ttl: int = 21600   # 6 hours
    
    class Config:
        env_file = ".env"
        case_sensitive = False


settings = Settings()

---

# tasks.py
from celery import Celery
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
import asyncio
from typing import List

from .config import settings
from .services.enrichment_service import EnrichmentService
from .services.blast_radius_service import BlastRadiusService
from .database import get_db

# Celery configuration
celery_app = Celery(
    "security_graph",
    broker=settings.redis_url,
    backend=settings.redis_url
)

celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# Database for background tasks
engine = create_engine(settings.database_url)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@celery_app.task
def enrich_asset_task(asset_id: str):
    """Background task for asset enrichment."""
    db = SessionLocal()
    try:
        enrichment_service = EnrichmentService(db)
        # Run async function in sync context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(enrichment_service.enrich_asset(asset_id))
        return {"status": "completed", "asset_id": asset_id, "data": result}
    except Exception as e:
        return {"status": "failed", "asset_id": asset_id, "error": str(e)}
    finally:
        db.close()


@celery_app.task
def bulk_enrich_assets_task(asset_ids: List[str]):
    """Background task for bulk asset enrichment."""
    db = SessionLocal()
    try:
        enrichment_service = EnrichmentService(db)
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        results = loop.run_until_complete(enrichment_service.bulk_enrich_assets(asset_ids))
        return {"status": "completed", "total_assets": len(asset_ids), "results": results}
    except Exception as e:
        return {"status": "failed", "total_assets": len(asset_ids), "error": str(e)}
    finally:
        db.close()


@celery_app.task
def calculate_blast_radius_task(asset_ids: List[str], max_depth: int = 3):
    """Background task for blast radius calculation."""
    db = SessionLocal()
    try:
        blast_service = BlastRadiusService(db)
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(
            blast_service.analyze_asset_blast_radius(asset_ids, max_depth)
        )
        return {"status": "completed", "result": result.dict()}
    except Exception as e:
        return {"status": "failed", "error": str(e)}
    finally:
        db.close()


@celery_app.task
def periodic_risk_recalculation():
    """Periodic task to recalculate risk scores for all assets."""
    db = SessionLocal()
    try:
        # This would implement periodic risk score updates
        # based on new vulnerability data, threat intelligence, etc.
        pass
    finally:
        db.close()

---

# README.md

# Security Graph API

A comprehensive FastAPI application for enterprise security graph mapping, blast radius analysis, and attack path visualization.

## Features

- **Asset Management**: Track and manage all enterprise assets (users, devices, applications, cloud services)
- **Relationship Mapping**: Model complex relationships between assets
- **Blast Radius Analysis**: Calculate potential impact of security incidents
- **Attack Path Discovery**: Identify potential attack vectors through the environment
- **Vulnerability Management**: Track and assess security vulnerabilities
- **Asset Enrichment**: Automatic intelligence gathering and risk assessment
- **Graph Visualization**: API endpoints optimized for security graph visualization
- **Soft Deletes**: Maintain data integrity with soft delete functionality

## Quick Start

### Using Docker Compose (Recommended)

1. Clone the repository
2. Copy environment configuration:
   ```bash
   cp .env.example .env
   ```
3. Start the services:
   ```bash
   docker-compose up -d
   ```
4. Run database migrations:
   ```bash
   docker-compose exec api alembic upgrade head
   ```
5. Access the API documentation at `http://localhost:8000/docs`

### Manual Installation

1. Install PostgreSQL and Redis
2. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```
3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
4. Set up environment variables:
   ```bash
   export DATABASE_URL="postgresql://user:password@localhost:5432/security_graph_db"
   export REDIS_URL="redis://localhost:6379"
   ```
5. Run database migrations:
   ```bash
   alembic upgrade head
   ```
6. Start the application:
   ```bash
   uvicorn main:app --reload
   ```

## API Documentation

### Core Endpoints

#### Assets
- `POST /assets/` - Create a new asset
- `GET /assets/` - List assets with filtering
- `GET /assets/{asset_id}` - Get specific asset
- `PUT /assets/{asset_id}` - Update asset
- `DELETE /assets/{asset_id}` - Soft delete asset
- `GET /assets/critical` - Get critical assets

#### Relationships
- `POST /relationships/` - Create asset relationship
- `GET /relationships/` - List relationships

#### Vulnerabilities
- `POST /vulnerabilities/` - Create vulnerability
- `GET /vulnerabilities/` - List vulnerabilities with filtering

#### Analysis
- `POST /blast-radius/analyze` - Perform blast radius analysis
- `POST /attack-paths/analyze` - Analyze attack paths
- `GET /graph/visualization` - Get graph visualization data

#### Enrichment
- `POST /enrichment/trigger/{asset_id}` - Trigger asset enrichment
- `POST /enrichment/bulk` - Bulk asset enrichment

### Example Usage

#### Creating Assets

```python
import httpx

# Create a user asset
user_asset = {
    "name": "john.doe",
    "asset_type": "user",
    "environment": "prod",
    "business_unit": "engineering",
    "owner": "IT Security",
    "risk_score": 3.5,
    "criticality_level": "medium"
}

response = httpx.post("http://localhost:8000/assets/", json=user_asset)
user_id = response.json()["id"]

# Create a device asset
device_asset = {
    "name": "laptop-001",
    "asset_type": "device",
    "hostname": "laptop-001.company.com",
    "ip_address": "*************",
    "operating_system": "Windows 11",
    "environment": "prod",
    "owner": "john.doe",
    "risk_score": 5.0
}

response = httpx.post("http://localhost:8000/assets/", json=device_asset)
device_id = response.json()["id"]
```

#### Creating Relationships

```python
# Create a relationship between user and device
relationship = {
    "source_asset_id": user_id,
    "target_asset_id": device_id,
    "relationship_type": "accesses",
    "strength": 0.9,
    "environment": "prod"
}

httpx.post("http://localhost:8000/relationships/", json=relationship)
```

#### Blast Radius Analysis

```python
# Analyze blast radius starting from compromised assets
blast_request = {
    "asset_ids": [device_id],
    "max_depth": 3,
    "include_low_risk": False
}

response = httpx.post("http://localhost:8000/blast-radius/analyze", json=blast_request)
blast_result = response.json()

print(f"Total affected assets: {blast_result['total_affected']}")
print(f"Impact score: {blast_result['impact_score']}")
```

#### Graph Visualization

```python
# Get graph data for visualization
response = httpx.get(
    "http://localhost:8000/graph/visualization",
    params={
        "center_asset_id": device_id,
        "depth": 2,
        "include_vulnerabilities": True
    }
)

viz_data = response.json()
nodes = viz_data["nodes"]
edges = viz_data["edges"]
```

## Data Model

### Core Entities

- **Asset**: Any entity in your environment (users, devices, applications, etc.)
- **Relationship**: Connections between assets (accesses, contains, depends_on, etc.)
- **Vulnerability**: Security vulnerabilities affecting assets
- **BlastRadiusAnalysis**: Stored analysis results for performance

### Asset Types
- User accounts
- Physical and virtual devices
- Applications and services
- Containers and cloud workloads
- Cloud services (S3, RDS, etc.)
- Databases
- Network infrastructure
- IoT devices
- API endpoints
- SaaS applications

### Relationship Types
- `accesses`: User or service accessing another asset
- `contains`: Hierarchical containment (server contains applications)
- `connects_to`: Network connectivity
- `depends_on`: Service dependencies
- `manages`: Administrative relationships
- `authenticates_with`: Authentication flows
- `stores_data_in`: Data storage relationships
- `communicates_with`: Communication channels
- `deploys_to`: Deployment relationships

## Configuration

Key configuration options in `.env`:

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/security_graph_db

# Redis (for caching and background tasks)
REDIS_URL=redis://localhost:6379

# External API keys for enrichment
VIRUSTOTAL_API_KEY=your-key
SHODAN_API_KEY=your-key
ABUSE_IPDB_API_KEY=your-key

# Performance tuning
MAX_GRAPH_DEPTH=10
BLAST_RADIUS_CACHE_TTL=3600
ENRICHMENT_CACHE_TTL=21600
```

## Background Tasks

The application uses Celery for background processing:

- Asset enrichment
- Bulk operations
- Periodic risk recalculation
- Large graph analysis

Monitor tasks with Flower: `http://localhost:5555`

## Security Considerations

- All endpoints support proper input validation
- Soft deletes maintain audit trails
- Rate limiting prevents abuse
- Structured logging for security monitoring
- Input sanitization prevents injection attacks

## Performance

- NetworkX for efficient graph operations
- Redis caching for frequently accessed data
- Database indexing for fast queries
- Pagination for large result sets
- Background processing for heavy operations

## Development

### Code Quality
```bash
# Format code
black .
isort .

# Lint
flake8 .
mypy .

# Test
pytest tests/ --cov=.
```

### Database Migrations
```bash
# Create migration
alembic revision --autogenerate -m "description"

# Apply migrations
alembic upgrade head
```

## License

This project follows PEP 8 coding standards and implements comprehensive security graph analysis capabilities for enterprise environments.