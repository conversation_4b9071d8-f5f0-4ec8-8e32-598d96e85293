"""Sphinx configuration for Blast-Radius Security Tool documentation."""

import os
import sys
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('..'))
sys.path.insert(0, os.path.abspath('../backend'))

# Project information
project = 'Blast-Radius Security Tool'
copyright = f'{datetime.now().year}, Blast-Radius Security Tool Team'
author = 'Blast-Radius Security Tool Team'
version = '1.0.0'
release = '1.0.0'

# Project description
project_description = 'Enterprise-grade security platform for attack path analysis, collaborative security operations, and threat modeling'
project_url = 'https://github.com/forkrul/blast-radius'

# General configuration
extensions = [
    'sphinx.ext.autodoc',
    'sphinx.ext.autosummary',
    'sphinx.ext.viewcode',
    'sphinx.ext.napoleon',
    'sphinx.ext.intersphinx',
    'sphinx.ext.todo',
    'sphinx.ext.coverage',
    'sphinx.ext.ifconfig',
    'sphinx.ext.githubpages',
    'sphinx_rtd_theme',
    'myst_parser',
]

# Mermaid configuration (if available)
try:
    import sphinxcontrib.mermaid
    extensions.append('sphinxcontrib.mermaid')
except ImportError:
    pass

# Source file suffixes
source_suffix = ['.rst', '.md']

# Master document
master_doc = 'index'

# Language
language = 'en'

# Exclude patterns
exclude_patterns = [
    '_build',
    'Thumbs.db',
    '.DS_Store',
    '**.ipynb_checkpoints',
    'venv',
    'env',
]

# Pygments style
pygments_style = 'sphinx'

# HTML theme
html_theme = 'sphinx_rtd_theme'
html_theme_options = {
    'analytics_id': '',
    'logo_only': False,
    'display_version': True,
    'prev_next_buttons_location': 'bottom',
    'style_external_links': True,
    'vcs_pageview_mode': 'blob',
    'style_nav_header_background': '#2980B9',
    'collapse_navigation': False,
    'sticky_navigation': True,
    'navigation_depth': 5,
    'includehidden': True,
    'titles_only': False,
    # Enhanced theme options
    'flyout_display': 'attached',
    'version_selector': True,
    'language_selector': True,
}

# HTML base URL (replaces deprecated canonical_url)
html_baseurl = 'https://blast-radius.readthedocs.io/'

# HTML static files
html_static_path = ['_static']
html_css_files = ['custom.css']

# HTML context
html_context = {
    'display_github': True,
    'github_user': 'forkrul',
    'github_repo': 'blast-radius',
    'github_version': 'master',
    'conf_py_path': '/docs/',
}

# HTML options
html_title = f'{project} v{version} - Enterprise Security Platform Documentation'
html_short_title = f'{project} Docs'
# html_logo = '_static/logo.png'  # Commented out - no logo file
# html_favicon = '_static/favicon.ico'  # Commented out - no favicon file
html_show_sourcelink = True
html_show_sphinx = False
html_show_copyright = True

# Enhanced HTML metadata
html_meta = {
    'description': project_description,
    'keywords': 'security platform, attack path analysis, MITRE ATT&CK, purple team, red team, blue team, SOC operations, threat modeling, cybersecurity',
    'author': author,
    'viewport': 'width=device-width, initial-scale=1.0',
    'robots': 'index, follow',
    'og:title': f'{project} - Enterprise Security Platform',
    'og:description': project_description,
    'og:type': 'website',
    'og:url': project_url,
    'twitter:card': 'summary_large_image',
    'twitter:title': f'{project} - Enterprise Security Platform',
    'twitter:description': project_description,
}

# Additional HTML options
html_use_index = True
html_split_index = True
html_copy_source = False
html_show_sourcelink = True
html_sourcelink_suffix = ''
html_use_opensearch = f'{project} Documentation'
html_baseurl = 'https://blast-radius.readthedocs.io/'

# LaTeX output
latex_elements = {
    'papersize': 'letterpaper',
    'pointsize': '10pt',
    'preamble': '',
    'fncychap': '',
    'maketitle': '',
}

latex_documents = [
    (master_doc, 'BlastRadiusSecurityTool.tex', 
     'Blast-Radius Security Tool Documentation',
     'Blast-Radius Security Tool Team', 'manual'),
]

# Manual page output
man_pages = [
    (master_doc, 'blast-radius', 'Blast-Radius Security Tool Documentation',
     [author], 1)
]

# Texinfo output
texinfo_documents = [
    (master_doc, 'BlastRadiusSecurityTool', 'Blast-Radius Security Tool Documentation',
     author, 'BlastRadiusSecurityTool', 'Comprehensive security platform for attack path analysis.',
     'Miscellaneous'),
]

# Autodoc configuration
autodoc_default_options = {
    'members': True,
    'member-order': 'bysource',
    'special-members': '__init__',
    'undoc-members': True,
    'exclude-members': '__weakref__',
    'show-inheritance': True,
}

autodoc_typehints = 'description'
autodoc_typehints_description_target = 'documented'

# Napoleon configuration (for Google/NumPy style docstrings)
napoleon_google_docstring = True
napoleon_numpy_docstring = True
napoleon_include_init_with_doc = False
napoleon_include_private_with_doc = False
napoleon_include_special_with_doc = True
napoleon_use_admonition_for_examples = False
napoleon_use_admonition_for_notes = False
napoleon_use_admonition_for_references = False
napoleon_use_ivar = False
napoleon_use_param = True
napoleon_use_rtype = True
napoleon_preprocess_types = False
napoleon_type_aliases = None
napoleon_attr_annotations = True

# Intersphinx mapping
intersphinx_mapping = {
    'python': ('https://docs.python.org/3', None),
    'fastapi': ('https://fastapi.tiangolo.com', None),
    'sqlalchemy': ('https://docs.sqlalchemy.org/en/20/', None),
    'networkx': ('https://networkx.org/documentation/stable/', None),
}

# Todo configuration
todo_include_todos = True

# Coverage configuration
coverage_show_missing_items = True

# MyST configuration
myst_enable_extensions = [
    'colon_fence',
    'deflist',
    'dollarmath',
    'fieldlist',
    'html_admonition',
    'html_image',
    'replacements',
    'smartquotes',
    'strikethrough',
    'substitution',
    'tasklist',
]

# Mermaid configuration
mermaid_version = "10.6.1"
mermaid_init_js = """
mermaid.initialize({
    startOnLoad: true,
    theme: 'default',
    themeVariables: {
        primaryColor: '#2980B9',
        primaryTextColor: '#ffffff',
        primaryBorderColor: '#1f5f99',
        lineColor: '#34495e',
        sectionBkgColor: '#ecf0f1',
        altSectionBkgColor: '#bdc3c7',
        gridColor: '#95a5a6',
        secondaryColor: '#3498db',
        tertiaryColor: '#e74c3c'
    },
    flowchart: {
        nodeSpacing: 50,
        rankSpacing: 50,
        curve: 'basis'
    },
    sequence: {
        diagramMarginX: 50,
        diagramMarginY: 10,
        actorMargin: 50,
        width: 150,
        height: 65,
        boxMargin: 10,
        boxTextMargin: 5,
        noteMargin: 10,
        messageMargin: 35
    },
    gantt: {
        titleTopMargin: 25,
        barHeight: 20,
        fontFamily: '"Open Sans", sans-serif',
        fontSize: 11,
        gridLineStartPadding: 35,
        bottomPadding: 25,
        leftPadding: 75,
        rightPadding: 50
    }
});
"""

# Custom roles and directives
def setup(app):
    """Sphinx setup function."""
    app.add_css_file('custom.css')
    
    # Add custom roles
    app.add_role('user-role', user_role)
    app.add_role('api-endpoint', api_endpoint)
    app.add_role('permission', permission_role)

def user_role(name, rawtext, text, lineno, inliner, options={}, content=[]):
    """Custom role for user roles."""
    from docutils import nodes
    node = nodes.literal(rawtext, text, classes=['user-role'])
    return [node], []

def api_endpoint(name, rawtext, text, lineno, inliner, options={}, content=[]):
    """Custom role for API endpoints."""
    from docutils import nodes
    node = nodes.literal(rawtext, text, classes=['api-endpoint'])
    return [node], []

def permission_role(name, rawtext, text, lineno, inliner, options={}, content=[]):
    """Custom role for permissions."""
    from docutils import nodes
    node = nodes.literal(rawtext, text, classes=['permission'])
    return [node], []
