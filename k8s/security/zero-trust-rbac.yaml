# Zero-Trust RBAC Configuration for Blast-Radius Security Tool
# This file implements comprehensive Role-Based Access Control (RBAC) 
# following zero-trust principles with least privilege access

apiVersion: v1
kind: Namespace
metadata:
  name: blast-radius
  labels:
    security.blast-radius.com/zone: "application"
    security.blast-radius.com/classification: "restricted"

---
# Service Accounts for Zero-Trust Architecture
apiVersion: v1
kind: ServiceAccount
metadata:
  name: blast-radius-backend
  namespace: blast-radius
  labels:
    app: blast-radius-backend
    component: backend
    security.blast-radius.com/role: "application"
  annotations:
    security.blast-radius.com/description: "Backend service account with minimal required permissions"

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: blast-radius-frontend
  namespace: blast-radius
  labels:
    app: blast-radius-frontend
    component: frontend
    security.blast-radius.com/role: "application"
  annotations:
    security.blast-radius.com/description: "Frontend service account with read-only permissions"

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: blast-radius-worker
  namespace: blast-radius
  labels:
    app: blast-radius-worker
    component: worker
    security.blast-radius.com/role: "worker"
  annotations:
    security.blast-radius.com/description: "Worker service account for background tasks"

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: blast-radius-monitoring
  namespace: blast-radius
  labels:
    app: blast-radius-monitoring
    component: monitoring
    security.blast-radius.com/role: "monitoring"
  annotations:
    security.blast-radius.com/description: "Monitoring service account with metrics access"

---
# Cluster Roles with Minimal Permissions
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: blast-radius-backend-cluster-role
  labels:
    security.blast-radius.com/scope: "cluster"
rules:
# Minimal cluster-level permissions for backend
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["get", "list"]
  resourceNames: [] # Restrict to specific nodes if needed
- apiGroups: ["metrics.k8s.io"]
  resources: ["nodes", "pods"]
  verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: blast-radius-monitoring-cluster-role
  labels:
    security.blast-radius.com/scope: "cluster"
rules:
# Monitoring permissions
- apiGroups: [""]
  resources: ["nodes", "nodes/metrics", "services", "endpoints", "pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["extensions", "apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["metrics.k8s.io"]
  resources: ["nodes", "pods"]
  verbs: ["get", "list"]

---
# Namespace-Scoped Roles
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: blast-radius
  name: blast-radius-backend-role
  labels:
    security.blast-radius.com/scope: "namespace"
rules:
# Backend application permissions
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
  resourceNames: ["blast-radius-config", "blast-radius-features"]
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list"]
  resourceNames: ["database-credentials", "redis-credentials", "app-secrets"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list"]
  resourceNames: ["blast-radius-backend", "blast-radius-worker"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: blast-radius
  name: blast-radius-frontend-role
  labels:
    security.blast-radius.com/scope: "namespace"
rules:
# Frontend minimal permissions (read-only)
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
  resourceNames: ["blast-radius-config", "nginx-config"]
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list"]
  resourceNames: ["blast-radius-backend"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: blast-radius
  name: blast-radius-worker-role
  labels:
    security.blast-radius.com/scope: "namespace"
rules:
# Worker permissions for background tasks
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
  resourceNames: ["blast-radius-config"]
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list"]
  resourceNames: ["database-credentials", "redis-credentials", "app-secrets", "external-api-keys"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "create", "delete"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["get", "list", "create", "delete"]
- apiGroups: [""]
  resources: ["persistentvolumeclaims"]
  verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: blast-radius
  name: blast-radius-monitoring-role
  labels:
    security.blast-radius.com/scope: "namespace"
rules:
# Monitoring permissions within namespace
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets", "statefulsets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["extensions"]
  resources: ["ingresses"]
  verbs: ["get", "list", "watch"]

---
# Role Bindings
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: blast-radius-backend-cluster-binding
  labels:
    security.blast-radius.com/scope: "cluster"
subjects:
- kind: ServiceAccount
  name: blast-radius-backend
  namespace: blast-radius
roleRef:
  kind: ClusterRole
  name: blast-radius-backend-cluster-role
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: blast-radius-monitoring-cluster-binding
  labels:
    security.blast-radius.com/scope: "cluster"
subjects:
- kind: ServiceAccount
  name: blast-radius-monitoring
  namespace: blast-radius
roleRef:
  kind: ClusterRole
  name: blast-radius-monitoring-cluster-role
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: blast-radius-backend-binding
  namespace: blast-radius
  labels:
    security.blast-radius.com/scope: "namespace"
subjects:
- kind: ServiceAccount
  name: blast-radius-backend
  namespace: blast-radius
roleRef:
  kind: Role
  name: blast-radius-backend-role
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: blast-radius-frontend-binding
  namespace: blast-radius
  labels:
    security.blast-radius.com/scope: "namespace"
subjects:
- kind: ServiceAccount
  name: blast-radius-frontend
  namespace: blast-radius
roleRef:
  kind: Role
  name: blast-radius-frontend-role
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: blast-radius-worker-binding
  namespace: blast-radius
  labels:
    security.blast-radius.com/scope: "namespace"
subjects:
- kind: ServiceAccount
  name: blast-radius-worker
  namespace: blast-radius
roleRef:
  kind: Role
  name: blast-radius-worker-role
  apiGroup: rbac.authorization.k8s.io

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: blast-radius-monitoring-binding
  namespace: blast-radius
  labels:
    security.blast-radius.com/scope: "namespace"
subjects:
- kind: ServiceAccount
  name: blast-radius-monitoring
  namespace: blast-radius
roleRef:
  kind: Role
  name: blast-radius-monitoring-role
  apiGroup: rbac.authorization.k8s.io

---
# Pod Security Policy for Zero-Trust
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: blast-radius-psp
  labels:
    security.blast-radius.com/policy: "zero-trust"
  annotations:
    seccomp.security.alpha.kubernetes.io/allowedProfileNames: 'runtime/default'
    seccomp.security.alpha.kubernetes.io/defaultProfileName: 'runtime/default'
    apparmor.security.beta.kubernetes.io/allowedProfileNames: 'runtime/default'
    apparmor.security.beta.kubernetes.io/defaultProfileName: 'runtime/default'
spec:
  # Prevent privileged containers
  privileged: false
  allowPrivilegeEscalation: false
  
  # Require non-root user
  runAsUser:
    rule: 'MustRunAsNonRoot'
  
  # Require specific user ID range
  runAsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: 1000
        max: 65535
  
  # File system group
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: 1000
        max: 65535
  
  # SELinux
  seLinux:
    rule: 'RunAsAny'
  
  # Volumes
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  
  # Host network/ports
  hostNetwork: false
  hostIPC: false
  hostPID: false
  hostPorts:
    - min: 0
      max: 0
  
  # Capabilities
  allowedCapabilities: []
  defaultAddCapabilities: []
  requiredDropCapabilities:
    - ALL
  
  # Read-only root filesystem
  readOnlyRootFilesystem: true
  
  # Forbidden sysctls
  forbiddenSysctls:
    - '*'

---
# ClusterRole for PSP usage
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: blast-radius-psp-user
  labels:
    security.blast-radius.com/policy: "zero-trust"
rules:
- apiGroups: ['policy']
  resources: ['podsecuritypolicies']
  verbs: ['use']
  resourceNames:
  - blast-radius-psp

---
# Bind PSP to service accounts
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: blast-radius-psp-binding
  labels:
    security.blast-radius.com/policy: "zero-trust"
roleRef:
  kind: ClusterRole
  name: blast-radius-psp-user
  apiGroup: rbac.authorization.k8s.io
subjects:
- kind: ServiceAccount
  name: blast-radius-backend
  namespace: blast-radius
- kind: ServiceAccount
  name: blast-radius-frontend
  namespace: blast-radius
- kind: ServiceAccount
  name: blast-radius-worker
  namespace: blast-radius
- kind: ServiceAccount
  name: blast-radius-monitoring
  namespace: blast-radius
