Glossary
========

This glossary defines key terms and concepts used in the Blast-Radius Security Tool, with particular focus on Continuous Threat Exposure Management (CTEM) and cybersecurity frameworks. Where applicable, NIST references and equivalent terms are provided.

.. note::
   **CTEM Integration**: The Blast-Radius Security Tool implements Continuous Threat Exposure Management (CTEM) principles to provide ongoing assessment and management of security exposure across your infrastructure.

A
-

**Asset Discovery**
   The automated process of identifying and cataloging all digital assets within an organization's infrastructure, including cloud resources, network devices, applications, and data stores. In the Blast-Radius platform, this includes multi-cloud discovery across AWS, Azure, and GCP.
   
   *NIST Reference*: `NIST CSF ID.AM-1 <https://www.nist.gov/cyberframework/online-learning/components-framework/identify-function>`_ - "Physical devices and systems within the organization are inventoried"

**Attack Path**
   A sequence of connected vulnerabilities, misconfigurations, or security weaknesses that an attacker could exploit to move from an initial compromise point to a target asset. The Blast-Radius platform analyzes multi-hop attack paths up to 10 degrees of separation.
   
   *Related Terms*: Attack Vector, Lateral Movement, Kill Chain

**Attack Path Analysis**
   The systematic examination of potential routes an attacker could take through an organization's infrastructure to reach critical assets. This includes analyzing network connectivity, access permissions, vulnerabilities, and security controls.
   
   *API Endpoint*: ``/api/v1/attack-paths/analyze``

**Attack Surface**
   The sum of all possible attack vectors and entry points that an attacker could use to gain unauthorized access to a system or network. The Blast-Radius platform continuously monitors and maps the attack surface across multi-cloud environments.
   
   *NIST Reference*: `NIST SP 800-53 SI-4 <https://csrc.nist.gov/Projects/risk-management/sp800-53-controls/release-search#!/control?version=5.1&number=SI-4>`_ - Information System Monitoring

B
-

**Blast Radius**
   The scope of potential damage or compromise that could result from a successful attack on a specific asset. This includes all systems, data, and resources that could be affected through lateral movement and privilege escalation.
   
   *API Endpoint*: ``/api/v1/attack-paths/blast-radius``
   
   *Calculation*: Measured in degrees of separation from the initial compromise point, with risk scoring based on asset criticality and connectivity.

**Business Impact Analysis (BIA)**
   Assessment of the potential consequences of security incidents on business operations, including financial impact, operational disruption, and reputational damage. The platform correlates technical vulnerabilities with business impact metrics.
   
   *NIST Reference*: `NIST SP 800-34 <https://csrc.nist.gov/publications/detail/sp/800-34/rev-1/final>`_ - Contingency Planning Guide

C
-

**Compliance Automation**
   The use of automated tools and processes to continuously monitor, assess, and report on compliance with regulatory frameworks and security standards. The Blast-Radius platform supports NIST CSF, SOC 2, ISO 27001, and GDPR.
   
   *API Endpoint*: ``/api/v1/compliance/frameworks``
   
   *NIST Reference*: `NIST CSF PR.IP-1 <https://www.nist.gov/cyberframework/online-learning/components-framework/protect-function>`_ - "A baseline configuration of information technology/industrial control systems is created and maintained"

**Continuous Threat Exposure Management (CTEM)**
   A systematic approach to continuously assess, prioritize, and mitigate security exposures across an organization's attack surface. CTEM focuses on understanding and reducing the likelihood and impact of successful attacks.
   
   *CTEM Phases*:
   1. **Scoping**: Define and prioritize assets and attack surfaces
   2. **Discovery**: Identify vulnerabilities and exposures
   3. **Prioritization**: Risk-based ranking of exposures
   4. **Validation**: Confirm exploitability and impact
   5. **Mobilization**: Coordinate remediation efforts

**Critical Asset**
   A system, application, or data repository that is essential to business operations and would cause significant impact if compromised. The platform automatically identifies critical assets based on connectivity, data sensitivity, and business importance.
   
   *Classification Criteria*: Business criticality, data sensitivity, regulatory requirements, operational dependencies

D
-

**Data Subject Rights**
   Individual rights under data protection regulations (such as GDPR) including access, rectification, erasure, and portability of personal data. The platform provides automated workflows for managing data subject requests.
   
   *API Endpoint*: ``/api/v1/compliance/gdpr/data-subject-request``
   
   *GDPR Articles*: Articles 15-22 of the General Data Protection Regulation

**Detection Coverage**
   The extent to which security monitoring and detection capabilities can identify threats and attacks across the organization's infrastructure. Measured as a percentage of attack techniques covered by security controls.
   
   *NIST Reference*: `NIST CSF DE.CM-1 <https://www.nist.gov/cyberframework/online-learning/components-framework/detect-function>`_ - "The network is monitored to detect potential cybersecurity events"

E
-

**Exposure**
   A security weakness or vulnerability that could be exploited by a threat actor to gain unauthorized access or cause damage. Exposures include unpatched vulnerabilities, misconfigurations, and excessive privileges.
   
   *Types*: Technical vulnerabilities, configuration weaknesses, access control gaps, network exposures

**Exposure Management**
   The continuous process of identifying, assessing, prioritizing, and remediating security exposures across an organization's infrastructure. Core component of CTEM methodology.

F
-

**Framework Mapping**
   The process of aligning security controls and requirements across multiple compliance frameworks to identify overlaps, gaps, and optimization opportunities. The platform provides automated mapping between NIST CSF, SOC 2, and ISO 27001.
   
   *API Endpoint*: ``/api/v1/compliance/mapping``

G
-

**GDPR Compliance**
   Adherence to the General Data Protection Regulation requirements for processing personal data of EU residents. The platform provides automated data discovery, consent management, and breach notification capabilities.
   
   *Key Requirements*: Lawful basis, data minimization, purpose limitation, accuracy, storage limitation, integrity and confidentiality

I
-

**Incident Response**
   The structured approach to addressing and managing security incidents, including preparation, detection, analysis, containment, eradication, and recovery. The platform integrates with TheHive for automated incident management.
   
   *NIST Reference*: `NIST SP 800-61 <https://csrc.nist.gov/publications/detail/sp/800-61/rev-2/final>`_ - Computer Security Incident Handling Guide
   
   *API Endpoint*: ``/api/v1/incidents/response``

**Indicator of Compromise (IOC)**
   Artifacts or evidence that suggest a security incident or malicious activity has occurred. The platform correlates IOCs with MITRE ATT&CK techniques and threat intelligence feeds.
   
   *Types*: File hashes, IP addresses, domain names, network signatures, behavioral patterns

L
-

**Lateral Movement**
   The technique used by attackers to move through a network after initial compromise, seeking to access additional systems and escalate privileges. The platform models lateral movement paths through network and access relationships.
   
   *MITRE ATT&CK*: `Tactic TA0008 <https://attack.mitre.org/tactics/TA0008/>`_ - Lateral Movement

**Least Privilege**
   The security principle of granting users and systems only the minimum access rights necessary to perform their functions. The platform provides automated least privilege analysis and recommendations.
   
   *NIST Reference*: `NIST SP 800-53 AC-6 <https://csrc.nist.gov/Projects/risk-management/sp800-53-controls/release-search#!/control?version=5.1&number=AC-6>`_ - Least Privilege
   
   *API Endpoint*: ``/api/v1/access-control/least-privilege``

M
-

**MITRE ATT&CK**
   A globally-accessible knowledge base of adversary tactics and techniques based on real-world observations. The platform integrates ATT&CK framework for threat modeling and detection coverage analysis.
   
   *API Endpoint*: ``/api/v1/mitre-attack/techniques``
   
   *Framework URL*: `MITRE ATT&CK <https://attack.mitre.org/>`_

**Multi-Cloud Security**
   Security practices and controls that span multiple cloud service providers (AWS, Azure, GCP) to ensure consistent protection across hybrid and multi-cloud environments.
   
   *Challenges*: Unified visibility, consistent policies, cross-cloud attack paths, compliance alignment

N
-

**NIST Cybersecurity Framework (CSF)**
   A voluntary framework consisting of standards, guidelines, and best practices to manage cybersecurity-related risk. The platform provides automated CSF assessment and reporting capabilities.
   
   *Functions*: Identify, Protect, Detect, Respond, Recover
   
   *API Endpoint*: ``/api/v1/compliance/nist-csf``
   
   *Framework URL*: `NIST CSF <https://www.nist.gov/cyberframework>`_

P
-

**Privileged Access Management (PAM)**
   The practice of securing, controlling, and monitoring access to critical systems and sensitive data by privileged users. The platform provides PAM integration and privileged access analytics.
   
   *NIST Reference*: `NIST SP 800-53 AC-2 <https://csrc.nist.gov/Projects/risk-management/sp800-53-controls/release-search#!/control?version=5.1&number=AC-2>`_ - Account Management

R
-

**Risk Assessment**
   The systematic process of identifying, analyzing, and evaluating cybersecurity risks to organizational operations, assets, individuals, and other organizations.
   
   *NIST Reference*: `NIST SP 800-30 <https://csrc.nist.gov/publications/detail/sp/800-30/rev-1/final>`_ - Guide for Conducting Risk Assessments
   
   *Components*: Threat identification, vulnerability assessment, impact analysis, likelihood determination

**Risk Score**
   A quantitative measure of cybersecurity risk calculated based on threat likelihood, vulnerability severity, and potential business impact. The platform uses a 0-10 scale with automated risk scoring algorithms.
   
   *Calculation Factors*: Asset criticality, vulnerability severity, threat intelligence, exposure metrics, control effectiveness

S
-

**Security Control**
   A safeguard or countermeasure prescribed for an information system or organization to protect the confidentiality, integrity, and availability of information and information systems.
   
   *NIST Reference*: `NIST SP 800-53 <https://csrc.nist.gov/Projects/risk-management/sp800-53-controls>`_ - Security and Privacy Controls
   
   *Types*: Technical, administrative, physical controls

**Security Orchestration, Automation and Response (SOAR)**
   Technology solutions that enable organizations to collect security data and alerts from various sources and respond to security events through automated playbooks and workflows.
   
   *Integration*: TheHive platform integration for automated incident response and case management

T
-

**Threat Actor**
   An individual or group that carries out cyberattacks with specific motivations, capabilities, and tactics. The platform models threat actor behavior for attack simulation and risk assessment.
   
   *Categories*: Nation-state, cybercriminal, hacktivist, insider threat, script kiddie
   
   *MITRE ATT&CK*: `Groups <https://attack.mitre.org/groups/>`_ - Threat actor profiles and TTPs

**Threat Hunting**
   The proactive search for threats and malicious activity within an organization's environment, using hypothesis-driven approaches and advanced analytics.
   
   *NIST Reference*: `NIST SP 800-150 <https://csrc.nist.gov/publications/detail/sp/800-150/final>`_ - Guide to Cyber Threat Information Sharing

**Threat Intelligence**
   Evidence-based knowledge about existing or emerging threats that can inform security decisions and improve defensive capabilities.
   
   *Types*: Strategic, tactical, technical, operational intelligence
   
   *Standards*: STIX/TAXII for threat intelligence sharing

**Threat Modeling**
   The systematic approach to identifying, quantifying, and addressing security threats to applications, systems, or business processes.
   
   *API Endpoint*: ``/api/v1/threat-modeling/scenarios``
   
   *Methodologies*: STRIDE, PASTA, OCTAVE, TRIKE

V
-

**Vulnerability**
   A weakness in a system, application, or network that could be exploited by a threat actor to gain unauthorized access or cause damage.
   
   *NIST Reference*: `NIST SP 800-40 <https://csrc.nist.gov/publications/detail/sp/800-40/rev-4/final>`_ - Guide to Enterprise Patch Management Planning
   
   *Classification*: CVE (Common Vulnerabilities and Exposures) identifiers, CVSS scoring

**Vulnerability Management**
   The continuous process of identifying, classifying, prioritizing, and remediating vulnerabilities in systems and applications.
   
   *Process*: Discovery, assessment, prioritization, remediation, verification

Z
-

**Zero Trust Architecture**
   A security model that requires strict identity verification for every person and device trying to access resources on a private network, regardless of whether they are sitting within or outside of the network perimeter.
   
   *NIST Reference*: `NIST SP 800-207 <https://csrc.nist.gov/publications/detail/sp/800-207/final>`_ - Zero Trust Architecture
   
   *Principles*: Never trust, always verify; least privilege access; assume breach

**Zero Trust Network Access (ZTNA)**
   A security solution that provides secure remote access to applications and services based on defined access control policies, regardless of user location or device.

Additional Resources
--------------------

**NIST Cybersecurity Framework**: https://www.nist.gov/cyberframework

**MITRE ATT&CK Framework**: https://attack.mitre.org/

**NIST Special Publications**: https://csrc.nist.gov/publications/sp

**CTEM Methodology**: Continuous Threat Exposure Management best practices and implementation guidance

**API Documentation**: Complete API reference available at ``/api/v1/docs``
