<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Testing &amp; Quality Assurance &mdash; Blast-Radius Security Tool v1.0.0 - Enterprise Security Platform Documentation</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=fa44fd50" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=19f00094" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ac1674a3" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ac1674a3" />

  
    <link rel="canonical" href="https://blast-radius.readthedocs.io/testing/index.html" />
  <!--[if lt IE 9]>
    <script src="../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script src="../_static/jquery.js?v=5d32c60e"></script>
        <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
        <script src="../_static/documentation_options.js?v=528dc5d9"></script>
        <script src="../_static/doctools.js?v=9a2dae69"></script>
        <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="search" type="application/opensearchdescription+xml"
          title="Search within Blast-Radius Security Tool v1.0.0 - Enterprise Security Platform Documentation"
          href="../_static/opensearch.xml"/>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Search" href="../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Blast-Radius Security Tool
          </a>
              <div class="version">
                1.0.0
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">🎉 Production Status</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../production-readiness-status.html">Production Readiness Status</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../production-readiness-status.html#executive-summary">🎯 Executive Summary</a></li>
<li class="toctree-l2"><a class="reference internal" href="../production-readiness-status.html#documentation-quality-metrics">📊 Documentation Quality Metrics</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../production-readiness-status.html#sphinx-build-quality">Sphinx Build Quality</a></li>
<li class="toctree-l3"><a class="reference internal" href="../production-readiness-status.html#content-coverage">Content Coverage</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../production-readiness-status.html#architecture-security-status">🏗️ Architecture &amp; Security Status</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../production-readiness-status.html#zero-trust-architecture">Zero-Trust Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../production-readiness-status.html#gdpr-compliance-framework">GDPR Compliance Framework</a></li>
<li class="toctree-l3"><a class="reference internal" href="../production-readiness-status.html#enhanced-audit-logging">Enhanced Audit Logging</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../production-readiness-status.html#security-compliance-frameworks">🔐 Security &amp; Compliance Frameworks</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../production-readiness-status.html#multi-framework-compliance">Multi-Framework Compliance</a></li>
<li class="toctree-l3"><a class="reference internal" href="../production-readiness-status.html#least-privilege-access-control">Least Privilege Access Control</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../production-readiness-status.html#advanced-technical-capabilities">🧠 Advanced Technical Capabilities</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../production-readiness-status.html#ml-threat-prediction">ML Threat Prediction</a></li>
<li class="toctree-l3"><a class="reference internal" href="../production-readiness-status.html#thehive-integration">TheHive Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../production-readiness-status.html#production-architecture">Production Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../production-readiness-status.html#deployment-readiness">🚀 Deployment Readiness</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../production-readiness-status.html#infrastructure-requirements">Infrastructure Requirements</a></li>
<li class="toctree-l3"><a class="reference internal" href="../production-readiness-status.html#documentation-completeness">Documentation Completeness</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../production-readiness-status.html#next-steps-for-production-deployment">📈 Next Steps for Production Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../production-readiness-status.html#immediate-actions">Immediate Actions</a></li>
<li class="toctree-l3"><a class="reference internal" href="../production-readiness-status.html#long-term-roadmap">Long-term Roadmap</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../production-readiness-status.html#conclusion">🎉 Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../latest-implementations-summary.html">Latest Implementations Summary - Production Ready Release</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#implementation-achievements">Implementation Achievements</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#infrastructure-as-code-iac-100-complete">Infrastructure as Code (IaC) - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#zero-trust-architecture-100-complete">Zero-Trust Architecture - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#gdpr-compliance-framework-100-complete">GDPR Compliance Framework - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#enhanced-audit-logging-100-complete">Enhanced Audit Logging - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#least-privilege-access-control-100-complete">Least Privilege Access Control - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#production-deployment-infrastructure-100-complete">Production Deployment Infrastructure - 100% Complete ✅</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#documentation-operations-100-complete">Documentation &amp; Operations - 100% Complete ✅</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#technical-specifications">Technical Specifications</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#performance-characteristics">Performance Characteristics</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#security-specifications">Security Specifications</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#compliance-coverage">Compliance Coverage</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#deployment-architecture">Deployment Architecture</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#next-steps-and-recommendations">Next Steps and Recommendations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#immediate-actions">Immediate Actions</a></li>
<li class="toctree-l3"><a class="reference internal" href="../latest-implementations-summary.html#long-term-roadmap">Long-term Roadmap</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../latest-implementations-summary.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">🚀 Getting Started</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation.html">Installation Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#prerequisites">Prerequisites</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#system-requirements">System Requirements</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#software-dependencies">Software Dependencies</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#installation-methods">Installation Methods</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#method-1-quick-start-with-docker-recommended">Method 1: Quick Start with Docker (Recommended)</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#method-2-development-installation">Method 2: Development Installation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#method-3-production-installation">Method 3: Production Installation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#configuration">Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#environment-variables">Environment Variables</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#initial-setup">Initial Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#creating-admin-user">Creating Admin User</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#configuring-integrations">Configuring Integrations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#verification">Verification</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#health-checks">Health Checks</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#performance-testing">Performance Testing</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../installation.html#getting-help">Getting Help</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../installation.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../configuration.html">Configuration Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#core-configuration">Core Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#application-settings">Application Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#database-configuration">Database Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#security-configuration">Security Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#authentication-settings">Authentication Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#encryption-settings">Encryption Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#access-control-settings">Access Control Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#integration-configuration">Integration Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#cloud-provider-settings">Cloud Provider Settings</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#servicenow-integration">ServiceNow Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#threat-intelligence-settings">Threat Intelligence Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#monitoring-and-logging">Monitoring and Logging</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#logging-configuration">Logging Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#monitoring-settings">Monitoring Settings</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#environment-specific-configuration">Environment-Specific Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#development-environment">Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#staging-environment">Staging Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#configuration-validation">Configuration Validation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#security-best-practices">Security Best Practices</a></li>
<li class="toctree-l3"><a class="reference internal" href="../configuration.html#performance-best-practices">Performance Best Practices</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../configuration.html#troubleshooting">Troubleshooting</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../quick-start-guide.html">Quick Start Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#prerequisites">Prerequisites</a></li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#quick-installation">Quick Installation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-1-get-the-code">Step 1: Get the Code</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-2-start-the-platform">Step 2: Start the Platform</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-3-access-the-platform">Step 3: Access the Platform</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#first-login-and-setup">First Login and Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#initial-login">Initial Login</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#password-change">Password Change</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#dashboard-overview">Dashboard Overview</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#basic-configuration">Basic Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#user-profile-setup">User Profile Setup</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#multi-factor-authentication-recommended">Multi-Factor Authentication (Recommended)</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#your-first-attack-path-analysis">Your First Attack Path Analysis</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-1-add-sample-assets">Step 1: Add Sample Assets</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-2-run-attack-path-analysis">Step 2: Run Attack Path Analysis</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#step-3-view-results">Step 3: View Results</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#understanding-the-visualization">Understanding the Visualization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#essential-features-tour">Essential Features Tour</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#real-time-monitoring">Real-Time Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#asset-management">Asset Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#user-and-role-management">User and Role Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#threat-intelligence">Threat Intelligence</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#integration-setup">Integration Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#cloud-provider-integration">Cloud Provider Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#servicenow-integration">ServiceNow Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#next-steps">Next Steps</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#immediate-actions">Immediate Actions</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#learning-resources">Learning Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#advanced-configuration">Advanced Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#common-quick-start-issues">Common Quick Start Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#platform-won-t-start">Platform Won’t Start</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#can-t-access-web-interface">Can’t Access Web Interface</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#login-issues">Login Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick-start-guide.html#performance-issues">Performance Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#getting-help">Getting Help</a></li>
<li class="toctree-l2"><a class="reference internal" href="../quick-start-guide.html#congratulations">Congratulations!</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📊 Documentation Excellence</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../documentation-overview.html">Documentation Overview and Achievements</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#documentation-excellence-achievements">🏆 Documentation Excellence Achievements</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#comprehensive-user-guide-ecosystem">📚 Comprehensive User Guide Ecosystem</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#key-documentation-features">🎯 Key Documentation Features</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#measurable-impact-and-value">📈 Measurable Impact and Value</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#industry-leadership-and-recognition">🌟 Industry Leadership and Recognition</a></li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-overview.html#future-enhancements-and-roadmap">🚀 Future Enhancements and Roadmap</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../documentation-achievements-summary.html">Documentation Achievements Summary</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../documentation-achievements-summary.html#major-achievements">🎉 Major Achievements</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../documentation-achievements-summary.html#zero-sphinx-warnings-achievement">Zero Sphinx Warnings Achievement</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-achievements-summary.html#comprehensive-documentation-ecosystem">📚 Comprehensive Documentation Ecosystem</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../documentation-achievements-summary.html#enterprise-user-guides-6-000-lines">Enterprise User Guides (6,000+ Lines)</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-achievements-summary.html#technical-architecture-documentation">🏗️ Technical Architecture Documentation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../documentation-achievements-summary.html#zero-trust-architecture">Zero-Trust Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../documentation-achievements-summary.html#gdpr-compliance-framework">GDPR Compliance Framework</a></li>
<li class="toctree-l3"><a class="reference internal" href="../documentation-achievements-summary.html#enhanced-audit-logging">Enhanced Audit Logging</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-achievements-summary.html#security-compliance-documentation">🔐 Security &amp; Compliance Documentation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../documentation-achievements-summary.html#least-privilege-access-control">Least Privilege Access Control</a></li>
<li class="toctree-l3"><a class="reference internal" href="../documentation-achievements-summary.html#production-architecture">Production Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-achievements-summary.html#advanced-technical-specifications">🧠 Advanced Technical Specifications</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../documentation-achievements-summary.html#ml-threat-prediction">ML Threat Prediction</a></li>
<li class="toctree-l3"><a class="reference internal" href="../documentation-achievements-summary.html#thehive-integration">TheHive Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../documentation-achievements-summary.html#compliance-framework-schema">Compliance Framework Schema</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-achievements-summary.html#documentation-quality-metrics">📊 Documentation Quality Metrics</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../documentation-achievements-summary.html#production-readiness-indicators">Production Readiness Indicators</a></li>
<li class="toctree-l3"><a class="reference internal" href="../documentation-achievements-summary.html#documentation-structure">Documentation Structure</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../documentation-achievements-summary.html#production-deployment-ready">🚀 Production Deployment Ready</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">📚 Enterprise User Guides</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../user-guides/index.html">User Guides</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#role-based-guides">Role-Based Guides</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#soc-operators">SOC Operators</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/soc-operators.html">SOC Operators Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#executive-summary">Executive Summary</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#role-definition-and-responsibilities">Role Definition and Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#core-platform-capabilities-for-soc-operations">Core Platform Capabilities for SOC Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#getting-started-soc-operator-onboarding">Getting Started: SOC Operator Onboarding</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#advanced-real-time-monitoring-and-threat-detection">Advanced Real-time Monitoring and Threat Detection</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#comprehensive-incident-response-framework">Comprehensive Incident Response Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#advanced-reporting-and-documentation-framework">Advanced Reporting and Documentation Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#soc-operations-best-practices-and-excellence-framework">SOC Operations Best Practices and Excellence Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#comprehensive-scenario-based-training-and-response-procedures">Comprehensive Scenario-Based Training and Response Procedures</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#comprehensive-support-resources-and-professional-development">Comprehensive Support Resources and Professional Development</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/soc-operators.html#conclusion-excellence-in-soc-operations">Conclusion: Excellence in SOC Operations</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#security-architects">Security Architects</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/security-architects.html">Security Architects Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#executive-summary-for-security-architects">Executive Summary for Security Architects</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-platform-capabilities-for-security-architecture">Advanced Platform Capabilities for Security Architecture</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#enterprise-security-architecture-methodology">Enterprise Security Architecture Methodology</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-risk-assessment-and-quantitative-analysis-framework">Advanced Risk Assessment and Quantitative Analysis Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-security-architecture-design-and-implementation">Advanced Security Architecture Design and Implementation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#comprehensive-compliance-and-governance-framework">Comprehensive Compliance and Governance Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#advanced-analytics-and-executive-communication-framework">Advanced Analytics and Executive Communication Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#enterprise-security-architecture-best-practices-and-excellence">Enterprise Security Architecture Best Practices and Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#professional-development-and-support-resources">Professional Development and Support Resources</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/security-architects.html#conclusion-excellence-in-security-architecture-leadership">Conclusion: Excellence in Security Architecture Leadership</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#red-team-members">Red Team Members</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/red-team-members.html">Red Team Members Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#executive-summary-for-red-team-operations">Executive Summary for Red Team Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#role-definition-and-offensive-security-responsibilities">Role Definition and Offensive Security Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-red-team-platform-capabilities">Advanced Red Team Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#comprehensive-red-team-methodology-and-campaign-framework">Comprehensive Red Team Methodology and Campaign Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-attack-path-discovery-and-exploitation-framework">Advanced Attack Path Discovery and Exploitation Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-attack-simulation-and-specialized-testing-scenarios">Advanced Attack Simulation and Specialized Testing Scenarios</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-reporting-and-performance-metrics-framework">Advanced Reporting and Performance Metrics Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#red-team-best-practices-and-operational-excellence">Red Team Best Practices and Operational Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#advanced-troubleshooting-and-technical-support">Advanced Troubleshooting and Technical Support</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/red-team-members.html#conclusion-excellence-in-red-team-operations">Conclusion: Excellence in Red Team Operations</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#purple-team-members">Purple Team Members</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/purple-team-members.html">Purple Team Members Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#executive-summary-for-purple-team-excellence">Executive Summary for Purple Team Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#advanced-purple-team-platform-capabilities">Advanced Purple Team Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#comprehensive-purple-team-methodology-and-framework">Comprehensive Purple Team Methodology and Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#advanced-detection-engineering-and-security-control-optimization">Advanced Detection Engineering and Security Control Optimization</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#comprehensive-purple-team-scenarios-and-implementation-framework">Comprehensive Purple Team Scenarios and Implementation Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#advanced-troubleshooting-and-technical-support-framework">Advanced Troubleshooting and Technical Support Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/purple-team-members.html#conclusion-excellence-in-purple-team-operations-and-collaborative-security">Conclusion: Excellence in Purple Team Operations and Collaborative Security</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#compliance-officers">Compliance Officers</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/compliance-officers.html">Compliance Officers Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#executive-summary-for-compliance-excellence">Executive Summary for Compliance Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-compliance-platform-capabilities">Advanced Compliance Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-regulatory-framework-management">Advanced Regulatory Framework Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#automated-compliance-monitoring-and-management">Automated Compliance Monitoring and Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#comprehensive-audit-management-and-preparation">Comprehensive Audit Management and Preparation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-risk-assessment-and-management-framework">Advanced Risk Assessment and Management Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#cross-functional-collaboration-and-integration">Cross-Functional Collaboration and Integration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#professional-development-and-excellence-framework">Professional Development and Excellence Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#advanced-support-and-troubleshooting-framework">Advanced Support and Troubleshooting Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/compliance-officers.html#conclusion-excellence-in-compliance-and-governance">Conclusion: Excellence in Compliance and Governance</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#executive-leadership">Executive Leadership</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/executive-leadership.html">Executive Leadership Comprehensive Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#executive-summary-for-strategic-security-leadership">Executive Summary for Strategic Security Leadership</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#role-definition-and-strategic-responsibilities">Role Definition and Strategic Responsibilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#advanced-executive-security-platform-capabilities">Advanced Executive Security Platform Capabilities</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#strategic-security-risk-management-framework">Strategic Security Risk Management Framework</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#business-aligned-security-performance-management">Business-Aligned Security Performance Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#strategic-security-investment-and-resource-management">Strategic Security Investment and Resource Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#crisis-management-and-business-continuity">Crisis Management and Business Continuity</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#regulatory-compliance-and-governance-excellence">Regulatory Compliance and Governance Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#strategic-security-communication-and-stakeholder-management">Strategic Security Communication and Stakeholder Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#digital-transformation-and-innovation-enablement">Digital Transformation and Innovation Enablement</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#professional-development-and-executive-excellence">Professional Development and Executive Excellence</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#advanced-support-and-advisory-services">Advanced Support and Advisory Services</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/executive-leadership.html#conclusion-excellence-in-executive-security-leadership">Conclusion: Excellence in Executive Security Leadership</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#administrators">Administrators</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/administrators.html">Administrators Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#dashboard-overview">Dashboard Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#getting-started">Getting Started</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#user-and-access-management">User and Access Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#system-configuration-and-management">System Configuration and Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#monitoring-and-maintenance">Monitoring and Maintenance</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#security-and-compliance">Security and Compliance</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#platform-updates-and-maintenance">Platform Updates and Maintenance</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#troubleshooting-and-support">Troubleshooting and Support</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#best-practices-for-platform-administration">Best Practices for Platform Administration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/administrators.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#feature-specific-guides">Feature-Specific Guides</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#attack-path-analysis">Attack Path Analysis</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../user-guides/attack-path-analysis.html">Attack Path Analysis User Guide</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#getting-started">Getting Started</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#core-concepts">Core Concepts</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#using-the-web-interface">Using the Web Interface</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#using-the-api">Using the API</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#use-cases-and-workflows">Use Cases and Workflows</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#best-practices">Best Practices</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#troubleshooting">Troubleshooting</a></li>
<li class="toctree-l5"><a class="reference internal" href="../user-guides/attack-path-analysis.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#threat-modeling">Threat Modeling</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#asset-discovery-and-management">Asset Discovery and Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#threat-intelligence-integration">Threat Intelligence Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#common-workflows">Common Workflows</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#security-assessment-workflow">Security Assessment Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#incident-response-workflow">Incident Response Workflow</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#red-team-exercise-workflow">Red Team Exercise Workflow</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#general-guidelines">General Guidelines</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#role-specific-tips">Role-Specific Tips</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../user-guides/index.html#getting-help">Getting Help</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#support-resources">Support Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#training-and-certification">Training and Certification</a></li>
<li class="toctree-l3"><a class="reference internal" href="../user-guides/index.html#feedback-and-contributions">Feedback and Contributions</a></li>
</ul>
</li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">API Reference</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../api/index.html">API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#base-url">Base URL</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#authentication">Authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#api-endpoints">API Endpoints</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#authentication-user-management">Authentication &amp; User Management</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/authentication.html">Authentication API</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#authentication-flow">Authentication Flow</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#security-and-authorization">Security and Authorization</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#integration-examples">Integration Examples</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/authentication.html#best-practices">Best Practices</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#asset-management">Asset Management</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/asset-management.html">Asset Management API</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-crud-operations">Asset CRUD Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-discovery">Asset Discovery</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#bulk-operations">Bulk Operations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-relationships">Asset Relationships</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#asset-tags-and-metadata">Asset Tags and Metadata</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#error-handling">Error Handling</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/asset-management.html#sdk-examples">SDK Examples</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#attack-path-analysis">Attack Path Analysis</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/attack-path-analysis.html">Attack Path Analysis API</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#endpoints">Endpoints</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#error-responses">Error Responses</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#rate-limiting">Rate Limiting</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#performance-considerations">Performance Considerations</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#best-practices">Best Practices</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/attack-path-analysis.html#examples">Examples</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#threat-modeling-risk-assessment">Threat Modeling &amp; Risk Assessment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/threat-modeling.html">Threat Modeling API Reference</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#threat-actor-simulation">Threat Actor Simulation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#risk-assessment">Risk Assessment</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#compliance-impact-analysis">Compliance Impact Analysis</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#mitigation-strategies">Mitigation Strategies</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#continuous-monitoring">Continuous Monitoring</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#error-handling">Error Handling</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/threat-modeling.html#rate-limiting">Rate Limiting</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../api/mitre-attack-integration.html">MITRE ATT&amp;CK Integration API Reference</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#overview">Overview</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#base-url">Base URL</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#authentication">Authentication</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#att-ck-data-management">ATT&amp;CK Data Management</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#technique-correlation">Technique Correlation</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#threat-actor-attribution">Threat Actor Attribution</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#attack-pattern-analysis">Attack Pattern Analysis</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#att-ck-navigator-integration">ATT&amp;CK Navigator Integration</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#threat-intelligence-enrichment">Threat Intelligence Enrichment</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#error-handling">Error Handling</a></li>
<li class="toctree-l5"><a class="reference internal" href="../api/mitre-attack-integration.html#rate-limiting">Rate Limiting</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#threat-intelligence">Threat Intelligence</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#monitoring-dashboards">Monitoring &amp; Dashboards</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#integrations">Integrations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#common-response-formats">Common Response Formats</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#success-response">Success Response</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#error-response">Error Response</a></li>
<li class="toctree-l3"><a class="reference internal" href="../api/index.html#http-status-codes">HTTP Status Codes</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#pagination">Pagination</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#filtering-and-sorting">Filtering and Sorting</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#field-selection">Field Selection</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#webhooks">Webhooks</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#sdk-and-libraries">SDK and Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#testing-and-development">Testing and Development</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#support-and-resources">Support and Resources</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#best-practices">Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="../api/index.html#examples">Examples</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Development</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../development/contributing.html">Contributing Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#getting-started">Getting Started</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#ways-to-contribute">Ways to Contribute</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#before-you-start">Before You Start</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#contribution-workflow">Contribution Workflow</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-1-find-or-create-an-issue">Step 1: Find or Create an Issue</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-2-fork-and-clone">Step 2: Fork and Clone</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-3-create-a-branch">Step 3: Create a Branch</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-4-make-changes">Step 4: Make Changes</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-5-test-your-changes">Step 5: Test Your Changes</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#step-6-submit-pull-request">Step 6: Submit Pull Request</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#code-standards">Code Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#python-code-style">Python Code Style</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#javascript-typescript-style">JavaScript/TypeScript Style</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#testing-guidelines">Testing Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#test-structure">Test Structure</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#documentation-standards">Documentation Standards</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#documentation-types">Documentation Types</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#writing-guidelines">Writing Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#review-process">Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#code-review-checklist">Code Review Checklist</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#review-guidelines">Review Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#community-guidelines">Community Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#code-of-conduct">Code of Conduct</a></li>
<li class="toctree-l3"><a class="reference internal" href="../development/contributing.html#communication">Communication</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#recognition">Recognition</a></li>
<li class="toctree-l2"><a class="reference internal" href="../development/contributing.html#getting-help">Getting Help</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Use Cases</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../use-cases/asset-discovery.html">Asset Discovery Use Cases</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#discovery-methods">Discovery Methods</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#cloud-provider-discovery">Cloud Provider Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#network-based-discovery">Network-Based Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#api-discovery">API Discovery</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#use-case-scenarios">Use Case Scenarios</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#scenario-1-multi-cloud-asset-inventory">Scenario 1: Multi-Cloud Asset Inventory</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#scenario-2-continuous-asset-monitoring">Scenario 2: Continuous Asset Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#scenario-3-compliance-asset-reporting">Scenario 3: Compliance Asset Reporting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#advanced-discovery-features">Advanced Discovery Features</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#agent-based-discovery">Agent-Based Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#container-discovery">Container Discovery</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#discovery-automation">Discovery Automation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#scheduled-discovery">Scheduled Discovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#integration-with-external-tools">Integration with External Tools</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#discovery-strategy">Discovery Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#performance-optimization">Performance Optimization</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#security-considerations">Security Considerations</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/asset-discovery.html#common-issues">Common Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/asset-discovery.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../use-cases/attack-path-analysis.html">Attack Path Analysis Use Cases</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#key-capabilities">Key Capabilities</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#analysis-types">Analysis Types</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#use-case-scenarios">Use Case Scenarios</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-1-red-team-attack-simulation">Scenario 1: Red Team Attack Simulation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-2-purple-team-validation-exercise">Scenario 2: Purple Team Validation Exercise</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-3-compliance-risk-assessment">Scenario 3: Compliance Risk Assessment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-4-incident-response-planning">Scenario 4: Incident Response Planning</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#scenario-5-zero-trust-architecture-planning">Scenario 5: Zero Trust Architecture Planning</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#advanced-analysis-features">Advanced Analysis Features</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#risk-scoring-and-prioritization">Risk Scoring and Prioritization</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#mitre-att-ck-integration">MITRE ATT&amp;CK Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#integration-and-automation">Integration and Automation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#siem-integration">SIEM Integration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#soar-integration">SOAR Integration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#analysis-guidelines">Analysis Guidelines</a></li>
<li class="toctree-l3"><a class="reference internal" href="../use-cases/attack-path-analysis.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../use-cases/attack-path-analysis.html#next-steps">Next Steps</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Architecture</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../architecture/zero-trust-architecture.html">Zero-Trust Architecture Implementation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#identity-verification-service">Identity Verification Service</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#service-account-management">Service Account Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#tls-1-3-enforcement">TLS 1.3+ Enforcement</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#real-time-security-event-correlation">Real-Time Security Event Correlation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#network-security">Network Security</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#kubernetes-network-policies">Kubernetes Network Policies</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#rbac-implementation">RBAC Implementation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#data-protection">Data Protection</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#encryption-implementation">Encryption Implementation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#data-classification">Data Classification</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#monitoring-and-alerting">Monitoring and Alerting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#security-monitoring">Security Monitoring</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#implementation-guidelines">Implementation Guidelines</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#deployment-checklist">Deployment Checklist</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#security-validation">Security Validation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#security-principles">Security Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#implementation-tips">Implementation Tips</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../architecture/zero-trust-architecture.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../architecture/zero-trust-architecture.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../deployment/production-architecture.html">Production Architecture Overview</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#kubernetes-infrastructure">Kubernetes Infrastructure</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#application-architecture">Application Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#data-architecture">Data Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#network-architecture">Network Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#security-architecture">Security Architecture</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#zero-trust-security-implementation">Zero-Trust Security Implementation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#monitoring-architecture">Monitoring Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#deployment-strategy">Deployment Strategy</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#blue-green-deployment">Blue-Green Deployment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#infrastructure-as-code">Infrastructure as Code</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#performance-specifications">Performance Specifications</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#scalability-metrics">Scalability Metrics</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#resource-requirements">Resource Requirements</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#disaster-recovery">Disaster Recovery</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#backup-and-recovery-strategy">Backup and Recovery Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#high-availability-design">High Availability Design</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#operational-excellence">Operational Excellence</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#security-best-practices">Security Best Practices</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-architecture.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-architecture.html#conclusion">Conclusion</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Security &amp; Compliance</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../security/enhanced-audit-logging.html">Enhanced Audit Logging System</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-event-structure">Audit Event Structure</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#tamper-proof-audit-chain">Tamper-Proof Audit Chain</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#event-enrichment-pipeline">Event Enrichment Pipeline</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#real-time-monitoring">Real-Time Monitoring</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#compliance-integration">Compliance Integration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#framework-mapping">Framework Mapping</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#compliance-reporting">Compliance Reporting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-service-architecture">Audit Service Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#database-schema">Database Schema</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#configuration">Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#monitoring-and-alerting">Monitoring and Alerting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-monitoring-dashboard">Audit Monitoring Dashboard</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#alert-configuration">Alert Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#audit-strategy">Audit Strategy</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#operational-guidelines">Operational Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/enhanced-audit-logging.html#performance-tuning">Performance Tuning</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/enhanced-audit-logging.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../security/incident-response-procedures.html">Incident Response Procedures - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#incident-classification">Incident Classification</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#severity-levels">Severity Levels</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#critical-p0">Critical (P0)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#high-p1">High (P1)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#medium-p2">Medium (P2)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#low-p3">Low (P3)</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#incident-response-team">Incident Response Team</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#roles-and-responsibilities">Roles and Responsibilities</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#incident-commander-ic">Incident Commander (IC)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#technical-lead">Technical Lead</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#security-lead">Security Lead</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#communications-lead">Communications Lead</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#incident-response-process">Incident Response Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-1-detection-and-initial-response-0-15-minutes">Phase 1: Detection and Initial Response (0-15 minutes)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#incident-detection">1.1 Incident Detection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#initial-assessment">1.2 Initial Assessment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#team-notification">1.3 Team Notification</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-2-investigation-and-containment-15-minutes-2-hours">Phase 2: Investigation and Containment (15 minutes - 2 hours)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#incident-war-room-setup">2.1 Incident War Room Setup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#technical-investigation">2.2 Technical Investigation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#security-assessment-if-applicable">2.3 Security Assessment (if applicable)</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-3-resolution-and-recovery-2-8-hours">Phase 3: Resolution and Recovery (2-8 hours)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#fix-implementation">3.1 Fix Implementation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#system-recovery">3.2 System Recovery</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-4-communication-and-documentation-ongoing">Phase 4: Communication and Documentation (Ongoing)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#status-updates">4.1 Status Updates</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#resolution-communication">4.2 Resolution Communication</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#phase-5-post-incident-review-24-72-hours-after-resolution">Phase 5: Post-Incident Review (24-72 hours after resolution)</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#post-mortem-meeting">5.1 Post-Mortem Meeting</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#documentation">5.2 Documentation</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#security-specific-procedures">Security-Specific Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#data-breach-response">Data Breach Response</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#immediate-actions-0-1-hour">Immediate Actions (0-1 hour)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#notification-requirements">Notification Requirements</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#vulnerability-response">Vulnerability Response</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#critical-vulnerabilities-cvss-9-0">Critical Vulnerabilities (CVSS 9.0+)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/incident-response-procedures.html#high-vulnerabilities-cvss-7-0-8-9">High Vulnerabilities (CVSS 7.0-8.9)</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#emergency-contacts">Emergency Contacts</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#internal-team">Internal Team</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#external-contacts">External Contacts</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#tools-and-resources">Tools and Resources</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#monitoring-and-alerting">Monitoring and Alerting</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#communication">Communication</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#id1">Documentation</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/incident-response-procedures.html#training-and-drills">Training and Drills</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#regular-training">Regular Training</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/incident-response-procedures.html#documentation-updates">Documentation Updates</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../security/security-review-processes.html">Security Review Processes - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#security-review-framework">Security Review Framework</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-types">Review Types</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#code-security-review">1. Code Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#infrastructure-security-review">2. Infrastructure Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#architecture-security-review">3. Architecture Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#compliance-review">4. Compliance Review</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#code-security-review-process">Code Security Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#pre-review-checklist">Pre-Review Checklist</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#developer-responsibilities">Developer Responsibilities</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-criteria">Review Criteria</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#input-validation-and-sanitization">1. Input Validation and Sanitization</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#authentication-and-authorization">2. Authentication and Authorization</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#data-protection">3. Data Protection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#error-handling-and-logging">4. Error Handling and Logging</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#cryptography-and-secrets">5. Cryptography and Secrets</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-process-workflow">Review Process Workflow</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#step-1-automated-security-checks">Step 1: Automated Security Checks</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#step-2-manual-security-review">Step 2: Manual Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#step-3-security-testing">Step 3: Security Testing</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#id1">Infrastructure Security Review</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#infrastructure-as-code-iac-review">Infrastructure as Code (IaC) Review</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#terraform-security-checklist">Terraform Security Checklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#example-review-process">Example Review Process</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#kubernetes-security-review">Kubernetes Security Review</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#manifest-security-checklist">Manifest Security Checklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#example-security-policies">Example Security Policies</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#id2">Architecture Security Review</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#design-review-process">Design Review Process</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#threat-modeling">1. Threat Modeling</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#security-architecture-patterns">2. Security Architecture Patterns</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#integration-security">3. Integration Security</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#review-documentation-template">Review Documentation Template</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#compliance-review-process">Compliance Review Process</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#regulatory-requirements">Regulatory Requirements</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#gdpr-compliance-checklist">GDPR Compliance Checklist</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#soc-2-compliance-checklist">SOC 2 Compliance Checklist</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#compliance-review-workflow">Compliance Review Workflow</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#change-impact-assessment">1. Change Impact Assessment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-review-processes.html#documentation-requirements">2. Documentation Requirements</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#security-review-tools-and-automation">Security Review Tools and Automation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#automated-security-scanning">Automated Security Scanning</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#security-metrics-and-kpis">Security Metrics and KPIs</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/security-review-processes.html#training-and-awareness">Training and Awareness</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#security-review-training">Security Review Training</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-review-processes.html#documentation-and-resources">Documentation and Resources</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html">GDPR Compliance Framework</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-subject-rights-management">Data Subject Rights Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#consent-management-system">Consent Management System</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-processing-records">Data Processing Records</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-breach-management">Data Breach Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#privacy-impact-assessment">Privacy Impact Assessment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#gdpr-service-architecture">GDPR Service Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#data-processing-workflows">Data Processing Workflows</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#compliance-monitoring">Compliance Monitoring</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#automated-compliance-checks">Automated Compliance Checks</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#compliance-reporting">Compliance Reporting</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#privacy-by-design-implementation">Privacy by Design Implementation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#operational-guidelines">Operational Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#technical-implementation">Technical Implementation</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#database-schema">Database Schema</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#configuration-example">Configuration Example</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../compliance/gdpr-compliance-framework.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../access-control/least-privilege-framework.html">Least Privilege Access Control Framework</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#core-components">Core Components</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-request-management">Access Request Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#just-in-time-access-provisioning">Just-in-Time Access Provisioning</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#privilege-escalation-management">Privilege Escalation Management</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-review-and-certification">Access Review and Certification</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#implementation-details">Implementation Details</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-control-architecture">Access Control Architecture</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#api-endpoints">API Endpoints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#role-based-access-templates">Role-Based Access Templates</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#risk-assessment-engine">Risk Assessment Engine</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#monitoring-and-analytics">Monitoring and Analytics</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-usage-monitoring">Access Usage Monitoring</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-analytics-dashboard">Access Analytics Dashboard</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#configuration-management">Configuration Management</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#policy-configuration">Policy Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#role-template-configuration">Role Template Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#best-practices">Best Practices</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#access-management-principles">Access Management Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#operational-guidelines">Operational Guidelines</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#common-issues">Common Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../access-control/least-privilege-framework.html#performance-optimization">Performance Optimization</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../access-control/least-privilege-framework.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../security/index.html">Security Documentation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../security/security-summary.html">Security Documentation Summary</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#executive-overview">Executive Overview</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#security-documentation-structure">Security Documentation Structure</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#recent-security-achievements">Recent Security Achievements</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#june-2025-security-review-results">June 2025 Security Review Results</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#key-security-fixes-implemented">Key Security Fixes Implemented</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#security-architecture-excellence">Security Architecture Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#multi-layered-security-approach">Multi-Layered Security Approach</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#security-testing-excellence">Security Testing Excellence</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#compliance-and-regulatory-excellence">Compliance and Regulatory Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#regulatory-compliance-status">Regulatory Compliance Status</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#industry-standards-compliance">Industry Standards Compliance</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#security-operations-excellence">Security Operations Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#vulnerability-management-program">Vulnerability Management Program</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#security-monitoring-and-incident-response">Security Monitoring and Incident Response</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#risk-management-excellence">Risk Management Excellence</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#enterprise-risk-assessment">Enterprise Risk Assessment</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#business-impact-and-value">Business Impact and Value</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#security-investment-roi">Security Investment ROI</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#future-security-roadmap">Future Security Roadmap</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#short-term-objectives-q3-q4-2025">Short-Term Objectives (Q3-Q4 2025)</a></li>
<li class="toctree-l4"><a class="reference internal" href="../security/security-summary.html#long-term-strategic-goals-2026">Long-Term Strategic Goals (2026)</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../security/security-summary.html#conclusion">Conclusion</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#overview">Overview</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../security/index.html#security-principles">Security Principles</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/index.html#security-framework">Security Framework</a></li>
<li class="toctree-l3"><a class="reference internal" href="../security/index.html#security-architecture">Security Architecture</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#security-documentation-sections">Security Documentation Sections</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#security-certifications-and-compliance">Security Certifications and Compliance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#security-contact-information">Security Contact Information</a></li>
<li class="toctree-l2"><a class="reference internal" href="../security/index.html#quick-security-reference">Quick Security Reference</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Deployment &amp; Operations</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../deployment/production-deployment-guide.html">Production Deployment Guide - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#prerequisites">Prerequisites</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#infrastructure-requirements">Infrastructure Requirements</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#minimum-system-requirements">Minimum System Requirements</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#network-requirements">Network Requirements</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#external-dependencies">External Dependencies</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#required-tools-and-access">Required Tools and Access</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#development-tools">Development Tools</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#aws-access-requirements">AWS Access Requirements</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#infrastructure-deployment">Infrastructure Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-1-environment-configuration">Step 1: Environment Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-aws-cli">1.1 Configure AWS CLI</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#set-environment-variables">1.2 Set Environment Variables</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-2-infrastructure-provisioning">Step 2: Infrastructure Provisioning</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#deploy-core-infrastructure">2.1 Deploy Core Infrastructure</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-kubectl-access">2.2 Configure kubectl Access</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#install-cluster-add-ons">2.3 Install Cluster Add-ons</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-3-database-setup">Step 3: Database Setup</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-initialization">3.1 Database Initialization</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-migration">3.2 Database Migration</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#application-deployment">Application Deployment</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-4-container-image-preparation">Step 4: Container Image Preparation</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#build-and-push-images">4.1 Build and Push Images</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-5-kubernetes-deployment">Step 5: Kubernetes Deployment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#create-namespace-and-secrets">5.1 Create Namespace and Secrets</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#deploy-application-components">5.2 Deploy Application Components</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-ingress-and-load-balancer">5.3 Configure Ingress and Load Balancer</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-6-ssl-tls-configuration">Step 6: SSL/TLS Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#request-ssl-certificate">6.1 Request SSL Certificate</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-https-redirect">6.2 Configure HTTPS Redirect</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#post-deployment-configuration">Post-Deployment Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-7-application-configuration">Step 7: Application Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#initialize-application-data">7.1 Initialize Application Data</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#configure-monitoring-and-alerting">7.2 Configure Monitoring and Alerting</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-8-security-hardening">Step 8: Security Hardening</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#network-policies">8.1 Network Policies</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#pod-security-policies">8.2 Pod Security Policies</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#verification-and-testing">Verification and Testing</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-9-deployment-verification">Step 9: Deployment Verification</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#health-checks">9.1 Health Checks</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#functional-testing">9.2 Functional Testing</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#security-testing">9.3 Security Testing</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#backup-and-recovery-setup">Backup and Recovery Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-10-backup-configuration">Step 10: Backup Configuration</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-backup">10.1 Database Backup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#application-data-backup">10.2 Application Data Backup</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#monitoring-and-maintenance">Monitoring and Maintenance</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#step-11-ongoing-monitoring">Step 11: Ongoing Monitoring</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#set-up-alerts">11.1 Set Up Alerts</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/production-deployment-guide.html#performance-monitoring">11.2 Performance Monitoring</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#troubleshooting-common-issues">Troubleshooting Common Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-connection-issues">Database Connection Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#pod-startup-issues">Pod Startup Issues</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#load-balancer-issues">Load Balancer Issues</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#rollback-procedures">Rollback Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#emergency-rollback">Emergency Rollback</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/production-deployment-guide.html#database-rollback">Database Rollback</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/production-deployment-guide.html#next-steps">Next Steps</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../deployment/environment-setup.html">Environment Setup Documentation - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#environment-types">Environment Types</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#development-environment">Development Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#staging-environment">Staging Environment</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#production-environment">Production Environment</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#local-development-setup">Local Development Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#prerequisites">Prerequisites</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#system-requirements">System Requirements</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#required-software">Required Software</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#development-environment-setup">Development Environment Setup</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#clone-repository">1. Clone Repository</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#environment-configuration">2. Environment Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#backend-setup">3. Backend Setup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#frontend-setup">4. Frontend Setup</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#start-development-services">5. Start Development Services</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#verify-development-setup">6. Verify Development Setup</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#staging-environment-setup">Staging Environment Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#infrastructure-provisioning">Infrastructure Provisioning</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#aws-configuration">1. AWS Configuration</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#terraform-deployment">2. Terraform Deployment</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#kubernetes-configuration">3. Kubernetes Configuration</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#application-deployment">Application Deployment</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#build-and-push-images">1. Build and Push Images</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#deploy-to-staging">2. Deploy to Staging</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#configure-staging-data">3. Configure Staging Data</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#production-environment-setup">Production Environment Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#pre-production-checklist">Pre-Production Checklist</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#security-review">Security Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#performance-review">Performance Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#operational-readiness">Operational Readiness</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#production-deployment-process">Production Deployment Process</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#final-infrastructure-review">1. Final Infrastructure Review</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#production-image-preparation">2. Production Image Preparation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/environment-setup.html#blue-green-deployment">3. Blue-Green Deployment</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#environment-specific-configurations">Environment-Specific Configurations</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#development-configuration">Development Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#staging-configuration">Staging Configuration</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#production-configuration">Production Configuration</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#ci-cd-pipeline-configuration">CI/CD Pipeline Configuration</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#github-actions-workflow">GitHub Actions Workflow</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/environment-setup.html#environment-management-scripts">Environment Management Scripts</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#deployment-script">Deployment Script</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/environment-setup.html#environment-cleanup-script">Environment Cleanup Script</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../deployment/troubleshooting-guide.html">Troubleshooting Guide - Blast-Radius Security Tool</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#general-troubleshooting-approach">General Troubleshooting Approach</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#information-gathering">1. Information Gathering</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#log-analysis">2. Log Analysis</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#network-connectivity">3. Network Connectivity</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#common-issues-and-solutions">Common Issues and Solutions</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#application-startup-issues">Application Startup Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-pod-stuck-in-pending-state">Issue: Pod Stuck in Pending State</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-pod-stuck-in-crashloopbackoff">Issue: Pod Stuck in CrashLoopBackOff</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#database-issues">Database Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-database-connection-timeout">Issue: Database Connection Timeout</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-database-migration-failures">Issue: Database Migration Failures</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#network-and-connectivity-issues">Network and Connectivity Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-service-not-accessible">Issue: Service Not Accessible</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-ssl-tls-certificate-problems">Issue: SSL/TLS Certificate Problems</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#performance-issues">Performance Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-high-response-times">Issue: High Response Times</a></li>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-memory-leaks">Issue: Memory Leaks</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#storage-issues">Storage Issues</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-persistent-volume-problems">Issue: Persistent Volume Problems</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#monitoring-and-alerting-issues">Monitoring and Alerting Issues</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#issue-missing-metrics">Issue: Missing Metrics</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#emergency-procedures">Emergency Procedures</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#complete-system-recovery">Complete System Recovery</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#rollback-procedure">Rollback Procedure</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../deployment/troubleshooting-guide.html#getting-help">Getting Help</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#internal-resources">Internal Resources</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#external-support">External Support</a></li>
<li class="toctree-l3"><a class="reference internal" href="../deployment/troubleshooting-guide.html#escalation-procedures">Escalation Procedures</a></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Blast-Radius Security Tool</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content style-external-links">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Testing &amp; Quality Assurance</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/forkrul/blast-radius/blob/master/docs/testing/index.rst" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="testing-quality-assurance">
<h1>Testing &amp; Quality Assurance<a class="headerlink" href="#testing-quality-assurance" title="Link to this heading"></a></h1>
<p>Comprehensive testing documentation for the Blast-Radius Security Tool, covering all aspects of quality assurance from unit tests to security validation.</p>
<nav class="contents local" id="table-of-contents">
<p class="topic-title">Table of Contents</p>
<ul class="simple">
<li><p><a class="reference internal" href="#overview" id="id1">Overview</a></p></li>
<li><p><a class="reference internal" href="#testing-philosophy" id="id2">Testing Philosophy</a></p></li>
<li><p><a class="reference internal" href="#test-categories" id="id3">Test Categories</a></p>
<ul>
<li><p><a class="reference internal" href="#unit-testing" id="id4">Unit Testing</a></p></li>
<li><p><a class="reference internal" href="#integration-testing" id="id5">Integration Testing</a></p></li>
<li><p><a class="reference internal" href="#end-to-end-testing" id="id6">End-to-End Testing</a></p></li>
<li><p><a class="reference internal" href="#security-testing" id="id7">Security Testing</a></p></li>
<li><p><a class="reference internal" href="#performance-testing" id="id8">Performance Testing</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#test-automation" id="id9">Test Automation</a></p>
<ul>
<li><p><a class="reference internal" href="#ci-cd-pipeline" id="id10">CI/CD Pipeline</a></p></li>
<li><p><a class="reference internal" href="#quality-gates" id="id11">Quality Gates</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#test-data-management" id="id12">Test Data Management</a></p>
<ul>
<li><p><a class="reference internal" href="#fixtures-and-factories" id="id13">Fixtures and Factories</a></p></li>
<li><p><a class="reference internal" href="#test-environment-setup" id="id14">Test Environment Setup</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#running-tests" id="id15">Running Tests</a></p>
<ul>
<li><p><a class="reference internal" href="#local-development" id="id16">Local Development</a></p></li>
<li><p><a class="reference internal" href="#continuous-integration" id="id17">Continuous Integration</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#test-reporting" id="id18">Test Reporting</a></p>
<ul>
<li><p><a class="reference internal" href="#coverage-reports" id="id19">Coverage Reports</a></p></li>
<li><p><a class="reference internal" href="#quality-metrics" id="id20">Quality Metrics</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#best-practices" id="id21">Best Practices</a></p>
<ul>
<li><p><a class="reference internal" href="#test-writing-guidelines" id="id22">Test Writing Guidelines</a></p></li>
<li><p><a class="reference internal" href="#maintenance" id="id23">Maintenance</a></p></li>
</ul>
</li>
<li><p><a class="reference internal" href="#next-steps" id="id24">Next Steps</a></p></li>
</ul>
</nav>
<section id="overview">
<h2><a class="toc-backref" href="#id1" role="doc-backlink">Overview</a><a class="headerlink" href="#overview" title="Link to this heading"></a></h2>
<p>The Blast-Radius Security Tool maintains enterprise-grade quality through:</p>
<ul class="simple">
<li><p><strong>95%+ test coverage</strong> across all components</p></li>
<li><p><strong>Multi-layered testing strategy</strong> from unit to integration</p></li>
<li><p><strong>Automated security testing</strong> with SAST/DAST tools</p></li>
<li><p><strong>Performance testing</strong> for scalability validation</p></li>
<li><p><strong>Continuous integration</strong> with automated quality gates</p></li>
</ul>
</section>
<section id="testing-philosophy">
<h2><a class="toc-backref" href="#id2" role="doc-backlink">Testing Philosophy</a><a class="headerlink" href="#testing-philosophy" title="Link to this heading"></a></h2>
<p><strong>Quality Principles:</strong></p>
<ol class="arabic simple">
<li><p><strong>Test Early, Test Often</strong> - Shift-left testing approach</p></li>
<li><p><strong>Comprehensive Coverage</strong> - Unit, integration, and end-to-end tests</p></li>
<li><p><strong>Security First</strong> - Security testing integrated throughout</p></li>
<li><p><strong>Performance Validation</strong> - Load and stress testing</p></li>
<li><p><strong>Automated Quality Gates</strong> - No manual testing bottlenecks</p></li>
</ol>
<p><strong>Testing Pyramid:</strong></p>
</section>
<section id="test-categories">
<h2><a class="toc-backref" href="#id3" role="doc-backlink">Test Categories</a><a class="headerlink" href="#test-categories" title="Link to this heading"></a></h2>
<section id="unit-testing">
<h3><a class="toc-backref" href="#id4" role="doc-backlink">Unit Testing</a><a class="headerlink" href="#unit-testing" title="Link to this heading"></a></h3>
<p><strong>Coverage Requirements:</strong></p>
<ul class="simple">
<li><p><strong>Minimum Coverage:</strong> 90% for all modules</p></li>
<li><p><strong>Critical Components:</strong> 95% coverage required</p></li>
<li><p><strong>New Code:</strong> 100% coverage for new features</p></li>
</ul>
<p><strong>Testing Framework:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># pytest configuration</span>
<span class="c1"># pytest.ini</span>
<span class="p">[</span><span class="n">tool</span><span class="p">:</span><span class="n">pytest</span><span class="p">]</span>
<span class="n">testpaths</span> <span class="o">=</span> <span class="n">tests</span>
<span class="n">python_files</span> <span class="o">=</span> <span class="n">test_</span><span class="o">*.</span><span class="n">py</span>
<span class="n">python_classes</span> <span class="o">=</span> <span class="n">Test</span><span class="o">*</span>
<span class="n">python_functions</span> <span class="o">=</span> <span class="n">test_</span><span class="o">*</span>
<span class="n">addopts</span> <span class="o">=</span>
    <span class="o">--</span><span class="n">cov</span><span class="o">=</span><span class="n">app</span>
    <span class="o">--</span><span class="n">cov</span><span class="o">-</span><span class="n">report</span><span class="o">=</span><span class="n">html</span>
    <span class="o">--</span><span class="n">cov</span><span class="o">-</span><span class="n">report</span><span class="o">=</span><span class="n">term</span><span class="o">-</span><span class="n">missing</span>
    <span class="o">--</span><span class="n">cov</span><span class="o">-</span><span class="n">fail</span><span class="o">-</span><span class="n">under</span><span class="o">=</span><span class="mi">90</span>
    <span class="o">--</span><span class="n">strict</span><span class="o">-</span><span class="n">markers</span>
    <span class="o">--</span><span class="n">disable</span><span class="o">-</span><span class="n">warnings</span>
</pre></div>
</div>
<p><strong>Example Unit Test:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># tests/test_attack_path_service.py</span>
<span class="kn">import</span> <span class="nn">pytest</span>
<span class="kn">from</span> <span class="nn">app.services.attack_path_service</span> <span class="kn">import</span> <span class="n">AttackPathService</span>
<span class="kn">from</span> <span class="nn">app.models.asset</span> <span class="kn">import</span> <span class="n">Asset</span>

<span class="k">class</span> <span class="nc">TestAttackPathService</span><span class="p">:</span>

    <span class="nd">@pytest</span><span class="o">.</span><span class="n">fixture</span>
    <span class="k">def</span> <span class="nf">service</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="n">AttackPathService</span><span class="p">()</span>

    <span class="nd">@pytest</span><span class="o">.</span><span class="n">fixture</span>
    <span class="k">def</span> <span class="nf">sample_assets</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="p">[</span>
            <span class="n">Asset</span><span class="p">(</span><span class="nb">id</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">name</span><span class="o">=</span><span class="s2">&quot;web-server&quot;</span><span class="p">,</span> <span class="nb">type</span><span class="o">=</span><span class="s2">&quot;server&quot;</span><span class="p">),</span>
            <span class="n">Asset</span><span class="p">(</span><span class="nb">id</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">name</span><span class="o">=</span><span class="s2">&quot;database&quot;</span><span class="p">,</span> <span class="nb">type</span><span class="o">=</span><span class="s2">&quot;database&quot;</span><span class="p">)</span>
        <span class="p">]</span>

    <span class="k">def</span> <span class="nf">test_find_attack_paths_success</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">service</span><span class="p">,</span> <span class="n">sample_assets</span><span class="p">):</span>
        <span class="c1"># Arrange</span>
        <span class="n">source</span> <span class="o">=</span> <span class="n">sample_assets</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
        <span class="n">target</span> <span class="o">=</span> <span class="n">sample_assets</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span>

        <span class="c1"># Act</span>
        <span class="n">paths</span> <span class="o">=</span> <span class="n">service</span><span class="o">.</span><span class="n">find_attack_paths</span><span class="p">(</span><span class="n">source</span><span class="p">,</span> <span class="n">target</span><span class="p">,</span> <span class="n">max_depth</span><span class="o">=</span><span class="mi">5</span><span class="p">)</span>

        <span class="c1"># Assert</span>
        <span class="k">assert</span> <span class="nb">len</span><span class="p">(</span><span class="n">paths</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">0</span>
        <span class="k">assert</span> <span class="nb">all</span><span class="p">(</span><span class="n">path</span><span class="o">.</span><span class="n">source_id</span> <span class="o">==</span> <span class="n">source</span><span class="o">.</span><span class="n">id</span> <span class="k">for</span> <span class="n">path</span> <span class="ow">in</span> <span class="n">paths</span><span class="p">)</span>
        <span class="k">assert</span> <span class="nb">all</span><span class="p">(</span><span class="n">path</span><span class="o">.</span><span class="n">target_id</span> <span class="o">==</span> <span class="n">target</span><span class="o">.</span><span class="n">id</span> <span class="k">for</span> <span class="n">path</span> <span class="ow">in</span> <span class="n">paths</span><span class="p">)</span>

    <span class="k">def</span> <span class="nf">test_find_attack_paths_no_path_exists</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">service</span><span class="p">):</span>
        <span class="c1"># Test edge case where no path exists</span>
        <span class="n">source</span> <span class="o">=</span> <span class="n">Asset</span><span class="p">(</span><span class="nb">id</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">name</span><span class="o">=</span><span class="s2">&quot;isolated&quot;</span><span class="p">,</span> <span class="nb">type</span><span class="o">=</span><span class="s2">&quot;server&quot;</span><span class="p">)</span>
        <span class="n">target</span> <span class="o">=</span> <span class="n">Asset</span><span class="p">(</span><span class="nb">id</span><span class="o">=</span><span class="mi">2</span><span class="p">,</span> <span class="n">name</span><span class="o">=</span><span class="s2">&quot;unreachable&quot;</span><span class="p">,</span> <span class="nb">type</span><span class="o">=</span><span class="s2">&quot;server&quot;</span><span class="p">)</span>

        <span class="n">paths</span> <span class="o">=</span> <span class="n">service</span><span class="o">.</span><span class="n">find_attack_paths</span><span class="p">(</span><span class="n">source</span><span class="p">,</span> <span class="n">target</span><span class="p">)</span>
        <span class="k">assert</span> <span class="nb">len</span><span class="p">(</span><span class="n">paths</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span>
</pre></div>
</div>
</section>
<section id="integration-testing">
<h3><a class="toc-backref" href="#id5" role="doc-backlink">Integration Testing</a><a class="headerlink" href="#integration-testing" title="Link to this heading"></a></h3>
<p><strong>Database Integration Tests:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># tests/integration/test_database_integration.py</span>
<span class="kn">import</span> <span class="nn">pytest</span>
<span class="kn">from</span> <span class="nn">sqlalchemy</span> <span class="kn">import</span> <span class="n">create_engine</span>
<span class="kn">from</span> <span class="nn">app.db.session</span> <span class="kn">import</span> <span class="n">SessionLocal</span>
<span class="kn">from</span> <span class="nn">app.models.asset</span> <span class="kn">import</span> <span class="n">Asset</span>

<span class="nd">@pytest</span><span class="o">.</span><span class="n">mark</span><span class="o">.</span><span class="n">integration</span>
<span class="k">class</span> <span class="nc">TestDatabaseIntegration</span><span class="p">:</span>

    <span class="k">def</span> <span class="nf">test_asset_crud_operations</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">db_session</span><span class="p">):</span>
        <span class="c1"># Create</span>
        <span class="n">asset</span> <span class="o">=</span> <span class="n">Asset</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;test-server&quot;</span><span class="p">,</span> <span class="nb">type</span><span class="o">=</span><span class="s2">&quot;server&quot;</span><span class="p">)</span>
        <span class="n">db_session</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">asset</span><span class="p">)</span>
        <span class="n">db_session</span><span class="o">.</span><span class="n">commit</span><span class="p">()</span>

        <span class="c1"># Read</span>
        <span class="n">retrieved</span> <span class="o">=</span> <span class="n">db_session</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">Asset</span><span class="p">)</span><span class="o">.</span><span class="n">filter_by</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;test-server&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">first</span><span class="p">()</span>
        <span class="k">assert</span> <span class="n">retrieved</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span>
        <span class="k">assert</span> <span class="n">retrieved</span><span class="o">.</span><span class="n">name</span> <span class="o">==</span> <span class="s2">&quot;test-server&quot;</span>

        <span class="c1"># Update</span>
        <span class="n">retrieved</span><span class="o">.</span><span class="n">description</span> <span class="o">=</span> <span class="s2">&quot;Updated description&quot;</span>
        <span class="n">db_session</span><span class="o">.</span><span class="n">commit</span><span class="p">()</span>

        <span class="c1"># Delete</span>
        <span class="n">db_session</span><span class="o">.</span><span class="n">delete</span><span class="p">(</span><span class="n">retrieved</span><span class="p">)</span>
        <span class="n">db_session</span><span class="o">.</span><span class="n">commit</span><span class="p">()</span>

        <span class="c1"># Verify deletion</span>
        <span class="n">deleted</span> <span class="o">=</span> <span class="n">db_session</span><span class="o">.</span><span class="n">query</span><span class="p">(</span><span class="n">Asset</span><span class="p">)</span><span class="o">.</span><span class="n">filter_by</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="s2">&quot;test-server&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">first</span><span class="p">()</span>
        <span class="k">assert</span> <span class="n">deleted</span> <span class="ow">is</span> <span class="kc">None</span>
</pre></div>
</div>
<p><strong>API Integration Tests:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># tests/integration/test_api_integration.py</span>
<span class="kn">import</span> <span class="nn">pytest</span>
<span class="kn">from</span> <span class="nn">fastapi.testclient</span> <span class="kn">import</span> <span class="n">TestClient</span>
<span class="kn">from</span> <span class="nn">app.main</span> <span class="kn">import</span> <span class="n">app</span>

<span class="nd">@pytest</span><span class="o">.</span><span class="n">mark</span><span class="o">.</span><span class="n">integration</span>
<span class="k">class</span> <span class="nc">TestAPIIntegration</span><span class="p">:</span>

    <span class="nd">@pytest</span><span class="o">.</span><span class="n">fixture</span>
    <span class="k">def</span> <span class="nf">client</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="k">return</span> <span class="n">TestClient</span><span class="p">(</span><span class="n">app</span><span class="p">)</span>

    <span class="nd">@pytest</span><span class="o">.</span><span class="n">fixture</span>
    <span class="k">def</span> <span class="nf">auth_headers</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">client</span><span class="p">):</span>
        <span class="c1"># Login and get JWT token</span>
        <span class="n">response</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/api/v1/auth/login&quot;</span><span class="p">,</span> <span class="n">json</span><span class="o">=</span><span class="p">{</span>
            <span class="s2">&quot;email&quot;</span><span class="p">:</span> <span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
            <span class="s2">&quot;password&quot;</span><span class="p">:</span> <span class="s2">&quot;testpassword&quot;</span>
        <span class="p">})</span>
        <span class="n">token</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()[</span><span class="s2">&quot;access_token&quot;</span><span class="p">]</span>
        <span class="k">return</span> <span class="p">{</span><span class="s2">&quot;Authorization&quot;</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;Bearer </span><span class="si">{</span><span class="n">token</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">}</span>

    <span class="k">def</span> <span class="nf">test_asset_discovery_workflow</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">client</span><span class="p">,</span> <span class="n">auth_headers</span><span class="p">):</span>
        <span class="c1"># Start discovery</span>
        <span class="n">response</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
            <span class="s2">&quot;/api/v1/discovery/start&quot;</span><span class="p">,</span>
            <span class="n">json</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;target&quot;</span><span class="p">:</span> <span class="s2">&quot;192.168.1.0/24&quot;</span><span class="p">},</span>
            <span class="n">headers</span><span class="o">=</span><span class="n">auth_headers</span>
        <span class="p">)</span>
        <span class="k">assert</span> <span class="n">response</span><span class="o">.</span><span class="n">status_code</span> <span class="o">==</span> <span class="mi">200</span>
        <span class="n">job_id</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()[</span><span class="s2">&quot;job_id&quot;</span><span class="p">]</span>

        <span class="c1"># Check status</span>
        <span class="n">response</span> <span class="o">=</span> <span class="n">client</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;/api/v1/discovery/status/</span><span class="si">{</span><span class="n">job_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">headers</span><span class="o">=</span><span class="n">auth_headers</span><span class="p">)</span>
        <span class="k">assert</span> <span class="n">response</span><span class="o">.</span><span class="n">status_code</span> <span class="o">==</span> <span class="mi">200</span>
        <span class="k">assert</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()[</span><span class="s2">&quot;status&quot;</span><span class="p">]</span> <span class="ow">in</span> <span class="p">[</span><span class="s2">&quot;running&quot;</span><span class="p">,</span> <span class="s2">&quot;completed&quot;</span><span class="p">]</span>
</pre></div>
</div>
</section>
<section id="end-to-end-testing">
<h3><a class="toc-backref" href="#id6" role="doc-backlink">End-to-End Testing</a><a class="headerlink" href="#end-to-end-testing" title="Link to this heading"></a></h3>
<p><strong>Browser Automation Tests:</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># tests/e2e/test_user_workflows.py</span>
<span class="kn">import</span> <span class="nn">pytest</span>
<span class="kn">from</span> <span class="nn">selenium</span> <span class="kn">import</span> <span class="n">webdriver</span>
<span class="kn">from</span> <span class="nn">selenium.webdriver.common.by</span> <span class="kn">import</span> <span class="n">By</span>
<span class="kn">from</span> <span class="nn">selenium.webdriver.support.ui</span> <span class="kn">import</span> <span class="n">WebDriverWait</span>
<span class="kn">from</span> <span class="nn">selenium.webdriver.support</span> <span class="kn">import</span> <span class="n">expected_conditions</span> <span class="k">as</span> <span class="n">EC</span>

<span class="nd">@pytest</span><span class="o">.</span><span class="n">mark</span><span class="o">.</span><span class="n">e2e</span>
<span class="k">class</span> <span class="nc">TestUserWorkflows</span><span class="p">:</span>

    <span class="nd">@pytest</span><span class="o">.</span><span class="n">fixture</span>
    <span class="k">def</span> <span class="nf">driver</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="n">options</span> <span class="o">=</span> <span class="n">webdriver</span><span class="o">.</span><span class="n">ChromeOptions</span><span class="p">()</span>
        <span class="n">options</span><span class="o">.</span><span class="n">add_argument</span><span class="p">(</span><span class="s2">&quot;--headless&quot;</span><span class="p">)</span>
        <span class="n">driver</span> <span class="o">=</span> <span class="n">webdriver</span><span class="o">.</span><span class="n">Chrome</span><span class="p">(</span><span class="n">options</span><span class="o">=</span><span class="n">options</span><span class="p">)</span>
        <span class="k">yield</span> <span class="n">driver</span>
        <span class="n">driver</span><span class="o">.</span><span class="n">quit</span><span class="p">()</span>

    <span class="k">def</span> <span class="nf">test_complete_attack_path_analysis</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">driver</span><span class="p">):</span>
        <span class="c1"># Login</span>
        <span class="n">driver</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;http://localhost:3000/login&quot;</span><span class="p">)</span>
        <span class="n">driver</span><span class="o">.</span><span class="n">find_element</span><span class="p">(</span><span class="n">By</span><span class="o">.</span><span class="n">ID</span><span class="p">,</span> <span class="s2">&quot;email&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">send_keys</span><span class="p">(</span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">)</span>
        <span class="n">driver</span><span class="o">.</span><span class="n">find_element</span><span class="p">(</span><span class="n">By</span><span class="o">.</span><span class="n">ID</span><span class="p">,</span> <span class="s2">&quot;password&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">send_keys</span><span class="p">(</span><span class="s2">&quot;password&quot;</span><span class="p">)</span>
        <span class="n">driver</span><span class="o">.</span><span class="n">find_element</span><span class="p">(</span><span class="n">By</span><span class="o">.</span><span class="n">ID</span><span class="p">,</span> <span class="s2">&quot;login-button&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">click</span><span class="p">()</span>

        <span class="c1"># Navigate to attack path analysis</span>
        <span class="n">WebDriverWait</span><span class="p">(</span><span class="n">driver</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span><span class="o">.</span><span class="n">until</span><span class="p">(</span>
            <span class="n">EC</span><span class="o">.</span><span class="n">element_to_be_clickable</span><span class="p">((</span><span class="n">By</span><span class="o">.</span><span class="n">ID</span><span class="p">,</span> <span class="s2">&quot;attack-paths-menu&quot;</span><span class="p">))</span>
        <span class="p">)</span><span class="o">.</span><span class="n">click</span><span class="p">()</span>

        <span class="c1"># Configure analysis</span>
        <span class="n">source_dropdown</span> <span class="o">=</span> <span class="n">driver</span><span class="o">.</span><span class="n">find_element</span><span class="p">(</span><span class="n">By</span><span class="o">.</span><span class="n">ID</span><span class="p">,</span> <span class="s2">&quot;source-asset&quot;</span><span class="p">)</span>
        <span class="n">source_dropdown</span><span class="o">.</span><span class="n">click</span><span class="p">()</span>
        <span class="n">driver</span><span class="o">.</span><span class="n">find_element</span><span class="p">(</span><span class="n">By</span><span class="o">.</span><span class="n">XPATH</span><span class="p">,</span> <span class="s2">&quot;//option[text()=&#39;Web Server&#39;]&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">click</span><span class="p">()</span>

        <span class="n">target_dropdown</span> <span class="o">=</span> <span class="n">driver</span><span class="o">.</span><span class="n">find_element</span><span class="p">(</span><span class="n">By</span><span class="o">.</span><span class="n">ID</span><span class="p">,</span> <span class="s2">&quot;target-asset&quot;</span><span class="p">)</span>
        <span class="n">target_dropdown</span><span class="o">.</span><span class="n">click</span><span class="p">()</span>
        <span class="n">driver</span><span class="o">.</span><span class="n">find_element</span><span class="p">(</span><span class="n">By</span><span class="o">.</span><span class="n">XPATH</span><span class="p">,</span> <span class="s2">&quot;//option[text()=&#39;Database&#39;]&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">click</span><span class="p">()</span>

        <span class="c1"># Start analysis</span>
        <span class="n">driver</span><span class="o">.</span><span class="n">find_element</span><span class="p">(</span><span class="n">By</span><span class="o">.</span><span class="n">ID</span><span class="p">,</span> <span class="s2">&quot;start-analysis&quot;</span><span class="p">)</span><span class="o">.</span><span class="n">click</span><span class="p">()</span>

        <span class="c1"># Wait for results</span>
        <span class="n">WebDriverWait</span><span class="p">(</span><span class="n">driver</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span><span class="o">.</span><span class="n">until</span><span class="p">(</span>
            <span class="n">EC</span><span class="o">.</span><span class="n">presence_of_element_located</span><span class="p">((</span><span class="n">By</span><span class="o">.</span><span class="n">ID</span><span class="p">,</span> <span class="s2">&quot;analysis-results&quot;</span><span class="p">))</span>
        <span class="p">)</span>

        <span class="c1"># Verify results</span>
        <span class="n">results</span> <span class="o">=</span> <span class="n">driver</span><span class="o">.</span><span class="n">find_element</span><span class="p">(</span><span class="n">By</span><span class="o">.</span><span class="n">ID</span><span class="p">,</span> <span class="s2">&quot;analysis-results&quot;</span><span class="p">)</span>
        <span class="k">assert</span> <span class="s2">&quot;Attack paths found&quot;</span> <span class="ow">in</span> <span class="n">results</span><span class="o">.</span><span class="n">text</span>
</pre></div>
</div>
</section>
<section id="security-testing">
<h3><a class="toc-backref" href="#id7" role="doc-backlink">Security Testing</a><a class="headerlink" href="#security-testing" title="Link to this heading"></a></h3>
<p><strong>SAST (Static Application Security Testing):</strong></p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Security testing with multiple tools</span>

<span class="c1"># Bandit - Python security linter</span>
bandit<span class="w"> </span>-r<span class="w"> </span>app/<span class="w"> </span>-f<span class="w"> </span>json<span class="w"> </span>-o<span class="w"> </span>reports/bandit-results.json

<span class="c1"># Semgrep - Multi-language static analysis</span>
semgrep<span class="w"> </span>--config<span class="o">=</span>auto<span class="w"> </span>--json<span class="w"> </span>--output<span class="o">=</span>reports/semgrep-results.json<span class="w"> </span>app/

<span class="c1"># Safety - Python dependency vulnerability scanner</span>
safety<span class="w"> </span>check<span class="w"> </span>--json<span class="w"> </span>--output<span class="w"> </span>reports/safety-results.json
</pre></div>
</div>
<p><strong>DAST (Dynamic Application Security Testing):</strong></p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># tests/security/test_api_security.py</span>
<span class="kn">import</span> <span class="nn">pytest</span>
<span class="kn">import</span> <span class="nn">requests</span>
<span class="kn">from</span> <span class="nn">app.core.config</span> <span class="kn">import</span> <span class="n">settings</span>

<span class="nd">@pytest</span><span class="o">.</span><span class="n">mark</span><span class="o">.</span><span class="n">security</span>
<span class="k">class</span> <span class="nc">TestAPISecurity</span><span class="p">:</span>

    <span class="k">def</span> <span class="nf">test_sql_injection_protection</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="c1"># Test SQL injection attempts</span>
        <span class="n">malicious_payloads</span> <span class="o">=</span> <span class="p">[</span>
            <span class="s2">&quot;&#39;; DROP TABLE users; --&quot;</span><span class="p">,</span>
            <span class="s2">&quot;1&#39; OR &#39;1&#39;=&#39;1&quot;</span><span class="p">,</span>
            <span class="s2">&quot;admin&#39;/*&quot;</span><span class="p">,</span>
            <span class="s2">&quot;1; SELECT * FROM users&quot;</span>
        <span class="p">]</span>

        <span class="k">for</span> <span class="n">payload</span> <span class="ow">in</span> <span class="n">malicious_payloads</span><span class="p">:</span>
            <span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">get</span><span class="p">(</span>
                <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">settings</span><span class="o">.</span><span class="n">API_V1_STR</span><span class="si">}</span><span class="s2">/assets&quot;</span><span class="p">,</span>
                <span class="n">params</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;search&quot;</span><span class="p">:</span> <span class="n">payload</span><span class="p">}</span>
            <span class="p">)</span>
            <span class="c1"># Should not return 500 or expose database errors</span>
            <span class="k">assert</span> <span class="n">response</span><span class="o">.</span><span class="n">status_code</span> <span class="o">!=</span> <span class="mi">500</span>
            <span class="k">assert</span> <span class="s2">&quot;database&quot;</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">response</span><span class="o">.</span><span class="n">text</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span>
            <span class="k">assert</span> <span class="s2">&quot;sql&quot;</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">response</span><span class="o">.</span><span class="n">text</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span>

    <span class="k">def</span> <span class="nf">test_xss_protection</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="c1"># Test XSS attempts</span>
        <span class="n">xss_payloads</span> <span class="o">=</span> <span class="p">[</span>
            <span class="s2">&quot;&lt;script&gt;alert(&#39;xss&#39;)&lt;/script&gt;&quot;</span><span class="p">,</span>
            <span class="s2">&quot;javascript:alert(&#39;xss&#39;)&quot;</span><span class="p">,</span>
            <span class="s2">&quot;&lt;img src=x onerror=alert(&#39;xss&#39;)&gt;&quot;</span>
        <span class="p">]</span>

        <span class="k">for</span> <span class="n">payload</span> <span class="ow">in</span> <span class="n">xss_payloads</span><span class="p">:</span>
            <span class="n">response</span> <span class="o">=</span> <span class="n">requests</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
                <span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">settings</span><span class="o">.</span><span class="n">API_V1_STR</span><span class="si">}</span><span class="s2">/assets&quot;</span><span class="p">,</span>
                <span class="n">json</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="n">payload</span><span class="p">,</span> <span class="s2">&quot;type&quot;</span><span class="p">:</span> <span class="s2">&quot;server&quot;</span><span class="p">}</span>
            <span class="p">)</span>
            <span class="c1"># Should sanitize input</span>
            <span class="k">if</span> <span class="n">response</span><span class="o">.</span><span class="n">status_code</span> <span class="o">==</span> <span class="mi">200</span><span class="p">:</span>
                <span class="k">assert</span> <span class="s2">&quot;&lt;script&gt;&quot;</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">response</span><span class="o">.</span><span class="n">text</span>
                <span class="k">assert</span> <span class="s2">&quot;javascript:&quot;</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">response</span><span class="o">.</span><span class="n">text</span>
</pre></div>
</div>
</section>
<section id="performance-testing">
<h3><a class="toc-backref" href="#id8" role="doc-backlink">Performance Testing</a><a class="headerlink" href="#performance-testing" title="Link to this heading"></a></h3>
<p><strong>Load Testing Configuration:</strong></p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="c1"># locustfile.py</span>
<span class="l l-Scalar l-Scalar-Plain">from locust import HttpUser, task, between</span>

<span class="l l-Scalar l-Scalar-Plain">class BlastRadiusUser(HttpUser)</span><span class="p p-Indicator">:</span>
<span class="w">    </span><span class="l l-Scalar l-Scalar-Plain">wait_time = between(1, 3)</span>

<span class="w">    </span><span class="l l-Scalar l-Scalar-Plain">def on_start(self)</span><span class="p p-Indicator">:</span>
<span class="w">        </span><span class="c1"># Login</span>
<span class="w">        </span><span class="l l-Scalar l-Scalar-Plain">response = self.client.post(&quot;/api/v1/auth/login&quot;, json={</span>
<span class="w">            </span><span class="l l-Scalar l-Scalar-Plain">&quot;email&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;<EMAIL>&quot;</span><span class="err">,</span>
<span class="w">            </span><span class="s">&quot;password&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="s">&quot;testpassword&quot;</span>
<span class="w">        </span><span class="err">}</span><span class="l l-Scalar l-Scalar-Plain">)</span>
<span class="w">        </span><span class="l l-Scalar l-Scalar-Plain">self.token = response.json()[&quot;access_token&quot;]</span>
<span class="w">        </span><span class="l l-Scalar l-Scalar-Plain">self.headers = {&quot;Authorization&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">f&quot;Bearer {self.token}&quot;}</span>

<span class="w">    </span><span class="err">@</span><span class="l l-Scalar l-Scalar-Plain">task(3)</span>
<span class="w">    </span><span class="nt">def view_assets(self)</span><span class="p">:</span>
<span class="w">        </span><span class="l l-Scalar l-Scalar-Plain">self.client.get(&quot;/api/v1/assets&quot;, headers=self.headers)</span>

<span class="w">    </span><span class="err">@</span><span class="l l-Scalar l-Scalar-Plain">task(2)</span>
<span class="w">    </span><span class="nt">def view_attack_paths(self)</span><span class="p">:</span>
<span class="w">        </span><span class="l l-Scalar l-Scalar-Plain">self.client.get(&quot;/api/v1/attack-paths&quot;, headers=self.headers)</span>

<span class="w">    </span><span class="err">@</span><span class="l l-Scalar l-Scalar-Plain">task(1)</span>
<span class="w">    </span><span class="nt">def run_analysis(self)</span><span class="p">:</span>
<span class="w">        </span><span class="l l-Scalar l-Scalar-Plain">self.client.post(&quot;/api/v1/analysis/attack-paths&quot;,</span>
<span class="w">            </span><span class="l l-Scalar l-Scalar-Plain">json={</span>
<span class="w">                </span><span class="l l-Scalar l-Scalar-Plain">&quot;source_asset_id&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1,</span>
<span class="w">                </span><span class="l l-Scalar l-Scalar-Plain">&quot;target_asset_id&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">2,</span>
<span class="w">                </span><span class="l l-Scalar l-Scalar-Plain">&quot;max_depth&quot;</span><span class="p p-Indicator">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5</span>
<span class="w">            </span><span class="l l-Scalar l-Scalar-Plain">},</span>
<span class="w">            </span><span class="l l-Scalar l-Scalar-Plain">headers=self.headers</span>
<span class="w">        </span><span class="l l-Scalar l-Scalar-Plain">)</span>
</pre></div>
</div>
</section>
</section>
<section id="test-automation">
<h2><a class="toc-backref" href="#id9" role="doc-backlink">Test Automation</a><a class="headerlink" href="#test-automation" title="Link to this heading"></a></h2>
<section id="ci-cd-pipeline">
<h3><a class="toc-backref" href="#id10" role="doc-backlink">CI/CD Pipeline</a><a class="headerlink" href="#ci-cd-pipeline" title="Link to this heading"></a></h3>
<p><strong>GitHub Actions Workflow:</strong></p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="c1"># .github/workflows/test.yml</span>
<span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Test Suite</span>

<span class="nt">on</span><span class="p">:</span>
<span class="w">  </span><span class="nt">push</span><span class="p">:</span>
<span class="w">    </span><span class="nt">branches</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="w"> </span><span class="nv">main</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="nv">develop</span><span class="w"> </span><span class="p p-Indicator">]</span>
<span class="w">  </span><span class="nt">pull_request</span><span class="p">:</span>
<span class="w">    </span><span class="nt">branches</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="w"> </span><span class="nv">main</span><span class="w"> </span><span class="p p-Indicator">]</span>

<span class="nt">jobs</span><span class="p">:</span>
<span class="w">  </span><span class="nt">test</span><span class="p">:</span>
<span class="w">    </span><span class="nt">runs-on</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">ubuntu-latest</span>

<span class="w">    </span><span class="nt">services</span><span class="p">:</span>
<span class="w">      </span><span class="nt">postgres</span><span class="p">:</span>
<span class="w">        </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">postgres:15</span>
<span class="w">        </span><span class="nt">env</span><span class="p">:</span>
<span class="w">          </span><span class="nt">POSTGRES_PASSWORD</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">postgres</span>
<span class="w">        </span><span class="nt">options</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">&gt;-</span>
<span class="w">          </span><span class="no">--health-cmd pg_isready</span>
<span class="w">          </span><span class="no">--health-interval 10s</span>
<span class="w">          </span><span class="no">--health-timeout 5s</span>
<span class="w">          </span><span class="no">--health-retries 5</span>

<span class="w">      </span><span class="nt">redis</span><span class="p">:</span>
<span class="w">        </span><span class="nt">image</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">redis:7</span>
<span class="w">        </span><span class="nt">options</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">&gt;-</span>
<span class="w">          </span><span class="no">--health-cmd &quot;redis-cli ping&quot;</span>
<span class="w">          </span><span class="no">--health-interval 10s</span>
<span class="w">          </span><span class="no">--health-timeout 5s</span>
<span class="w">          </span><span class="no">--health-retries 5</span>

<span class="w">    </span><span class="nt">steps</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/checkout@v3</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Set up Python</span>
<span class="w">      </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">actions/setup-python@v4</span>
<span class="w">      </span><span class="nt">with</span><span class="p">:</span>
<span class="w">        </span><span class="nt">python-version</span><span class="p">:</span><span class="w"> </span><span class="s">&#39;3.11&#39;</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Install dependencies</span>
<span class="w">      </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">        </span><span class="no">pip install -r requirements-test.txt</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Run unit tests</span>
<span class="w">      </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">        </span><span class="no">pytest tests/unit/ --cov=app --cov-report=xml</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Run integration tests</span>
<span class="w">      </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">        </span><span class="no">pytest tests/integration/ --cov=app --cov-append --cov-report=xml</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Run security tests</span>
<span class="w">      </span><span class="nt">run</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">|</span>
<span class="w">        </span><span class="no">bandit -r app/ -f json -o bandit-report.json</span>
<span class="w">        </span><span class="no">safety check --json --output safety-report.json</span>

<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">name</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">Upload coverage</span>
<span class="w">      </span><span class="nt">uses</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">codecov/codecov-action@v3</span>
<span class="w">      </span><span class="nt">with</span><span class="p">:</span>
<span class="w">        </span><span class="nt">file</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">./coverage.xml</span>
</pre></div>
</div>
</section>
<section id="quality-gates">
<h3><a class="toc-backref" href="#id11" role="doc-backlink">Quality Gates</a><a class="headerlink" href="#quality-gates" title="Link to this heading"></a></h3>
<p><strong>Pre-commit Hooks:</strong></p>
<div class="highlight-yaml notranslate"><div class="highlight"><pre><span></span><span class="c1"># .pre-commit-config.yaml</span>
<span class="nt">repos</span><span class="p">:</span>
<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">repo</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">https://github.com/pre-commit/pre-commit-hooks</span>
<span class="w">    </span><span class="nt">rev</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">v4.4.0</span>
<span class="w">    </span><span class="nt">hooks</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">trailing-whitespace</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">end-of-file-fixer</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">check-yaml</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">check-added-large-files</span>

<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">repo</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">https://github.com/psf/black</span>
<span class="w">    </span><span class="nt">rev</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">23.3.0</span>
<span class="w">    </span><span class="nt">hooks</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">black</span>

<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">repo</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">https://github.com/pycqa/isort</span>
<span class="w">    </span><span class="nt">rev</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">5.12.0</span>
<span class="w">    </span><span class="nt">hooks</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">isort</span>

<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">repo</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">https://github.com/pycqa/flake8</span>
<span class="w">    </span><span class="nt">rev</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">6.0.0</span>
<span class="w">    </span><span class="nt">hooks</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">flake8</span>

<span class="w">  </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">repo</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">https://github.com/PyCQA/bandit</span>
<span class="w">    </span><span class="nt">rev</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1.7.5</span>
<span class="w">    </span><span class="nt">hooks</span><span class="p">:</span>
<span class="w">      </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">id</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">bandit</span>
<span class="w">        </span><span class="nt">args</span><span class="p">:</span><span class="w"> </span><span class="p p-Indicator">[</span><span class="s">&#39;-r&#39;</span><span class="p p-Indicator">,</span><span class="w"> </span><span class="s">&#39;app/&#39;</span><span class="p p-Indicator">]</span>
</pre></div>
</div>
</section>
</section>
<section id="test-data-management">
<h2><a class="toc-backref" href="#id12" role="doc-backlink">Test Data Management</a><a class="headerlink" href="#test-data-management" title="Link to this heading"></a></h2>
<section id="fixtures-and-factories">
<h3><a class="toc-backref" href="#id13" role="doc-backlink">Fixtures and Factories</a><a class="headerlink" href="#fixtures-and-factories" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># tests/factories.py</span>
<span class="kn">import</span> <span class="nn">factory</span>
<span class="kn">from</span> <span class="nn">app.models.asset</span> <span class="kn">import</span> <span class="n">Asset</span>
<span class="kn">from</span> <span class="nn">app.models.user</span> <span class="kn">import</span> <span class="n">User</span>

<span class="k">class</span> <span class="nc">AssetFactory</span><span class="p">(</span><span class="n">factory</span><span class="o">.</span><span class="n">Factory</span><span class="p">):</span>
    <span class="k">class</span> <span class="nc">Meta</span><span class="p">:</span>
        <span class="n">model</span> <span class="o">=</span> <span class="n">Asset</span>

    <span class="n">name</span> <span class="o">=</span> <span class="n">factory</span><span class="o">.</span><span class="n">Sequence</span><span class="p">(</span><span class="k">lambda</span> <span class="n">n</span><span class="p">:</span> <span class="sa">f</span><span class="s2">&quot;asset-</span><span class="si">{</span><span class="n">n</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">type</span> <span class="o">=</span> <span class="n">factory</span><span class="o">.</span><span class="n">Iterator</span><span class="p">([</span><span class="s2">&quot;server&quot;</span><span class="p">,</span> <span class="s2">&quot;database&quot;</span><span class="p">,</span> <span class="s2">&quot;workstation&quot;</span><span class="p">])</span>
    <span class="n">ip_address</span> <span class="o">=</span> <span class="n">factory</span><span class="o">.</span><span class="n">Faker</span><span class="p">(</span><span class="s2">&quot;ipv4&quot;</span><span class="p">)</span>
    <span class="n">description</span> <span class="o">=</span> <span class="n">factory</span><span class="o">.</span><span class="n">Faker</span><span class="p">(</span><span class="s2">&quot;text&quot;</span><span class="p">,</span> <span class="n">max_nb_chars</span><span class="o">=</span><span class="mi">200</span><span class="p">)</span>

<span class="k">class</span> <span class="nc">UserFactory</span><span class="p">(</span><span class="n">factory</span><span class="o">.</span><span class="n">Factory</span><span class="p">):</span>
    <span class="k">class</span> <span class="nc">Meta</span><span class="p">:</span>
        <span class="n">model</span> <span class="o">=</span> <span class="n">User</span>

    <span class="n">email</span> <span class="o">=</span> <span class="n">factory</span><span class="o">.</span><span class="n">Faker</span><span class="p">(</span><span class="s2">&quot;email&quot;</span><span class="p">)</span>
    <span class="n">first_name</span> <span class="o">=</span> <span class="n">factory</span><span class="o">.</span><span class="n">Faker</span><span class="p">(</span><span class="s2">&quot;first_name&quot;</span><span class="p">)</span>
    <span class="n">last_name</span> <span class="o">=</span> <span class="n">factory</span><span class="o">.</span><span class="n">Faker</span><span class="p">(</span><span class="s2">&quot;last_name&quot;</span><span class="p">)</span>
    <span class="n">is_active</span> <span class="o">=</span> <span class="kc">True</span>
</pre></div>
</div>
</section>
<section id="test-environment-setup">
<h3><a class="toc-backref" href="#id14" role="doc-backlink">Test Environment Setup</a><a class="headerlink" href="#test-environment-setup" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># conftest.py</span>
<span class="kn">import</span> <span class="nn">pytest</span>
<span class="kn">from</span> <span class="nn">sqlalchemy</span> <span class="kn">import</span> <span class="n">create_engine</span>
<span class="kn">from</span> <span class="nn">sqlalchemy.orm</span> <span class="kn">import</span> <span class="n">sessionmaker</span>
<span class="kn">from</span> <span class="nn">app.db.base</span> <span class="kn">import</span> <span class="n">Base</span>
<span class="kn">from</span> <span class="nn">app.core.config</span> <span class="kn">import</span> <span class="n">settings</span>

<span class="nd">@pytest</span><span class="o">.</span><span class="n">fixture</span><span class="p">(</span><span class="n">scope</span><span class="o">=</span><span class="s2">&quot;session&quot;</span><span class="p">)</span>
<span class="k">def</span> <span class="nf">engine</span><span class="p">():</span>
    <span class="k">return</span> <span class="n">create_engine</span><span class="p">(</span><span class="n">settings</span><span class="o">.</span><span class="n">TEST_DATABASE_URL</span><span class="p">)</span>

<span class="nd">@pytest</span><span class="o">.</span><span class="n">fixture</span><span class="p">(</span><span class="n">scope</span><span class="o">=</span><span class="s2">&quot;session&quot;</span><span class="p">)</span>
<span class="k">def</span> <span class="nf">tables</span><span class="p">(</span><span class="n">engine</span><span class="p">):</span>
    <span class="n">Base</span><span class="o">.</span><span class="n">metadata</span><span class="o">.</span><span class="n">create_all</span><span class="p">(</span><span class="n">engine</span><span class="p">)</span>
    <span class="k">yield</span>
    <span class="n">Base</span><span class="o">.</span><span class="n">metadata</span><span class="o">.</span><span class="n">drop_all</span><span class="p">(</span><span class="n">engine</span><span class="p">)</span>

<span class="nd">@pytest</span><span class="o">.</span><span class="n">fixture</span>
<span class="k">def</span> <span class="nf">db_session</span><span class="p">(</span><span class="n">engine</span><span class="p">,</span> <span class="n">tables</span><span class="p">):</span>
    <span class="n">connection</span> <span class="o">=</span> <span class="n">engine</span><span class="o">.</span><span class="n">connect</span><span class="p">()</span>
    <span class="n">transaction</span> <span class="o">=</span> <span class="n">connection</span><span class="o">.</span><span class="n">begin</span><span class="p">()</span>
    <span class="n">session</span> <span class="o">=</span> <span class="n">sessionmaker</span><span class="p">(</span><span class="n">bind</span><span class="o">=</span><span class="n">connection</span><span class="p">)()</span>

    <span class="k">yield</span> <span class="n">session</span>

    <span class="n">session</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
    <span class="n">transaction</span><span class="o">.</span><span class="n">rollback</span><span class="p">()</span>
    <span class="n">connection</span><span class="o">.</span><span class="n">close</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>
<section id="running-tests">
<h2><a class="toc-backref" href="#id15" role="doc-backlink">Running Tests</a><a class="headerlink" href="#running-tests" title="Link to this heading"></a></h2>
<section id="local-development">
<h3><a class="toc-backref" href="#id16" role="doc-backlink">Local Development</a><a class="headerlink" href="#local-development" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Install test dependencies</span>
pip<span class="w"> </span>install<span class="w"> </span>-r<span class="w"> </span>requirements-test.txt

<span class="c1"># Run all tests</span>
make<span class="w"> </span><span class="nb">test</span>

<span class="c1"># Run specific test categories</span>
make<span class="w"> </span>test-unit<span class="w">          </span><span class="c1"># Unit tests only</span>
make<span class="w"> </span>test-integration<span class="w">   </span><span class="c1"># Integration tests only</span>
make<span class="w"> </span>test-e2e<span class="w">          </span><span class="c1"># End-to-end tests only</span>
make<span class="w"> </span>test-security<span class="w">     </span><span class="c1"># Security tests only</span>
make<span class="w"> </span>test-performance<span class="w">  </span><span class="c1"># Performance tests only</span>

<span class="c1"># Run with coverage</span>
make<span class="w"> </span>test-coverage

<span class="c1"># Run specific test file</span>
pytest<span class="w"> </span>tests/test_attack_path_service.py<span class="w"> </span>-v

<span class="c1"># Run tests matching pattern</span>
pytest<span class="w"> </span>-k<span class="w"> </span><span class="s2">&quot;test_attack_path&quot;</span><span class="w"> </span>-v
</pre></div>
</div>
</section>
<section id="continuous-integration">
<h3><a class="toc-backref" href="#id17" role="doc-backlink">Continuous Integration</a><a class="headerlink" href="#continuous-integration" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># CI test commands</span>

<span class="c1"># Fast feedback loop (unit tests)</span>
pytest<span class="w"> </span>tests/unit/<span class="w"> </span>--maxfail<span class="o">=</span><span class="m">1</span><span class="w"> </span>--tb<span class="o">=</span>short

<span class="c1"># Full test suite</span>
pytest<span class="w"> </span>tests/<span class="w"> </span>--cov<span class="o">=</span>app<span class="w"> </span>--cov-report<span class="o">=</span>html<span class="w"> </span>--cov-report<span class="o">=</span>term

<span class="c1"># Security testing</span>
bandit<span class="w"> </span>-r<span class="w"> </span>app/<span class="w"> </span>-ll
safety<span class="w"> </span>check

<span class="c1"># Performance baseline</span>
locust<span class="w"> </span>--headless<span class="w"> </span>--users<span class="w"> </span><span class="m">100</span><span class="w"> </span>--spawn-rate<span class="w"> </span><span class="m">10</span><span class="w"> </span>--run-time<span class="w"> </span>60s
</pre></div>
</div>
</section>
</section>
<section id="test-reporting">
<h2><a class="toc-backref" href="#id18" role="doc-backlink">Test Reporting</a><a class="headerlink" href="#test-reporting" title="Link to this heading"></a></h2>
<section id="coverage-reports">
<h3><a class="toc-backref" href="#id19" role="doc-backlink">Coverage Reports</a><a class="headerlink" href="#coverage-reports" title="Link to this heading"></a></h3>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span><span class="c1"># Generate coverage report</span>
pytest<span class="w"> </span>--cov<span class="o">=</span>app<span class="w"> </span>--cov-report<span class="o">=</span>html<span class="w"> </span>--cov-report<span class="o">=</span>term-missing

<span class="c1"># View HTML report</span>
open<span class="w"> </span>htmlcov/index.html
</pre></div>
</div>
</section>
<section id="quality-metrics">
<h3><a class="toc-backref" href="#id20" role="doc-backlink">Quality Metrics</a><a class="headerlink" href="#quality-metrics" title="Link to this heading"></a></h3>
<p><strong>Key Quality Indicators:</strong></p>
<ul class="simple">
<li><p><strong>Test Coverage:</strong> &gt;90% overall, &gt;95% for critical components</p></li>
<li><p><strong>Test Execution Time:</strong> &lt;5 minutes for full suite</p></li>
<li><p><strong>Security Scan Results:</strong> Zero high/critical vulnerabilities</p></li>
<li><p><strong>Performance Benchmarks:</strong> Meet defined SLA requirements</p></li>
</ul>
</section>
</section>
<section id="best-practices">
<h2><a class="toc-backref" href="#id21" role="doc-backlink">Best Practices</a><a class="headerlink" href="#best-practices" title="Link to this heading"></a></h2>
<section id="test-writing-guidelines">
<h3><a class="toc-backref" href="#id22" role="doc-backlink">Test Writing Guidelines</a><a class="headerlink" href="#test-writing-guidelines" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Follow AAA Pattern</strong> - Arrange, Act, Assert</p></li>
<li><p><strong>Use Descriptive Names</strong> - Test names should explain what they test</p></li>
<li><p><strong>Test One Thing</strong> - Each test should verify one specific behavior</p></li>
<li><p><strong>Use Fixtures</strong> - Reuse common test setup</p></li>
<li><p><strong>Mock External Dependencies</strong> - Isolate units under test</p></li>
<li><p><strong>Test Edge Cases</strong> - Include boundary conditions and error cases</p></li>
</ol>
</section>
<section id="maintenance">
<h3><a class="toc-backref" href="#id23" role="doc-backlink">Maintenance</a><a class="headerlink" href="#maintenance" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Regular Test Review</strong> - Remove obsolete tests</p></li>
<li><p><strong>Update Test Data</strong> - Keep test fixtures current</p></li>
<li><p><strong>Monitor Test Performance</strong> - Optimize slow tests</p></li>
<li><p><strong>Review Coverage</strong> - Identify untested code paths</p></li>
<li><p><strong>Update Dependencies</strong> - Keep testing tools current</p></li>
</ol>
</section>
</section>
<section id="next-steps">
<h2><a class="toc-backref" href="#id24" role="doc-backlink">Next Steps</a><a class="headerlink" href="#next-steps" title="Link to this heading"></a></h2>
<p>For detailed testing guidance:</p>
<ol class="arabic simple">
<li><p>Review <a class="reference internal" href="unit-tests.html"><span class="doc">Unit Testing Guide</span></a> for comprehensive unit testing</p></li>
<li><p>Set up <span class="xref std std-doc">integration-tests</span> for service validation</p></li>
<li><p>Implement <span class="xref std std-doc">security-tests</span> for vulnerability scanning</p></li>
<li><p>Configure <span class="xref std std-doc">performance-tests</span> for load validation</p></li>
</ol>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Testing is a continuous process. Regularly review and improve your test suite
to maintain high quality and catch issues early.</p>
</div>
</section>
</section>


           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 1980, Blast-Radius Security Tool Team.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>