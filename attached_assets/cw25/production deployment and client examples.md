# Security Graph FastAPI - Production Deployment Guide

## Architecture Overview

The Security Graph API is designed for enterprise-scale deployment with the following components:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │────│   Fast<PERSON>I App   │────│   PostgreSQL    │
│   (nginx/HAProxy)│    │   (multiple     │    │   (Primary +    │
│                 │    │    instances)   │    │   Read Replicas)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │      Redis      │              │
         │              │  (Cache + Queue)│              │
         │              └─────────────────┘              │
         │                       │                       │
    ┌─────────────────┐ ┌─────────────────┐    ┌─────────────────┐
    │   Frontend      │ │  Celery Workers │    │   Monitoring    │
    │   (React/Vue)   │ │  (Background    │    │  (Prometheus +  │
    │                 │ │   Processing)   │    │   Grafana)      │
    └─────────────────┘ └─────────────────┘    └─────────────────┘
```

## Production Configuration

### 1. Environment Variables

Create a comprehensive `.env` file for production:

```bash
# Database Configuration
DATABASE_URL=postgresql://security_graph_user:<EMAIL>:5432/security_graph_prod
DATABASE_READ_REPLICA_URL=postgresql://security_graph_user:<EMAIL>:5432/security_graph_prod

# Redis Configuration
REDIS_URL=redis://redis-cluster.internal:6379/0
REDIS_SENTINEL_HOSTS=sentinel1.internal:26379,sentinel2.internal:26379,sentinel3.internal:26379

# Environment
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO
LOG_FORMAT=json

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=8
MAX_CONNECTIONS=1000

# Security
SECRET_KEY=your-super-secure-secret-key-here-use-secrets-manager
ACCESS_TOKEN_EXPIRE_MINUTES=60
ALLOWED_IPS=10.0.0.0/8,**********/16,***********/16

# External Service APIs
VIRUSTOTAL_API_KEY=your-virustotal-key
SHODAN_API_KEY=your-shodan-key
ABUSE_IPDB_API_KEY=your-abuseipdb-key

# Performance Tuning
MAX_GRAPH_DEPTH=8
BLAST_RADIUS_CACHE_TTL=3600
ENRICHMENT_CACHE_TTL=21600
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_PERIOD=60

# Monitoring
PROMETHEUS_ENABLED=true
JAEGER_ENDPOINT=http://jaeger-collector.internal:14268/api/traces
SENTRY_DSN=https://your-sentry-dsn

# Email Notifications
SMTP_HOST=smtp.company.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=smtp-password
ALERT_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>
```

### 2. Docker Production Deployment

**docker-compose.prod.yml:**

```yaml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
    networks:
      - frontend
      - backend

  api:
    build:
      context: .
      dockerfile: Dockerfile.prod
    deploy:
      replicas: 4
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - ENVIRONMENT=production
    volumes:
      - ./logs:/app/logs
    networks:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=security_graph_prod
      - POSTGRES_USER=security_graph_user
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgresql.conf:/etc/postgresql/postgresql.conf:ro
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    networks:
      - backend

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - backend

  worker:
    build:
      context: .
      dockerfile: Dockerfile.prod
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - ENVIRONMENT=production
    command: celery -A tasks.celery_app worker --loglevel=info --concurrency=4
    networks:
      - backend

  flower:
    build:
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "5555:5555"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    command: celery -A tasks.celery_app flower --basic_auth=admin:${FLOWER_PASSWORD}
    networks:
      - backend

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    networks:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - monitoring

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  frontend:
  backend:
  monitoring:
```

### 3. Nginx Configuration

**nginx.conf:**

```nginx
events {
    worker_connections 1024;
}

http {
    upstream api_backend {
        least_conn;
        server api:8000 max_fails=3 fail_timeout=30s;
    }

    map $http_upgrade $connection_upgrade {
        default upgrade;
        '' close;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=1r/s;

    server {
        listen 80;
        server_name security-graph.company.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name security-graph.company.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
        ssl_prefer_server_ciphers off;

        # Security headers
        add_header Strict-Transport-Security "max-age=63072000" always;
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;

        # API endpoints
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # Authentication endpoints with stricter rate limiting
        location ~ ^/api/(auth|login|token) {
            limit_req zone=auth burst=5 nodelay;
            proxy_pass http://api_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health check endpoint (no rate limiting)
        location /health {
            proxy_pass http://api_backend;
            access_log off;
        }

        # Frontend static files
        location / {
            root /var/www/html;
            try_files $uri $uri/ /index.html;
            expires 1h;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

### 4. Kubernetes Deployment

**k8s/namespace.yaml:**

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: security-graph
  labels:
    name: security-graph
```

**k8s/configmap.yaml:**

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: security-graph-config
  namespace: security-graph
data:
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  API_WORKERS: "4"
  RATE_LIMIT_REQUESTS: "1000"
  MAX_GRAPH_DEPTH: "8"
```

**k8s/deployment.yaml:**

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: security-graph-api
  namespace: security-graph
spec:
  replicas: 6
  selector:
    matchLabels:
      app: security-graph-api
  template:
    metadata:
      labels:
        app: security-graph-api
    spec:
      containers:
      - name: api
        image: security-graph:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: security-graph-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: security-graph-secrets
              key: redis-url
        envFrom:
        - configMapRef:
            name: security-graph-config
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

## Client Implementation Examples

### 1. Python Client

```python
# security_graph_client.py
import httpx
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime
import pandas as pd


class SecurityGraphClient:
    """Python client for Security Graph API."""
    
    def __init__(self, base_url: str, api_key: Optional[str] = None):
        self.base_url = base_url.rstrip('/')
        self.headers = {}
        if api_key:
            self.headers['Authorization'] = f'Bearer {api_key}'
    
    async def create_asset(self, asset_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new asset."""
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/assets/",
                json=asset_data,
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()
    
    async def bulk_create_assets(self, assets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create multiple assets concurrently."""
        tasks = [self.create_asset(asset) for asset in assets]
        return await asyncio.gather(*tasks)
    
    async def analyze_blast_radius(self, asset_ids: List[str], max_depth: int = 3) -> Dict[str, Any]:
        """Analyze blast radius for given assets."""
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                f"{self.base_url}/blast-radius/analyze",
                json={
                    "asset_ids": asset_ids,
                    "max_depth": max_depth,
                    "include_low_risk": False
                },
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()
    
    async def get_visualization_data(self, center_asset: Optional[str] = None, 
                                   depth: int = 2) -> Dict[str, Any]:
        """Get graph visualization data."""
        params = {"depth": depth, "include_vulnerabilities": True}
        if center_asset:
            params["center_asset_id"] = center_asset
        
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/graph/visualization",
                params=params,
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()
    
    async def import_from_csv(self, csv_file_path: str) -> List[Dict[str, Any]]:
        """Import assets from CSV file."""
        df = pd.read_csv(csv_file_path)
        
        assets = []
        for _, row in df.iterrows():
            asset_data = {
                "name": row["name"],
                "asset_type": row["asset_type"],
                "hostname": row.get("hostname"),
                "ip_address": row.get("ip_address"),
                "environment": row.get("environment", "dev"),
                "risk_score": float(row.get("risk_score", 0.0)),
                "business_unit": row.get("business_unit"),
                "owner": row.get("owner")
            }
            assets.append(asset_data)
        
        return await self.bulk_create_assets(assets)
    
    async def get_critical_assets_report(self) -> pd.DataFrame:
        """Generate a critical assets report."""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.base_url}/assets/critical",
                params={"min_risk_score": 7.0, "min_connections": 3},
                headers=self.headers
            )
            response.raise_for_status()
            critical_assets = response.json()
        
        # Convert to DataFrame for analysis
        df = pd.DataFrame(critical_assets)
        return df


# Example usage
async def main():
    client = SecurityGraphClient("https://security-graph.company.com/api")
    
    # Create sample assets
    sample_assets = [
        {
            "name": "prod-web-server-01",
            "asset_type": "application",
            "hostname": "web01.prod.company.com",
            "ip_address": "**********",
            "environment": "prod",
            "risk_score": 7.5,
            "criticality_level": "high"
        },
        {
            "name": "user-database",
            "asset_type": "database",
            "hostname": "userdb.prod.company.com",
            "ip_address": "**********",
            "environment": "prod",
            "risk_score": 9.0,
            "criticality_level": "critical",
            "data_classification": "confidential"
        }
    ]
    
    # Create assets
    created_assets = await client.bulk_create_assets(sample_assets)
    print(f"Created {len(created_assets)} assets")
    
    # Analyze blast radius
    asset_ids = [asset["id"] for asset in created_assets]
    blast_radius = await client.analyze_blast_radius(asset_ids)
    print(f"Blast radius analysis: {blast_radius['total_affected']} assets affected")
    
    # Get visualization data
    viz_data = await client.get_visualization_data()
    print(f"Graph has {len(viz_data['nodes'])} nodes and {len(viz_data['edges'])} edges")


if __name__ == "__main__":
    asyncio.run(main())
```

### 2. JavaScript/Node.js Client

```javascript
// SecurityGraphClient.js
const axios = require('axios');

class SecurityGraphClient {
    constructor(baseUrl, apiKey = null) {
        this.baseUrl = baseUrl.replace(/\/$/, '');
        this.headers = {};
        if (apiKey) {
            this.headers['Authorization'] = `Bearer ${apiKey}`;
        }
        
        this.client = axios.create({
            baseURL: this.baseUrl,
            headers: this.headers,
            timeout: 30000
        });
    }
    
    async createAsset(assetData) {
        try {
            const response = await this.client.post('/assets/', assetData);
            return response.data;
        } catch (error) {
            console.error('Error creating asset:', error.response?.data || error.message);
            throw error;
        }
    }
    
    async getAssets(filters = {}) {
        try {
            const response = await this.client.get('/assets/', { params: filters });
            return response.data;
        } catch (error) {
            console.error('Error fetching assets:', error.response?.data || error.message);
            throw error;
        }
    }
    
    async analyzeBlastRadius(assetIds, maxDepth = 3) {
        try {
            const response = await this.client.post('/blast-radius/analyze', {
                asset_ids: assetIds,
                max_depth: maxDepth,
                include_low_risk: false
            }, { timeout: 60000 });
            return response.data;
        } catch (error) {
            console.error('Error analyzing blast radius:', error.response?.data || error.message);
            throw error;
        }
    }
    
    async getVisualizationData(centerAsset = null, depth = 2) {
        try {
            const params = { 
                depth: depth, 
                include_vulnerabilities: true,
                include_relationships: true
            };
            if (centerAsset) {
                params.center_asset_id = centerAsset;
            }
            
            const response = await this.client.get('/graph/visualization', { params });
            return response.data;
        } catch (error) {
            console.error('Error fetching visualization data:', error.response?.data || error.message);
            throw error;
        }
    }
    
    async findAttackPaths(sourceAssets, targetAssets, maxLength = 5) {
        try {
            const response = await this.client.post('/attack-paths/analyze', {
                source_assets: sourceAssets,
                target_assets: targetAssets,
                max_path_length: maxLength,
                min_risk_threshold: 0.0
            });
            return response.data;
        } catch (error) {
            console.error('Error finding attack paths:', error.response?.data || error.message);
            throw error;
        }
    }
    
    async getGraphMetrics() {
        try {
            const response = await this.client.get('/metrics/graph-stats');
            return response.data;
        } catch (error) {
            console.error('Error fetching graph metrics:', error.response?.data || error.message);
            throw error;
        }
    }
}

// Example usage
async function example() {
    const client = new SecurityGraphClient('https://security-graph.company.com/api');
    
    try {
        // Create sample assets
        const webServer = await client.createAsset({
            name: 'web-server-prod-01',
            asset_type: 'application',
            hostname: 'web01.company.com',
            environment: 'prod',
            risk_score: 6.5
        });
        
        const database = await client.createAsset({
            name: 'customer-database',
            asset_type: 'database',
            hostname: 'db01.company.com',
            environment: 'prod',
            risk_score: 8.5,
            criticality_level: 'critical'
        });
        
        // Analyze blast radius
        const blastRadius = await client.analyzeBlastRadius([webServer.id]);
        console.log(`Blast radius: ${blastRadius.total_affected} assets affected`);
        
        // Get attack paths
        const attackPaths = await client.findAttackPaths([webServer.id], [database.id]);
        console.log(`Found ${attackPaths.total_paths} potential attack paths`);
        
        // Get visualization data
        const vizData = await client.getVisualizationData(webServer.id, 3);
        console.log(`Graph visualization: ${vizData.nodes.length} nodes, ${vizData.edges.length} edges`);
        
    } catch (error) {
        console.error('Example failed:', error);
    }
}

module.exports = SecurityGraphClient;
```

### 3. PowerShell Client

```powershell
# SecurityGraphClient.ps1
class SecurityGraphClient {
    [string]$BaseUrl
    [hashtable]$Headers
    
    SecurityGraphClient([string]$baseUrl, [string]$apiKey = $null) {
        $this.BaseUrl = $baseUrl.TrimEnd('/')
        $this.Headers = @{
            'Content-Type' = 'application/json'
        }
        if ($apiKey) {
            $this.Headers['Authorization'] = "Bearer $apiKey"
        }
    }
    
    [object] CreateAsset([hashtable]$assetData) {
        $uri = "$($this.BaseUrl)/assets/"
        $body = $assetData | ConvertTo-Json -Depth 10
        
        try {
            $response = Invoke-RestMethod -Uri $uri -Method Post -Headers $this.Headers -Body $body
            return $response
        }
        catch {
            Write-Error "Failed to create asset: $($_.Exception.Message)"
            throw
        }
    }
    
    [object[]] GetAssets([hashtable]$filters = @{}) {
        $uri = "$($this.BaseUrl)/assets/"
        
        if ($filters.Count -gt 0) {
            $queryString = ($filters.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "&"
            $uri += "?$queryString"
        }
        
        try {
            $response = Invoke-RestMethod -Uri $uri -Method Get -Headers $this.Headers
            return $response
        }
        catch {
            Write-Error "Failed to get assets: $($_.Exception.Message)"
            throw
        }
    }
    
    [object] AnalyzeBlastRadius([string[]]$assetIds, [int]$maxDepth = 3) {
        $uri = "$($this.BaseUrl)/blast-radius/analyze"
        $body = @{
            asset_ids = $assetIds
            max_depth = $maxDepth
            include_low_risk = $false
        } | ConvertTo-Json -Depth 10
        
        try {
            $response = Invoke-RestMethod -Uri $uri -Method Post -Headers $this.Headers -Body $body -TimeoutSec 120
            return $response
        }
        catch {
            Write-Error "Failed to analyze blast radius: $($_.Exception.Message)"
            throw
        }
    }
    
    [object] GetVisualizationData([string]$centerAsset = $null, [int]$depth = 2) {
        $uri = "$($this.BaseUrl)/graph/visualization"
        $params = @{
            depth = $depth
            include_vulnerabilities = $true
            include_relationships = $true
        }
        
        if ($centerAsset) {
            $params['center_asset_id'] = $centerAsset
        }
        
        $queryString = ($params.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "&"
        $uri += "?$queryString"
        
        try {
            $response = Invoke-RestMethod -Uri $uri -Method Get -Headers $this.Headers
            return $response
        }
        catch {
            Write-Error "Failed to get visualization data: $($_.Exception.Message)"
            throw
        }
    }
    
    [void] ExportAssetsToCSV([string]$filePath) {
        $assets = $this.GetAssets()
        $assets | Export-Csv -Path $filePath -NoTypeInformation
        Write-Host "Exported $($assets.Count) assets to $filePath"
    }
    
    [object] ImportAssetsFromCSV([string]$filePath) {
        $csvData = Import-Csv -Path $filePath
        $results = @()
        
        foreach ($row in $csvData) {
            $assetData = @{
                name = $row.name
                asset_type = $row.asset_type
                hostname = $row.hostname
                ip_address = $row.ip_address
                environment = $row.environment
                risk_score = [double]$row.risk_score
                business_unit = $row.business_unit
                owner = $row.owner
            }
            
            $result = $this.CreateAsset($assetData)
            $results += $result
        }
        
        return $results
    }
}

# Example usage
$client = [SecurityGraphClient]::new("https://security-graph.company.com/api")

# Create sample assets
$webServer = $client.CreateAsset(@{
    name = "web-server-01"
    asset_type = "application"
    hostname = "web01.company.com"
    environment = "prod"
    risk_score = 7.0
})

$database = $client.CreateAsset(@{
    name = "user-database"
    asset_type = "database" 
    hostname = "db01.company.com"
    environment = "prod"
    risk_score = 9.0
    criticality_level = "critical"
})

# Analyze blast radius
$blastRadius = $client.AnalyzeBlastRadius(@($webServer.id))
Write-Host "Blast radius affects $($blastRadius.total_affected) assets"

# Export current assets to CSV
$client.ExportAssetsToCSV("C:\temp\security_assets.csv")
```

## Monitoring and Alerting

### 1. Grafana Dashboard Configuration

```json
{
  "dashboard": {
    "title": "Security Graph Metrics",
    "panels": [
      {
        "title": "API Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{method}} {{endpoint}}"
          }
        ]
      },
      {
        "title": "Blast Radius Analyses",
        "type": "stat",
        "targets": [
          {
            "expr": "increase(blast_radius_analyses_total[1h])",
            "legendFormat": "Analyses per hour"
          }
        ]
      },
      {
        "title": "High-Risk Assets",
        "type": "table",
        "targets": [
          {
            "expr": "security_graph_high_risk_assets",
            "format": "table"
          }
        ]
      },
      {
        "title": "Graph Growth",
        "type": "graph",
        "targets": [
          {
            "expr": "security_graph_total_assets",
            "legendFormat": "Total Assets"
          },
          {
            "expr": "security_graph_total_relationships", 
            "legendFormat": "Total Relationships"
          }
        ]
      }
    ]
  }
}
```

### 2. Alert Rules

```yaml
# alerts.yml
groups:
  - name: security_graph_alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status_code=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} requests per second"
      
      - alert: CriticalAssetCompromise
        expr: security_graph_blast_radius_critical_assets > 5
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Critical asset compromise detected"
          description: "Blast radius analysis shows {{ $value }} critical assets affected"
      
      - alert: DatabaseConnectionIssue
        expr: up{job="postgresql"} == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Database connection lost"
          description: "PostgreSQL database is unreachable"
```

## Security Best Practices

### 1. Input Validation
- All API inputs are validated using Pydantic models
- SQL injection prevention through parameterized queries
- Rate limiting implemented at multiple levels
- IP whitelisting for admin endpoints

### 2. Data Protection
- Encryption at rest for sensitive data
- TLS 1.3 for data in transit
- Audit logging for all data access
- Regular database backups with encryption

### 3. Access Control
- JWT-based authentication
- Role-based access control (RBAC)
- API key management for service accounts
- Regular access reviews and rotation

### 4. Monitoring
- Comprehensive logging with structured format
- Real-time metrics collection
- Anomaly detection for unusual patterns
- Automated alerting for security events

This production-ready Security Graph FastAPI application provides comprehensive enterprise security analysis capabilities while maintaining high performance, security, and scalability standards. The modular architecture allows for easy extension and integration with existing security tools and workflows.