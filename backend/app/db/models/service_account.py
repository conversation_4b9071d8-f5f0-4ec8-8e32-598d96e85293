"""
Service Account models for Zero-Trust Architecture implementation.

This module provides service account management for machine-to-machine authentication
and authorization in a zero-trust environment.
"""

import secrets
import hashlib
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from uuid import UUID, uuid4

from sqlalchemy import Column, String, Boolean, DateTime, Integer, Text, ForeignKey, Index
from sqlalchemy.dialects.postgresql import UUID as PGUUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.ext.hybrid import hybrid_property

from app.db.base import Base
from app.core.permissions import Permission, UserRole


class ServiceAccount(Base):
    """
    Service Account model for machine-to-machine authentication.
    
    Implements zero-trust principles for service authentication:
    - Unique identity for each service
    - Scoped permissions and access controls
    - Audit trail and monitoring
    - Automatic key rotation capabilities
    """
    
    __tablename__ = "service_accounts"
    
    # Primary identification
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    name = Column(String(255), nullable=False, unique=True, index=True)
    description = Column(Text, nullable=True)
    
    # Service metadata
    service_type = Column(String(100), nullable=False, index=True)  # api, worker, integration, etc.
    environment = Column(String(50), nullable=False, index=True)  # production, staging, development
    namespace = Column(String(100), nullable=True, index=True)  # Kubernetes namespace or logical grouping
    
    # Authentication credentials
    client_id = Column(String(255), nullable=False, unique=True, index=True)
    client_secret_hash = Column(String(255), nullable=False)  # Hashed secret
    api_key_hash = Column(String(255), nullable=True)  # Optional API key
    
    # Access control
    permissions = Column(JSONB, nullable=False, default=list)  # List of permission strings
    scopes = Column(JSONB, nullable=False, default=list)  # OAuth-style scopes
    allowed_ips = Column(JSONB, nullable=True)  # IP whitelist
    allowed_user_agents = Column(JSONB, nullable=True)  # User agent patterns
    
    # Status and lifecycle
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_system = Column(Boolean, default=False, nullable=False)  # System-level service account
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_used_at = Column(DateTime, nullable=True, index=True)
    expires_at = Column(DateTime, nullable=True, index=True)
    
    # Security settings
    require_mfa = Column(Boolean, default=False, nullable=False)
    max_token_lifetime_minutes = Column(Integer, default=60, nullable=False)
    allowed_grant_types = Column(JSONB, default=["client_credentials"], nullable=False)
    
    # Rotation and security
    secret_rotation_days = Column(Integer, default=90, nullable=False)
    last_secret_rotation = Column(DateTime, default=datetime.utcnow, nullable=False)
    failed_auth_count = Column(Integer, default=0, nullable=False)
    locked_until = Column(DateTime, nullable=True)
    
    # Audit and compliance
    created_by_user_id = Column(PGUUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    last_modified_by_user_id = Column(PGUUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    
    # Metadata for monitoring and compliance
    metadata = Column(JSONB, nullable=True)  # Additional service-specific metadata
    tags = Column(JSONB, nullable=True)  # Tags for organization and filtering
    
    # Relationships
    created_by = relationship("User", foreign_keys=[created_by_user_id], backref="created_service_accounts")
    last_modified_by = relationship("User", foreign_keys=[last_modified_by_user_id])
    access_tokens = relationship("ServiceAccountToken", back_populates="service_account", cascade="all, delete-orphan")
    audit_logs = relationship("ServiceAccountAuditLog", back_populates="service_account", cascade="all, delete-orphan")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_service_account_type_env', 'service_type', 'environment'),
        Index('idx_service_account_active_expires', 'is_active', 'expires_at'),
        Index('idx_service_account_last_used', 'last_used_at'),
    )
    
    def __repr__(self) -> str:
        return f"<ServiceAccount(name={self.name}, type={self.service_type}, active={self.is_active})>"
    
    @hybrid_property
    def is_expired(self) -> bool:
        """Check if service account is expired."""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at
    
    @hybrid_property
    def is_locked(self) -> bool:
        """Check if service account is locked due to failed authentication attempts."""
        if not self.locked_until:
            return False
        return datetime.utcnow() < self.locked_until
    
    @hybrid_property
    def needs_secret_rotation(self) -> bool:
        """Check if service account secret needs rotation."""
        if not self.last_secret_rotation:
            return True
        rotation_due = self.last_secret_rotation + timedelta(days=self.secret_rotation_days)
        return datetime.utcnow() > rotation_due
    
    def generate_client_credentials(self) -> tuple[str, str]:
        """
        Generate new client ID and secret for the service account.
        
        Returns:
            Tuple of (client_id, client_secret)
        """
        # Generate client ID with service type prefix
        client_id = f"{self.service_type}_{secrets.token_urlsafe(16)}"
        
        # Generate secure client secret
        client_secret = secrets.token_urlsafe(32)
        
        # Hash the secret for storage
        self.client_id = client_id
        self.client_secret_hash = self._hash_secret(client_secret)
        
        return client_id, client_secret
    
    def verify_client_secret(self, client_secret: str) -> bool:
        """
        Verify the provided client secret against the stored hash.
        
        Args:
            client_secret: The secret to verify
            
        Returns:
            True if secret is valid, False otherwise
        """
        if not self.client_secret_hash:
            return False
        
        return self._verify_secret(client_secret, self.client_secret_hash)
    
    def rotate_secret(self) -> str:
        """
        Rotate the service account secret.
        
        Returns:
            New client secret
        """
        new_secret = secrets.token_urlsafe(32)
        self.client_secret_hash = self._hash_secret(new_secret)
        self.last_secret_rotation = datetime.utcnow()
        self.failed_auth_count = 0  # Reset failed attempts
        self.locked_until = None  # Unlock if locked
        
        return new_secret
    
    def add_permission(self, permission: Permission) -> None:
        """Add a permission to the service account."""
        if not self.permissions:
            self.permissions = []
        
        permission_str = permission.value if hasattr(permission, 'value') else str(permission)
        if permission_str not in self.permissions:
            self.permissions = self.permissions + [permission_str]
    
    def remove_permission(self, permission: Permission) -> None:
        """Remove a permission from the service account."""
        if not self.permissions:
            return
        
        permission_str = permission.value if hasattr(permission, 'value') else str(permission)
        if permission_str in self.permissions:
            self.permissions = [p for p in self.permissions if p != permission_str]
    
    def has_permission(self, permission: Permission) -> bool:
        """Check if service account has a specific permission."""
        if not self.permissions:
            return False
        
        permission_str = permission.value if hasattr(permission, 'value') else str(permission)
        return permission_str in self.permissions
    
    def add_scope(self, scope: str) -> None:
        """Add an OAuth-style scope to the service account."""
        if not self.scopes:
            self.scopes = []
        
        if scope not in self.scopes:
            self.scopes = self.scopes + [scope]
    
    def has_scope(self, scope: str) -> bool:
        """Check if service account has a specific scope."""
        if not self.scopes:
            return False
        return scope in self.scopes
    
    def record_authentication_attempt(self, success: bool, ip_address: str = None, user_agent: str = None) -> None:
        """Record an authentication attempt for audit and security monitoring."""
        if success:
            self.last_used_at = datetime.utcnow()
            self.failed_auth_count = 0
            self.locked_until = None
        else:
            self.failed_auth_count += 1
            
            # Lock account after 5 failed attempts
            if self.failed_auth_count >= 5:
                self.locked_until = datetime.utcnow() + timedelta(minutes=30)
    
    def is_ip_allowed(self, ip_address: str) -> bool:
        """Check if IP address is allowed for this service account."""
        if not self.allowed_ips:
            return True  # No restrictions
        
        return ip_address in self.allowed_ips
    
    def is_user_agent_allowed(self, user_agent: str) -> bool:
        """Check if user agent is allowed for this service account."""
        if not self.allowed_user_agents:
            return True  # No restrictions
        
        # Simple pattern matching - could be enhanced with regex
        return any(pattern in user_agent for pattern in self.allowed_user_agents)
    
    def to_dict(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """Convert service account to dictionary representation."""
        data = {
            "id": str(self.id),
            "name": self.name,
            "description": self.description,
            "service_type": self.service_type,
            "environment": self.environment,
            "namespace": self.namespace,
            "client_id": self.client_id,
            "permissions": self.permissions or [],
            "scopes": self.scopes or [],
            "is_active": self.is_active,
            "is_system": self.is_system,
            "is_expired": self.is_expired,
            "is_locked": self.is_locked,
            "needs_secret_rotation": self.needs_secret_rotation,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "last_used_at": self.last_used_at.isoformat() if self.last_used_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "metadata": self.metadata,
            "tags": self.tags,
        }
        
        if include_sensitive:
            data.update({
                "allowed_ips": self.allowed_ips,
                "allowed_user_agents": self.allowed_user_agents,
                "failed_auth_count": self.failed_auth_count,
                "locked_until": self.locked_until.isoformat() if self.locked_until else None,
            })
        
        return data
    
    @staticmethod
    def _hash_secret(secret: str) -> str:
        """Hash a secret using SHA-256 with salt."""
        salt = secrets.token_hex(16)
        hash_obj = hashlib.sha256((secret + salt).encode())
        return f"{salt}:{hash_obj.hexdigest()}"
    
    @staticmethod
    def _verify_secret(secret: str, hash_with_salt: str) -> bool:
        """Verify a secret against a hash with salt."""
        try:
            salt, stored_hash = hash_with_salt.split(":", 1)
            hash_obj = hashlib.sha256((secret + salt).encode())
            return hash_obj.hexdigest() == stored_hash
        except ValueError:
            return False


class ServiceAccountToken(Base):
    """
    Access tokens for service accounts with limited lifetime and scope.
    """
    
    __tablename__ = "service_account_tokens"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    service_account_id = Column(PGUUID(as_uuid=True), ForeignKey("service_accounts.id"), nullable=False, index=True)
    
    # Token details
    token_hash = Column(String(255), nullable=False, unique=True, index=True)
    token_type = Column(String(50), default="bearer", nullable=False)
    scopes = Column(JSONB, nullable=False, default=list)
    
    # Lifecycle
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    expires_at = Column(DateTime, nullable=False, index=True)
    last_used_at = Column(DateTime, nullable=True)
    revoked_at = Column(DateTime, nullable=True, index=True)
    
    # Usage tracking
    usage_count = Column(Integer, default=0, nullable=False)
    max_usage_count = Column(Integer, nullable=True)  # Optional usage limit
    
    # Security context
    issued_ip = Column(String(45), nullable=True)  # IPv4/IPv6
    issued_user_agent = Column(Text, nullable=True)
    
    # Relationships
    service_account = relationship("ServiceAccount", back_populates="access_tokens")
    
    def __repr__(self) -> str:
        return f"<ServiceAccountToken(service_account_id={self.service_account_id}, expires_at={self.expires_at})>"
    
    @hybrid_property
    def is_expired(self) -> bool:
        """Check if token is expired."""
        return datetime.utcnow() > self.expires_at
    
    @hybrid_property
    def is_revoked(self) -> bool:
        """Check if token is revoked."""
        return self.revoked_at is not None
    
    @hybrid_property
    def is_valid(self) -> bool:
        """Check if token is valid (not expired and not revoked)."""
        return not self.is_expired and not self.is_revoked
    
    def revoke(self) -> None:
        """Revoke the token."""
        self.revoked_at = datetime.utcnow()
    
    def record_usage(self) -> bool:
        """
        Record token usage and check if usage limit is exceeded.
        
        Returns:
            True if usage is allowed, False if limit exceeded
        """
        if self.max_usage_count and self.usage_count >= self.max_usage_count:
            return False
        
        self.usage_count += 1
        self.last_used_at = datetime.utcnow()
        return True


class ServiceAccountAuditLog(Base):
    """
    Audit log for service account activities.
    """
    
    __tablename__ = "service_account_audit_logs"
    
    id = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    service_account_id = Column(PGUUID(as_uuid=True), ForeignKey("service_accounts.id"), nullable=False, index=True)
    
    # Event details
    event_type = Column(String(100), nullable=False, index=True)  # created, updated, authenticated, etc.
    event_description = Column(Text, nullable=True)
    event_data = Column(JSONB, nullable=True)  # Additional event-specific data
    
    # Context
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    
    # Actor (who performed the action)
    actor_type = Column(String(50), nullable=False)  # user, system, service_account
    actor_id = Column(String(255), nullable=True)  # ID of the actor
    
    # Result
    success = Column(Boolean, nullable=False, index=True)
    error_message = Column(Text, nullable=True)
    
    # Relationships
    service_account = relationship("ServiceAccount", back_populates="audit_logs")
    
    def __repr__(self) -> str:
        return f"<ServiceAccountAuditLog(event_type={self.event_type}, timestamp={self.timestamp})>"
