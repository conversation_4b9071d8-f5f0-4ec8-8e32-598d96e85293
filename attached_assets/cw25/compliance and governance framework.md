# compliance/frameworks.py
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import structlog
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

from ..models import Asset, Vulnerability, Relationship
from ..services.graph_service import GraphService

logger = structlog.get_logger()


class ComplianceStatus(Enum):
    """Compliance status enumeration."""
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    PARTIALLY_COMPLIANT = "partially_compliant"
    NOT_APPLICABLE = "not_applicable"
    PENDING_REVIEW = "pending_review"


class RiskRating(Enum):
    """Risk rating enumeration."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFORMATIONAL = "informational"


@dataclass
class ComplianceResult:
    """Represents a compliance check result."""
    control_id: str
    control_name: str
    description: str
    status: ComplianceStatus
    risk_rating: RiskRating
    findings: List[str]
    affected_assets: List[str]
    remediation_steps: List[str]
    evidence: Dict[str, Any]
    last_assessed: datetime
    next_assessment: Optional[datetime] = None


class ComplianceFramework(ABC):
    """Abstract base class for compliance frameworks."""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.graph_service = GraphService(db_session)
        
    @abstractmethod
    def get_framework_name(self) -> str:
        """Return the name of the compliance framework."""
        pass
    
    @abstractmethod
    def get_controls(self) -> List[str]:
        """Return list of control IDs for this framework."""
        pass
    
    @abstractmethod
    async def assess_control(self, control_id: str) -> ComplianceResult:
        """Assess a specific control."""
        pass
    
    async def assess_all_controls(self) -> List[ComplianceResult]:
        """Assess all controls in the framework."""
        results = []
        for control_id in self.get_controls():
            try:
                result = await self.assess_control(control_id)
                results.append(result)
            except Exception as e:
                logger.error(f"Error assessing control {control_id}: {str(e)}")
                # Create error result
                error_result = ComplianceResult(
                    control_id=control_id,
                    control_name=f"Control {control_id}",
                    description="Assessment failed due to error",
                    status=ComplianceStatus.PENDING_REVIEW,
                    risk_rating=RiskRating.MEDIUM,
                    findings=[f"Assessment error: {str(e)}"],
                    affected_assets=[],
                    remediation_steps=["Review assessment configuration and retry"],
                    evidence={"error": str(e)},
                    last_assessed=datetime.utcnow()
                )
                results.append(error_result)
        
        return results


class NIST_CSF_Framework(ComplianceFramework):
    """NIST Cybersecurity Framework implementation."""
    
    def get_framework_name(self) -> str:
        return "NIST Cybersecurity Framework"
    
    def get_controls(self) -> List[str]:
        return [
            "ID.AM-1", "ID.AM-2", "ID.AM-3", "ID.AM-4", "ID.AM-5", "ID.AM-6",
            "PR.AC-1", "PR.AC-3", "PR.AC-4", "PR.AC-6", "PR.AC-7",
            "PR.DS-1", "PR.DS-2", "PR.DS-3", "PR.DS-5", "PR.DS-6",
            "PR.IP-1", "PR.IP-3", "PR.IP-4", "PR.IP-8", "PR.IP-12",
            "PR.PT-1", "PR.PT-3", "PR.PT-4",
            "DE.AE-1", "DE.AE-2", "DE.AE-3", "DE.CM-1", "DE.CM-7",
            "RS.RP-1", "RS.CO-2", "RS.AN-1", "RS.AN-3",
            "RC.RP-1", "RC.IM-1", "RC.IM-2", "RC.CO-3"
        ]
    
    async def assess_control(self, control_id: str) -> ComplianceResult:
        """Assess a specific NIST CSF control."""
        
        if control_id == "ID.AM-1":
            return await self._assess_asset_inventory()
        elif control_id == "ID.AM-2":
            return await self._assess_software_inventory()
        elif control_id == "ID.AM-3":
            return await self._assess_communication_relationships()
        elif control_id == "PR.AC-1":
            return await self._assess_access_control()
        elif control_id == "PR.DS-1":
            return await self._assess_data_protection()
        elif control_id == "PR.IP-12":
            return await self._assess_vulnerability_management()
        elif control_id == "DE.CM-1":
            return await self._assess_monitoring()
        else:
            # Default implementation for other controls
            return await self._assess_generic_control(control_id)
    
    async def _assess_asset_inventory(self) -> ComplianceResult:
        """Assess ID.AM-1: Physical devices and systems are inventoried."""
        
        # Count total assets
        total_assets = self.db.query(Asset).filter(Asset.deleted_at.is_(None)).count()
        
        # Count assets with complete information
        complete_assets = self.db.query(Asset).filter(
            Asset.deleted_at.is_(None),
            Asset.name.isnot(None),
            Asset.asset_type.isnot(None),
            Asset.environment.isnot(None),
            Asset.owner.isnot(None)
        ).count()
        
        # Calculate completeness percentage
        completeness = (complete_assets / total_assets * 100) if total_assets > 0 else 0
        
        # Identify assets with missing information
        incomplete_assets = self.db.query(Asset).filter(
            Asset.deleted_at.is_(None),
            or_(
                Asset.name.is_(None),
                Asset.owner.is_(None),
                Asset.environment.is_(None)
            )
        ).all()
        
        # Determine compliance status
        if completeness >= 95:
            status = ComplianceStatus.COMPLIANT
            risk_rating = RiskRating.LOW
        elif completeness >= 80:
            status = ComplianceStatus.PARTIALLY_COMPLIANT
            risk_rating = RiskRating.MEDIUM
        else:
            status = ComplianceStatus.NON_COMPLIANT
            risk_rating = RiskRating.HIGH
        
        findings = []
        if completeness < 100:
            findings.append(f"Asset inventory is {completeness:.1f}% complete")
            findings.append(f"{len(incomplete_assets)} assets have incomplete information")
        
        return ComplianceResult(
            control_id="ID.AM-1",
            control_name="Asset Inventory Management",
            description="Physical devices and systems within the organization are inventoried",
            status=status,
            risk_rating=risk_rating,
            findings=findings,
            affected_assets=[asset.id for asset in incomplete_assets[:10]],  # Limit to first 10
            remediation_steps=[
                "Complete missing asset information",
                "Implement automated asset discovery",
                "Establish asset inventory update procedures",
                "Assign asset owners and custodians"
            ],
            evidence={
                "total_assets": total_assets,
                "complete_assets": complete_assets,
                "completeness_percentage": completeness,
                "incomplete_count": len(incomplete_assets)
            },
            last_assessed=datetime.utcnow(),
            next_assessment=datetime.utcnow() + timedelta(days=90)
        )
    
    async def _assess_software_inventory(self) -> ComplianceResult:
        """Assess ID.AM-2: Software platforms are inventoried."""
        
        # Count software assets
        software_assets = self.db.query(Asset).filter(
            Asset.deleted_at.is_(None),
            Asset.asset_type.in_(['application', 'cloud_service', 'saas_app'])
        ).all()
        
        # Count software with version information
        versioned_software = [
            asset for asset in software_assets 
            if asset.version is not None and asset.vendor is not None
        ]
        
        completeness = (len(versioned_software) / len(software_assets) * 100) if software_assets else 100
        
        if completeness >= 90:
            status = ComplianceStatus.COMPLIANT
            risk_rating = RiskRating.LOW
        elif completeness >= 70:
            status = ComplianceStatus.PARTIALLY_COMPLIANT
            risk_rating = RiskRating.MEDIUM
        else:
            status = ComplianceStatus.NON_COMPLIANT
            risk_rating = RiskRating.HIGH
        
        unversioned_assets = [asset for asset in software_assets if asset not in versioned_software]
        
        return ComplianceResult(
            control_id="ID.AM-2",
            control_name="Software Platform Inventory",
            description="Software platforms and applications are inventoried",
            status=status,
            risk_rating=risk_rating,
            findings=[
                f"Software inventory is {completeness:.1f}% complete",
                f"{len(unversioned_assets)} software assets lack version/vendor information"
            ] if completeness < 100 else ["Software inventory is complete"],
            affected_assets=[asset.id for asset in unversioned_assets[:10]],
            remediation_steps=[
                "Update software asset records with version and vendor information",
                "Implement software discovery tools",
                "Establish software inventory maintenance procedures"
            ],
            evidence={
                "total_software_assets": len(software_assets),
                "versioned_software": len(versioned_software),
                "completeness_percentage": completeness
            },
            last_assessed=datetime.utcnow(),
            next_assessment=datetime.utcnow() + timedelta(days=90)
        )
    
    async def _assess_communication_relationships(self) -> ComplianceResult:
        """Assess ID.AM-3: Organizational communication and data flows are mapped."""
        
        graph = await self.graph_service.build_networkx_graph()
        
        # Count assets and relationships
        total_assets = len(graph.nodes())
        total_relationships = len(graph.edges())
        
        # Calculate relationship density
        max_possible_relationships = total_assets * (total_assets - 1) if total_assets > 1 else 0
        relationship_density = (total_relationships / max_possible_relationships * 100) if max_possible_relationships > 0 else 0
        
        # Count assets with documented relationships
        assets_with_relationships = len([node for node in graph.nodes() if graph.degree(node) > 0])
        coverage = (assets_with_relationships / total_assets * 100) if total_assets > 0 else 0
        
        # Assess communication flow documentation
        documented_flows = self.db.query(Relationship).filter(
            Relationship.deleted_at.is_(None),
            Relationship.protocol.isnot(None),
            Relationship.port.isnot(None)
        ).count()
        
        documentation_ratio = (documented_flows / total_relationships * 100) if total_relationships > 0 else 0
        
        if coverage >= 80 and documentation_ratio >= 70:
            status = ComplianceStatus.COMPLIANT
            risk_rating = RiskRating.LOW
        elif coverage >= 60 and documentation_ratio >= 50:
            status = ComplianceStatus.PARTIALLY_COMPLIANT
            risk_rating = RiskRating.MEDIUM
        else:
            status = ComplianceStatus.NON_COMPLIANT
            risk_rating = RiskRating.HIGH
        
        return ComplianceResult(
            control_id="ID.AM-3",
            control_name="Communication and Data Flow Mapping",
            description="Organizational communication and data flows are mapped",
            status=status,
            risk_rating=risk_rating,
            findings=[
                f"{coverage:.1f}% of assets have documented relationships",
                f"{documentation_ratio:.1f}% of relationships have technical details",
                f"Network density: {relationship_density:.2f}%"
            ],
            affected_assets=[],
            remediation_steps=[
                "Document communication flows for isolated assets",
                "Add protocol and port information to relationships",
                "Implement network discovery and mapping tools",
                "Regular review and update of network topology"
            ],
            evidence={
                "total_assets": total_assets,
                "total_relationships": total_relationships,
                "assets_with_relationships": assets_with_relationships,
                "coverage_percentage": coverage,
                "documentation_percentage": documentation_ratio,
                "relationship_density": relationship_density
            },
            last_assessed=datetime.utcnow(),
            next_assessment=datetime.utcnow() + timedelta(days=180)
        )
    
    async def _assess_vulnerability_management(self) -> ComplianceResult:
        """Assess PR.IP-12: A vulnerability management plan is developed and implemented."""
        
        # Count vulnerabilities by severity and status
        critical_open = self.db.query(Vulnerability).filter(
            Vulnerability.severity == 'critical',
            Vulnerability.status.in_(['open', 'in_progress']),
            Vulnerability.deleted_at.is_(None)
        ).count()
        
        high_open = self.db.query(Vulnerability).filter(
            Vulnerability.severity == 'high',
            Vulnerability.status.in_(['open', 'in_progress']),
            Vulnerability.deleted_at.is_(None)
        ).count()
        
        # Calculate remediation timeframes
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        ninety_days_ago = datetime.utcnow() - timedelta(days=90)
        
        overdue_critical = self.db.query(Vulnerability).filter(
            Vulnerability.severity == 'critical',
            Vulnerability.status.in_(['open', 'in_progress']),
            Vulnerability.discovered_date < thirty_days_ago,
            Vulnerability.deleted_at.is_(None)
        ).count()
        
        overdue_high = self.db.query(Vulnerability).filter(
            Vulnerability.severity == 'high',
            Vulnerability.status.in_(['open', 'in_progress']),
            Vulnerability.discovered_date < ninety_days_ago,
            Vulnerability.deleted_at.is_(None)
        ).count()
        
        # Check scanning coverage
        total_scannable_assets = self.db.query(Asset).filter(
            Asset.deleted_at.is_(None),
            Asset.asset_type.in_(['device', 'application', 'cloud_service'])
        ).count()
        
        recently_scanned = self.db.query(Asset).filter(
            Asset.deleted_at.is_(None),
            Asset.asset_type.in_(['device', 'application', 'cloud_service']),
            Asset.last_scan_date >= thirty_days_ago
        ).count()
        
        scan_coverage = (recently_scanned / total_scannable_assets * 100) if total_scannable_assets > 0 else 0
        
        # Determine compliance status
        if (overdue_critical == 0 and overdue_high == 0 and scan_coverage >= 90):
            status = ComplianceStatus.COMPLIANT
            risk_rating = RiskRating.LOW
        elif (overdue_critical <= 2 and overdue_high <= 5 and scan_coverage >= 70):
            status = ComplianceStatus.PARTIALLY_COMPLIANT
            risk_rating = RiskRating.MEDIUM
        else:
            status = ComplianceStatus.NON_COMPLIANT
            risk_rating = RiskRating.HIGH if overdue_critical > 5 else RiskRating.MEDIUM
        
        findings = []
        if critical_open > 0:
            findings.append(f"{critical_open} critical vulnerabilities remain open")
        if overdue_critical > 0:
            findings.append(f"{overdue_critical} critical vulnerabilities are overdue (>30 days)")
        if overdue_high > 0:
            findings.append(f"{overdue_high} high vulnerabilities are overdue (>90 days)")
        if scan_coverage < 100:
            findings.append(f"Vulnerability scanning coverage is {scan_coverage:.1f}%")
        
        return ComplianceResult(
            control_id="PR.IP-12",
            control_name="Vulnerability Management Plan",
            description="A vulnerability management plan is developed and implemented",
            status=status,
            risk_rating=risk_rating,
            findings=findings,
            affected_assets=[],
            remediation_steps=[
                "Remediate overdue critical vulnerabilities immediately",
                "Establish SLA for vulnerability remediation",
                "Improve vulnerability scanning coverage",
                "Implement automated vulnerability assessment",
                "Develop risk-based prioritization process"
            ],
            evidence={
                "critical_open": critical_open,
                "high_open": high_open,
                "overdue_critical": overdue_critical,
                "overdue_high": overdue_high,
                "scan_coverage_percentage": scan_coverage,
                "total_scannable_assets": total_scannable_assets,
                "recently_scanned": recently_scanned
            },
            last_assessed=datetime.utcnow(),
            next_assessment=datetime.utcnow() + timedelta(days=30)
        )
    
    async def _assess_generic_control(self, control_id: str) -> ComplianceResult:
        """Generic assessment for controls not specifically implemented."""
        
        return ComplianceResult(
            control_id=control_id,
            control_name=f"NIST CSF Control {control_id}",
            description=f"Assessment for {control_id} not yet implemented",
            status=ComplianceStatus.PENDING_REVIEW,
            risk_rating=RiskRating.MEDIUM,
            findings=["Manual assessment required"],
            affected_assets=[],
            remediation_steps=["Implement automated assessment for this control"],
            evidence={"assessment_type": "generic"},
            last_assessed=datetime.utcnow(),
            next_assessment=datetime.utcnow() + timedelta(days=90)
        )


class SOC2_Framework(ComplianceFramework):
    """SOC 2 compliance framework implementation."""
    
    def get_framework_name(self) -> str:
        return "SOC 2 Type II"
    
    def get_controls(self) -> List[str]:
        return [
            "CC1.1", "CC1.2", "CC1.3", "CC1.4",
            "CC2.1", "CC2.2", "CC2.3",
            "CC3.1", "CC3.2", "CC3.3", "CC3.4",
            "CC4.1", "CC4.2",
            "CC5.1", "CC5.2", "CC5.3",
            "CC6.1", "CC6.2", "CC6.3", "CC6.6", "CC6.7", "CC6.8",
            "CC7.1", "CC7.2", "CC7.3", "CC7.4", "CC7.5",
            "CC8.1", "CC8.2"
        ]
    
    async def assess_control(self, control_id: str) -> ComplianceResult:
        """Assess a specific SOC 2 control."""
        
        if control_id == "CC6.1":
            return await self._assess_logical_access_controls()
        elif control_id == "CC6.6":
            return await self._assess_vulnerability_management_soc2()
        elif control_id == "CC7.1":
            return await self._assess_system_monitoring()
        else:
            return await self._assess_generic_soc2_control(control_id)
    
    async def _assess_logical_access_controls(self) -> ComplianceResult:
        """Assess CC6.1: Logical access security measures."""
        
        # Count user assets and their access patterns
        user_assets = self.db.query(Asset).filter(
            Asset.deleted_at.is_(None),
            Asset.asset_type == 'user'
        ).all()
        
        # Analyze access relationships
        graph = await self.graph_service.build_networkx_graph()
        
        privileged_users = []
        excessive_access_users = []
        
        for user in user_assets:
            if user.id in graph.nodes():
                # Count what this user can access
                accessible_assets = list(graph.neighbors(user.id))
                
                # Check for privileged access (access to critical systems)
                critical_access = [
                    asset_id for asset_id in accessible_assets
                    if graph.nodes[asset_id].get('criticality_level') == 'critical'
                ]
                
                if critical_access:
                    privileged_users.append(user.id)
                
                # Check for excessive access (high number of connections)
                if len(accessible_assets) > 20:  # Threshold for excessive access
                    excessive_access_users.append(user.id)
        
        # Calculate compliance metrics
        total_users = len(user_assets)
        privileged_ratio = (len(privileged_users) / total_users * 100) if total_users > 0 else 0
        excessive_access_ratio = (len(excessive_access_users) / total_users * 100) if total_users > 0 else 0
        
        if privileged_ratio <= 10 and excessive_access_ratio <= 5:
            status = ComplianceStatus.COMPLIANT
            risk_rating = RiskRating.LOW
        elif privileged_ratio <= 20 and excessive_access_ratio <= 15:
            status = ComplianceStatus.PARTIALLY_COMPLIANT
            risk_rating = RiskRating.MEDIUM
        else:
            status = ComplianceStatus.NON_COMPLIANT
            risk_rating = RiskRating.HIGH
        
        return ComplianceResult(
            control_id="CC6.1",
            control_name="Logical Access Controls",
            description="Logical access security measures are implemented",
            status=status,
            risk_rating=risk_rating,
            findings=[
                f"{privileged_ratio:.1f}% of users have privileged access",
                f"{excessive_access_ratio:.1f}% of users have excessive access rights"
            ],
            affected_assets=privileged_users[:10] + excessive_access_users[:10],
            remediation_steps=[
                "Review and reduce privileged access assignments",
                "Implement principle of least privilege",
                "Conduct regular access reviews",
                "Implement role-based access control"
            ],
            evidence={
                "total_users": total_users,
                "privileged_users": len(privileged_users),
                "excessive_access_users": len(excessive_access_users),
                "privileged_ratio": privileged_ratio,
                "excessive_access_ratio": excessive_access_ratio
            },
            last_assessed=datetime.utcnow(),
            next_assessment=datetime.utcnow() + timedelta(days=90)
        )
    
    async def _assess_generic_soc2_control(self, control_id: str) -> ComplianceResult:
        """Generic assessment for SOC 2 controls not specifically implemented."""
        
        return ComplianceResult(
            control_id=control_id,
            control_name=f"SOC 2 Control {control_id}",
            description=f"Assessment for {control_id} requires manual review",
            status=ComplianceStatus.PENDING_REVIEW,
            risk_rating=RiskRating.MEDIUM,
            findings=["Manual assessment and evidence collection required"],
            affected_assets=[],
            remediation_steps=["Implement automated assessment for this control"],
            evidence={"assessment_type": "manual_required"},
            last_assessed=datetime.utcnow(),
            next_assessment=datetime.utcnow() + timedelta(days=90)
        )


class ISO27001_Framework(ComplianceFramework):
    """ISO 27001 compliance framework implementation."""
    
    def get_framework_name(self) -> str:
        return "ISO 27001:2022"
    
    def get_controls(self) -> List[str]:
        return [
            "A.5.1", "A.5.2", "A.5.3",
            "A.6.1", "A.6.2",
            "A.7.1", "A.7.2", "A.7.3",
            "A.8.1", "A.8.2", "A.8.3", "A.8.4", "A.8.5", "A.8.6",
            "A.9.1", "A.9.2", "A.9.3", "A.9.4",
            "A.10.1", "A.10.2",
            "A.11.1", "A.11.2",
            "A.12.1", "A.12.2", "A.12.3", "A.12.4", "A.12.5", "A.12.6",
            "A.13.1", "A.13.2",
            "A.14.1", "A.14.2", "A.14.3"
        ]
    
    async def assess_control(self, control_id: str) -> ComplianceResult:
        """Assess a specific ISO 27001 control."""
        
        if control_id == "A.8.1":
            return await self._assess_asset_management_iso()
        elif control_id == "A.12.6":
            return await self._assess_vulnerability_management_iso()
        else:
            return await self._assess_generic_iso_control(control_id)
    
    async def _assess_asset_management_iso(self) -> ComplianceResult:
        """Assess A.8.1: Asset management."""
        
        # Comprehensive asset inventory assessment
        total_assets = self.db.query(Asset).filter(Asset.deleted_at.is_(None)).count()
        
        # Check for required asset attributes
        required_attributes = ['name', 'asset_type', 'owner', 'environment', 'criticality_level']
        complete_assets = self.db.query(Asset).filter(
            Asset.deleted_at.is_(None),
            *[getattr(Asset, attr).isnot(None) for attr in required_attributes]
        ).count()
        
        # Check for data classification
        classified_assets = self.db.query(Asset).filter(
            Asset.deleted_at.is_(None),
            Asset.data_classification.isnot(None)
        ).count()
        
        # Calculate completeness scores
        basic_completeness = (complete_assets / total_assets * 100) if total_assets > 0 else 0
        classification_completeness = (classified_assets / total_assets * 100) if total_assets > 0 else 0
        
        overall_score = (basic_completeness + classification_completeness) / 2
        
        if overall_score >= 95:
            status = ComplianceStatus.COMPLIANT
            risk_rating = RiskRating.LOW
        elif overall_score >= 80:
            status = ComplianceStatus.PARTIALLY_COMPLIANT
            risk_rating = RiskRating.MEDIUM
        else:
            status = ComplianceStatus.NON_COMPLIANT
            risk_rating = RiskRating.HIGH
        
        return ComplianceResult(
            control_id="A.8.1",
            control_name="Asset Management",
            description="Assets associated with information and information processing facilities are identified and an inventory of these assets is drawn up and maintained",
            status=status,
            risk_rating=risk_rating,
            findings=[
                f"Asset inventory completeness: {basic_completeness:.1f}%",
                f"Data classification completeness: {classification_completeness:.1f}%",
                f"Overall asset management score: {overall_score:.1f}%"
            ],
            affected_assets=[],
            remediation_steps=[
                "Complete asset inventory with all required attributes",
                "Implement data classification for all assets",
                "Establish asset ownership and responsibility",
                "Regular review and update of asset inventory"
            ],
            evidence={
                "total_assets": total_assets,
                "complete_assets": complete_assets,
                "classified_assets": classified_assets,
                "basic_completeness": basic_completeness,
                "classification_completeness": classification_completeness,
                "overall_score": overall_score
            },
            last_assessed=datetime.utcnow(),
            next_assessment=datetime.utcnow() + timedelta(days=180)
        )
    
    async def _assess_generic_iso_control(self, control_id: str) -> ComplianceResult:
        """Generic assessment for ISO 27001 controls not specifically implemented."""
        
        return ComplianceResult(
            control_id=control_id,
            control_name=f"ISO 27001 Control {control_id}",
            description=f"Assessment for {control_id} requires manual implementation",
            status=ComplianceStatus.PENDING_REVIEW,
            risk_rating=RiskRating.MEDIUM,
            findings=["Control assessment requires manual review and evidence collection"],
            affected_assets=[],
            remediation_steps=["Implement control assessment procedures"],
            evidence={"assessment_type": "manual_required"},
            last_assessed=datetime.utcnow(),
            next_assessment=datetime.utcnow() + timedelta(days=180)
        )


# compliance/reporting.py
class ComplianceReportGenerator:
    """Generate compliance reports for various frameworks."""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.frameworks = {
            'nist_csf': NIST_CSF_Framework(db_session),
            'soc2': SOC2_Framework(db_session),
            'iso27001': ISO27001_Framework(db_session)
        }
    
    async def generate_comprehensive_report(self, framework_names: List[str] = None) -> Dict[str, Any]:
        """Generate a comprehensive compliance report."""
        
        if framework_names is None:
            framework_names = list(self.frameworks.keys())
        
        report = {
            "report_metadata": {
                "generated_at": datetime.utcnow().isoformat(),
                "report_type": "comprehensive_compliance_assessment",
                "frameworks_assessed": framework_names,
                "assessment_period": {
                    "start": (datetime.utcnow() - timedelta(days=90)).isoformat(),
                    "end": datetime.utcnow().isoformat()
                }
            },
            "executive_summary": {},
            "framework_assessments": {},
            "risk_summary": {},
            "recommendations": []
        }
        
        all_results = []
        framework_summaries = {}
        
        # Assess each framework
        for framework_name in framework_names:
            if framework_name in self.frameworks:
                framework = self.frameworks[framework_name]
                logger.info(f"Assessing {framework.get_framework_name()}")
                
                results = await framework.assess_all_controls()
                all_results.extend(results)
                
                # Calculate framework summary
                total_controls = len(results)
                compliant_controls = len([r for r in results if r.status == ComplianceStatus.COMPLIANT])
                non_compliant_controls = len([r for r in results if r.status == ComplianceStatus.NON_COMPLIANT])
                partially_compliant_controls = len([r for r in results if r.status == ComplianceStatus.PARTIALLY_COMPLIANT])
                
                compliance_percentage = (compliant_controls / total_controls * 100) if total_controls > 0 else 0
                
                framework_summaries[framework_name] = {
                    "framework_name": framework.get_framework_name(),
                    "total_controls": total_controls,
                    "compliant_controls": compliant_controls,
                    "partially_compliant_controls": partially_compliant_controls,
                    "non_compliant_controls": non_compliant_controls,
                    "compliance_percentage": compliance_percentage,
                    "overall_status": self._determine_overall_status(compliance_percentage)
                }
                
                report["framework_assessments"][framework_name] = {
                    "summary": framework_summaries[framework_name],
                    "control_results": [self._serialize_compliance_result(r) for r in results]
                }
        
        # Generate executive summary
        total_controls_all = len(all_results)
        total_compliant = len([r for r in all_results if r.status == ComplianceStatus.COMPLIANT])
        total_non_compliant = len([r for r in all_results if r.status == ComplianceStatus.NON_COMPLIANT])
        
        overall_compliance = (total_compliant / total_controls_all * 100) if total_controls_all > 0 else 0
        
        report["executive_summary"] = {
            "overall_compliance_percentage": overall_compliance,
            "total_controls_assessed": total_controls_all,
            "compliant_controls": total_compliant,
            "non_compliant_controls": total_non_compliant,
            "frameworks_assessed": len(framework_names),
            "overall_risk_rating": self._calculate_overall_risk(all_results),
            "key_findings": self._generate_key_findings(all_results),
            "improvement_trend": "stable"  # This would be calculated from historical data
        }
        
        # Risk summary
        risk_counts = {
            "critical": len([r for r in all_results if r.risk_rating == RiskRating.CRITICAL]),
            "high": len([r for r in all_results if r.risk_rating == RiskRating.HIGH]),
            "medium": len([r for r in all_results if r.risk_rating == RiskRating.MEDIUM]),
            "low": len([r for r in all_results if r.risk_rating == RiskRating.LOW])
        }
        
        report["risk_summary"] = {
            "risk_distribution": risk_counts,
            "high_risk_controls": [
                self._serialize_compliance_result(r) for r in all_results 
                if r.risk_rating in [RiskRating.CRITICAL, RiskRating.HIGH]
            ][:10],  # Top 10 high-risk controls
            "total_high_risk": risk_counts["critical"] + risk_counts["high"]
        }
        
        # Generate recommendations
        report["recommendations"] = self._generate_recommendations(all_results, framework_summaries)
        
        return report
    
    def _determine_overall_status(self, compliance_percentage: float) -> str:
        """Determine overall compliance status."""
        if compliance_percentage >= 95:
            return "excellent"
        elif compliance_percentage >= 80:
            return "good"
        elif compliance_percentage >= 60:
            return "needs_improvement"
        else:
            return "poor"
    
    def _calculate_overall_risk(self, results: List[ComplianceResult]) -> str:
        """Calculate overall risk rating."""
        risk_scores = {
            RiskRating.CRITICAL: 4,
            RiskRating.HIGH: 3,
            RiskRating.MEDIUM: 2,
            RiskRating.LOW: 1,
            RiskRating.INFORMATIONAL: 0
        }
        
        if not results:
            return "unknown"
        
        avg_risk_score = sum(risk_scores.get(r.risk_rating, 2) for r in results) / len(results)
        
        if avg_risk_score >= 3.5:
            return "critical"
        elif avg_risk_score >= 2.5:
            return "high"
        elif avg_risk_score >= 1.5:
            return "medium"
        else:
            return "low"
    
    def _generate_key_findings(self, results: List[ComplianceResult]) -> List[str]:
        """Generate key findings from assessment results."""
        findings = []
        
        # Critical findings
        critical_results = [r for r in results if r.risk_rating == RiskRating.CRITICAL]
        if critical_results:
            findings.append(f"{len(critical_results)} critical compliance gaps identified requiring immediate attention")
        
        # Most common issues
        all_findings = []
        for result in results:
            all_findings.extend(result.findings)
        
        # Simple frequency analysis (in production, use more sophisticated NLP)
        common_terms = ["vulnerability", "access", "inventory", "monitoring", "documentation"]
        for term in common_terms:
            count = sum(1 for finding in all_findings if term in finding.lower())
            if count >= 3:
                findings.append(f"{term.title()} management issues identified across {count} controls")
        
        # Framework-specific insights
        nist_results = [r for r in results if r.control_id.startswith('ID.') or r.control_id.startswith('PR.')]
        if nist_results:
            nist_compliance = len([r for r in nist_results if r.status == ComplianceStatus.COMPLIANT]) / len(nist_results) * 100
            findings.append(f"NIST Cybersecurity Framework compliance at {nist_compliance:.1f}%")
        
        return findings[:5]  # Limit to top 5 findings
    
    def _generate_recommendations(self, results: List[ComplianceResult], framework_summaries: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate actionable recommendations."""
        recommendations = []
        
        # Priority 1: Critical and high-risk items
        high_risk_results = [r for r in results if r.risk_rating in [RiskRating.CRITICAL, RiskRating.HIGH]]
        if high_risk_results:
            recommendations.append({
                "priority": "critical",
                "title": "Address High-Risk Compliance Gaps",
                "description": f"Immediately remediate {len(high_risk_results)} high-risk compliance issues",
                "timeline": "0-30 days",
                "effort": "high",
                "impact": "high",
                "controls_affected": [r.control_id for r in high_risk_results[:5]]
            })
        
        # Priority 2: Framework-specific improvements
        for framework_name, summary in framework_summaries.items():
            if summary["compliance_percentage"] < 80:
                recommendations.append({
                    "priority": "high",
                    "title": f"Improve {summary['framework_name']} Compliance",
                    "description": f"Focus on {summary['framework_name']} compliance currently at {summary['compliance_percentage']:.1f}%",
                    "timeline": "30-90 days",
                    "effort": "medium",
                    "impact": "medium",
                    "framework": framework_name
                })
        
        # Priority 3: Asset management improvements
        asset_related = [r for r in results if "asset" in r.control_name.lower() or "inventory" in r.control_name.lower()]
        non_compliant_asset = [r for r in asset_related if r.status == ComplianceStatus.NON_COMPLIANT]
        if non_compliant_asset:
            recommendations.append({
                "priority": "medium",
                "title": "Strengthen Asset Management",
                "description": "Improve asset inventory and management practices",
                "timeline": "60-180 days",
                "effort": "medium",
                "impact": "high",
                "controls_affected": [r.control_id for r in non_compliant_asset]
            })
        
        # Priority 4: Monitoring and detection
        monitoring_related = [r for r in results if "monitor" in r.control_name.lower() or "detect" in r.control_name.lower()]
        if monitoring_related:
            non_compliant_monitoring = [r for r in monitoring_related if r.status != ComplianceStatus.COMPLIANT]
            if non_compliant_monitoring:
                recommendations.append({
                    "priority": "medium",
                    "title": "Enhance Security Monitoring",
                    "description": "Implement comprehensive security monitoring and detection capabilities",
                    "timeline": "90-180 days",
                    "effort": "high",
                    "impact": "high",
                    "controls_affected": [r.control_id for r in non_compliant_monitoring]
                })
        
        return recommendations
    
    def _serialize_compliance_result(self, result: ComplianceResult) -> Dict[str, Any]:
        """Serialize compliance result for JSON output."""
        return {
            "control_id": result.control_id,
            "control_name": result.control_name,
            "description": result.description,
            "status": result.status.value,
            "risk_rating": result.risk_rating.value,
            "findings": result.findings,
            "affected_assets_count": len(result.affected_assets),
            "remediation_steps": result.remediation_steps,
            "evidence_summary": {k: v for k, v in result.evidence.items() if isinstance(v, (int, float, str, bool))},
            "last_assessed": result.last_assessed.isoformat(),
            "next_assessment": result.next_assessment.isoformat() if result.next_assessment else None
        }
    
    async def generate_executive_dashboard_data(self) -> Dict[str, Any]:
        """Generate data for executive compliance dashboard."""
        
        # Quick assessment of key metrics
        total_assets = self.db.query(Asset).filter(Asset.deleted_at.is_(None)).count()
        
        # Critical vulnerabilities
        critical_vulns = self.db.query(Vulnerability).filter(
            Vulnerability.severity == 'critical',
            Vulnerability.status.in_(['open', 'in_progress']),
            Vulnerability.deleted_at.is_(None)
        ).count()
        
        # Assets without recent scans
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        unscanned_assets = self.db.query(Asset).filter(
            Asset.deleted_at.is_(None),
            or_(Asset.last_scan_date.is_(None), Asset.last_scan_date < thirty_days_ago)
        ).count()
        
        # Quick compliance scores (simplified)
        asset_completeness = await self._calculate_asset_inventory_completeness()
        vuln_management_score = await self._calculate_vulnerability_management_score()
        
        return {
            "last_updated": datetime.utcnow().isoformat(),
            "key_metrics": {
                "total_assets": total_assets,
                "critical_vulnerabilities": critical_vulns,
                "unscanned_assets": unscanned_assets,
                "asset_inventory_completeness": asset_completeness,
                "vulnerability_management_score": vuln_management_score
            },
            "compliance_scores": {
                "nist_csf_estimated": min(100, max(0, 80 - (critical_vulns * 5) + (asset_completeness * 0.2))),
                "soc2_estimated": min(100, max(0, 75 + (vuln_management_score * 0.25))),
                "iso27001_estimated": min(100, max(0, 70 + (asset_completeness * 0.3)))
            },
            "trend_indicators": {
                "risk_trend": "stable",  # Would be calculated from historical data
                "compliance_trend": "improving",
                "vulnerability_trend": "decreasing"
            },
            "next_actions": [
                "Address critical vulnerabilities",
                "Complete asset inventory",
                "Update security monitoring"
            ]
        }
    
    async def _calculate_asset_inventory_completeness(self) -> float:
        """Calculate asset inventory completeness percentage."""
        total_assets = self.db.query(Asset).filter(Asset.deleted_at.is_(None)).count()
        
        if total_assets == 0:
            return 0.0
        
        complete_assets = self.db.query(Asset).filter(
            Asset.deleted_at.is_(None),
            Asset.name.isnot(None),
            Asset.asset_type.isnot(None),
            Asset.owner.isnot(None),
            Asset.environment.isnot(None)
        ).count()
        
        return (complete_assets / total_assets) * 100
    
    async def _calculate_vulnerability_management_score(self) -> float:
        """Calculate vulnerability management effectiveness score."""
        total_vulns = self.db.query(Vulnerability).filter(
            Vulnerability.deleted_at.is_(None)
        ).count()
        
        if total_vulns == 0:
            return 100.0  # No vulnerabilities = perfect score
        
        # Score based on remediation timeliness and severity handling
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        ninety_days_ago = datetime.utcnow() - timedelta(days=90)
        
        overdue_critical = self.db.query(Vulnerability).filter(
            Vulnerability.severity == 'critical',
            Vulnerability.status.in_(['open', 'in_progress']),
            Vulnerability.discovered_date < thirty_days_ago,
            Vulnerability.deleted_at.is_(None)
        ).count()
        
        overdue_high = self.db.query(Vulnerability).filter(
            Vulnerability.severity == 'high',
            Vulnerability.status.in_(['open', 'in_progress']),
            Vulnerability.discovered_date < ninety_days_ago,
            Vulnerability.deleted_at.is_(None)
        ).count()
        
        # Simple scoring algorithm
        base_score = 100
        base_score -= (overdue_critical * 20)  # -20 points per overdue critical
        base_score -= (overdue_high * 10)     # -10 points per overdue high
        
        return max(0, min(100, base_score))


# API endpoints for compliance
@app.get("/compliance/frameworks", response_model=List[Dict[str, str]])
async def list_compliance_frameworks():
    """List available compliance frameworks."""
    return [
        {"id": "nist_csf", "name": "NIST Cybersecurity Framework", "version": "1.1"},
        {"id": "soc2", "name": "SOC 2 Type II", "version": "2017"},
        {"id": "iso27001", "name": "ISO 27001", "version": "2022"}
    ]


@app.post("/compliance/assess/{framework_id}", response_model=Dict[str, Any])
async def assess_compliance_framework(
    framework_id: str,
    control_ids: Optional[List[str]] = None,
    db: Session = Depends(get_db)
):
    """Assess compliance for a specific framework."""
    try:
        reporter = ComplianceReportGenerator(db)
        
        if framework_id not in reporter.frameworks:
            raise HTTPException(status_code=404, detail=f"Framework {framework_id} not found")
        
        framework = reporter.frameworks[framework_id]
        
        if control_ids:
            # Assess specific controls
            results = []
            for control_id in control_ids:
                result = await framework.assess_control(control_id)
                results.append(reporter._serialize_compliance_result(result))
        else:
            # Assess all controls
            results = await framework.assess_all_controls()
            results = [reporter._serialize_compliance_result(r) for r in results]
        
        return {
            "framework_id": framework_id,
            "framework_name": framework.get_framework_name(),
            "assessment_timestamp": datetime.utcnow().isoformat(),
            "controls_assessed": len(results),
            "results": results
        }
        
    except Exception as e:
        logger.error(f"Error in compliance assessment: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Assessment failed: {str(e)}")


@app.post("/compliance/report/comprehensive", response_model=Dict[str, Any])
async def generate_comprehensive_compliance_report(
    framework_ids: Optional[List[str]] = None,
    db: Session = Depends(get_db)
):
    """Generate a comprehensive compliance report across multiple frameworks."""
    try:
        reporter = ComplianceReportGenerator(db)
        report = await reporter.generate_comprehensive_report(framework_ids)
        
        return report
        
    except Exception as e:
        logger.error(f"Error generating compliance report: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Report generation failed: {str(e)}")


@app.get("/compliance/dashboard", response_model=Dict[str, Any])
async def get_compliance_dashboard(db: Session = Depends(get_db)):
    """Get compliance dashboard data for executives."""
    try:
        reporter = ComplianceReportGenerator(db)
        dashboard_data = await reporter.generate_executive_dashboard_data()
        
        return dashboard_data
        
    except Exception as e:
        logger.error(f"Error generating dashboard data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Dashboard generation failed: {str(e)}")


@app.get("/compliance/report/export/{format}")
async def export_compliance_report(
    format: str = Path(..., regex="^(pdf|xlsx|csv)$"),
    framework_ids: Optional[List[str]] = Query(default=None),
    db: Session = Depends(get_db)
):
    """Export compliance report in various formats."""
    try:
        reporter = ComplianceReportGenerator(db)
        report_data = await reporter.generate_comprehensive_report(framework_ids)
        
        if format == "pdf":
            # Generate PDF report (would require additional PDF library)
            content = generate_pdf_report(report_data)
            media_type = "application/pdf"
            filename = f"compliance_report_{datetime.utcnow().strftime('%Y%m%d')}.pdf"
        elif format == "xlsx":
            # Generate Excel report (would require openpyxl or xlsxwriter)
            content = generate_excel_report(report_data)
            media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            filename = f"compliance_report_{datetime.utcnow().strftime('%Y%m%d')}.xlsx"
        elif format == "csv":
            # Generate CSV export
            content = generate_csv_report(report_data)
            media_type = "text/csv"
            filename = f"compliance_report_{datetime.utcnow().strftime('%Y%m%d')}.csv"
        
        return Response(
            content=content,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except Exception as e:
        logger.error(f"Error exporting report: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Export failed: {str(e)}")


def generate_pdf_report(report_data: Dict[str, Any]) -> bytes:
    """Generate PDF report from compliance data."""
    # This would use a library like reportlab or weasyprint
    # For now, return placeholder
    return b"PDF report content would be generated here"


def generate_excel_report(report_data: Dict[str, Any]) -> bytes:
    """Generate Excel report from compliance data."""
    # This would use openpyxl or xlsxwriter
    # For now, return placeholder
    return b"Excel report content would be generated here"


def generate_csv_report(report_data: Dict[str, Any]) -> str:
    """Generate CSV export from compliance data."""
    import csv
    import io
    
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow([
        "Framework", "Control ID", "Control Name", "Status", 
        "Risk Rating", "Findings Count", "Last Assessed"
    ])
    
    # Write data
    for framework_name, framework_data in report_data.get("framework_assessments", {}).items():
        for result in framework_data.get("control_results", []):
            writer.writerow([
                framework_name,
                result["control_id"],
                result["control_name"],
                result["status"],
                result["risk_rating"],
                len(result.get("findings", [])),
                result["last_assessed"]
            ])
    
    return output.getvalue()