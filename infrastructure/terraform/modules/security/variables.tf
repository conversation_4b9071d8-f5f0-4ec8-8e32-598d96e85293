# Security Module Variables for Blast-Radius Security Tool

variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "blast-radius"
}

variable "environment" {
  description = "Environment name (dev, staging, production)"
  type        = string
  validation {
    condition     = contains(["dev", "staging", "production"], var.environment)
    error_message = "Environment must be one of: dev, staging, production."
  }
}

variable "vpc_id" {
  description = "VPC ID where security resources will be created"
  type        = string
}

variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default = {
    Project     = "blast-radius"
    ManagedBy   = "terraform"
    Environment = "unknown"
  }
}

# KMS Configuration
variable "kms_deletion_window" {
  description = "KMS key deletion window in days"
  type        = number
  default     = 7
  validation {
    condition     = var.kms_deletion_window >= 7 && var.kms_deletion_window <= 30
    error_message = "KMS deletion window must be between 7 and 30 days."
  }
}

# Secrets Manager Configuration
variable "secret_recovery_window" {
  description = "Secrets Manager recovery window in days"
  type        = number
  default     = 7
  validation {
    condition     = var.secret_recovery_window >= 7 && var.secret_recovery_window <= 30
    error_message = "Secret recovery window must be between 7 and 30 days."
  }
}

# Database Credentials
variable "database_username" {
  description = "Database username"
  type        = string
  default     = "blast_radius_user"
  sensitive   = true
}

variable "database_password" {
  description = "Database password"
  type        = string
  sensitive   = true
}

variable "database_host" {
  description = "Database host"
  type        = string
  default     = "localhost"
}

variable "database_port" {
  description = "Database port"
  type        = number
  default     = 5432
}

variable "database_name" {
  description = "Database name"
  type        = string
  default     = "blast_radius"
}

# API Keys and Secrets
variable "jwt_secret_key" {
  description = "JWT secret key for authentication"
  type        = string
  sensitive   = true
}

variable "api_key" {
  description = "API key for external integrations"
  type        = string
  sensitive   = true
}

variable "webhook_secret" {
  description = "Webhook secret for secure communications"
  type        = string
  sensitive   = true
}

# WAF Configuration
variable "waf_rate_limit" {
  description = "WAF rate limit per 5-minute period"
  type        = number
  default     = 2000
  validation {
    condition     = var.waf_rate_limit >= 100 && var.waf_rate_limit <= 20000000
    error_message = "WAF rate limit must be between 100 and 20,000,000."
  }
}

variable "waf_log_retention_days" {
  description = "WAF log retention in days"
  type        = number
  default     = 30
  validation {
    condition = contains([
      1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1827, 3653
    ], var.waf_log_retention_days)
    error_message = "WAF log retention must be a valid CloudWatch Logs retention value."
  }
}

variable "allowed_ip_addresses" {
  description = "List of IP addresses allowed to access the application"
  type        = list(string)
  default     = []
  validation {
    condition = alltrue([
      for ip in var.allowed_ip_addresses : can(cidrhost(ip, 0))
    ])
    error_message = "All IP addresses must be valid CIDR blocks."
  }
}

# Security Policies
variable "enable_encryption_at_rest" {
  description = "Enable encryption at rest for all resources"
  type        = bool
  default     = true
}

variable "enable_encryption_in_transit" {
  description = "Enable encryption in transit for all communications"
  type        = bool
  default     = true
}

variable "enable_waf_logging" {
  description = "Enable WAF request logging"
  type        = bool
  default     = true
}

variable "enable_detailed_monitoring" {
  description = "Enable detailed CloudWatch monitoring"
  type        = bool
  default     = true
}

# Compliance Configuration
variable "compliance_framework" {
  description = "Compliance framework (SOC2, ISO27001, etc.)"
  type        = string
  default     = "SOC2"
  validation {
    condition     = contains(["SOC2", "ISO27001", "PCI-DSS", "HIPAA"], var.compliance_framework)
    error_message = "Compliance framework must be one of: SOC2, ISO27001, PCI-DSS, HIPAA."
  }
}

variable "data_classification" {
  description = "Data classification level"
  type        = string
  default     = "confidential"
  validation {
    condition     = contains(["public", "internal", "confidential", "restricted"], var.data_classification)
    error_message = "Data classification must be one of: public, internal, confidential, restricted."
  }
}

# Security Monitoring
variable "enable_security_hub" {
  description = "Enable AWS Security Hub"
  type        = bool
  default     = true
}

variable "enable_guardduty" {
  description = "Enable AWS GuardDuty"
  type        = bool
  default     = true
}

variable "enable_config" {
  description = "Enable AWS Config for compliance monitoring"
  type        = bool
  default     = true
}

variable "enable_cloudtrail" {
  description = "Enable AWS CloudTrail for API logging"
  type        = bool
  default     = true
}

# Access Control
variable "mfa_required" {
  description = "Require MFA for all users"
  type        = bool
  default     = true
}

variable "password_policy_minimum_length" {
  description = "Minimum password length"
  type        = number
  default     = 14
  validation {
    condition     = var.password_policy_minimum_length >= 8 && var.password_policy_minimum_length <= 128
    error_message = "Password minimum length must be between 8 and 128 characters."
  }
}

variable "password_policy_require_symbols" {
  description = "Require symbols in passwords"
  type        = bool
  default     = true
}

variable "password_policy_require_numbers" {
  description = "Require numbers in passwords"
  type        = bool
  default     = true
}

variable "password_policy_require_uppercase" {
  description = "Require uppercase letters in passwords"
  type        = bool
  default     = true
}

variable "password_policy_require_lowercase" {
  description = "Require lowercase letters in passwords"
  type        = bool
  default     = true
}

# Backup and Recovery
variable "backup_retention_days" {
  description = "Backup retention period in days"
  type        = number
  default     = 30
  validation {
    condition     = var.backup_retention_days >= 1 && var.backup_retention_days <= 35
    error_message = "Backup retention must be between 1 and 35 days."
  }
}

variable "enable_point_in_time_recovery" {
  description = "Enable point-in-time recovery for databases"
  type        = bool
  default     = true
}

# Network Security
variable "enable_vpc_flow_logs" {
  description = "Enable VPC Flow Logs"
  type        = bool
  default     = true
}

variable "enable_network_acls" {
  description = "Enable Network ACLs for additional security"
  type        = bool
  default     = true
}

variable "enable_security_groups_strict_mode" {
  description = "Enable strict mode for security groups (least privilege)"
  type        = bool
  default     = true
}

# Incident Response
variable "security_notification_email" {
  description = "Email address for security notifications"
  type        = string
  default     = ""
}

variable "enable_automated_response" {
  description = "Enable automated incident response"
  type        = bool
  default     = false
}

variable "security_response_lambda_timeout" {
  description = "Timeout for security response Lambda functions"
  type        = number
  default     = 300
  validation {
    condition     = var.security_response_lambda_timeout >= 60 && var.security_response_lambda_timeout <= 900
    error_message = "Lambda timeout must be between 60 and 900 seconds."
  }
}
