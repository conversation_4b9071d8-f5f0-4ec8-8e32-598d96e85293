# Security Module Outputs for Blast-Radius Security Tool

# KMS Outputs
output "kms_key_id" {
  description = "ID of the KMS key"
  value       = aws_kms_key.main.key_id
}

output "kms_key_arn" {
  description = "ARN of the KMS key"
  value       = aws_kms_key.main.arn
}

output "kms_alias_name" {
  description = "Name of the KMS alias"
  value       = aws_kms_alias.main.name
}

output "kms_alias_arn" {
  description = "ARN of the KMS alias"
  value       = aws_kms_alias.main.arn
}

# Secrets Manager Outputs
output "database_secret_arn" {
  description = "ARN of the database credentials secret"
  value       = aws_secretsmanager_secret.database.arn
}

output "database_secret_name" {
  description = "Name of the database credentials secret"
  value       = aws_secretsmanager_secret.database.name
}

output "api_keys_secret_arn" {
  description = "ARN of the API keys secret"
  value       = aws_secretsmanager_secret.api_keys.arn
}

output "api_keys_secret_name" {
  description = "Name of the API keys secret"
  value       = aws_secretsmanager_secret.api_keys.name
}

# WAF Outputs
output "waf_web_acl_id" {
  description = "ID of the WAF Web ACL"
  value       = aws_wafv2_web_acl.main.id
}

output "waf_web_acl_arn" {
  description = "ARN of the WAF Web ACL"
  value       = aws_wafv2_web_acl.main.arn
}

output "waf_web_acl_name" {
  description = "Name of the WAF Web ACL"
  value       = aws_wafv2_web_acl.main.name
}

output "waf_ip_set_id" {
  description = "ID of the WAF IP Set (if created)"
  value       = length(aws_wafv2_ip_set.allowed_ips) > 0 ? aws_wafv2_ip_set.allowed_ips[0].id : null
}

output "waf_ip_set_arn" {
  description = "ARN of the WAF IP Set (if created)"
  value       = length(aws_wafv2_ip_set.allowed_ips) > 0 ? aws_wafv2_ip_set.allowed_ips[0].arn : null
}

# CloudWatch Logs Outputs
output "waf_log_group_name" {
  description = "Name of the WAF CloudWatch Log Group"
  value       = aws_cloudwatch_log_group.waf.name
}

output "waf_log_group_arn" {
  description = "ARN of the WAF CloudWatch Log Group"
  value       = aws_cloudwatch_log_group.waf.arn
}

# Security Group Outputs
output "waf_alb_security_group_id" {
  description = "ID of the WAF ALB security group"
  value       = aws_security_group.waf_alb.id
}

output "waf_alb_security_group_arn" {
  description = "ARN of the WAF ALB security group"
  value       = aws_security_group.waf_alb.arn
}

# Security Configuration Summary
output "security_configuration" {
  description = "Summary of security configuration"
  value = {
    kms_key_rotation_enabled    = true
    secrets_manager_enabled     = true
    waf_enabled                = true
    waf_rate_limit             = var.waf_rate_limit
    encryption_at_rest         = var.enable_encryption_at_rest
    encryption_in_transit      = var.enable_encryption_in_transit
    compliance_framework       = var.compliance_framework
    data_classification        = var.data_classification
    mfa_required              = var.mfa_required
  }
}

# Compliance Outputs
output "compliance_framework" {
  description = "Compliance framework applied"
  value       = var.compliance_framework
}

output "data_classification" {
  description = "Data classification level"
  value       = var.data_classification
}

# Security Monitoring Outputs
output "security_monitoring_enabled" {
  description = "Security monitoring services enabled"
  value = {
    security_hub = var.enable_security_hub
    guardduty    = var.enable_guardduty
    config       = var.enable_config
    cloudtrail   = var.enable_cloudtrail
    vpc_flow_logs = var.enable_vpc_flow_logs
  }
}

# Access Control Outputs
output "password_policy" {
  description = "Password policy configuration"
  value = {
    minimum_length      = var.password_policy_minimum_length
    require_symbols     = var.password_policy_require_symbols
    require_numbers     = var.password_policy_require_numbers
    require_uppercase   = var.password_policy_require_uppercase
    require_lowercase   = var.password_policy_require_lowercase
    mfa_required       = var.mfa_required
  }
}

# Backup and Recovery Outputs
output "backup_configuration" {
  description = "Backup and recovery configuration"
  value = {
    retention_days              = var.backup_retention_days
    point_in_time_recovery     = var.enable_point_in_time_recovery
  }
}

# Network Security Outputs
output "network_security_configuration" {
  description = "Network security configuration"
  value = {
    vpc_flow_logs_enabled      = var.enable_vpc_flow_logs
    network_acls_enabled       = var.enable_network_acls
    security_groups_strict_mode = var.enable_security_groups_strict_mode
  }
}

# Incident Response Outputs
output "incident_response_configuration" {
  description = "Incident response configuration"
  value = {
    notification_email     = var.security_notification_email != "" ? "configured" : "not_configured"
    automated_response     = var.enable_automated_response
    lambda_timeout        = var.security_response_lambda_timeout
  }
}

# Security Metrics for Monitoring
output "security_metrics" {
  description = "Security metrics for monitoring and alerting"
  value = {
    waf_blocked_requests   = "${var.project_name}-waf-blocked-requests"
    waf_allowed_requests   = "${var.project_name}-waf-allowed-requests"
    kms_key_usage         = "${var.project_name}-kms-key-usage"
    secrets_access        = "${var.project_name}-secrets-access"
    security_group_changes = "${var.project_name}-sg-changes"
  }
}

# Resource ARNs for IAM Policies
output "resource_arns" {
  description = "ARNs of security resources for IAM policy creation"
  value = {
    kms_key              = aws_kms_key.main.arn
    database_secret      = aws_secretsmanager_secret.database.arn
    api_keys_secret      = aws_secretsmanager_secret.api_keys.arn
    waf_web_acl         = aws_wafv2_web_acl.main.arn
    waf_log_group       = aws_cloudwatch_log_group.waf.arn
  }
}

# Tags Applied
output "applied_tags" {
  description = "Tags applied to security resources"
  value       = var.common_tags
}

# Security Best Practices Status
output "security_best_practices" {
  description = "Status of security best practices implementation"
  value = {
    encryption_at_rest_enabled     = var.enable_encryption_at_rest
    encryption_in_transit_enabled  = var.enable_encryption_in_transit
    key_rotation_enabled          = true
    secrets_encrypted             = true
    waf_protection_enabled        = true
    detailed_monitoring_enabled   = var.enable_detailed_monitoring
    compliance_aligned           = var.compliance_framework != ""
    data_classified              = var.data_classification != ""
  }
}
