Running Sphinx v7.4.7
Warning: sphinxcontrib.mermaid not available. Mermaid diagrams will be shown as code blocks.
loading translations [en]... done
Converting `source_suffix = ['.rst', '.md']` to `source_suffix = {'.rst': 'restructuredtext', '.md': 'restructuredtext'}`.
loading pickled environment... done
[autosummary] generating autosummary for: DOCUMENTATION_EXPANSION_SUMMARY.md, DOCUMENTATION_SUMMARY.md, LOCAL-DEVELOPMENT.md, README.md, access-control/least-privilege-framework.rst, api/asset-management.rst, api/attack-path-analysis.rst, api/authentication.rst, api/index.rst, api/mitre-attack-integration.rst, ..., user-guides/attack-path-analysis.rst, user-guides/compliance-officers.rst, user-guides/executive-leadership.rst, user-guides/index.rst, user-guides/mitre-attack-integration.rst, user-guides/purple-team-members.rst, user-guides/red-team-members.rst, user-guides/security-architects.rst, user-guides/soc-operators.rst, user-guides/threat-modeling.rst
myst v4.0.0: MdParserConfig(commonmark_only=False, gfm_only=False, enable_extensions={'colon_fence', 'deflist', 'smartquotes', 'substitution', 'tasklist', 'dollarmath', 'html_image', 'html_admonition', 'replacements', 'fieldlist', 'strikethrough'}, disable_syntax=[], all_links_external=False, links_external_new_tab=False, url_schemes=('http', 'https', 'mailto', 'ftp'), ref_domains=None, fence_as_directive=set(), number_code_blocks=[], title_to_header=False, heading_anchors=0, heading_slug_func=None, html_meta={}, footnote_sort=True, footnote_transition=True, words_per_minute=200, substitutions={}, linkify_fuzzy_links=True, dmath_allow_labels=True, dmath_allow_space=True, dmath_allow_digits=True, dmath_double_inline=False, update_mathjax=True, mathjax_classes='tex2jax_process|mathjax_process|math|output_area', enable_checkboxes=False, suppress_warnings=[], highlight_code_blocks=True)
building [mo]: targets for 0 po files that are out of date
writing output... 
building [html]: targets for 0 source files that are out of date
updating environment: 0 added, 0 changed, 0 removed
reading sources... 
looking for now-outdated files... none found
no targets are out of date.
build succeeded.

The HTML pages are in _build/html.
